<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pizza Corner - Fast Food Delivery</title>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Roboto:wght@400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            background: #f8f9fa;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(231, 76, 60, 0.3);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-family: 'Fredoka One', cursive;
            font-size: 22px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .delivery-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 4px;
            backdrop-filter: blur(10px);
        }

        /* Desktop Header */
        .desktop-header {
            display: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            padding: 90px 0 50px;
            margin-top: 70px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            opacity: 0.2;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: #f39c12;
            color: white;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 900;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        .hero-title {
            font-family: 'Fredoka One', cursive;
            font-size: 36px;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 16px;
            margin-bottom: 32px;
            opacity: 0.95;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
        }

        .cta-button {
            background: #f39c12;
            color: white;
            padding: 16px 32px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 900;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.4);
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 200px;
            justify-content: center;
        }

        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(243, 156, 18, 0.5);
        }

        .cta-secondary {
            background: transparent;
            border: 2px solid white;
            color: white;
        }

        .cta-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        /* Quick Categories */
        .quick-categories {
            padding: 40px 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .section-badge {
            display: inline-block;
            background: #e74c3c;
            color: white;
            padding: 6px 16px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 900;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .section-title {
            font-family: 'Fredoka One', cursive;
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 12px;
        }

        .section-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            max-width: 500px;
            margin: 0 auto;
        }

        .categories-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding: 0 16px 16px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .categories-scroll::-webkit-scrollbar {
            display: none;
        }

        .category-card {
            min-width: 100px;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            border-radius: 16px;
            padding: 20px 12px;
            text-align: center;
            text-decoration: none;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: scale(0);
            transition: transform 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-6px) scale(1.05);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
        }

        .category-card:hover::before {
            transform: scale(1);
        }

        .category-card:nth-child(2) {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.2);
        }

        .category-card:nth-child(3) {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
        }

        .category-card:nth-child(4) {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.2);
        }

        .category-card:nth-child(5) {
            background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
            box-shadow: 0 4px 15px rgba(155, 89, 182, 0.2);
        }

        .category-icon {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
            position: relative;
            z-index: 2;
        }

        .category-name {
            font-size: 12px;
            font-weight: 700;
            position: relative;
            z-index: 2;
        }

        /* Featured Items */
        .featured-items {
            padding: 40px 0;
            background: #f8f9fa;
        }

        .items-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .item-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .item-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            border-color: #e74c3c;
        }

        .item-image {
            width: 100%;
            height: 160px;
            object-fit: cover;
        }

        .item-content {
            padding: 16px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .item-title {
            font-family: 'Fredoka One', cursive;
            font-size: 16px;
            color: #2c3e50;
            margin-bottom: 4px;
        }

        .item-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: #f39c12;
            font-weight: 700;
        }

        .item-price {
            font-family: 'Fredoka One', cursive;
            font-size: 18px;
            color: #e74c3c;
        }

        .item-description {
            font-size: 13px;
            color: #7f8c8d;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-tags {
            display: flex;
            gap: 4px;
        }

        .tag {
            background: #ecf0f1;
            color: #2c3e50;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 9px;
            font-weight: 700;
            text-transform: uppercase;
        }

        .tag.bestseller {
            background: #e74c3c;
            color: white;
        }

        .tag.new {
            background: #27ae60;
            color: white;
        }

        .tag.spicy {
            background: #f39c12;
            color: white;
        }

        .order-btn {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 900;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .order-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ecf0f1;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 15px rgba(0,0,0,0.1);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            text-decoration: none;
            color: #7f8c8d;
            transition: color 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: #e74c3c;
        }

        .nav-item i {
            font-size: 18px;
        }

        .nav-item span {
            font-size: 9px;
            font-weight: 700;
            text-transform: uppercase;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 999;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Desktop Styles */
        @media (min-width: 768px) {
            body {
                padding-bottom: 0;
            }

            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: block;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                border-bottom: 1px solid #ecf0f1;
                box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            }

            .desktop-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .desktop-logo {
                font-family: 'Fredoka One', cursive;
                font-size: 28px;
                color: #e74c3c;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .desktop-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .desktop-menu a {
                text-decoration: none;
                color: #2c3e50;
                font-weight: 700;
                transition: color 0.3s ease;
                text-transform: uppercase;
                font-size: 14px;
                letter-spacing: 0.5px;
            }

            .desktop-menu a:hover {
                color: #e74c3c;
            }

            .hero {
                margin-top: 80px;
                padding: 100px 0 60px;
            }

            .hero-title {
                font-size: 52px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .hero-buttons {
                flex-direction: row;
                justify-content: center;
            }

            .categories-scroll {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 16px;
                padding: 0;
                overflow: visible;
            }

            .items-grid {
                grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
                gap: 20px;
            }

            .bottom-nav {
                display: none;
            }

            .whatsapp-chat {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-pizza-slice"></i>
                Pizza Corner
            </div>
            <div class="delivery-info">
                <i class="fas fa-motorcycle"></i>
                <span>30 Min Delivery</span>
            </div>
        </div>
    </header>

    <!-- Desktop Header -->
    <header class="desktop-header">
        <nav class="desktop-nav">
            <a href="#" class="desktop-logo">
                <i class="fas fa-pizza-slice"></i>
                Pizza Corner
            </a>
            <ul class="desktop-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#menu">मेन्यू</a></li>
                <li><a href="#deals">डील्स</a></li>
                <li><a href="#track">ट्रैक ऑर्डर</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-fire"></i>
                    Hot & Fresh
                </div>
                <h1 class="hero-title">
                    Fast Food<br>
                    Delivered Fast!
                </h1>
                <p class="hero-subtitle">
                    गर्मागर्म पिज्जा, बर्गर और फास्ट फूड सिर्फ 30 मिनट में। 
                    अब ऑर्डर करें और मजे लें!
                </p>
                <div class="hero-buttons">
                    <a href="#menu" class="cta-button">
                        <i class="fas fa-utensils"></i>
                        Order Now
                    </a>
                    <a href="tel:+919876543210" class="cta-button cta-secondary">
                        <i class="fas fa-phone"></i>
                        Call Now
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Categories -->
    <section class="quick-categories">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Quick Order</div>
                <h2 class="section-title">Fast Categories</h2>
                <p class="section-subtitle">
                    तुरंत ऑर्डर करने के लिए कैटेगरी चुनें
                </p>
            </div>

            <div class="categories-scroll">
                <a href="#menu" class="category-card">
                    <i class="fas fa-pizza-slice category-icon"></i>
                    <div class="category-name">Pizza</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-hamburger category-icon"></i>
                    <div class="category-name">Burger</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-drumstick-bite category-icon"></i>
                    <div class="category-name">Chicken</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-french-fries category-icon"></i>
                    <div class="category-name">Sides</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-glass-whiskey category-icon"></i>
                    <div class="category-name">Drinks</div>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Items -->
    <section class="featured-items" id="menu">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Bestsellers</div>
                <h2 class="section-title">Popular Items</h2>
                <p class="section-subtitle">
                    सबसे ज्यादा ऑर्डर किए जाने वाले आइटम्स
                </p>
            </div>

            <div class="items-grid">
                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Margherita Pizza" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Margherita Pizza</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (250)</span>
                                </div>
                            </div>
                            <div class="item-price">₹299</div>
                        </div>
                        <p class="item-description">
                            फ्रेश मोज़ेरेला चीज़, टोमेटो सॉस और बेसिल के साथ क्लासिक इटैलियन पिज्जा
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag bestseller">Bestseller</span>
                                <span class="tag">Veg</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Margherita Pizza', 299)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Chicken Burger" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Chicken Burger</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.7 (180)</span>
                                </div>
                            </div>
                            <div class="item-price">₹199</div>
                        </div>
                        <p class="item-description">
                            जूसी चिकन पैटी, फ्रेश लेट्यूस, टोमेटो और स्पेशल सॉस के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag bestseller">Bestseller</span>
                                <span class="tag spicy">Spicy</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Chicken Burger', 199)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Pepperoni Pizza" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Pepperoni Pizza</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (320)</span>
                                </div>
                            </div>
                            <div class="item-price">₹399</div>
                        </div>
                        <p class="item-description">
                            स्पाइसी पेप्परोनी, मोज़ेरेला चीज़ और टोमेटो सॉस के साथ क्लासिक अमेरिकन पिज्जा
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag new">New</span>
                                <span class="tag spicy">Spicy</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Pepperoni Pizza', 399)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1626645738196-c2a7c87a8f58?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Chicken Wings" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Buffalo Wings</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.6 (140)</span>
                                </div>
                            </div>
                            <div class="item-price">₹249</div>
                        </div>
                        <p class="item-description">
                            क्रिस्पी चिकन विंग्स बफैलो सॉस के साथ, सेलेरी स्टिक्स और रैंच डिप के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag spicy">Spicy</span>
                                <span class="tag">Crispy</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Buffalo Wings', 249)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1573080496219-bb080dd4f877?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="French Fries" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Loaded Fries</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.5 (95)</span>
                                </div>
                            </div>
                            <div class="item-price">₹149</div>
                        </div>
                        <p class="item-description">
                            क्रिस्पी फ्रेंच फ्राइज़ चीज़ सॉस, बेकन बिट्स और ग्रीन ऑनियन के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag">Crispy</span>
                                <span class="tag">Cheesy</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Loaded Fries', 149)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1544145945-f90425340c7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Coke" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">Cold Drinks Combo</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.4 (75)</span>
                                </div>
                            </div>
                            <div class="item-price">₹99</div>
                        </div>
                        <p class="item-description">
                            कोका-कोला, स्प्राइट या फैंटा - 2 कैन्स का कॉम्बो ऑफर
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag">Combo</span>
                                <span class="tag">Refreshing</span>
                            </div>
                            <button class="order-btn" onclick="orderFood('Cold Drinks Combo', 99)">
                                Order Now
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>Home</span>
            </a>
            <a href="#menu" class="nav-item">
                <i class="fas fa-utensils"></i>
                <span>Menu</span>
            </a>
            <a href="#deals" class="nav-item">
                <i class="fas fa-tags"></i>
                <span>Deals</span>
            </a>
            <a href="#track" class="nav-item">
                <i class="fas fa-motorcycle"></i>
                <span>Track</span>
            </a>
            <a href="#contact" class="nav-item">
                <i class="fas fa-phone"></i>
                <span>Contact</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=Hi! I want to order from Pizza Corner." class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Order food function
        function orderFood(itemName, price) {
            const message = `🍕 *Pizza Corner - Fast Food Order*

🍽️ *Item:* ${itemName}
💰 *Price:* ₹${price}

Please confirm this order. Thank you!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showFastNotification(`${itemName} added to cart! 🛒`);
        }

        // Show fast notification
        function showFastNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 90px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #e74c3c, #c0392b);
                color: white;
                padding: 14px 28px;
                border-radius: 25px;
                font-family: 'Fredoka One', cursive;
                font-size: 14px;
                z-index: 2000;
                box-shadow: 0 6px 25px rgba(231, 76, 60, 0.4);
                border: 2px solid #f39c12;
                animation: fastPop 0.4s ease;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes fastPop {
                    0% {
                        transform: translateX(-50%) scale(0.5) rotate(-10deg);
                        opacity: 0;
                    }
                    50% {
                        transform: translateX(-50%) scale(1.1) rotate(5deg);
                        opacity: 1;
                    }
                    100% {
                        transform: translateX(-50%) scale(1) rotate(0deg);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'fastPop 0.4s ease reverse';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 400);
                }
            }, 3000);
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to section
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add energetic hover effects
        document.querySelectorAll('.item-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-6px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Category card dynamic effects
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.1) rotate(5deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1) rotate(0deg)';
            });
        });

        // Add pulsing effect to order buttons
        document.querySelectorAll('.order-btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.animation = 'pulse 0.6s infinite';
            });

            btn.addEventListener('mouseleave', function() {
                this.style.animation = 'none';
            });
        });

        // Add pulse animation
        const pulseStyle = document.createElement('style');
        pulseStyle.textContent = `
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(pulseStyle);

        // Add delivery timer effect
        function updateDeliveryTimer() {
            const deliveryInfo = document.querySelector('.delivery-info span');
            if (deliveryInfo) {
                const minutes = Math.floor(Math.random() * 5) + 25; // 25-30 minutes
                deliveryInfo.textContent = `${minutes} Min Delivery`;
            }
        }

        // Update timer every 30 seconds
        setInterval(updateDeliveryTimer, 30000);
    </script>
</body>
</html>
