<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoodieHub - Street Food Paradise</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: #fff8e1;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 12px 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-size: 20px;
            font-weight: 900;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .cart-icon {
            position: relative;
            font-size: 20px;
            cursor: pointer;
        }

        .cart-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #e53e3e;
            color: white;
            border-radius: 50%;
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
        }

        /* Desktop Header (Hidden on Mobile) */
        .desktop-header {
            display: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 80px 0 40px;
            margin-top: 60px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            opacity: 0.2;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 16px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .hero-button {
            background: white;
            color: #ff6b35;
            padding: 16px 32px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 800;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
            transition: transform 0.2s ease;
        }

        .hero-button:hover {
            transform: translateY(-2px);
        }

        /* Categories Section */
        .categories {
            padding: 40px 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 24px;
            font-weight: 900;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .section-subtitle {
            font-size: 14px;
            color: #718096;
        }

        .categories-scroll {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding: 0 16px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .categories-scroll::-webkit-scrollbar {
            display: none;
        }

        .category-card {
            min-width: 120px;
            background: linear-gradient(135deg, #ffd89b 0%, #19547b 100%);
            border-radius: 16px;
            padding: 20px 16px;
            text-align: center;
            text-decoration: none;
            color: white;
            transition: transform 0.2s ease;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .category-card:hover {
            transform: translateY(-4px);
        }

        .category-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
        }

        .category-name {
            font-size: 14px;
            font-weight: 700;
        }

        /* Featured Items */
        .featured {
            padding: 40px 0;
            background: #fff8e1;
        }

        .items-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .item-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .item-card:hover {
            transform: translateY(-2px);
        }

        .item-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .item-content {
            padding: 20px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .item-title {
            font-size: 18px;
            font-weight: 800;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .item-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #f7931e;
            font-weight: 700;
        }

        .item-price {
            font-size: 20px;
            font-weight: 900;
            color: #ff6b35;
        }

        .item-description {
            font-size: 14px;
            color: #718096;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-tags {
            display: flex;
            gap: 6px;
        }

        .tag {
            background: #fed7d7;
            color: #c53030;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
        }

        .tag.veg {
            background: #c6f6d5;
            color: #22543d;
        }

        .add-btn {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .add-btn:hover {
            transform: scale(1.05);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e2e8f0;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            text-decoration: none;
            color: #718096;
            transition: color 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: #ff6b35;
        }

        .nav-item i {
            font-size: 20px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 700;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 999;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Desktop Styles */
        @media (min-width: 768px) {
            body {
                padding-bottom: 0;
            }

            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: block;
                background: white;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
            }

            .desktop-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .desktop-logo {
                font-size: 24px;
                font-weight: 900;
                color: #ff6b35;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .desktop-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .desktop-menu a {
                text-decoration: none;
                color: #2d3748;
                font-weight: 600;
                transition: color 0.2s ease;
            }

            .desktop-menu a:hover {
                color: #ff6b35;
            }

            .hero {
                margin-top: 80px;
                padding: 100px 0 60px;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .categories-scroll {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 20px;
                padding: 0;
                overflow: visible;
            }

            .items-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 24px;
            }

            .bottom-nav {
                display: none;
            }

            .whatsapp-chat {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-utensils"></i>
                FoodieHub
            </div>
            <div class="cart-icon">
                <i class="fas fa-shopping-cart"></i>
                <span class="cart-badge">0</span>
            </div>
        </div>
    </header>

    <!-- Desktop Header -->
    <header class="desktop-header">
        <nav class="desktop-nav">
            <a href="#" class="desktop-logo">
                <i class="fas fa-utensils"></i>
                FoodieHub
            </a>
            <ul class="desktop-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#menu">मेन्यू</a></li>
                <li><a href="#offers">ऑफर्स</a></li>
                <li><a href="#about">हमारे बारे में</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-fire"></i>
                    असली स्ट्रीट फूड का स्वाद
                </div>
                <h1 class="hero-title">
                    गली का स्वाद<br>
                    घर तक पहुंचाएं
                </h1>
                <p class="hero-subtitle">
                    मुंह में पानी लाने वाला स्ट्रीट फूड अब सिर्फ एक क्लिक दूर। 
                    ताज़ा बना, गर्मागर्म डिलीवरी।
                </p>
                <a href="#menu" class="hero-button">
                    <i class="fas fa-search"></i>
                    मेन्यू देखें
                </a>
            </div>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">स्ट्रीट फूड कैटेगरी</h2>
                <p class="section-subtitle">हर स्वाद के लिए कुछ खास</p>
            </div>

            <div class="categories-scroll">
                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);">
                    <i class="fas fa-pepper-hot category-icon"></i>
                    <span class="category-name">चाट</span>
                </a>

                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);">
                    <i class="fas fa-cookie-bite category-icon"></i>
                    <span class="category-name">समोसा</span>
                </a>

                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);">
                    <i class="fas fa-bread-slice category-icon"></i>
                    <span class="category-name">पाव भाजी</span>
                </a>

                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #a8caba 0%, #5d4e75 100%);">
                    <i class="fas fa-seedling category-icon"></i>
                    <span class="category-name">दोसा</span>
                </a>

                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%);">
                    <i class="fas fa-ice-cream category-icon"></i>
                    <span class="category-name">कुल्फी</span>
                </a>

                <a href="#menu" class="category-card" style="background: linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%);">
                    <i class="fas fa-glass-whiskey category-icon"></i>
                    <span class="category-name">जूस</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Items -->
    <section class="featured" id="menu">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">आज के स्पेशल</h2>
                <p class="section-subtitle">सबसे ज्यादा पसंद किए जाने वाले</p>
            </div>

            <div class="items-grid">
                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1606491956689-2ea866880c84?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Pani Puri" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">पानी पूरी</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (120)</span>
                                </div>
                            </div>
                            <div class="item-price">₹40</div>
                        </div>
                        <p class="item-description">
                            खट्टे-मीठे पानी के साथ क्रिस्पी पूरी, आलू और चटनी का मजेदार कॉम्बो
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">स्पाइसी</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('पानी पूरी', 40)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1601050690597-df0568f70950?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Pav Bhaji" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">पाव भाजी</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (200)</span>
                                </div>
                            </div>
                            <div class="item-price">₹80</div>
                        </div>
                        <p class="item-description">
                            मक्खन के साथ तली हुई पाव और मसालेदार भाजी, प्याज और नींबू के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">बटरी</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('पाव भाजी', 80)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1630383249896-424e482df921?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Vada Pav" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">वड़ा पाव</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.7 (150)</span>
                                </div>
                            </div>
                            <div class="item-price">₹25</div>
                        </div>
                        <p class="item-description">
                            मुंबई का फेमस वड़ा पाव, तीखी चटनी और हरी मिर्च के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">क्रिस्पी</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('वड़ा पाव', 25)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Masala Dosa" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">मसाला डोसा</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.6 (90)</span>
                                </div>
                            </div>
                            <div class="item-price">₹60</div>
                        </div>
                        <p class="item-description">
                            साउथ इंडियन स्टाइल क्रिस्पी डोसा, आलू मसाला और चटनी के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">हेल्दी</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('मसाला डोसा', 60)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1574484284002-952d92456975?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Chole Bhature" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">छोले भटूरे</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (180)</span>
                                </div>
                            </div>
                            <div class="item-price">₹90</div>
                        </div>
                        <p class="item-description">
                            फूले हुए भटूरे के साथ मसालेदार छोले, प्याज और अचार के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">हेवी</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('छोले भटूरे', 90)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Kulfi" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">मलाई कुल्फी</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (250)</span>
                                </div>
                            </div>
                            <div class="item-price">₹35</div>
                        </div>
                        <p class="item-description">
                            देसी दूध से बनी क्रीमी कुल्फी, पिस्ता और बादाम के टुकड़ों के साथ
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag veg">वेज</span>
                                <span class="tag">कोल्ड</span>
                            </div>
                            <button class="add-btn" onclick="addToCart('मलाई कुल्फी', 35)">
                                <i class="fas fa-plus"></i> ऐड करें
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>होम</span>
            </a>
            <a href="#menu" class="nav-item">
                <i class="fas fa-utensils"></i>
                <span>मेन्यू</span>
            </a>
            <a href="#offers" class="nav-item">
                <i class="fas fa-tags"></i>
                <span>ऑफर्स</span>
            </a>
            <a href="#about" class="nav-item">
                <i class="fas fa-info-circle"></i>
                <span>हमारे बारे में</span>
            </a>
            <a href="#contact" class="nav-item">
                <i class="fas fa-phone"></i>
                <span>संपर्क</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं FoodieHub से स्ट्रीट फूड ऑर्डर करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        let cart = [];
        let cartCount = 0;

        // Add to cart function
        function addToCart(itemName, price) {
            cart.push({name: itemName, price: price});
            cartCount++;
            updateCartBadge();

            const message = `🍽️ *FoodieHub - स्ट्रीट फूड ऑर्डर*

📋 *आइटम:* ${itemName}
💰 *प्राइस:* ₹${price}

कृपया इस ऑर्डर को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showNotification(`${itemName} कार्ट में ऐड हो गया! 🛒`);
        }

        // Update cart badge
        function updateCartBadge() {
            const badge = document.querySelector('.cart-badge');
            badge.textContent = cartCount;
            badge.style.display = cartCount > 0 ? 'flex' : 'none';
        }

        // Show notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #ff6b35, #f7931e);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                font-weight: 700;
                font-size: 14px;
                z-index: 2000;
                box-shadow: 0 4px 20px rgba(0,0,0,0.2);
                animation: slideDown 0.3s ease;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideDown {
                    from { transform: translateX(-50%) translateY(-20px); opacity: 0; }
                    to { transform: translateX(-50%) translateY(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to section
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 60;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 60;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Initialize cart badge
        updateCartBadge();
    </script>
</body>
</html>
