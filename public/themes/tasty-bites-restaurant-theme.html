<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tasty Bites - Restaurant & Food Delivery</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            line-height: 1.6;
            color: #2d3748;
            background: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 800;
            color: #e53e3e;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-menu {
            display: none;
        }

        .menu-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #2d3748;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);
            color: white;
            padding: 80px 0 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            opacity: 0.2;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .hero-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
        }

        .cta-button {
            background: #ffffff;
            color: #e53e3e;
            padding: 16px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            width: 100%;
            max-width: 280px;
            justify-content: center;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        .cta-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        /* Menu Categories */
        .menu-categories {
            padding: 60px 0;
            background: #f7fafc;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 800;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .category-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease;
            text-decoration: none;
            color: inherit;
        }

        .category-card:hover {
            transform: translateY(-4px);
        }

        .category-image {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 16px;
        }

        .category-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .category-count {
            font-size: 14px;
            color: #718096;
        }

        /* Featured Menu */
        .featured-menu {
            padding: 60px 0;
            background: white;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .menu-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            display: flex;
            gap: 16px;
            transition: transform 0.2s ease;
        }

        .menu-item:hover {
            transform: translateY(-2px);
        }

        .menu-image {
            width: 100px;
            height: 100px;
            border-radius: 8px;
            object-fit: cover;
            flex-shrink: 0;
        }

        .menu-content {
            flex: 1;
        }

        .menu-title {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .menu-description {
            font-size: 14px;
            color: #718096;
            margin-bottom: 12px;
            line-height: 1.5;
        }

        .menu-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-price {
            font-size: 18px;
            font-weight: 800;
            color: #e53e3e;
        }

        .order-btn {
            background: #e53e3e;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .order-btn:hover {
            background: #c53030;
        }

        /* Features */
        .features {
            padding: 60px 0;
            background: #f7fafc;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            background: white;
            padding: 24px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: #fed7d7;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e53e3e;
            font-size: 20px;
            flex-shrink: 0;
        }

        .feature-content h3 {
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .feature-content p {
            color: #718096;
            line-height: 1.6;
        }

        /* Contact Section */
        .contact {
            padding: 60px 0;
            background: white;
        }

        .contact-info {
            background: #f7fafc;
            border-radius: 12px;
            padding: 32px 24px;
            text-align: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            margin-bottom: 16px;
            font-size: 16px;
            color: #2d3748;
        }

        .contact-item i {
            color: #e53e3e;
            width: 20px;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Footer */
        .footer {
            background: #2d3748;
            color: white;
            padding: 40px 0 20px;
            text-align: center;
        }

        .footer-content {
            margin-bottom: 20px;
        }

        .footer-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .footer-text {
            color: #a0aec0;
            margin-bottom: 20px;
        }

        .footer-bottom {
            border-top: 1px solid #4a5568;
            padding-top: 20px;
            color: #a0aec0;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .nav-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .nav-menu a {
                text-decoration: none;
                color: #2d3748;
                font-weight: 500;
                transition: color 0.2s ease;
            }

            .nav-menu a:hover {
                color: #e53e3e;
            }

            .menu-btn {
                display: none;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .hero-buttons {
                flex-direction: row;
                justify-content: center;
            }

            .categories-grid {
                grid-template-columns: repeat(4, 1fr);
            }

            .menu-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .features-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="logo">
                <i class="fas fa-utensils"></i> Tasty Bites
            </a>
            <ul class="nav-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#menu">मेन्यू</a></li>
                <li><a href="#about">हमारे बारे में</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
            <button class="menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    स्वादिष्ट खाना, तुरंत डिलीवरी
                </div>
                <h1 class="hero-title">
                    घर जैसा स्वाद<br>
                    दिल को छू जाए
                </h1>
                <p class="hero-subtitle">
                    ताज़ा बना हुआ, स्वादिष्ट खाना सीधे आपके घर तक। 
                    अब ऑर्डर करें और मजे लें।
                </p>
                <div class="hero-buttons">
                    <a href="#menu" class="cta-button">
                        <i class="fas fa-utensils"></i>
                        मेन्यू देखें
                    </a>
                    <a href="tel:+919876543210" class="cta-button cta-secondary">
                        <i class="fas fa-phone"></i>
                        अभी ऑर्डर करें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Categories -->
    <section class="menu-categories">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">हमारे स्पेशल कैटेगरी</h2>
                <p class="section-subtitle">
                    हर स्वाद के लिए कुछ खास
                </p>
            </div>

            <div class="categories-grid">
                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="North Indian" class="category-image">
                    <h3 class="category-title">नॉर्थ इंडियन</h3>
                    <p class="category-count">15+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="South Indian" class="category-image">
                    <h3 class="category-title">साउथ इंडियन</h3>
                    <p class="category-count">12+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Pizza" class="category-image">
                    <h3 class="category-title">पिज्जा</h3>
                    <p class="category-count">8+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Chinese" class="category-image">
                    <h3 class="category-title">चाइनीज</h3>
                    <p class="category-count">10+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1551782450-a2132b4ba21d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Burger" class="category-image">
                    <h3 class="category-title">बर्गर</h3>
                    <p class="category-count">6+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Desserts" class="category-image">
                    <h3 class="category-title">डेजर्ट</h3>
                    <p class="category-count">8+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1544145945-f90425340c7e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Beverages" class="category-image">
                    <h3 class="category-title">ड्रिंक्स</h3>
                    <p class="category-count">12+ आइटम्स</p>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1504113888839-1c8eb50233d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Snacks" class="category-image">
                    <h3 class="category-title">स्नैक्स</h3>
                    <p class="category-count">10+ आइटम्स</p>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Menu -->
    <section class="featured-menu" id="menu">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">आज के स्पेशल</h2>
                <p class="section-subtitle">
                    शेफ की पसंदीदा डिशेज
                </p>
            </div>

            <div class="menu-grid">
                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1585937421612-70a008356fbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Butter Chicken" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">बटर चिकन</h3>
                        <p class="menu-description">क्रीमी टोमेटो ग्रेवी में पका हुआ टेंडर चिकन, बासमती राइस के साथ</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹280</span>
                            <button class="order-btn" onclick="orderItem('बटर चिकन', '280')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1567188040759-fb8a883dc6d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Masala Dosa" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">मसाला डोसा</h3>
                        <p class="menu-description">क्रिस्पी डोसा आलू मसाला के साथ, सांबर और चटनी के साथ सर्व</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹120</span>
                            <button class="order-btn" onclick="orderItem('मसाला डोसा', '120')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Margherita Pizza" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">मार्गेरिटा पिज्जा</h3>
                        <p class="menu-description">फ्रेश मोज़ेरेला, टोमेटो सॉस और बेसिल के साथ क्लासिक पिज्जा</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹220</span>
                            <button class="order-btn" onclick="orderItem('मार्गेरिटा पिज्जा', '220')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1555939594-58d7cb561ad1?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Veg Burger" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">वेज बर्गर</h3>
                        <p class="menu-description">क्रिस्पी वेज पैटी, फ्रेश सलाद और चीज़ के साथ, फ्राइज़ के साथ</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹150</span>
                            <button class="order-btn" onclick="orderItem('वेज बर्गर', '150')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1546833999-b9f581a1996d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Hakka Noodles" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">हक्का नूडल्स</h3>
                        <p class="menu-description">स्टिर-फ्राइड नूडल्स मिक्स वेजिटेबल्स और स्पेशल सॉस के साथ</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹180</span>
                            <button class="order-btn" onclick="orderItem('हक्का नूडल्स', '180')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Gulab Jamun" class="menu-image">
                    <div class="menu-content">
                        <h3 class="menu-title">गुलाब जामुन</h3>
                        <p class="menu-description">मुंह में घुल जाने वाले गुलाब जामुन, गर्म चाशनी के साथ (2 पीस)</p>
                        <div class="menu-footer">
                            <span class="menu-price">₹80</span>
                            <button class="order-btn" onclick="orderItem('गुलाब जामुन', '80')">ऑर्डर करें</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features -->
    <section class="features" id="about">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">क्यों चुनें Tasty Bites</h2>
                <p class="section-subtitle">
                    हमारी विशेषताएं जो हमें बेहतर बनाती हैं
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="feature-content">
                        <h3>तुरंत डिलीवरी</h3>
                        <p>30 मिनट में गर्मागर्म खाना सीधे आपके घर तक पहुंचाते हैं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="feature-content">
                        <h3>फ्रेश इंग्रीडिएंट्स</h3>
                        <p>हमेशा ताज़ी सब्जियां और बेहतरीन क्वालिटी के मसाले इस्तेमाल करते हैं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>हाइजीन गारंटी</h3>
                        <p>पूरी तरह से साफ-सुथरी किचन में बना हुआ, पैकेजिंग भी हाइजीनिक।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="feature-content">
                        <h3>किफायती दाम</h3>
                        <p>बेस्ट क्वालिटी का खाना सबसे कम दाम में, कोई हिडन चार्जेस नहीं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div class="feature-content">
                        <h3>घर जैसा स्वाद</h3>
                        <p>मां के हाथ का खाना जैसा स्वाद, प्यार से बनाया गया हर डिश।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <div class="feature-content">
                        <h3>24/7 सपोर्ट</h3>
                        <p>किसी भी समस्या के लिए हमारी टीम हमेशा आपकी सेवा में तैयार है।</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">हमसे जुड़ें</h2>
                <p class="section-subtitle">
                    ऑर्डर करने के लिए या किसी भी जानकारी के लिए संपर्क करें
                </p>
            </div>

            <div class="contact-info">
                <div class="contact-item">
                    <i class="fas fa-phone"></i>
                    <span>+91 98765 43210</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-envelope"></i>
                    <span><EMAIL></span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>123, फूड स्ट्रीट, मुंबई - 400001</span>
                </div>
                <div class="contact-item">
                    <i class="fas fa-clock"></i>
                    <span>सुबह 9:00 - रात 11:00 (रोज़ाना)</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <h3 class="footer-title">Tasty Bites</h3>
                <p class="footer-text">
                    घर जैसा स्वाद, दिल को छू जाए - आपका भरोसेमंद फूड पार्टनर
                </p>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Tasty Bites. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं Tasty Bites से खाना ऑर्डर करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Order item function
        function orderItem(itemName, price) {
            const message = `🍽️ *Tasty Bites - ऑर्डर*

📋 *आइटम:* ${itemName}
💰 *प्राइस:* ₹${price}

कृपया इस ऑर्डर को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Mobile menu toggle
        document.querySelector('.menu-btn').addEventListener('click', function() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        });

        // Add to cart functionality (basic)
        let cart = [];

        function addToCart(itemName, price) {
            cart.push({name: itemName, price: price});
            updateCartCount();
        }

        function updateCartCount() {
            // This would update a cart counter if you had one
            console.log('Cart items:', cart.length);
        }
    </script>
</body>
</html>
