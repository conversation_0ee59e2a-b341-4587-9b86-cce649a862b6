<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modern Mobile E-Commerce Theme - Dukkan</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Inter', sans-serif;
        }

        body {
            background-color: #fafafa;
            color: #1a1a1a;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Header Styles */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #1a1a1a;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .logo {
            font-size: 1.6rem;
            font-weight: 800;
            letter-spacing: -0.5px;
            color: #ff3366;
        }

        .nav-icons {
            display: flex;
            gap: 16px;
        }

        .nav-icons i {
            font-size: 1.3rem;
            cursor: pointer;
            transition: transform 0.2s ease, color 0.2s ease;
        }

        .nav-icons i:hover {
            transform: scale(1.1);
            color: #ff3366;
        }

        .cart-icon {
            position: relative;
        }

        .cart-count {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ff3366;
            color: white;
            border-radius: 50%;
            padding: 3px 7px;
            font-size: 0.7rem;
            font-weight: bold;
        }

        /* Hero Section with Animation */
        .hero {
            height: 220px;
            background: linear-gradient(135deg, #ff3366 0%, #ff9933 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            width: 150%;
            height: 150%;
            background: url('https://via.placeholder.com/500x220?text=Store+Banner');
            background-size: cover;
            background-position: center;
            opacity: 0.3;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .hero h1 {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 8px;
            z-index: 1;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
        }

        .hero p {
            font-size: 1rem;
            z-index: 1;
        }

        .hero-btn {
            margin-top: 12px;
            background: white;
            color: #ff3366;
            border: none;
            padding: 10px 20px;
            border-radius: 30px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: box-shadow 0.3s ease, transform 0.3s ease;
            z-index: 1;
        }

        .hero-btn:hover {
            box-shadow: 0 8px 15px rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Categories Section with Glassmorphism */
        .categories {
            padding: 16px;
            background: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(8px);
            margin: 16px;
            border-radius: 16px;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .categories h2 {
            font-size: 1.3rem;
            margin-bottom: 12px;
            color: #1a1a1a;
            font-weight: 700;
        }

        .category-list {
            display: flex;
            overflow-x: auto;
            gap: 12px;
            padding-bottom: 8px;
            scrollbar-width: none; /* Hide scrollbar for Firefox */
        }

        .category-list::-webkit-scrollbar {
            display: none; /* Hide scrollbar for Chrome */
        }

        .category-item {
            background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.4));
            color: #ff3366;
            padding: 10px 18px;
            border-radius: 30px;
            font-size: 0.9rem;
            font-weight: 500;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.5);
        }

        .category-item:hover {
            background: linear-gradient(145deg, #ff3366, #ff9933);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(255, 51, 102, 0.3);
        }

        /* Products Section with Card Animation */
        .products {
            padding: 16px;
            background: transparent;
        }

        .products h2 {
            font-size: 1.3rem;
            margin-bottom: 12px;
            color: #1a1a1a;
            font-weight: 700;
        }

        .product-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .product-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275), box-shadow 0.4s ease;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
        }

        .product-img {
            width: 100%;
            height: 180px;
            background: url('https://via.placeholder.com/300x180?text=Product') no-repeat center center/cover;
            position: relative;
            overflow: hidden;
        }

        .product-img::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.2) 100%);
        }

        .product-info {
            padding: 14px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .product-name {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 6px;
            color: #1a1a1a;
        }

        .product-price {
            font-size: 1.2rem;
            font-weight: 700;
            color: #ff3366;
            margin-bottom: 12px;
        }

        .add-to-cart {
            background: linear-gradient(135deg, #ff3366, #ff9933);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            align-self: center;
            box-shadow: 0 4px 10px rgba(255, 51, 102, 0.3);
        }

        .add-to-cart:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 51, 102, 0.5);
            background: linear-gradient(135deg, #ff9933, #ff3366);
        }

        /* Cart Section with Slide-Up Animation */
        .cart-section {
            position: fixed;
            bottom: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 -4px 15px rgba(0, 0, 0, 0.1);
            padding: 16px;
            transform: translateY(100%);
            transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            z-index: 1000;
            border-top-left-radius: 20px;
            border-top-right-radius: 20px;
        }

        .cart-section.active {
            transform: translateY(0);
            display: block;
        }

        .cart-section h2 {
            font-size: 1.3rem;
            margin-bottom: 12px;
            color: #1a1a1a;
            font-weight: 700;
        }

        .cart-items {
            max-height: 250px;
            overflow-y: auto;
            margin-bottom: 12px;
            padding-right: 5px;
        }

        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px dashed #ddd;
        }

        .cart-item-name {
            font-weight: 500;
            color: #1a1a1a;
        }

        .cart-item-price {
            font-weight: 600;
            color: #ff3366;
        }

        .cart-total {
            font-size: 1.2rem;
            font-weight: 800;
            margin-bottom: 12px;
            text-align: right;
            color: #1a1a1a;
        }

        .checkout-btn {
            background: linear-gradient(135deg, #ff3366, #ff9933);
            color: white;
            border: none;
            padding: 14px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(255, 51, 102, 0.3);
        }

        .checkout-btn:hover {
            background: linear-gradient(135deg, #ff9933, #ff3366);
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(255, 51, 102, 0.5);
        }

        /* Footer Styles */
        footer {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ccc;
            padding: 20px 16px;
            text-align: center;
            font-size: 0.85rem;
            margin-bottom: 80px; /* Space for fixed cart */
            position: relative;
        }

        footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #ff3366, #ff9933);
        }

        /* Media Queries for larger screens */
        @media (min-width: 768px) {
            .product-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .hero {
                height: 300px;
            }
            .hero h1 {
                font-size: 2.5rem;
            }
        }

        @media (min-width: 1024px) {
            .product-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="logo">Dukkan</div>
        <div class="nav-icons">
            <i class="fas fa-search"></i>
            <div class="cart-icon">
                <i class="fas fa-shopping-cart" onclick="toggleCart()"></i>
                <span class="cart-count">0</span>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <h1>Trendy Deals Await!</h1>
        <p>Explore the latest products with exclusive discounts.</p>
        <button class="hero-btn">Shop Now</button>
    </section>

    <!-- Categories Section -->
    <section class="categories">
        <h2>Browse Categories</h2>
        <div class="category-list">
            <div class="category-item">Tech Gadgets</div>
            <div class="category-item">Streetwear</div>
            <div class="category-item">Home Decor</div>
            <div class="category-item">Skincare</div>
            <div class="category-item">Fitness</div>
            <div class="category-item">Books</div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products">
        <h2>Top Picks for You</h2>
        <div class="product-grid">
            <div class="product-card">
                <div class="product-img"></div>
                <div class="product-info">
                    <div class="product-name">Wireless Earbuds</div>
                    <div class="product-price">₹2,499</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img"></div>
                <div class="product-info">
                    <div class="product-name">Graphic Tee</div>
                    <div class="product-price">₹999</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img"></div>
                <div class="product-info">
                    <div class="product-name">Aesthetic Lamp</div>
                    <div class="product-price">₹1,799</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-img"></div>
                <div class="product-info">
                    <div class="product-name">Face Serum</div>
                    <div class="product-price">₹1,299</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Section (Slide-Up) -->
    <section class="cart-section" id="cartSection">
        <h2>Your Cart</h2>
        <div class="cart-items">
            <p style="text-align: center; color: #777; font-style: italic;">Your cart is empty. Start shopping!</p>
        </div>
        <div class="cart-total">Total: ₹0</div>
        <button class="checkout-btn">Checkout Now</button>
    </section>

    <!-- Footer -->
    <footer>
        <p>&copy; 2025 Dukkan. Crafted with <span style="color: #ff3366;">♥</span> for modern shoppers.</p>
        <p style="margin-top: 8px;">Support: +91 98765 43210 | <EMAIL></p>
    </footer>

    <script>
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.toggle('active');
        }

        // Basic cart functionality for demo
        let cartCount = 0;
        let cartTotal = 0;
        document.querySelectorAll('.add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-name').textContent;
                const productPriceStr = productCard.querySelector('.product-price').textContent;
                const productPrice = parseFloat(productPriceStr.replace('₹', '').replace(',', ''));
                
                // Update cart count
                cartCount++;
                document.querySelector('.cart-count').textContent = cartCount;
                
                // Update cart total
                cartTotal += productPrice;
                document.querySelector('.cart-total').textContent = `Total: ₹${cartTotal.toLocaleString('en-IN')}`;
                
                // Add item to cart UI
                const cartItems = document.querySelector('.cart-items');
                if (cartItems.querySelector('p')) {
                    cartItems.innerHTML = '';
                }
                const cartItem = document.createElement('div');
                cartItem.classList.add('cart-item');
                cartItem.innerHTML = `
                    <span class="cart-item-name">${productName}</span>
                    <span class="cart-item-price">${productPriceStr}</span>
                `;
                cartItems.appendChild(cartItem);
                
                // Show notification
                alert(`${productName} added to cart!`);
            });
        });
    </script>
</body>
</html>
