<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Sweet Heritage – Candyland Experience</title>
    <meta name="description" content="Sweet Heritage: Discover a playful, pastel world of sweets, snacks, and treats. Order online and enjoy the taste of happiness!">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka:wght@500;700&family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
    /* --- Redesigned Sweet Shop Theme --- */
    body {
        background: linear-gradient(135deg, #fff5e6 0%, #ffe6f7 100%);
        color: #5c2f1f;
        font-family: 'Caveat', cursive;
        overflow-x: hidden;
    }
    .sweet-header {
        background: #fff5e6;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 18px 32px;
        box-shadow: 0 4px 18px rgba(217, 140, 95, 0.12);
        border-bottom: 4px solid #ffb6d5;
        position: sticky;
        top: 0;
        z-index: 1000;
    }
    .candy-gradient-text {
        background: linear-gradient(90deg, #ffb6d5 0%, #ffd580 100%);
        -webkit-background-clip: text;
        background-clip: text;
        font-weight: 700;
        letter-spacing: 2px;
    }
    .sweet-nav {
        display: flex;
        gap: 18px;
    }
    .nav-btn {
        background: #ffe6f7;
        border: none;
        border-radius: 20px;
        padding: 8px 22px;
        font-size: 1.1rem;
        color: #a94422;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s, color 0.2s, transform 0.2s;
        box-shadow: 0 2px 8px rgba(255, 182, 213, 0.15);
    }
    .nav-btn.active, .nav-btn:hover {
        background: #ffd580;
        color: #fff;
        transform: translateY(-2px) scale(1.05);
    }
    .sweet-header .nav-icons {
        display: flex;
        gap: 18px;
    }
    .sweet-header .nav-icons i {
        font-size: 1.5rem;
        color: #ffb6d5;
        transition: color 0.2s, transform 0.2s;
        cursor: pointer;
    }
    .sweet-header .nav-icons i:hover {
        color: #a94422;
        transform: scale(1.2) rotate(-8deg);
    }
    .cart-icon {
        position: relative;
    }
    .cart-count {
        position: absolute;
        top: -10px;
        right: -10px;
        background: #ffd580;
        color: #fff;
        border-radius: 50%;
        padding: 4px 8px;
        font-size: 0.8rem;
        font-weight: bold;
        border: 2px solid #fff5e6;
    }
    .sweet-hero {
        position: relative;
        min-height: 320px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: none;
        overflow: visible;
    }
    .hero-svg-bg {
        position: absolute;
        top: 0; left: 0; width: 100%; height: 100%;
        z-index: 0;
        pointer-events: none;
    }
    .hero-content {
        position: relative;
        z-index: 1;
        text-align: center;
        padding: 40px 0 0 0;
    }
    .sweet-hero h1 {
        font-size: 3rem;
        margin-bottom: 10px;
    }
    .sweet-hero p {
        font-size: 1.3rem;
        background: rgba(255, 245, 230, 0.7);
        display: inline-block;
        padding: 6px 18px;
        border-radius: 20px;
        margin-bottom: 18px;
    }
    .hero-btn {
        margin-top: 18px;
        background: linear-gradient(90deg, #ffb6d5 0%, #ffd580 100%);
        color: #a94422;
        border: none;
        padding: 14px 32px;
        border-radius: 16px;
        font-size: 1.2rem;
        font-weight: 700;
        cursor: pointer;
        transition: box-shadow 0.3s, transform 0.3s, background 0.2s;
        box-shadow: 0 6px 18px rgba(255, 182, 213, 0.25);
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    .hero-btn:hover {
        background: linear-gradient(90deg, #ffd580 0%, #ffb6d5 100%);
        color: #fff;
        box-shadow: 0 10px 28px rgba(255, 182, 213, 0.35);
        transform: translateY(-3px) scale(1.04);
    }
    .sweet-categories {
        background: #fff5e6;
        margin: 32px 0 0 0;
        border-radius: 18px;
        box-shadow: 0 4px 18px rgba(255, 182, 213, 0.10);
        border: 2px dashed #ffd580;
        padding: 28px 0 18px 0;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
    }
    .sweet-categories h2 {
        font-size: 2rem;
        margin-bottom: 18px;
        text-align: center;
    }
    .category-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 18px;
    }
    .category-pill {
        background: linear-gradient(90deg, #ffd580 0%, #ffb6d5 100%);
        color: #a94422;
        padding: 12px 28px;
        border-radius: 30px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s, color 0.2s, transform 0.2s;
        box-shadow: 0 2px 8px rgba(255, 182, 213, 0.12);
        border: 2px solid #fff5e6;
        position: relative;
    }
    .category-pill:hover {
        background: linear-gradient(90deg, #ffb6d5 0%, #ffd580 100%);
        color: #fff;
        transform: translateY(-2px) scale(1.05);
    }
    .sweet-products {
        background: none;
        padding: 32px 0 0 0;
        max-width: 1100px;
        margin: 0 auto;
    }
    .sweet-products h2 {
        font-size: 2rem;
        margin-bottom: 18px;
        text-align: center;
    }
    .sweet-product-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 32px;
    }
    @media (min-width: 700px) {
        .sweet-product-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    @media (min-width: 1100px) {
        .sweet-product-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }
    .sweet-card {
        background: #fff5e6;
        border-radius: 22px;
        overflow: hidden;
        box-shadow: 0 8px 24px rgba(255, 182, 213, 0.18);
        transition: transform 0.3s, box-shadow 0.3s;
        display: flex;
        flex-direction: column;
        border: 3px solid #ffd580;
        position: relative;
    }
    .sweet-card:hover {
        transform: translateY(-8px) scale(1.03);
        box-shadow: 0 16px 36px rgba(255, 182, 213, 0.28);
    }
    .sweet-img {
        width: 100%;
        height: 180px;
        background-size: cover;
        background-position: center;
        border-bottom: 2px dashed #ffd580;
        border-radius: 22px 22px 0 0;
        position: relative;
    }
    .product-info {
        padding: 18px 14px 18px 14px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    .product-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: #a94422;
        text-align: center;
        margin-bottom: 8px;
    }
    .product-price {
        font-size: 1.2rem;
        font-weight: 700;
        color: #ffb6d5;
        margin-bottom: 12px;
        text-align: center;
    }
    .add-to-cart {
        background: linear-gradient(90deg, #ffd580 0%, #ffb6d5 100%);
        color: #a94422;
        border: none;
        padding: 12px 0;
        border-radius: 12px;
        cursor: pointer;
        font-size: 1.1rem;
        font-weight: 700;
        transition: background 0.2s, color 0.2s, transform 0.2s;
        width: 100%;
        align-self: center;
        box-shadow: 0 4px 12px rgba(255, 182, 213, 0.18);
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    .add-to-cart:hover {
        background: linear-gradient(90deg, #ffb6d5 0%, #ffd580 100%);
        color: #fff;
        transform: translateY(-2px) scale(1.04);
    }
    .sweet-testimonials {
        background: #ffe6f7;
        margin: 40px 0 0 0;
        border-radius: 18px;
        box-shadow: 0 4px 18px rgba(255, 182, 213, 0.10);
        padding: 28px 0 18px 0;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
    }
    .sweet-testimonials h2 {
        font-size: 2rem;
        margin-bottom: 18px;
        text-align: center;
    }
    .testimonial-bubbles {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 24px;
    }
    .testimonial-bubble {
        background: #fff5e6;
        border-radius: 30px 30px 30px 0;
        box-shadow: 0 2px 8px rgba(255, 182, 213, 0.10);
        padding: 22px 28px;
        max-width: 320px;
        font-size: 1.1rem;
        color: #a94422;
        position: relative;
        border: 2px solid #ffd580;
        font-family: 'Caveat', cursive;
    }
    .testimonial-bubble:after {
        content: '';
        position: absolute;
        left: 24px;
        bottom: -18px;
        width: 24px;
        height: 24px;
        background: #fff5e6;
        border-bottom-left-radius: 50%;
        border: 2px solid #ffd580;
        border-top: none;
        border-right: none;
        transform: rotate(-20deg);
    }
    .testimonial-bubble span {
        display: block;
        margin-top: 10px;
        font-size: 1rem;
        color: #ffb6d5;
        font-weight: 700;
    }
    .sweet-gallery {
        background: none;
        margin: 40px 0 0 0;
        max-width: 1100px;
        margin-left: auto;
        margin-right: auto;
    }
    .sweet-gallery h2 {
        font-size: 2rem;
        margin-bottom: 18px;
        text-align: center;
    }
    .gallery-zigzag {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        justify-content: center;
        align-items: flex-end;
    }
    .gallery-img {
        width: 140px;
        height: 140px;
        object-fit: cover;
        border-radius: 50%;
        border: 4px solid #ffd580;
        box-shadow: 0 4px 16px rgba(255, 182, 213, 0.18);
        margin-bottom: 0;
        transition: transform 0.2s, box-shadow 0.2s;
        background: #fff5e6;
    }
    .gallery-img:nth-child(2n) {
        margin-bottom: 32px;
    }
    .gallery-img:hover {
        transform: scale(1.08) rotate(-4deg);
        box-shadow: 0 8px 32px rgba(255, 182, 213, 0.28);
    }
    .cart-section {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: #fff5e6;
        box-shadow: 0 -4px 18px rgba(255, 182, 213, 0.15);
        padding: 24px 0 18px 0;
        transform: translateY(100%);
        transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        z-index: 1000;
        border-top: 4px solid #ffd580;
        border-top-left-radius: 18px;
        border-top-right-radius: 18px;
    }
    .cart-section.active {
        transform: translateY(0);
        display: block;
    }
    .cart-section h2 {
        font-size: 1.8rem;
        margin-bottom: 15px;
        color: #a94422;
        font-weight: 700;
        text-align: center;
    }
    .cart-items {
        max-height: 220px;
        overflow-y: auto;
        margin-bottom: 15px;
        padding-right: 5px;
        border: 1px dashed #ffd580;
        padding: 10px;
        background: rgba(255, 252, 247, 0.5);
        border-radius: 10px;
    }
    .cart-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px dashed #ffd580;
    }
    .cart-item-name {
        font-weight: 500;
        color: #a94422;
        font-size: 1.2rem;
    }
    .cart-item-price {
        font-weight: 700;
        color: #ffb6d5;
        font-size: 1.2rem;
    }
    .cart-total {
        font-size: 1.5rem;
        font-weight: 800;
        margin-bottom: 15px;
        text-align: right;
        color: #a94422;
        background: rgba(255, 182, 213, 0.18);
        padding: 5px 10px;
        border-radius: 5px;
    }
    .checkout-btn {
        background: linear-gradient(90deg, #ffd580 0%, #ffb6d5 100%);
        color: #a94422;
        border: none;
        padding: 15px 0;
        border-radius: 14px;
        cursor: pointer;
        font-size: 1.2rem;
        font-weight: 700;
        width: 100%;
        transition: background 0.2s, color 0.2s, transform 0.2s;
        box-shadow: 0 5px 15px rgba(255, 182, 213, 0.18);
        text-transform: uppercase;
        letter-spacing: 2px;
    }
    .checkout-btn:hover {
        background: linear-gradient(90deg, #ffb6d5 0%, #ffd580 100%);
        color: #fff;
        transform: translateY(-3px) scale(1.03);
        box-shadow: 0 8px 20px rgba(255, 182, 213, 0.28);
    }
    .sweet-footer {
        background: #a94422;
        color: #fff5e6;
        padding: 32px 20px 0 20px;
        text-align: center;
        font-size: 1rem;
        margin-bottom: 80px;
        position: relative;
        border-top: 4px solid #ffd580;
        border-radius: 24px 24px 0 0;
        overflow: hidden;
    }
    .footer-waves {
        position: absolute;
        top: -60px;
        left: 0;
        width: 100%;
        z-index: 0;
    }
    .sweet-footer p {
        position: relative;
        z-index: 1;
    }
    @media (max-width: 700px) {
        .sweet-header {
            flex-direction: column;
            gap: 10px;
            padding: 12px 8px;
        }
        .sweet-nav {
            gap: 8px;
        }
        .sweet-product-grid {
            grid-template-columns: 1fr;
        }
        .gallery-img {
            width: 100px;
            height: 100px;
        }
    }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="sweet-header">
        <div class="logo candy-gradient-text">Sweet Heritage</div>
        <nav class="sweet-nav">
            <button class="nav-btn active">Home</button>
            <button class="nav-btn">Menu</button>
            <button class="nav-btn">Offers</button>
            <button class="nav-btn">Contact</button>
        </nav>
        <div class="nav-icons">
            <i class="fas fa-search"></i>
            <div class="cart-icon">
                <i class="fas fa-shopping-cart" onclick="toggleCart()"></i>
                <span class="cart-count">0</span>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero sweet-hero">
        <div class="hero-svg-bg">
            <svg width="100%" height="100%" viewBox="0 0 1440 320" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="#ffe6f7" fill-opacity="1" d="M0,160L60,176C120,192,240,224,360,229.3C480,235,600,213,720,197.3C840,181,960,171,1080,154.7C1200,139,1320,117,1380,106.7L1440,96L1440,320L1380,320C1320,320,1200,320,1080,320C960,320,840,320,720,320C600,320,480,320,360,320C240,320,120,320,60,320L0,320Z"></path></svg>
        </div>
        <div class="hero-content">
            <h1 class="candy-gradient-text">Handcrafted Sweets!</h1>
            <p>Experience the taste of tradition with every bite.</p>
            <button class="hero-btn">Taste Now</button>
        </div>
    </section>

    <!-- Categories Section -->
    <section class="categories sweet-categories">
        <h2 class="candy-gradient-text">Explore Our Range</h2>
        <div class="category-list">
            <div class="category-pill">Classic Mithai</div>
            <div class="category-pill">Savory Namkeen</div>
            <div class="category-pill">Festival Treats</div>
            <div class="category-pill">Nutty Delights</div>
            <div class="category-pill">Gift Hampers</div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products sweet-products">
        <h2 class="candy-gradient-text">Our Best Sellers</h2>
        <div class="product-grid sweet-product-grid">
            <div class="product-card sweet-card">
                <div class="product-img sweet-img" style="background-image:url('https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Rasgulla (1 Kg)</div>
                    <div class="product-price">₹400</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card sweet-card">
                <div class="product-img sweet-img" style="background-image:url('https://images.unsplash.com/photo-1519864600265-abb23847ef2c?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Besan Ladoo (500g)</div>
                    <div class="product-price">₹350</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card sweet-card">
                <div class="product-img sweet-img" style="background-image:url('https://images.unsplash.com/photo-1519864600265-abb23847ef2c?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Spicy Sev (250g)</div>
                    <div class="product-price">₹120</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
            <div class="product-card sweet-card">
                <div class="product-img sweet-img" style="background-image:url('https://images.unsplash.com/photo-1502741338009-cac2772e18bc?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Almond Pack (300g)</div>
                    <div class="product-price">₹500</div>
                    <button class="add-to-cart">Add to Cart</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="sweet-testimonials">
        <h2 class="candy-gradient-text">What Our Customers Say</h2>
        <div class="testimonial-bubbles">
            <div class="testimonial-bubble">
                <p>"Absolutely delicious! Reminds me of my childhood."</p>
                <span>- Priya S.</span>
            </div>
            <div class="testimonial-bubble">
                <p>"The best sweets in town. Beautifully packed and fresh."</p>
                <span>- Rahul M.</span>
            </div>
            <div class="testimonial-bubble">
                <p>"Perfect for gifting during festivals. Everyone loved it!"</p>
                <span>- Anjali K.</span>
            </div>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="sweet-gallery">
        <h2 class="candy-gradient-text">Gallery</h2>
        <div class="gallery-zigzag">
            <img src="https://images.unsplash.com/photo-1502741338009-cac2772e18bc?auto=format&fit=crop&w=200&q=80" alt="Sweet 1" class="gallery-img">
            <img src="https://images.unsplash.com/photo-1504674900247-0877df9cc836?auto=format&fit=crop&w=200&q=80" alt="Sweet 2" class="gallery-img">
            <img src="https://images.unsplash.com/photo-1519864600265-abb23847ef2c?auto=format&fit=crop&w=200&q=80" alt="Sweet 3" class="gallery-img">
            <img src="https://images.unsplash.com/photo-1464306076886-debca5e8a6b0?auto=format&fit=crop&w=200&q=80" alt="Sweet 4" class="gallery-img">
        </div>
    </section>

    <!-- Cart Section (Slide-Up) -->
    <section class="cart-section" id="cartSection">
        <h2>Your Sweet Cart</h2>
        <div class="cart-items">
            <p style="text-align: center; color: #777; font-style: italic; font-size: 1.2rem;">Your cart is empty. Add some sweets!</p>
        </div>
        <div class="cart-total">Total: ₹0</div>
        <button class="checkout-btn">Place Order</button>
    </section>

    <!-- Footer -->
    <footer class="sweet-footer">
        <div class="footer-waves">
            <svg width="100%" height="60" viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill="#ffe6f7" fill-opacity="1" d="M0,32L60,37.3C120,43,240,53,360,53.3C480,53,600,43,720,37.3C840,32,960,32,1080,37.3C1200,43,1320,53,1380,58.7L1440,64L1440,0L1380,0C1320,0,1200,0,1080,0C960,0,840,0,720,0C600,0,480,0,360,0C240,0,120,0,60,0L0,0Z"></path></svg>
        </div>
        <p>&copy; 2025 Sweet Heritage. Crafting sweetness since generations.</p>
        <p style="margin-top: 10px;">Visit our shop or Call: +91 91234 56789 | <EMAIL></p>
    </footer>

    <script>
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.toggle('active');
        }
        // Floating Order Now badge scrolls to products
        document.querySelector('.order-badge').addEventListener('click', () => {
            document.getElementById('products').scrollIntoView({behavior:'smooth'});
        });
        // Taste Now scroll to products
        document.getElementById('tasteNowBtn').addEventListener('click', () => {
            document.getElementById('products').scrollIntoView({behavior:'smooth'});
        });
        // Cart functionality (demo)
        let cartCount = 0;
        document.querySelectorAll('.add-to-cart').forEach(btn => {
            btn.addEventListener('click', function() {
                cartCount++;
                document.getElementById('cartCount').textContent = cartCount;
                this.textContent = 'Added!';
                this.disabled = true;
                setTimeout(() => {
                    this.textContent = 'Add to Cart';
                    this.disabled = false;
                }, 1200);
            });
        });
        // Gallery Lightbox
        const gallery = document.querySelectorAll('.gallery-sticker');
        const lightbox = document.getElementById('lightbox');
        const lightboxImg = document.getElementById('lightboxImg');
        const lightboxClose = document.getElementById('lightboxClose');
        gallery.forEach(item => {
            item.addEventListener('click', function() {
                lightboxImg.src = this.getAttribute('data-img');
                lightbox.classList.add('active');
            });
            item.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    lightboxImg.src = this.getAttribute('data-img');
                    lightbox.classList.add('active');
                }
            });
        });
        lightboxClose.addEventListener('click', () => {
            lightbox.classList.remove('active');
            lightboxImg.src = '';
        });
        lightbox.addEventListener('click', (e) => {
            if (e.target === lightbox) {
                lightbox.classList.remove('active');
                lightboxImg.src = '';
            }
        });
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                lightbox.classList.remove('active');
                lightboxImg.src = '';
            }
        });
    </script>
</body>
</html>
