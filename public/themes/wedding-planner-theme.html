<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>DreamWed – Signature Wedding Experiences</title>
    <meta name="description" content="DreamWed: Signature wedding planning with a bold, modern, and artistic layout. Explore unique packages, creative services, and real love stories.">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@700&family=Montserrat:wght@400;600&family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --sidebar-bg: #2d2230;
            --sidebar-accent: #e7b8b8;
            --sidebar-gold: #d4af37;
            --main-bg: #f7f3f0;
            --hero-pink: #f7e3e3;
            --hero-purple: #b39ddb;
            --card-bg: #fff;
            --card-accent: #f7e3e3;
            --text-dark: #2d2230;
            --text-gold: #d4af37;
            --text-pink: #e7b8b8;
            --shadow: 0 8px 32px rgba(45,34,48,0.10);
        }
        html, body {
            max-width: 100vw;
            overflow-x: hidden;
        }
        body {
            background: var(--main-bg);
            color: var(--text-dark);
            font-family: 'Montserrat', sans-serif;
            min-height: 100vh;
            display: flex;
        }
        /* Vertical Sidebar */
        .sidebar {
            width: 90px;
            background: linear-gradient(180deg, var(--sidebar-bg) 80%, var(--sidebar-accent) 100%);
            color: #fff;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 2rem 0 1rem 0;
            position: fixed;
            top: 0; left: 0; bottom: 0;
            z-index: 1000;
            box-shadow: 4px 0 24px rgba(45,34,48,0.08);
        }
        .sidebar-logo {
            margin-bottom: 2.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .sidebar-logo svg {
            width: 48px; height: 48px;
            margin-bottom: 0.5rem;
        }
        .sidebar-logo span {
            font-family: 'Cinzel', serif;
            font-size: 1.1rem;
            font-weight: 700;
            letter-spacing: 2px;
            color: var(--sidebar-gold);
            writing-mode: vertical-rl;
            text-orientation: mixed;
        }
        .sidebar-nav {
            display: flex;
            flex-direction: column;
            gap: 2.2rem;
            flex: 1;
        }
        .sidebar-nav a {
            color: #fff;
            text-decoration: none;
            font-size: 1.4rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            transition: color 0.2s;
            position: relative;
        }
        .sidebar-nav a.active, .sidebar-nav a:hover {
            color: var(--sidebar-gold);
        }
        .sidebar-nav a span {
            font-size: 0.7rem;
            margin-top: 0.2rem;
            letter-spacing: 1px;
        }
        .sidebar-social {
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
            margin-top: 2rem;
        }
        .sidebar-social a {
            color: var(--sidebar-gold);
            font-size: 1.2rem;
            transition: color 0.2s;
        }
        .sidebar-social a:hover {
            color: var(--sidebar-accent);
        }
        /* Main Content */
        .main-content {
            margin-left: 90px;
            width: calc(100vw - 90px);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        /* Split Hero Section */
        .hero {
            display: flex;
            min-height: 60vh;
            background: linear-gradient(90deg, var(--hero-pink) 60%, var(--hero-purple) 100%);
            border-radius: 0 0 48px 48px;
            box-shadow: var(--shadow);
            margin: 0 2vw 2.5rem 2vw;
            overflow: hidden;
        }
        .hero-left {
            flex: 1.2;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 4vw 3vw;
        }
        .hero-title {
            font-family: 'Cinzel', serif;
            font-size: 2.7rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1.2rem;
            letter-spacing: 1px;
        }
        .hero-desc {
            font-size: 1.2rem;
            color: var(--text-dark);
            margin-bottom: 2.2rem;
            line-height: 1.7;
        }
        .hero-btn {
            background: var(--sidebar-gold);
            color: var(--sidebar-bg);
            border: none;
            padding: 1rem 2.5rem;
            border-radius: 32px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            box-shadow: 0 6px 32px var(--sidebar-accent);
            transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
        }
        .hero-btn:hover {
            background: var(--sidebar-bg);
            color: #fff;
            box-shadow: 0 8px 32px var(--sidebar-gold);
        }
        .hero-right {
            flex: 1.5;
            display: flex;
            align-items: center;
            justify-content: center;
            background: none;
            position: relative;
        }
        .hero-img {
            width: 100%;
            max-width: 480px;
            border-radius: 32px;
            box-shadow: 0 8px 32px var(--sidebar-accent);
            margin: 2vw 0;
        }
        /* Timeline Section */
        .timeline-section {
            margin: 2.5rem 2vw;
            padding: 2.5rem 2vw;
            background: var(--card-bg);
            border-radius: 32px;
            box-shadow: var(--shadow);
        }
        .timeline-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 2rem;
            text-align: center;
        }
        .timeline {
            position: relative;
            margin-left: 40px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: -18px;
            top: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(var(--sidebar-gold), var(--sidebar-accent));
            border-radius: 2px;
        }
        .timeline-step {
            position: relative;
            margin-bottom: 2.5rem;
            padding-left: 32px;
        }
        .timeline-step:last-child { margin-bottom: 0; }
        .timeline-icon {
            position: absolute;
            left: -38px;
            top: 0;
            background: var(--sidebar-gold);
            color: var(--sidebar-bg);
            width: 36px; height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            box-shadow: 0 2px 8px var(--sidebar-accent);
        }
        .timeline-label {
            font-family: 'Cinzel', serif;
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 0.3rem;
        }
        .timeline-desc {
            font-size: 1rem;
            color: var(--sidebar-bg);
        }
        /* Horizontal Scrollable Packages */
        .packages-section {
            margin: 2.5rem 2vw;
            padding: 2.5rem 0;
        }
        .packages-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .package-scroll {
            display: flex;
            gap: 2.2rem;
            overflow-x: auto;
            padding-bottom: 1rem;
            scroll-snap-type: x mandatory;
        }
        .package-card {
            min-width: 320px;
            max-width: 340px;
            background: linear-gradient(135deg, var(--hero-purple) 60%, var(--card-bg) 100%);
            border-radius: 24px;
            box-shadow: var(--shadow);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            scroll-snap-align: start;
            transition: box-shadow 0.2s, transform 0.2s;
        }
        .package-card:hover {
            box-shadow: 0 12px 40px var(--sidebar-gold);
            transform: translateY(-8px) scale(1.04);
        }
        .package-img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            border-bottom: 1.5px solid var(--sidebar-gold);
        }
        .package-info {
            padding: 1.5rem 1.2rem 1.2rem 1.2rem;
            width: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }
        .package-name {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--sidebar-bg);
            margin-bottom: 0.7rem;
        }
        .package-desc {
            font-size: 1rem;
            color: var(--sidebar-accent);
            margin-bottom: 1.2rem;
        }
        .package-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--sidebar-gold);
            margin-bottom: 1.2rem;
        }
        .package-card .hero-btn {
            align-self: flex-end;
            margin-top: auto;
            font-size: 1rem;
            padding: 0.7rem 1.5rem;
        }
        /* Polaroid Testimonials */
        .testimonials-section {
            margin: 2.5rem 2vw;
            padding: 2.5rem 0;
        }
        .testimonials-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .testimonials-grid {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        .testimonial-polaroid {
            background: #fff;
            border-radius: 18px;
            box-shadow: 0 8px 32px rgba(45,34,48,0.13);
            width: 220px;
            padding: 1.2rem 1rem 1.5rem 1rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
            margin-bottom: 1.5rem;
            border: 2px solid var(--sidebar-gold);
        }
        .testimonial-polaroid img {
            width: 100px; height: 100px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid var(--sidebar-accent);
            margin-bottom: 1rem;
        }
        .testimonial-polaroid .quote {
            font-family: 'Pacifico', cursive;
            font-size: 1.05rem;
            color: var(--sidebar-bg);
            margin-bottom: 1rem;
            text-align: center;
        }
        .testimonial-polaroid .client-name {
            font-weight: 700;
            color: var(--sidebar-gold);
            font-size: 1rem;
        }
        /* Circular Gallery */
        .gallery-section {
            margin: 2.5rem 2vw;
            padding: 2.5rem 0;
        }
        .gallery-title {
            font-family: 'Cinzel', serif;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .gallery-circles {
            display: flex;
            flex-wrap: wrap;
            gap: 2.2rem;
            justify-content: center;
        }
        .gallery-circle {
            width: 140px; height: 140px;
            border-radius: 50%;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(45,34,48,0.13);
            border: 4px solid var(--sidebar-gold);
            cursor: pointer;
            transition: box-shadow 0.2s, transform 0.2s;
            position: relative;
        }
        .gallery-circle:hover {
            box-shadow: 0 12px 40px var(--sidebar-gold);
            transform: scale(1.07);
        }
        .gallery-circle img {
            width: 100%; height: 100%; object-fit: cover;
        }
        /* Footer */
        footer {
            background: var(--sidebar-bg);
            color: var(--sidebar-gold);
            padding: 2rem 2vw 1.2rem 2vw;
            text-align: center;
            font-size: 1rem;
            border-radius: 32px 32px 0 0;
            margin: 3rem 2vw 0 2vw;
            letter-spacing: 1px;
        }
        .footer-social {
            margin-top: 1.2rem;
            display: flex;
            justify-content: center;
            gap: 2.2rem;
        }
        .footer-social a {
            color: var(--sidebar-gold);
            font-size: 1.3rem;
            transition: color 0.2s;
        }
        .footer-social a:hover {
            color: var(--sidebar-accent);
        }
        /* Responsive */
        @media (max-width: 900px) {
            .sidebar { width: 60px; }
            .main-content { margin-left: 60px; width: calc(100vw - 60px); }
            .sidebar-logo svg { width: 36px; height: 36px; }
            .sidebar-logo span { font-size: 0.9rem; }
        }
        @media (max-width: 700px) {
            .hero { flex-direction: column; min-height: 40vh; }
            .hero-left, .hero-right { padding: 2vw 4vw; }
            .hero-img { max-width: 90vw; }
            .timeline-section, .packages-section, .testimonials-section, .gallery-section, footer { margin: 1.2rem 1vw; padding: 1.2rem 0; }
            .testimonials-grid, .gallery-circles { gap: 1.2rem; }
        }
    </style>
</head>
<body>
    <!-- Vertical Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-logo">
            <svg viewBox="0 0 40 40" fill="none"><ellipse cx="20" cy="20" rx="18" ry="18" fill="#d4af37"/><path d="M14 24c2-6 10-6 12 0" stroke="#fff" stroke-width="2" stroke-linecap="round"/><ellipse cx="17" cy="17" rx="2" ry="2.5" fill="#fff"/><ellipse cx="23" cy="17" rx="2" ry="2.5" fill="#fff"/></svg>
            <span>DreamWed</span>
        </div>
        <nav class="sidebar-nav" aria-label="Sidebar Navigation">
            <a href="#hero" class="active"><i class="fas fa-home"></i><span>Home</span></a>
            <a href="#timeline"><i class="fas fa-stream"></i><span>Steps</span></a>
            <a href="#packages"><i class="fas fa-gift"></i><span>Packages</span></a>
            <a href="#testimonials"><i class="fas fa-heart"></i><span>Stories</span></a>
            <a href="#gallery"><i class="fas fa-images"></i><span>Gallery</span></a>
            <a href="#contact"><i class="fas fa-envelope"></i><span>Contact</span></a>
        </nav>
        <div class="sidebar-social">
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
        </div>
    </aside>
    <main class="main-content">
        <!-- Split Hero Section -->
        <section class="hero" id="hero">
            <div class="hero-left">
                <h1 class="hero-title">Signature Weddings, Unforgettable Memories</h1>
                <p class="hero-desc">Break the mold. DreamWed crafts bold, creative, and truly personal celebrations for modern couples. Let’s make your story legendary.</p>
                <button class="hero-btn" id="bookNowBtn">Start Your Journey</button>
            </div>
            <div class="hero-right">
                <img class="hero-img" src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80" alt="Modern Wedding">
            </div>
        </section>
        <!-- Timeline Section -->
        <section class="timeline-section" id="timeline">
            <h2 class="timeline-title">How We Work</h2>
            <div class="timeline">
                <div class="timeline-step">
                    <div class="timeline-icon"><i class="fas fa-lightbulb"></i></div>
                    <div class="timeline-label">Inspiration</div>
                    <div class="timeline-desc">We listen, brainstorm, and get to know your unique style and dreams.</div>
                </div>
                <div class="timeline-step">
                    <div class="timeline-icon"><i class="fas fa-drafting-compass"></i></div>
                    <div class="timeline-label">Concept & Design</div>
                    <div class="timeline-desc">Our team sketches a creative, one-of-a-kind plan just for you.</div>
                </div>
                <div class="timeline-step">
                    <div class="timeline-icon"><i class="fas fa-cogs"></i></div>
                    <div class="timeline-label">Planning</div>
                    <div class="timeline-desc">We handle logistics, vendors, and every detail with precision.</div>
                </div>
                <div class="timeline-step">
                    <div class="timeline-icon"><i class="fas fa-glass-cheers"></i></div>
                    <div class="timeline-label">The Celebration</div>
                    <div class="timeline-desc">You enjoy a seamless, spectacular event—stress free!</div>
                </div>
            </div>
        </section>
        <!-- Wedding Packages Horizontal Scroll -->
        <section class="packages-section" id="packages">
            <h2 class="packages-title">Our Signature Packages</h2>
            <div class="package-scroll">
                <div class="package-card">
                    <img class="package-img" src="https://images.unsplash.com/photo-1518020382113-a7e8fc38eac9?auto=format&fit=crop&w=600&q=80" alt="Luxury Wedding Package">
                    <div class="package-info">
                        <div class="package-name">Luxury Wedding</div>
                        <div class="package-desc">All-inclusive, high-glamour, and fully bespoke. For those who want it all.</div>
                        <div class="package-price">₹5,00,000</div>
                        <button class="hero-btn add-to-cart">Book Now</button>
                    </div>
                </div>
                <div class="package-card">
                    <img class="package-img" src="https://images.unsplash.com/photo-1570194065650-d99fb4cb6a23?auto=format&fit=crop&w=600&q=80" alt="Intimate Wedding Package">
                    <div class="package-info">
                        <div class="package-name">Intimate Affair</div>
                        <div class="package-desc">Chic, cozy, and meaningful. Perfect for close friends and family.</div>
                        <div class="package-price">₹2,00,000</div>
                        <button class="hero-btn add-to-cart">Book Now</button>
                    </div>
                </div>
                <div class="package-card">
                    <img class="package-img" src="https://images.unsplash.com/photo-1607604276583-eef5d0764d38?auto=format&fit=crop&w=600&q=80" alt="Decor & Design Only">
                    <div class="package-info">
                        <div class="package-name">Design Only</div>
                        <div class="package-desc">Stunning decor, florals, and ambiance for a show-stopping venue.</div>
                        <div class="package-price">₹1,50,000</div>
                        <button class="hero-btn add-to-cart">Book Now</button>
                    </div>
                </div>
                <div class="package-card">
                    <img class="package-img" src="https://images.unsplash.com/photo-1545389334-cf559b23b9c7?auto=format&fit=crop&w=600&q=80" alt="Day-of Coordination">
                    <div class="package-info">
                        <div class="package-name">Day-of Coordination</div>
                        <div class="package-desc">Expert management and support so you can enjoy every moment.</div>
                        <div class="package-price">₹50,000</div>
                        <button class="hero-btn add-to-cart">Book Now</button>
                    </div>
                </div>
            </div>
        </section>
        <!-- Polaroid Testimonials -->
        <section class="testimonials-section" id="testimonials">
            <h2 class="testimonials-title">Real Love Stories</h2>
            <div class="testimonials-grid">
                <div class="testimonial-polaroid">
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Amit & Priya">
                    <div class="quote">“DreamWed made our wedding magical! Every detail was perfect.”</div>
                    <div class="client-name">Amit & Priya</div>
                </div>
                <div class="testimonial-polaroid">
                    <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="Rohit & Aarti">
                    <div class="quote">“The decor and food were outstanding. Our guests still talk about it!”</div>
                    <div class="client-name">Rohit & Aarti</div>
                </div>
                <div class="testimonial-polaroid">
                    <img src="https://randomuser.me/api/portraits/men/12.jpg" alt="Neeraj & Neha">
                    <div class="quote">“Professional, creative, and so caring. Highly recommended!”</div>
                    <div class="client-name">Neeraj & Neha</div>
                </div>
            </div>
        </section>
        <!-- Circular Gallery -->
        <section class="gallery-section" id="gallery">
            <h2 class="gallery-title">Gallery</h2>
            <div class="gallery-circles">
                <div class="gallery-circle" tabindex="0" data-img="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1464983953574-0892a716854b?auto=format&fit=crop&w=400&q=80" alt="Wedding Ceremony">
                </div>
                <div class="gallery-circle" tabindex="0" data-img="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80" alt="Wedding Decor">
                </div>
                <div class="gallery-circle" tabindex="0" data-img="https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=400&q=80" alt="Wedding Cake">
                </div>
                <div class="gallery-circle" tabindex="0" data-img="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=400&q=80" alt="Couple Dance">
                </div>
                <div class="gallery-circle" tabindex="0" data-img="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=800&q=80">
                    <img src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=400&q=80" alt="Wedding Venue">
                </div>
            </div>
            <!-- Lightbox -->
            <div class="lightbox" id="lightbox" aria-modal="true" role="dialog">
                <span class="close" id="lightboxClose" aria-label="Close">&times;</span>
                <img src="" alt="Gallery Image" id="lightboxImg">
            </div>
        </section>
        <!-- Footer -->
        <footer id="contact">
            <div>&copy; 2025 DreamWed. Signature weddings, legendary memories.</div>
            <div style="margin-top: 0.5rem;">Contact: <a href="mailto:<EMAIL>" style="color:var(--sidebar-gold);text-decoration:underline;"><EMAIL></a> | +91 98765 43210</div>
            <div class="footer-social">
                <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
                <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
            </div>
        </footer>
    </main>
    <script>
    // Book Now scroll to packages
    document.getElementById('bookNowBtn').addEventListener('click', () => {
        document.getElementById('packages').scrollIntoView({behavior:'smooth'});
    });
    // Cart functionality (demo)
    let cartCount = 0;
    document.querySelectorAll('.add-to-cart').forEach(btn => {
        btn.addEventListener('click', function() {
            cartCount++;
            // You can add a floating cart badge or notification here
            this.textContent = 'Added!';
            this.disabled = true;
            setTimeout(() => {
                this.textContent = 'Book Now';
                this.disabled = false;
            }, 1200);
        });
    });
    // Gallery Lightbox
    const gallery = document.querySelectorAll('.gallery-circle');
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightboxImg');
    const lightboxClose = document.getElementById('lightboxClose');
    gallery.forEach(item => {
        item.addEventListener('click', function() {
            lightboxImg.src = this.getAttribute('data-img');
            lightbox.classList.add('active');
        });
        item.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                lightboxImg.src = this.getAttribute('data-img');
                lightbox.classList.add('active');
            }
        });
    });
    lightboxClose.addEventListener('click', () => {
        lightbox.classList.remove('active');
        lightboxImg.src = '';
    });
    lightbox.addEventListener('click', (e) => {
        if (e.target === lightbox) {
            lightbox.classList.remove('active');
            lightboxImg.src = '';
        }
    });
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            lightbox.classList.remove('active');
            lightboxImg.src = '';
        }
    });
    // Sidebar nav active state
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        link.addEventListener('click', function() {
            document.querySelectorAll('.sidebar-nav a').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    </script>
</body>
</html>
