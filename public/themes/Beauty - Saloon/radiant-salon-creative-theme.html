<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Radiant Salon - Creative Beauty Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 75%, #ff9a9e 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        .creative-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 50px 20px;
            position: relative;
            clip-path: polygon(0 0, 100% 0, 85% 100%, 0 85%);
            margin-bottom: 40px;
        }

        /* Mobile Header & Bottom Menu */
        .mobile-header {
            display: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 15px 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            color: white;
        }

        .mobile-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-logo {
            font-size: 1.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .mobile-bottom-menu {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            z-index: 1000;
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            padding: 5px;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            transform: translateY(-2px) scale(1.1);
        }

        .menu-icon {
            font-size: 1.2rem;
            margin-bottom: 3px;
        }

        .header-content {
            max-width: 600px;
            margin: 0 auto;
            text-align: center;
            color: white;
        }

        .salon-title {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .salon-motto {
            font-size: 1.2rem;
            opacity: 0.9;
            font-style: italic;
        }

        .diagonal-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .service-diagonal {
            display: flex;
            margin-bottom: 40px;
            min-height: 200px;
            position: relative;
        }

        .service-diagonal:nth-child(odd) {
            justify-content: flex-start;
        }

        .service-diagonal:nth-child(even) {
            justify-content: flex-end;
        }

        .diagonal-card {
            width: 70%;
            background: white;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transform: rotate(-2deg);
            transition: all 0.4s ease;
            position: relative;
        }

        .service-diagonal:nth-child(even) .diagonal-card {
            transform: rotate(2deg);
        }

        .diagonal-card:hover {
            transform: rotate(0deg) scale(1.05);
            box-shadow: 0 20px 50px rgba(0,0,0,0.2);
        }

        .card-gradient {
            height: 8px;
            width: 100%;
        }

        .service-diagonal:nth-child(1) .card-gradient { background: linear-gradient(90deg, #ff6b6b, #feca57); }
        .service-diagonal:nth-child(2) .card-gradient { background: linear-gradient(90deg, #48dbfb, #0abde3); }
        .service-diagonal:nth-child(3) .card-gradient { background: linear-gradient(90deg, #ff9ff3, #f368e0); }
        .service-diagonal:nth-child(4) .card-gradient { background: linear-gradient(90deg, #54a0ff, #2e86de); }
        .service-diagonal:nth-child(5) .card-gradient { background: linear-gradient(90deg, #5f27cd, #341f97); }

        .card-content {
            padding: 30px;
            display: flex;
            gap: 25px;
            align-items: center;
        }

        .service-image {
            width: 100px;
            height: 100px;
            background-size: cover;
            background-position: center;
            border-radius: 20px;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .service-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255, 107, 107, 0.3), rgba(254, 202, 87, 0.3));
            opacity: 0;
            transition: all 0.3s ease;
        }

        .diagonal-card:hover .service-image::before {
            opacity: 1;
        }

        .service-visual {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .service-diagonal:nth-child(1) .service-visual { background: linear-gradient(135deg, #ff6b6b, #feca57); }
        .service-diagonal:nth-child(2) .service-visual { background: linear-gradient(135deg, #48dbfb, #0abde3); }
        .service-diagonal:nth-child(3) .service-visual { background: linear-gradient(135deg, #ff9ff3, #f368e0); }
        .service-diagonal:nth-child(4) .service-visual { background: linear-gradient(135deg, #54a0ff, #2e86de); }
        .service-diagonal:nth-child(5) .service-visual { background: linear-gradient(135deg, #5f27cd, #341f97); }

        .service-visual::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            animation: shine 3s infinite;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(0%) translateY(0%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .service-info {
            flex: 1;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .service-description {
            color: #7f8c8d;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .service-highlights {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .highlight {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            color: white;
        }

        .service-diagonal:nth-child(1) .highlight { background: #ff6b6b; }
        .service-diagonal:nth-child(2) .highlight { background: #48dbfb; }
        .service-diagonal:nth-child(3) .highlight { background: #ff9ff3; }
        .service-diagonal:nth-child(4) .highlight { background: #54a0ff; }
        .service-diagonal:nth-child(5) .highlight { background: #5f27cd; }

        .price-book-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;
        }

        .service-price {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .creative-book-btn {
            padding: 12px 25px;
            border: none;
            border-radius: 20px;
            font-weight: bold;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .service-diagonal:nth-child(1) .creative-book-btn { background: linear-gradient(45deg, #ff6b6b, #feca57); }
        .service-diagonal:nth-child(2) .creative-book-btn { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .service-diagonal:nth-child(3) .creative-book-btn { background: linear-gradient(45deg, #ff9ff3, #f368e0); }
        .service-diagonal:nth-child(4) .creative-book-btn { background: linear-gradient(45deg, #54a0ff, #2e86de); }
        .service-diagonal:nth-child(5) .creative-book-btn { background: linear-gradient(45deg, #5f27cd, #341f97); }

        .creative-book-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .creative-book-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.5s ease;
        }

        .creative-book-btn:hover::before {
            left: 100%;
        }

        .artistic-footer {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            margin-top: 60px;
            padding: 50px 20px;
            position: relative;
            clip-path: polygon(0 15%, 100% 0, 100% 100%, 0 100%);
        }

        .footer-artistic-bg {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="%23ff6b6b" opacity="0.3"/><circle cx="80" cy="40" r="3" fill="%2348dbfb" opacity="0.3"/><circle cx="60" cy="80" r="2" fill="%23ff9ff3" opacity="0.3"/></svg>') repeat;
            pointer-events: none;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            color: white;
            position: relative;
            z-index: 1;
        }

        .footer-title {
            font-size: 2rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b6b, #feca57, #48dbfb, #ff9ff3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .footer-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-section {
            text-align: left;
        }

        .footer-section h4 {
            color: #48dbfb;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .footer-section p {
            color: #bdc3c7;
            margin-bottom: 5px;
        }

        .creative-social {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 30px;
        }

        .social-bubble {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .social-bubble:nth-child(1) { background: linear-gradient(45deg, #ff6b6b, #feca57); }
        .social-bubble:nth-child(2) { background: linear-gradient(45deg, #48dbfb, #0abde3); }
        .social-bubble:nth-child(3) { background: linear-gradient(45deg, #ff9ff3, #f368e0); }
        .social-bubble:nth-child(4) { background: linear-gradient(45deg, #54a0ff, #2e86de); }

        .social-bubble:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
                padding-bottom: 80px;
            }

            .mobile-header,
            .mobile-bottom-menu {
                display: block;
            }

            .creative-header {
                display: none;
            }

            .salon-title {
                font-size: 2.2rem;
            }

            .diagonal-card {
                width: 95%;
                transform: rotate(0deg) !important;
            }

            .card-content {
                flex-direction: column;
                text-align: center;
            }

            .service-image {
                width: 100%;
                height: 120px;
            }

            .price-book-section {
                flex-direction: column;
                gap: 15px;
            }

            .footer-info {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .footer-section {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">RADIANT SALON</div>
            <button class="mobile-menu-btn">☰</button>
        </div>
    </div>

    <header class="creative-header">
        <div class="header-content">
            <h1 class="salon-title">RADIANT SALON</h1>
            <p class="salon-motto">Where Creativity Meets Beauty</p>
        </div>
    </header>

    <div class="diagonal-container">
        <div class="service-diagonal">
            <div class="diagonal-card">
                <div class="card-gradient"></div>
                <div class="card-content">
                    <div class="service-image" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');"></div>
                    <div class="service-visual">💇‍♀️</div>
                    <div class="service-info">
                        <h3 class="service-title">Creative Hair Design</h3>
                        <p class="service-description">Innovative hair cutting and coloring with artistic flair and modern techniques</p>
                        <div class="service-highlights">
                            <span class="highlight">Color Artistry</span>
                            <span class="highlight">Creative Cuts</span>
                            <span class="highlight">Hair Painting</span>
                        </div>
                        <div class="price-book-section">
                            <div class="service-price">₹2,800</div>
                            <button class="creative-book-btn">Book Art</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="service-diagonal">
            <div class="diagonal-card">
                <div class="card-gradient"></div>
                <div class="card-content">
                    <div class="service-image" style="background-image: url('https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');"></div>
                    <div class="service-visual">✨</div>
                    <div class="service-info">
                        <h3 class="service-title">Glow Facial Therapy</h3>
                        <p class="service-description">Radiant skin treatments with advanced techniques and premium products</p>
                        <div class="service-highlights">
                            <span class="highlight">LED Therapy</span>
                            <span class="highlight">Vitamin Infusion</span>
                            <span class="highlight">Glow Boost</span>
                        </div>
                        <div class="price-book-section">
                            <div class="service-price">₹3,200</div>
                            <button class="creative-book-btn">Get Glow</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="service-diagonal">
            <div class="diagonal-card">
                <div class="card-gradient"></div>
                <div class="card-content">
                    <div class="service-image" style="background-image: url('https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');"></div>
                    <div class="service-visual">💅</div>
                    <div class="service-info">
                        <h3 class="service-title">Nail Art Studio</h3>
                        <p class="service-description">Creative nail designs with artistic patterns and premium gel applications</p>
                        <div class="service-highlights">
                            <span class="highlight">3D Art</span>
                            <span class="highlight">Gel Extensions</span>
                            <span class="highlight">Custom Design</span>
                        </div>
                        <div class="price-book-section">
                            <div class="service-price">₹1,800</div>
                            <button class="creative-book-btn">Create Art</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="service-diagonal">
            <div class="diagonal-card">
                <div class="card-gradient"></div>
                <div class="card-content">
                    <div class="service-image" style="background-image: url('https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');"></div>
                    <div class="service-visual">👁️</div>
                    <div class="service-info">
                        <h3 class="service-title">Eye Enhancement</h3>
                        <p class="service-description">Professional eyebrow sculpting and eyelash extensions for stunning eyes</p>
                        <div class="service-highlights">
                            <span class="highlight">Microblading</span>
                            <span class="highlight">Lash Lift</span>
                            <span class="highlight">Tinting</span>
                        </div>
                        <div class="price-book-section">
                            <div class="service-price">₹1,500</div>
                            <button class="creative-book-btn">Enhance</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="service-diagonal">
            <div class="diagonal-card">
                <div class="card-gradient"></div>
                <div class="card-content">
                    <div class="service-image" style="background-image: url('https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80');"></div>
                    <div class="service-visual">💄</div>
                    <div class="service-info">
                        <h3 class="service-title">Glamour Makeup</h3>
                        <p class="service-description">Professional makeup artistry for special occasions and photoshoots</p>
                        <div class="service-highlights">
                            <span class="highlight">HD Makeup</span>
                            <span class="highlight">Airbrush</span>
                            <span class="highlight">Photoshoot Ready</span>
                        </div>
                        <div class="price-book-section">
                            <div class="service-price">₹4,500</div>
                            <button class="creative-book-btn">Glamorize</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="artistic-footer">
        <div class="footer-artistic-bg"></div>
        <div class="footer-content">
            <h2 class="footer-title">Connect With Radiant</h2>
            <div class="footer-info">
                <div class="footer-section">
                    <h4>Visit Us</h4>
                    <p>789 Creative Avenue</p>
                    <p>Bangalore, Karnataka</p>
                    <p>560001</p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>📞 +91 76543 21098</p>
                    <p>✉️ <EMAIL></p>
                    <p>🌐 www.radiantsalon.com</p>
                </div>
                <div class="footer-section">
                    <h4>Studio Hours</h4>
                    <p>Mon-Sat: 10 AM - 8 PM</p>
                    <p>Sunday: 11 AM - 6 PM</p>
                    <p>By appointment only</p>
                </div>
            </div>
            <div class="creative-social">
                <a href="#" class="social-bubble">📘</a>
                <a href="#" class="social-bubble">📷</a>
                <a href="#" class="social-bubble">🐦</a>
                <a href="#" class="social-bubble">💬</a>
            </div>
        </div>
    </footer>

    <!-- Mobile Bottom Menu -->
    <div class="mobile-bottom-menu">
        <div class="bottom-menu-items">
            <a href="#" class="menu-item">
                <div class="menu-icon">🏠</div>
                <span>Home</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">🎨</div>
                <span>Services</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">📅</div>
                <span>Book</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">📞</div>
                <span>Contact</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">👤</div>
                <span>Profile</span>
            </a>
        </div>
    </div>
</body>
</html>
