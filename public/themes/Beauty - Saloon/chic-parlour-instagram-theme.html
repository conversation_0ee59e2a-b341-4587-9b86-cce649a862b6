<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chic Parlour - Instagram Beauty Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
            overflow-x: hidden;
        }

        .insta-header {
            background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
            padding: 40px 20px;
            text-align: center;
            position: relative;
        }

        /* Mobile Header & Bottom Menu */
        .mobile-header {
            display: none;
            background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
            padding: 15px 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            color: white;
        }

        .mobile-header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-logo {
            font-size: 1.5rem;
            color: white;
            font-weight: bold;
        }

        .mobile-menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
        }

        .mobile-bottom-menu {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
            z-index: 1000;
        }

        .bottom-menu-items {
            display: flex;
            justify-content: space-around;
            padding: 10px 0;
        }

        .menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            padding: 5px;
            transition: all 0.3s ease;
        }

        .menu-item:hover {
            transform: translateY(-2px) scale(1.1);
        }

        .menu-icon {
            font-size: 1.2rem;
            margin-bottom: 3px;
        }

        .header-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.3);
        }

        .header-content {
            position: relative;
            z-index: 1;
            color: white;
        }

        .parlour-name {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .insta-handle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .story-highlights {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        .story-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 3px solid white;
        }

        .story-circle:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .grid-container {
            max-width: 900px;
            margin: 40px auto;
            padding: 0 20px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
        }

        .insta-post {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
        }

        .insta-post:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .post-header {
            padding: 15px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #efefef;
        }

        .service-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .insta-post:nth-child(1) .service-avatar { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .insta-post:nth-child(2) .service-avatar { background: linear-gradient(45deg, #a55eea, #8b5cf6); }
        .insta-post:nth-child(3) .service-avatar { background: linear-gradient(45deg, #26de81, #20bf6b); }
        .insta-post:nth-child(4) .service-avatar { background: linear-gradient(45deg, #fd79a8, #e84393); }
        .insta-post:nth-child(5) .service-avatar { background: linear-gradient(45deg, #54a0ff, #2e86de); }

        .post-info {
            flex: 1;
        }

        .service-username {
            font-weight: bold;
            font-size: 0.9rem;
            color: #333;
        }

        .service-location {
            font-size: 0.8rem;
            color: #666;
        }

        .post-image {
            height: 200px;
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .insta-post:nth-child(1) .post-image { background: linear-gradient(135deg, #ff9a9e, #fecfef); }
        .insta-post:nth-child(2) .post-image { background: linear-gradient(135deg, #a8edea, #fed6e3); }
        .insta-post:nth-child(3) .post-image { background: linear-gradient(135deg, #d299c2, #fef9d7); }
        .insta-post:nth-child(4) .post-image { background: linear-gradient(135deg, #89f7fe, #66a6ff); }
        .insta-post:nth-child(5) .post-image { background: linear-gradient(135deg, #fdbb2d, #22c1c3); }

        .post-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: all 0.6s ease;
        }

        .insta-post:hover .post-image::before {
            left: 100%;
        }

        .post-content {
            padding: 15px;
        }

        .post-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 10px;
        }

        .action-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            transform: scale(1.2);
        }

        .post-likes {
            font-weight: bold;
            font-size: 0.9rem;
            margin-bottom: 8px;
        }

        .post-caption {
            font-size: 0.9rem;
            line-height: 1.4;
            margin-bottom: 10px;
        }

        .service-name {
            font-weight: bold;
            color: #333;
        }

        .hashtags {
            color: #3897f0;
            font-size: 0.85rem;
            margin-bottom: 15px;
        }

        .price-book-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 10px;
            border-top: 1px solid #efefef;
        }

        .insta-price {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }

        .book-insta-btn {
            background: linear-gradient(45deg, #833ab4, #fd1d1d, #fcb045);
            color: white;
            border: none;
            padding: 8px 20px;
            border-radius: 20px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.85rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .book-insta-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(131, 58, 180, 0.4);
        }

        .trending-footer {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 50px 20px;
            margin-top: 50px;
            color: white;
            text-align: center;
        }

        .footer-grid {
            max-width: 800px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .footer-section h4 {
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #ffecd2;
        }

        .footer-section p {
            margin-bottom: 5px;
            opacity: 0.9;
            font-size: 0.9rem;
        }

        .social-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            max-width: 300px;
            margin: 30px auto 0;
        }

        .social-insta {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.3rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .social-insta:nth-child(1) { background: linear-gradient(45deg, #ff6b6b, #ee5a24); }
        .social-insta:nth-child(2) { background: linear-gradient(45deg, #a55eea, #8b5cf6); }
        .social-insta:nth-child(3) { background: linear-gradient(45deg, #26de81, #20bf6b); }
        .social-insta:nth-child(4) { background: linear-gradient(45deg, #fd79a8, #e84393); }

        .social-insta:hover {
            transform: translateY(-3px) rotate(5deg);
            box-shadow: 0 10px 25px rgba(0,0,0,0.3);
        }

        .social-insta::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: all 0.5s ease;
        }

        .social-insta:hover::before {
            left: 100%;
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
                padding-bottom: 80px;
            }

            .mobile-header,
            .mobile-bottom-menu {
                display: block;
            }

            .insta-header {
                display: none;
            }

            .parlour-name {
                font-size: 2rem;
            }

            .story-highlights {
                flex-wrap: wrap;
            }

            .grid-container {
                grid-template-columns: 1fr;
                padding: 0 15px;
            }

            .footer-grid {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .social-grid {
                grid-template-columns: repeat(4, 1fr);
                max-width: 250px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <div class="mobile-header">
        <div class="mobile-header-content">
            <div class="mobile-logo">✨ Chic Parlour</div>
            <button class="mobile-menu-btn">☰</button>
        </div>
    </div>

    <header class="insta-header">
        <div class="header-overlay"></div>
        <div class="header-content">
            <h1 class="parlour-name">✨ Chic Parlour ✨</h1>
            <p class="insta-handle">@chicparlour_official • Beauty Studio</p>
            <div class="story-highlights">
                <div class="story-circle">💇‍♀️</div>
                <div class="story-circle">✨</div>
                <div class="story-circle">💅</div>
                <div class="story-circle">👁️</div>
                <div class="story-circle">💄</div>
            </div>
        </div>
    </header>

    <div class="grid-container">
        <div class="insta-post">
            <div class="post-header">
                <div class="service-avatar">💇‍♀️</div>
                <div class="post-info">
                    <div class="service-username">chicparlour_hair</div>
                    <div class="service-location">Hair Studio, Mumbai</div>
                </div>
                <div>⋯</div>
            </div>
            <div class="post-image" style="background-image: url('https://images.unsplash.com/photo-1560066984-138dadb4c035?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');">💇‍♀️</div>
            <div class="post-content">
                <div class="post-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                </div>
                <div class="post-likes">1,247 likes</div>
                <div class="post-caption">
                    <span class="service-name">chicparlour_official</span> Transform your look with our signature hair styling! ✨
                </div>
                <div class="hashtags">#hairstyling #transformation #chicparlour #mumbai #beauty</div>
                <div class="price-book-row">
                    <div class="insta-price">₹1,800</div>
                    <button class="book-insta-btn">Book Now</button>
                </div>
            </div>
        </div>

        <div class="insta-post">
            <div class="post-header">
                <div class="service-avatar">✨</div>
                <div class="post-info">
                    <div class="service-username">chicparlour_glow</div>
                    <div class="service-location">Facial Studio, Mumbai</div>
                </div>
                <div>⋯</div>
            </div>
            <div class="post-image" style="background-image: url('https://images.unsplash.com/photo-1616394584738-fc6e612e71b9?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');">✨</div>
            <div class="post-content">
                <div class="post-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                </div>
                <div class="post-likes">892 likes</div>
                <div class="post-caption">
                    <span class="service-name">chicparlour_official</span> Glow up with our premium facial treatments! 🌟
                </div>
                <div class="hashtags">#glowup #facial #skincare #chicparlour #beauty #selfcare</div>
                <div class="price-book-row">
                    <div class="insta-price">₹2,500</div>
                    <button class="book-insta-btn">Book Now</button>
                </div>
            </div>
        </div>

        <div class="insta-post">
            <div class="post-header">
                <div class="service-avatar">💅</div>
                <div class="post-info">
                    <div class="service-username">chicparlour_nails</div>
                    <div class="service-location">Nail Art Studio, Mumbai</div>
                </div>
                <div>⋯</div>
            </div>
            <div class="post-image" style="background-image: url('https://images.unsplash.com/photo-1604654894610-df63bc536371?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');">💅</div>
            <div class="post-content">
                <div class="post-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                </div>
                <div class="post-likes">1,456 likes</div>
                <div class="post-caption">
                    <span class="service-name">chicparlour_official</span> Nail art that speaks your style! 💅✨
                </div>
                <div class="hashtags">#nailart #manicure #nails #chicparlour #style #trending</div>
                <div class="price-book-row">
                    <div class="insta-price">₹1,200</div>
                    <button class="book-insta-btn">Book Now</button>
                </div>
            </div>
        </div>

        <div class="insta-post">
            <div class="post-header">
                <div class="service-avatar">👁️</div>
                <div class="post-info">
                    <div class="service-username">chicparlour_brows</div>
                    <div class="service-location">Brow Bar, Mumbai</div>
                </div>
                <div>⋯</div>
            </div>
            <div class="post-image" style="background-image: url('https://images.unsplash.com/photo-1522337360788-8b13dee7a37e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');">👁️</div>
            <div class="post-content">
                <div class="post-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                </div>
                <div class="post-likes">734 likes</div>
                <div class="post-caption">
                    <span class="service-name">chicparlour_official</span> Perfect brows frame your beautiful eyes! 👁️✨
                </div>
                <div class="hashtags">#eyebrows #threading #browart #chicparlour #eyes #beauty</div>
                <div class="price-book-row">
                    <div class="insta-price">₹800</div>
                    <button class="book-insta-btn">Book Now</button>
                </div>
            </div>
        </div>

        <div class="insta-post">
            <div class="post-header">
                <div class="service-avatar">💄</div>
                <div class="post-info">
                    <div class="service-username">chicparlour_makeup</div>
                    <div class="service-location">Makeup Studio, Mumbai</div>
                </div>
                <div>⋯</div>
            </div>
            <div class="post-image" style="background-image: url('https://images.unsplash.com/photo-1487412947147-5cebf100ffc2?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');">💄</div>
            <div class="post-content">
                <div class="post-actions">
                    <button class="action-btn">❤️</button>
                    <button class="action-btn">💬</button>
                    <button class="action-btn">📤</button>
                </div>
                <div class="post-likes">2,103 likes</div>
                <div class="post-caption">
                    <span class="service-name">chicparlour_official</span> Makeup magic for your special moments! 💄✨
                </div>
                <div class="hashtags">#makeup #glam #party #chicparlour #beauty #makeover</div>
                <div class="price-book-row">
                    <div class="insta-price">₹3,200</div>
                    <button class="book-insta-btn">Book Now</button>
                </div>
            </div>
        </div>
    </div>

    <footer class="trending-footer">
        <h2 style="margin-bottom: 30px; font-size: 2rem;">Follow the Trend</h2>
        <div class="footer-grid">
            <div class="footer-section">
                <h4>📍 Location</h4>
                <p>567 Fashion Street</p>
                <p>Bandra West, Mumbai</p>
                <p>400050</p>
            </div>
            <div class="footer-section">
                <h4>📱 Connect</h4>
                <p>+91 54321 09876</p>
                <p><EMAIL></p>
                <p>@chicparlour_official</p>
            </div>
            <div class="footer-section">
                <h4>⏰ Studio Hours</h4>
                <p>Mon-Sat: 10 AM - 9 PM</p>
                <p>Sunday: 11 AM - 7 PM</p>
                <p>Walk-ins welcome!</p>
            </div>
        </div>
        <div class="social-grid">
            <a href="#" class="social-insta">📘</a>
            <a href="#" class="social-insta">📷</a>
            <a href="#" class="social-insta">🐦</a>
            <a href="#" class="social-insta">💬</a>
        </div>
        <p style="margin-top: 30px; opacity: 0.8;">© 2024 Chic Parlour. Stay Trendy, Stay Beautiful.</p>
    </footer>

    <!-- Mobile Bottom Menu -->
    <div class="mobile-bottom-menu">
        <div class="bottom-menu-items">
            <a href="#" class="menu-item">
                <div class="menu-icon">🏠</div>
                <span>Home</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">📷</div>
                <span>Posts</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">📅</div>
                <span>Book</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">📞</div>
                <span>Contact</span>
            </a>
            <a href="#" class="menu-item">
                <div class="menu-icon">👤</div>
                <span>Profile</span>
            </a>
        </div>
    </div>
</body>
</html>
