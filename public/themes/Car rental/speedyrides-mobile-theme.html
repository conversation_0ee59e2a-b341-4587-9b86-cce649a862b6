<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SpeedyRides - Car Rental Service</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 800;
            color: #2563eb;
            text-decoration: none;
        }

        .nav-menu {
            display: none;
        }

        .menu-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #1a1a1a;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            color: white;
            padding: 80px 0 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            opacity: 0.2;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .hero-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .cta-button {
            background: #ffffff;
            color: #2563eb;
            padding: 16px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        /* Services Section */
        .services {
            padding: 60px 0;
            background: #f8fafc;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 800;
            color: #1a1a1a;
            margin-bottom: 12px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #64748b;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease;
        }

        .service-card:hover {
            transform: translateY(-4px);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .service-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            object-fit: cover;
        }

        .service-info h3 {
            font-size: 20px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 4px;
        }

        .service-price {
            font-size: 18px;
            font-weight: 800;
            color: #2563eb;
        }

        .service-description {
            color: #64748b;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .service-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .feature-tag {
            background: #eff6ff;
            color: #2563eb;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        /* Features Section */
        .features {
            padding: 60px 0;
            background: white;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: #eff6ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2563eb;
            font-size: 20px;
            flex-shrink: 0;
        }

        .feature-content h3 {
            font-size: 18px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .feature-content p {
            color: #64748b;
            line-height: 1.6;
        }

        /* Booking Section */
        .booking {
            padding: 60px 0;
            background: #f8fafc;
        }

        .booking-form {
            background: white;
            border-radius: 12px;
            padding: 32px 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #2563eb;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .submit-btn {
            width: 100%;
            background: #2563eb;
            color: white;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .submit-btn:hover {
            background: #1d4ed8;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Footer */
        .footer {
            background: #1a1a1a;
            color: white;
            padding: 40px 0 20px;
            text-align: center;
        }

        .footer-content {
            margin-bottom: 20px;
        }

        .footer-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .footer-text {
            color: #94a3b8;
            margin-bottom: 20px;
        }

        .footer-contact {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #94a3b8;
            font-size: 14px;
        }

        .footer-bottom {
            border-top: 1px solid #374151;
            padding-top: 20px;
            color: #94a3b8;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .nav-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .nav-menu a {
                text-decoration: none;
                color: #1a1a1a;
                font-weight: 500;
                transition: color 0.2s ease;
            }

            .nav-menu a:hover {
                color: #2563eb;
            }

            .menu-btn {
                display: none;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .services-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }

            .features-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .form-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .footer-contact {
                flex-direction: row;
                justify-content: center;
                gap: 32px;
            }
        }

        @media (min-width: 1024px) {
            .features-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="logo">
                <i class="fas fa-car"></i> SpeedyRides
            </a>
            <ul class="nav-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#services">सेवाएं</a></li>
                <li><a href="#booking">बुकिंग</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
            <button class="menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    भरोसेमंद कार रेंटल सेवा
                </div>
                <h1 class="hero-title">
                    आपकी यात्रा का<br>
                    सबसे अच्छा साथी
                </h1>
                <p class="hero-subtitle">
                    24/7 उपलब्ध, सुरक्षित और किफायती कार रेंटल सेवा। 
                    अब आपकी मंजिल सिर्फ एक कॉल दूर है।
                </p>
                <a href="#booking" class="cta-button">
                    <i class="fas fa-phone"></i>
                    अभी बुक करें
                </a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">हमारी सेवाएं</h2>
                <p class="section-subtitle">
                    हर जरूरत के लिए बेहतरीन कार रेंटल सेवा
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header">
                        <img src="https://images.unsplash.com/photo-1552519507-da3b142c6e3d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Economy Car" class="service-image">
                        <div class="service-info">
                            <h3>इकॉनमी कार</h3>
                            <div class="service-price">₹12/किमी से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        शहर में घूमने के लिए किफायती और आरामदायक कारें। फ्यूल एफिशिएंट और साफ-सुथरी।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">AC</span>
                        <span class="feature-tag">4-5 सीटर</span>
                        <span class="feature-tag">म्यूजिक सिस्टम</span>
                        <span class="feature-tag">GPS</span>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <img src="https://images.unsplash.com/photo-1544636331-e26879cd4d9b?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="SUV Car" class="service-image">
                        <div class="service-info">
                            <h3>SUV/MUV</h3>
                            <div class="service-price">₹18/किमी से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        परिवार और दोस्तों के साथ यात्रा के लिए विशाल और आरामदायक कारें।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">7-8 सीटर</span>
                        <span class="feature-tag">बड़ा लगेज</span>
                        <span class="feature-tag">पावरफुल इंजन</span>
                        <span class="feature-tag">सेफ्टी फीचर्स</span>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <img src="https://images.unsplash.com/photo-1563720223185-11003d516935?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Luxury Car" class="service-image">
                        <div class="service-info">
                            <h3>प्रीमियम कार</h3>
                            <div class="service-price">₹25/किमी से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        खास मौकों के लिए लक्जरी और स्टाइलिश कारें। प्रीमियम एक्सपीरियंस।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">लक्जरी इंटीरियर</span>
                        <span class="feature-tag">लेदर सीट्स</span>
                        <span class="feature-tag">प्रीमियम साउंड</span>
                        <span class="feature-tag">प्रोफेशनल ड्राइवर</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">क्यों चुनें SpeedyRides</h2>
                <p class="section-subtitle">
                    हमारी विशेषताएं जो हमें बेहतर बनाती हैं
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>100% सुरक्षित</h3>
                        <p>सभी कारें पूरी तरह से इंश्योर्ड हैं और नियमित सर्विसिंग के साथ मेंटेन की जाती हैं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="feature-content">
                        <h3>24/7 सपोर्ट</h3>
                        <p>किसी भी समय, कहीं भी समस्या हो तो हमारी टीम आपकी मदद के लिए तैयार है।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <div class="feature-content">
                        <h3>बेस्ट प्राइस</h3>
                        <p>मार्केट में सबसे कम दरों पर प्रीमियम कार रेंटल सेवा का लाभ उठाएं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>आसान बुकिंग</h3>
                        <p>सिर्फ 2 मिनट में ऑनलाइन बुकिंग करें और तुरंत कन्फर्मेशन पाएं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="feature-content">
                        <h3>नई कारें</h3>
                        <p>हमारे पास सभी लेटेस्ट मॉडल की साफ-सुथरी और अच्छी कंडीशन की कारें हैं।</p>
                    </div>
                </div>

                <div class="feature-item">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div class="feature-content">
                        <h3>GPS ट्रैकिंग</h3>
                        <p>सभी कारों में GPS ट्रैकिंग सिस्टम है जो आपकी सुरक्षा को बढ़ाता है।</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">अभी बुक करें</h2>
                <p class="section-subtitle">
                    आसान बुकिंग प्रोसेस - केवल 2 मिनट में
                </p>
            </div>

            <div class="booking-form">
                <form id="bookingForm">
                    <div class="form-group">
                        <label class="form-label">आपका नाम *</label>
                        <input type="text" class="form-input" name="name" placeholder="अपना पूरा नाम लिखें" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">मोबाइल नंबर *</label>
                        <input type="tel" class="form-input" name="phone" placeholder="+91 98765 43210" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">कार का प्रकार *</label>
                            <select class="form-input" name="carType" required>
                                <option value="">कार का प्रकार चुनें</option>
                                <option value="economy">इकॉनमी कार</option>
                                <option value="suv">SUV/MUV</option>
                                <option value="premium">प्रीमियम कार</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">रेंटल प्रकार *</label>
                            <select class="form-input" name="rentalType" required>
                                <option value="">रेंटल प्रकार चुनें</option>
                                <option value="hourly">घंटे के हिसाब</option>
                                <option value="daily">दैनिक</option>
                                <option value="weekly">साप्ताहिक</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">पिकअप दिनांक *</label>
                            <input type="date" class="form-input" name="pickupDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">पिकअप समय *</label>
                            <input type="time" class="form-input" name="pickupTime" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">पिकअप लोकेशन</label>
                        <input type="text" class="form-input" name="pickupLocation" placeholder="पिकअप का पता">
                    </div>

                    <div class="form-group">
                        <label class="form-label">विशेष आवश्यकताएं</label>
                        <textarea class="form-input" name="requirements" rows="3" placeholder="कोई विशेष जरूरत हो तो बताएं..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        बुकिंग कन्फर्म करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <h3 class="footer-title">SpeedyRides</h3>
                <p class="footer-text">
                    भरोसेमंद कार रेंटल सेवा - आपकी यात्रा, हमारी जिम्मेदारी
                </p>
                <div class="footer-contact">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+91 98765 43210</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>मुंबई, दिल्ली, बेंगलुरु</span>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SpeedyRides. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं SpeedyRides की कार रेंटल सेवा के बारे में जानकारी चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.querySelector('input[name="pickupDate"]');
            if (pickupDate) {
                pickupDate.min = today;
            }
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.name || !data.phone || !data.carType || !data.rentalType || !data.pickupDate || !data.pickupTime) {
                alert('कृपया सभी आवश्यक फील्ड भरें।');
                return;
            }

            const message = `🚗 *SpeedyRides - कार बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार का प्रकार:* ${data.carType}
📅 *रेंटल प्रकार:* ${data.rentalType}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
📍 *पिकअप लोकेशन:* ${data.pickupLocation || 'नहीं बताया'}

📝 *विशेष आवश्यकताएं:* ${data.requirements || 'कोई नहीं'}

कृपया इस बुकिंग को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            alert('✅ आपकी बुकिंग रिक्वेस्ट WhatsApp पर भेज दी गई है!');
            this.reset();
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Mobile menu toggle
        document.querySelector('.menu-btn').addEventListener('click', function() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        });
    </script>
</body>
</html>
