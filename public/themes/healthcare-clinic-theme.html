<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr<PERSON> <PERSON> - Healthcare Services</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1a202c;
            background: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .logo {
            font-size: 24px;
            font-weight: 800;
            color: #2b6cb0;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-menu {
            display: none;
        }

        .menu-btn {
            background: none;
            border: none;
            font-size: 20px;
            color: #1a202c;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #2b6cb0 0%, #2c5282 100%);
            color: white;
            padding: 80px 0 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            opacity: 0.1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .hero-title {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 18px;
            margin-bottom: 32px;
            opacity: 0.9;
        }

        .hero-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            align-items: center;
        }

        .cta-button {
            background: #ffffff;
            color: #2b6cb0;
            padding: 16px 32px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
            width: 100%;
            max-width: 280px;
            justify-content: center;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        .cta-secondary {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        /* Doctor Info */
        .doctor-info {
            padding: 60px 0;
            background: #f7fafc;
        }

        .doctor-card {
            background: white;
            border-radius: 12px;
            padding: 32px 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            text-align: center;
        }

        .doctor-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 20px;
            border: 4px solid #e2e8f0;
        }

        .doctor-name {
            font-size: 24px;
            font-weight: 800;
            color: #1a202c;
            margin-bottom: 8px;
        }

        .doctor-specialty {
            font-size: 16px;
            color: #2b6cb0;
            font-weight: 600;
            margin-bottom: 16px;
        }

        .doctor-experience {
            font-size: 14px;
            color: #718096;
            margin-bottom: 20px;
        }

        .doctor-qualifications {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .qualification-tag {
            background: #ebf8ff;
            color: #2b6cb0;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        /* Services Section */
        .services {
            padding: 60px 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 28px;
            font-weight: 800;
            color: #1a202c;
            margin-bottom: 12px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #718096;
            max-width: 600px;
            margin: 0 auto;
        }

        .services-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .service-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
            transition: transform 0.2s ease;
        }

        .service-card:hover {
            transform: translateY(-4px);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .service-icon {
            width: 60px;
            height: 60px;
            background: #ebf8ff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #2b6cb0;
            font-size: 24px;
            flex-shrink: 0;
        }

        .service-info h3 {
            font-size: 20px;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 4px;
        }

        .service-price {
            font-size: 16px;
            font-weight: 600;
            color: #38a169;
        }

        .service-description {
            color: #718096;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .service-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 16px;
        }

        .feature-tag {
            background: #f0fff4;
            color: #38a169;
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 600;
        }

        .book-btn {
            background: #2b6cb0;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
            width: 100%;
        }

        .book-btn:hover {
            background: #2c5282;
        }

        /* Appointment Section */
        .appointment {
            padding: 60px 0;
            background: #f7fafc;
        }

        .appointment-form {
            background: white;
            border-radius: 12px;
            padding: 32px 24px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e2e8f0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #2b6cb0;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .submit-btn {
            width: 100%;
            background: #2b6cb0;
            color: white;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .submit-btn:hover {
            background: #2c5282;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Footer */
        .footer {
            background: #1a202c;
            color: white;
            padding: 40px 0 20px;
            text-align: center;
        }

        .footer-content {
            margin-bottom: 20px;
        }

        .footer-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 12px;
        }

        .footer-text {
            color: #a0aec0;
            margin-bottom: 20px;
        }

        .footer-contact {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #a0aec0;
            font-size: 14px;
        }

        .footer-bottom {
            border-top: 1px solid #4a5568;
            padding-top: 20px;
            color: #a0aec0;
            font-size: 14px;
        }

        /* Responsive Design */
        @media (min-width: 768px) {
            .nav-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .nav-menu a {
                text-decoration: none;
                color: #1a202c;
                font-weight: 500;
                transition: color 0.2s ease;
            }

            .nav-menu a:hover {
                color: #2b6cb0;
            }

            .menu-btn {
                display: none;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .hero-buttons {
                flex-direction: row;
                justify-content: center;
            }

            .services-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }

            .form-row {
                grid-template-columns: repeat(2, 1fr);
            }

            .footer-contact {
                flex-direction: row;
                justify-content: center;
                gap: 32px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav">
            <a href="#" class="logo">
                <i class="fas fa-stethoscope"></i> Dr. Sharma Clinic
            </a>
            <ul class="nav-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#services">सेवाएं</a></li>
                <li><a href="#appointment">अपॉइंटमेंट</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
            <button class="menu-btn">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-award"></i>
                    15+ साल का अनुभव
                </div>
                <h1 class="hero-title">
                    आपकी सेहत<br>
                    हमारी प्राथमिकता
                </h1>
                <p class="hero-subtitle">
                    अनुभवी डॉक्टर, आधुनिक उपकरण और बेहतरीन इलाज। 
                    आपके स्वास्थ्य की देखभाल हमारी जिम्मेदारी है।
                </p>
                <div class="hero-buttons">
                    <a href="#appointment" class="cta-button">
                        <i class="fas fa-calendar-check"></i>
                        अपॉइंटमेंट बुक करें
                    </a>
                    <a href="tel:+919876543210" class="cta-button cta-secondary">
                        <i class="fas fa-phone"></i>
                        इमरजेंसी कॉल
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Doctor Info -->
    <section class="doctor-info">
        <div class="container">
            <div class="doctor-card">
                <img src="https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80" alt="Dr. Sharma" class="doctor-image">
                <h2 class="doctor-name">डॉ. राजेश शर्मा</h2>
                <p class="doctor-specialty">जनरल फिजिशियन & इंटर्नल मेडिसिन स्पेशलिस्ट</p>
                <p class="doctor-experience">15+ साल का अनुभव | 10,000+ मरीजों का सफल इलाज</p>
                <div class="doctor-qualifications">
                    <span class="qualification-tag">MBBS</span>
                    <span class="qualification-tag">MD (Internal Medicine)</span>
                    <span class="qualification-tag">AIIMS Delhi</span>
                    <span class="qualification-tag">IMA Member</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">हमारी सेवाएं</h2>
                <p class="section-subtitle">
                    व्यापक स्वास्थ्य सेवाएं एक ही जगह
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-stethoscope"></i>
                        </div>
                        <div class="service-info">
                            <h3>जनरल चेकअप</h3>
                            <div class="service-price">₹300 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        संपूर्ण स्वास्थ्य जांच, बुखार, सर्दी-जुकाम, और सामान्य बीमारियों का इलाज।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">BP चेक</span>
                        <span class="feature-tag">वजन मॉनिटरिंग</span>
                        <span class="feature-tag">बेसिक टेस्ट</span>
                    </div>
                    <button class="book-btn" onclick="bookService('जनरल चेकअप', '300')">अपॉइंटमेंट बुक करें</button>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-heartbeat"></i>
                        </div>
                        <div class="service-info">
                            <h3>हार्ट चेकअप</h3>
                            <div class="service-price">₹800 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        दिल की संपूर्ण जांच, ECG, और हृदय संबंधी समस्याओं का विशेष इलाज।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">ECG</span>
                        <span class="feature-tag">BP मॉनिटरिंग</span>
                        <span class="feature-tag">कार्डियक रिस्क</span>
                    </div>
                    <button class="book-btn" onclick="bookService('हार्ट चेकअप', '800')">अपॉइंटमेंट बुक करें</button>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-vial"></i>
                        </div>
                        <div class="service-info">
                            <h3>लैब टेस्ट</h3>
                            <div class="service-price">₹200 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        ब्लड टेस्ट, यूरिन टेस्ट, शुगर टेस्ट और अन्य सभी लैब जांच।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">ब्लड टेस्ट</span>
                        <span class="feature-tag">यूरिन टेस्ट</span>
                        <span class="feature-tag">शुगर टेस्ट</span>
                    </div>
                    <button class="book-btn" onclick="bookService('लैब टेस्ट', '200')">अपॉइंटमेंट बुक करें</button>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-syringe"></i>
                        </div>
                        <div class="service-info">
                            <h3>वैक्सीनेशन</h3>
                            <div class="service-price">₹500 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        सभी प्रकार के टीकाकरण - बच्चों और बड़ों के लिए उपलब्ध।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">COVID वैक्सीन</span>
                        <span class="feature-tag">फ्लू शॉट</span>
                        <span class="feature-tag">चाइल्ड वैक्सीन</span>
                    </div>
                    <button class="book-btn" onclick="bookService('वैक्सीनेशन', '500')">अपॉइंटमेंट बुक करें</button>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="service-info">
                            <h3>स्पेशलिस्ट कंसल्टेशन</h3>
                            <div class="service-price">₹600 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        विशेष बीमारियों के लिए एक्सपर्ट डॉक्टर से सलाह और इलाज।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">डायबिटीज</span>
                        <span class="feature-tag">हाइपरटेंशन</span>
                        <span class="feature-tag">थायराइड</span>
                    </div>
                    <button class="book-btn" onclick="bookService('स्पेशलिस्ट कंसल्टेशन', '600')">अपॉइंटमेंट बुक करें</button>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <div class="service-info">
                            <h3>होम विजिट</h3>
                            <div class="service-price">₹1000 से शुरू</div>
                        </div>
                    </div>
                    <p class="service-description">
                        घर पर डॉक्टर की सेवा - बुजुर्गों और गंभीर मरीजों के लिए।
                    </p>
                    <div class="service-features">
                        <span class="feature-tag">घर पर चेकअप</span>
                        <span class="feature-tag">दवा डिलीवरी</span>
                        <span class="feature-tag">24/7 उपलब्ध</span>
                    </div>
                    <button class="book-btn" onclick="bookService('होम विजिट', '1000')">अपॉइंटमेंट बुक करें</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Appointment Section -->
    <section class="appointment" id="appointment">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">अपॉइंटमेंट बुक करें</h2>
                <p class="section-subtitle">
                    आसान ऑनलाइन अपॉइंटमेंट बुकिंग - 24 घंटे पहले तक
                </p>
            </div>

            <div class="appointment-form">
                <form id="appointmentForm">
                    <div class="form-group">
                        <label class="form-label">मरीज का नाम *</label>
                        <input type="text" class="form-input" name="patientName" placeholder="मरीज का पूरा नाम" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">उम्र *</label>
                            <input type="number" class="form-input" name="age" placeholder="उम्र" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">लिंग *</label>
                            <select class="form-input" name="gender" required>
                                <option value="">लिंग चुनें</option>
                                <option value="male">पुरुष</option>
                                <option value="female">महिला</option>
                                <option value="other">अन्य</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">मोबाइल नंबर *</label>
                        <input type="tel" class="form-input" name="phone" placeholder="+91 98765 43210" required>
                    </div>

                    <div class="form-group">
                        <label class="form-label">सेवा का प्रकार *</label>
                        <select class="form-input" name="serviceType" required>
                            <option value="">सेवा चुनें</option>
                            <option value="general-checkup">जनरल चेकअप</option>
                            <option value="heart-checkup">हार्ट चेकअप</option>
                            <option value="lab-test">लैब टेस्ट</option>
                            <option value="vaccination">वैक्सीनेशन</option>
                            <option value="specialist">स्पेशलिस्ट कंसल्टेशन</option>
                            <option value="home-visit">होम विजिट</option>
                        </select>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">पसंदीदा दिनांक *</label>
                            <input type="date" class="form-input" name="appointmentDate" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">पसंदीदा समय *</label>
                            <select class="form-input" name="appointmentTime" required>
                                <option value="">समय चुनें</option>
                                <option value="09:00">सुबह 9:00</option>
                                <option value="10:00">सुबह 10:00</option>
                                <option value="11:00">सुबह 11:00</option>
                                <option value="12:00">दोपहर 12:00</option>
                                <option value="14:00">दोपहर 2:00</option>
                                <option value="15:00">दोपहर 3:00</option>
                                <option value="16:00">शाम 4:00</option>
                                <option value="17:00">शाम 5:00</option>
                                <option value="18:00">शाम 6:00</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">समस्या का विवरण</label>
                        <textarea class="form-input" name="symptoms" rows="4" placeholder="अपनी समस्या या लक्षणों के बारे में बताएं..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-calendar-check"></i>
                        अपॉइंटमेंट कन्फर्म करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer" id="contact">
        <div class="container">
            <div class="footer-content">
                <h3 class="footer-title">Dr. Sharma Clinic</h3>
                <p class="footer-text">
                    आपकी सेहत, हमारी प्राथमिकता - भरोसेमंद स्वास्थ्य सेवा
                </p>
                <div class="footer-contact">
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+91 98765 43210</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>123, हेल्थ स्ट्रीट, मुंबई - 400001</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <span>सुबह 9:00 - शाम 7:00 (सोमवार-शनिवार)</span>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Dr. Sharma Clinic. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते डॉक्टर! मैं अपॉइंटमेंट बुक करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Set minimum date to tomorrow
        document.addEventListener('DOMContentLoaded', function() {
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tomorrowString = tomorrow.toISOString().split('T')[0];

            const appointmentDate = document.querySelector('input[name="appointmentDate"]');
            if (appointmentDate) {
                appointmentDate.min = tomorrowString;
            }
        });

        // Book service function
        function bookService(serviceName, price) {
            const message = `🏥 *Dr. Sharma Clinic - अपॉइंटमेंट*

🩺 *सेवा:* ${serviceName}
💰 *फीस:* ₹${price} से शुरू

कृपया अपॉइंटमेंट कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Form submission
        document.getElementById('appointmentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.patientName || !data.age || !data.gender || !data.phone || !data.serviceType || !data.appointmentDate || !data.appointmentTime) {
                alert('कृपया सभी आवश्यक फील्ड भरें।');
                return;
            }

            const message = `🏥 *Dr. Sharma Clinic - अपॉइंटमेंट बुकिंग*

👤 *मरीज का नाम:* ${data.patientName}
🎂 *उम्र:* ${data.age} साल
👫 *लिंग:* ${data.gender}
📱 *मोबाइल:* ${data.phone}

🩺 *सेवा:* ${data.serviceType}
📅 *दिनांक:* ${data.appointmentDate}
🕐 *समय:* ${data.appointmentTime}

📝 *समस्या:* ${data.symptoms || 'नहीं बताई'}

कृपया इस अपॉइंटमेंट को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            alert('✅ आपकी अपॉइंटमेंट रिक्वेस्ट WhatsApp पर भेज दी गई है!');
            this.reset();
        });

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Mobile menu toggle
        document.querySelector('.menu-btn').addEventListener('click', function() {
            const navMenu = document.querySelector('.nav-menu');
            navMenu.style.display = navMenu.style.display === 'flex' ? 'none' : 'flex';
        });
    </script>
</body>
</html>
