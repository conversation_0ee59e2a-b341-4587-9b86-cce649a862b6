<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Royal Kitchen - Premium Fine Dining</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            background: #faf8f5;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #d4af37;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-family: 'Playfair Display', serif;
            font-size: 22px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .reservation-btn {
            background: linear-gradient(135deg, #d4af37 0%, #f7e98e 100%);
            color: #1a1a1a;
            padding: 8px 16px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: 600;
            transition: transform 0.2s ease;
        }

        .reservation-btn:hover {
            transform: translateY(-1px);
        }

        /* Desktop Header */
        .desktop-header {
            display: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(0,0,0,0.6), rgba(0,0,0,0.4)), url('https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            color: white;
            padding: 100px 0 60px;
            margin-top: 70px;
            text-align: center;
            position: relative;
            min-height: 70vh;
            display: flex;
            align-items: center;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(212, 175, 55, 0.2);
            border: 1px solid #d4af37;
            color: #d4af37;
            padding: 8px 20px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 24px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 36px;
            font-weight: 800;
            margin-bottom: 20px;
            line-height: 1.2;
            color: #d4af37;
        }

        .hero-subtitle {
            font-size: 16px;
            margin-bottom: 32px;
            opacity: 0.9;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
            align-items: center;
        }

        .cta-button {
            background: linear-gradient(135deg, #d4af37 0%, #f7e98e 100%);
            color: #1a1a1a;
            padding: 16px 32px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 700;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            min-width: 200px;
            justify-content: center;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
        }

        .cta-secondary {
            background: transparent;
            color: white;
            border: 2px solid #d4af37;
        }

        .cta-secondary:hover {
            background: rgba(212, 175, 55, 0.1);
        }

        /* Menu Categories */
        .menu-categories {
            padding: 60px 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 48px;
        }

        .section-badge {
            display: inline-block;
            background: #d4af37;
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 16px;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .section-title {
            font-family: 'Playfair Display', serif;
            font-size: 32px;
            font-weight: 700;
            color: #1a1a1a;
            margin-bottom: 16px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #666;
            max-width: 500px;
            margin: 0 auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }

        .category-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            border: 1px solid #f0f0f0;
        }

        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .category-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }

        .category-content {
            padding: 20px;
            text-align: center;
        }

        .category-title {
            font-family: 'Playfair Display', serif;
            font-size: 18px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .category-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }

        .category-count {
            font-size: 12px;
            color: #d4af37;
            font-weight: 600;
        }

        /* Featured Menu */
        .featured-menu {
            padding: 60px 0;
            background: #faf8f5;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .menu-item {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        }

        .menu-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.12);
        }

        .menu-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .menu-content {
            padding: 24px;
        }

        .menu-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 16px;
        }

        .menu-title {
            font-family: 'Playfair Display', serif;
            font-size: 20px;
            font-weight: 600;
            color: #1a1a1a;
            margin-bottom: 8px;
        }

        .menu-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #d4af37;
            font-weight: 600;
        }

        .menu-price {
            font-family: 'Playfair Display', serif;
            font-size: 24px;
            font-weight: 700;
            color: #d4af37;
        }

        .menu-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .menu-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .menu-tags {
            display: flex;
            gap: 8px;
        }

        .tag {
            background: #f8f8f8;
            color: #666;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .tag.premium {
            background: #d4af37;
            color: white;
        }

        .tag.chef-special {
            background: #1a1a1a;
            color: white;
        }

        .order-btn {
            background: linear-gradient(135deg, #d4af37 0%, #f7e98e 100%);
            color: #1a1a1a;
            border: none;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .order-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(212, 175, 55, 0.3);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e0e0e0;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            text-decoration: none;
            color: #666;
            transition: color 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: #d4af37;
        }

        .nav-item i {
            font-size: 18px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 600;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 999;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Desktop Styles */
        @media (min-width: 768px) {
            body {
                padding-bottom: 0;
            }

            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: block;
                background: rgba(26, 26, 26, 0.95);
                backdrop-filter: blur(20px);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                border-bottom: 1px solid #333;
            }

            .desktop-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 20px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .desktop-logo {
                font-family: 'Playfair Display', serif;
                font-size: 28px;
                font-weight: 700;
                color: #d4af37;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 12px;
            }

            .desktop-menu {
                display: flex;
                gap: 40px;
                list-style: none;
            }

            .desktop-menu a {
                text-decoration: none;
                color: white;
                font-weight: 500;
                transition: color 0.3s ease;
                font-size: 16px;
            }

            .desktop-menu a:hover {
                color: #d4af37;
            }

            .hero {
                margin-top: 90px;
                padding: 120px 0 80px;
                min-height: 80vh;
            }

            .hero-title {
                font-size: 56px;
            }

            .hero-subtitle {
                font-size: 20px;
            }

            .hero-buttons {
                flex-direction: row;
                justify-content: center;
            }

            .categories-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 24px;
            }

            .menu-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 32px;
            }

            .bottom-nav {
                display: none;
            }

            .whatsapp-chat {
                bottom: 20px;
            }
        }

        @media (min-width: 1024px) {
            .menu-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-crown"></i>
                Royal Kitchen
            </div>
            <a href="#reservation" class="reservation-btn">
                <i class="fas fa-calendar"></i>
                रिज़र्वेशन
            </a>
        </div>
    </header>

    <!-- Desktop Header -->
    <header class="desktop-header">
        <nav class="desktop-nav">
            <a href="#" class="desktop-logo">
                <i class="fas fa-crown"></i>
                Royal Kitchen
            </a>
            <ul class="desktop-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#menu">मेन्यू</a></li>
                <li><a href="#chef">शेफ</a></li>
                <li><a href="#reservation">रिज़र्वेशन</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-award"></i>
                    Michelin Star Experience
                </div>
                <h1 class="hero-title">
                    Royal Dining<br>
                    Experience
                </h1>
                <p class="hero-subtitle">
                    शाही खाने का अनुभव। बेहतरीन स्वाद, शानदार माहौल, 
                    और यादगार पलों के लिए आपका स्वागत है।
                </p>
                <div class="hero-buttons">
                    <a href="#menu" class="cta-button">
                        <i class="fas fa-utensils"></i>
                        मेन्यू देखें
                    </a>
                    <a href="#reservation" class="cta-button cta-secondary">
                        <i class="fas fa-calendar-check"></i>
                        टेबल बुक करें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Menu Categories -->
    <section class="menu-categories">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Signature Collection</div>
                <h2 class="section-title">हमारे विशेष व्यंजन</h2>
                <p class="section-subtitle">
                    शेफ की खास रेसिपी से तैयार किए गए प्रीमियम व्यंजन
                </p>
            </div>

            <div class="categories-grid">
                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1585937421612-70a008356fbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Royal Curries" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">रॉयल करी</h3>
                        <p class="category-description">महाराजाओं के जमाने की खास करी</p>
                        <span class="category-count">8 व्यंजन</span>
                    </div>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Tandoor Specials" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">तंदूर स्पेशल</h3>
                        <p class="category-description">मिट्टी के तंदूर में पके व्यंजन</p>
                        <span class="category-count">12 व्यंजन</span>
                    </div>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1563379091339-03246963d96c?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Royal Biryani" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">शाही बिरयानी</h3>
                        <p class="category-description">हैदराबादी और लखनवी स्टाइल</p>
                        <span class="category-count">6 व्यंजन</span>
                    </div>
                </a>

                <a href="#menu" class="category-card">
                    <img src="https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="Royal Desserts" class="category-image">
                    <div class="category-content">
                        <h3 class="category-title">रॉयल डेजर्ट</h3>
                        <p class="category-description">पारंपरिक मिठाइयां और हलवे</p>
                        <span class="category-count">10 व्यंजन</span>
                    </div>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Menu -->
    <section class="featured-menu" id="menu">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Chef's Special</div>
                <h2 class="section-title">शेफ की पसंद</h2>
                <p class="section-subtitle">
                    हमारे मास्टर शेफ की सबसे खास रेसिपी
                </p>
            </div>

            <div class="menu-grid">
                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1585937421612-70a008356fbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Royal Butter Chicken" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">रॉयल बटर चिकन</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>5.0</span>
                                </div>
                            </div>
                            <div class="menu-price">₹650</div>
                        </div>
                        <p class="menu-description">
                            केसर और मक्खन की समृद्ध ग्रेवी में पका हुआ टेंडर चिकन, बादाम और काजू के साथ
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag chef-special">शेफ स्पेशल</span>
                                <span class="tag premium">प्रीमियम</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('रॉयल बटर चिकन', 650)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1563379091339-03246963d96c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Hyderabadi Biryani" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">हैदराबादी बिरयानी</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                            </div>
                            <div class="menu-price">₹750</div>
                        </div>
                        <p class="menu-description">
                            दम पुख्त स्टाइल में पकी बासमती चावल और मटन की बिरयानी, रायता और शोरबा के साथ
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag chef-special">शेफ स्पेशल</span>
                                <span class="tag">दम पुख्त</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('हैदराबादी बिरयानी', 750)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Tandoori Platter" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">रॉयल तंदूरी प्लेटर</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <div class="menu-price">₹950</div>
                        </div>
                        <p class="menu-description">
                            चिकन टिक्का, सीख कबाब, तंदूरी चिकन और फिश टिक्का का मिक्स प्लेटर
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag premium">प्रीमियम</span>
                                <span class="tag">मिक्स प्लेटर</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('रॉयल तंदूरी प्लेटर', 950)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1567620905732-2d1ec7ab7445?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Dal Makhani" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">दाल मखनी रॉयल</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.7</span>
                                </div>
                            </div>
                            <div class="menu-price">₹450</div>
                        </div>
                        <p class="menu-description">
                            18 घंटे धीमी आंच पर पकी काली दाल, मक्खन और क्रीम के साथ
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag">वेजिटेरियन</span>
                                <span class="tag">18 घंटे पकी</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('दाल मखनी रॉयल', 450)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Royal Kulfi" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">शाही कुल्फी फालूदा</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                            </div>
                            <div class="menu-price">₹250</div>
                        </div>
                        <p class="menu-description">
                            केसर और पिस्ता से बनी घर की कुल्फी, फालूदा और गुलाब शर्बत के साथ
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag">डेजर्ट</span>
                                <span class="tag">केसर स्पेशल</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('शाही कुल्फी फालूदा', 250)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="menu-item">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Paneer Makhani" class="menu-image">
                    <div class="menu-content">
                        <div class="menu-header">
                            <div>
                                <h3 class="menu-title">पनीर मखनी रॉयल</h3>
                                <div class="menu-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.6</span>
                                </div>
                            </div>
                            <div class="menu-price">₹550</div>
                        </div>
                        <p class="menu-description">
                            घर का बना पनीर, टमाटर और मक्खन की रिच ग्रेवी में, काजू पेस्ट के साथ
                        </p>
                        <div class="menu-footer">
                            <div class="menu-tags">
                                <span class="tag">वेजिटेरियन</span>
                                <span class="tag premium">प्रीमियम</span>
                            </div>
                            <button class="order-btn" onclick="orderDish('पनीर मखनी रॉयल', 550)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>होम</span>
            </a>
            <a href="#menu" class="nav-item">
                <i class="fas fa-utensils"></i>
                <span>मेन्यू</span>
            </a>
            <a href="#chef" class="nav-item">
                <i class="fas fa-chef-hat"></i>
                <span>शेफ</span>
            </a>
            <a href="#reservation" class="nav-item">
                <i class="fas fa-calendar-check"></i>
                <span>रिज़र्वेशन</span>
            </a>
            <a href="#contact" class="nav-item">
                <i class="fas fa-phone"></i>
                <span>संपर्क</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं Royal Kitchen में टेबल बुक करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Order dish function
        function orderDish(dishName, price) {
            const message = `👑 *Royal Kitchen - प्रीमियम ऑर्डर*

🍽️ *व्यंजन:* ${dishName}
💰 *प्राइस:* ₹${price}

कृपया इस ऑर्डर को कन्फर्म करें और टेबल रिज़र्वेशन की जानकारी दें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showElegantNotification(`${dishName} ऑर्डर में जोड़ा गया! 👑`);
        }

        // Show elegant notification
        function showElegantNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 90px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
                color: #d4af37;
                padding: 16px 32px;
                border-radius: 30px;
                font-weight: 600;
                font-size: 14px;
                z-index: 2000;
                box-shadow: 0 8px 30px rgba(0,0,0,0.3);
                border: 1px solid #d4af37;
                animation: elegantSlide 0.4s ease;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes elegantSlide {
                    from {
                        transform: translateX(-50%) translateY(-30px);
                        opacity: 0;
                        scale: 0.9;
                    }
                    to {
                        transform: translateX(-50%) translateY(0);
                        opacity: 1;
                        scale: 1;
                    }
                }
            `;
            document.head.appendChild(style);

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'elegantSlide 0.4s ease reverse';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 400);
                }
            }, 3500);
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to section
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add elegant hover effects
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>
