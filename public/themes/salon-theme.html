<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Elegance Salon – Modern Beauty E-Commerce</title>
    <meta name="description" content="Book premium salon services, explore curated packages, and discover beauty inspiration at Elegance Salon. Modern, elegant, and immersive e-commerce experience.">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;600;800&family=Playfair+Display:wght@700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #e8b7c3;
            --secondary: #d89ca9;
            --accent: #ffd700;
            --dark: #2d2327;
            --glass: rgba(255,255,255,0.25);
            --glass-border: rgba(255,255,255,0.18);
            --neon: #ffb6b9;
            --gradient: linear-gradient(90deg, #e8b7c3 0%, #ffd700 100%);
            --card-bg: linear-gradient(135deg, rgba(232,183,195,0.18) 0%, rgba(255,255,255,0.85) 100%);
        }
        html, body {
            max-width: 100vw;
            overflow-x: hidden;
        }
        html {
            scroll-behavior: smooth;
        }
        body {
            background: linear-gradient(120deg, #f9f5f6 60%, #fffbe6 100%);
            color: var(--dark);
            font-family: 'Montserrat', sans-serif;
            min-height: 100vh;
            overflow-x: hidden;
        }
        /* Scroll Progress Bar */
        #scroll-progress {
            position: fixed;
            top: 0; left: 0;
            width: 0%;
            height: 4px;
            background: var(--gradient);
            z-index: 2000;
            transition: width 0.2s;
        }
        /* Header UI Fix */
        header {
            position: sticky;
            top: 0;
            width: 100%;
            z-index: 1000;
            background: rgba(255,255,255,0.85);
            backdrop-filter: blur(16px);
            border-bottom: 2px solid var(--primary);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 4vw 1.5rem 4vw;
            box-shadow: 0 4px 32px 0 rgba(232,183,195,0.13);
            transition: background 0.3s;
        }
        .logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-family: 'Playfair Display', serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dark);
            letter-spacing: 1.5px;
        }
        .logo svg {
            width: 44px; height: 44px;
        }
        nav {
            display: flex;
            gap: 3rem;
        }
        nav a {
            color: var(--dark);
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
            position: relative;
            transition: color 0.2s;
            padding: 0.3rem 0.7rem;
            border-radius: 8px;
        }
        nav a:hover, nav a:focus {
            color: var(--secondary);
            background: var(--primary);
        }
        .header-icons {
            display: flex;
            gap: 1.5rem;
            align-items: center;
        }
        .icon-btn {
            background: var(--glass);
            border: 1.5px solid var(--glass-border);
            border-radius: 50%;
            width: 48px; height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            color: var(--secondary);
            box-shadow: 0 2px 8px rgba(232,183,195,0.10);
            cursor: pointer;
            transition: box-shadow 0.2s, color 0.2s, background 0.2s;
            position: relative;
        }
        .icon-btn:hover, .icon-btn:focus {
            color: var(--accent);
            background: var(--glass-border);
            box-shadow: 0 4px 16px var(--neon);
        }
        .cart-count {
            position: absolute;
            top: 6px; right: 6px;
            background: var(--neon);
            color: #fff;
            border-radius: 50%;
            font-size: 0.8rem;
            font-weight: bold;
            padding: 2px 7px;
            box-shadow: 0 1px 4px var(--neon);
        }
        /* Mobile Menu */
        .menu-toggle {
            display: none;
            flex-direction: column;
            gap: 5px;
            cursor: pointer;
        }
        .menu-toggle span {
            width: 28px; height: 3px;
            background: var(--dark);
            border-radius: 2px;
            transition: all 0.3s;
        }
        @media (max-width: 900px) {
            nav { display: none; }
            .menu-toggle { display: flex; }
            .mobile-nav {
                display: flex;
                flex-direction: column;
                position: fixed;
                top: 0; right: 0;
                width: 70vw; max-width: 340px;
                height: 100vh;
                background: var(--glass);
                backdrop-filter: blur(18px);
                z-index: 2001;
                box-shadow: -4px 0 24px rgba(232,183,195,0.18);
                padding: 2.5rem 2rem;
                transform: translateX(100%);
                transition: transform 0.4s cubic-bezier(.77,0,.18,1);
            }
            .mobile-nav.active { transform: translateX(0); }
            .mobile-nav a {
                margin-bottom: 1.5rem;
                font-size: 1.2rem;
                color: var(--dark);
                font-weight: 600;
            }
        }
        /* Hero Section */
        .hero {
            position: relative;
            min-height: 70vh;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            background: #fff;
            overflow: hidden;
            border-radius: 0 0 48px 48px;
            margin-bottom: 2.5rem;
        }
        .hero-bg {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            z-index: 0;
            overflow: hidden;
        }
        .hero-bg video {
            width: 100%; height: 100%; object-fit: cover;
            filter: brightness(0.85) saturate(1.1);
        }
        .hero-content {
            position: relative;
            z-index: 2;
            padding: 4vw 6vw;
            max-width: 600px;
        }
        .hero-title {
            font-family: 'Playfair Display', serif;
            font-size: 3.2rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 1.2rem;
            letter-spacing: 1px;
            text-shadow: 0 4px 24px #fffbe6;
        }
        .hero-desc {
            font-size: 1.25rem;
            color: #4a363d;
            margin-bottom: 2.2rem;
            line-height: 1.7;
        }
        .glass-btn {
            background: var(--glass);
            border: 2px solid var(--accent);
            color: var(--dark);
            font-weight: 700;
            font-size: 1.2rem;
            padding: 1rem 2.5rem;
            border-radius: 32px;
            box-shadow: 0 6px 32px var(--glass-border);
            backdrop-filter: blur(8px);
            cursor: pointer;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s, border 0.2s;
            position: relative;
            overflow: hidden;
        }
        .glass-btn::before {
            content: '';
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            background: linear-gradient(90deg, var(--accent) 0%, transparent 100%);
            opacity: 0.12;
            z-index: 0;
        }
        .glass-btn:hover {
            background: var(--accent);
            color: #fff;
            box-shadow: 0 8px 32px var(--neon);
            border-color: var(--secondary);
        }
        /* Floating Loyalty Badge */
        .loyalty-badge {
            position: fixed;
            bottom: 32px; left: 32px;
            background: var(--gradient);
            color: #fff;
            font-weight: 700;
            font-size: 1.1rem;
            padding: 0.7rem 1.5rem;
            border-radius: 32px 32px 32px 8px;
            box-shadow: 0 4px 24px var(--neon);
            display: flex;
            align-items: center;
            gap: 0.7rem;
            z-index: 2002;
            animation: floatBadge 2.5s infinite ease-in-out alternate;
        }
        .loyalty-badge i {
            font-size: 1.3rem;
        }
        @keyframes floatBadge {
            0% { transform: translateY(0); }
            100% { transform: translateY(-10px); }
        }
        /* Asymmetrical Service Grid */
        .services-section {
            padding: 3rem 6vw 2rem 6vw;
            background: #fffbe6;
            border-radius: 48px 48px 0 0;
            margin-bottom: 2.5rem;
            position: relative;
        }
        .services-title {
            font-family: 'Playfair Display', serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 2.2rem;
            text-align: left;
        }
        .service-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            gap: 2.2rem;
            align-items: stretch;
        }
        .service-card {
            background: var(--card-bg);
            border: 1.5px solid var(--primary);
            border-radius: 24px;
            box-shadow: 0 8px 32px rgba(232,183,195,0.13);
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            overflow: hidden;
            transition: box-shadow 0.2s, transform 0.2s;
        }
        .service-card:hover {
            box-shadow: 0 12px 40px var(--neon);
            transform: translateY(-8px) scale(1.04);
        }
        .service-img {
            width: 100%;
            height: 180px;
            object-fit: cover;
            border-top-left-radius: 24px;
            border-top-right-radius: 24px;
            border-bottom: 1.5px solid var(--primary);
        }
        .service-info {
            padding: 1.5rem 1.2rem 1.2rem 1.2rem;
            width: 100%;
        }
        .service-icon {
            font-size: 2.2rem;
            color: var(--accent);
            margin-bottom: 1rem;
            filter: drop-shadow(0 2px 8px var(--accent));
        }
        .service-name {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 0.7rem;
        }
        .service-desc {
            font-size: 1rem;
            color: #4a363d;
            margin-bottom: 1.2rem;
        }
        .service-price {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--secondary);
            margin-bottom: 1.2rem;
        }
        .service-card .glass-btn {
            align-self: flex-end;
            margin-top: auto;
        }
        /* Testimonials Carousel */
        .testimonials-section {
            background: linear-gradient(120deg, #fffbe6 60%, #f9f5f6 100%);
            padding: 3rem 6vw 2rem 6vw;
            position: relative;
        }
        .testimonials-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 2rem;
            text-align: left;
        }
        .carousel {
            display: flex;
            align-items: center;
            gap: 2rem;
            overflow-x: auto;
            scroll-snap-type: x mandatory;
            padding-bottom: 1rem;
        }
        .testimonial {
            min-width: 320px;
            max-width: 340px;
            background: var(--glass);
            border: 1.5px solid var(--glass-border);
            border-radius: 24px;
            box-shadow: 0 4px 24px rgba(232,183,195,0.10);
            padding: 2rem 1.5rem;
            scroll-snap-align: start;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            position: relative;
            margin-right: 1rem;
        }
        .testimonial .quote {
            font-size: 1.1rem;
            color: #4a363d;
            margin-bottom: 1.2rem;
            font-style: italic;
        }
        .testimonial .client {
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }
        .testimonial .client-img {
            width: 44px; height: 44px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--accent);
        }
        .testimonial .client-name {
            font-weight: 700;
            color: var(--dark);
        }
        /* Portfolio Gallery */
        .portfolio-section {
            padding: 3rem 6vw 2rem 6vw;
            background: #fff;
            position: relative;
        }
        .portfolio-title {
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            color: var(--dark);
            margin-bottom: 2rem;
            text-align: left;
        }
        .gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
        }
        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 18px;
            cursor: pointer;
            box-shadow: 0 2px 12px rgba(232,183,195,0.10);
            transition: box-shadow 0.2s, transform 0.2s;
        }
        .gallery-item:hover {
            box-shadow: 0 8px 32px var(--neon);
            transform: scale(1.04);
        }
        .gallery-item img {
            width: 100%; height: 180px; object-fit: cover;
            display: block;
            transition: filter 0.2s;
        }
        .gallery-item:hover img {
            filter: brightness(0.85) blur(1px);
        }
        .gallery-item .overlay {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            background: linear-gradient(120deg, var(--glass) 60%, var(--accent) 100%);
            opacity: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 1.2rem;
            font-weight: 700;
            transition: opacity 0.2s;
        }
        .gallery-item:hover .overlay {
            opacity: 1;
        }
        /* Lightbox */
        .lightbox {
            display: none;
            position: fixed;
            top: 0; left: 0; width: 100vw; height: 100vh;
            background: rgba(0,0,0,0.85);
            align-items: center;
            justify-content: center;
            z-index: 3000;
        }
        .lightbox.active {
            display: flex;
        }
        .lightbox img {
            max-width: 90vw;
            max-height: 80vh;
            border-radius: 18px;
            box-shadow: 0 8px 32px var(--neon);
        }
        .lightbox .close {
            position: absolute;
            top: 32px; right: 48px;
            font-size: 2.2rem;
            color: #fff;
            cursor: pointer;
            z-index: 1;
        }
        /* Minimalist Footer */
        footer {
            background: var(--dark);
            color: #fffbe6;
            padding: 2rem 6vw 1.2rem 6vw;
            text-align: center;
            font-size: 1rem;
            border-radius: 32px 32px 0 0;
            margin-top: 3rem;
        }
        .footer-social {
            margin-top: 1rem;
            display: flex;
            justify-content: center;
            gap: 1.5rem;
        }
        .footer-social a {
            color: var(--accent);
            font-size: 1.3rem;
            transition: color 0.2s;
        }
        .footer-social a:hover {
            color: var(--secondary);
        }
        /* Responsive */
        @media (max-width: 700px) {
            .hero-content { padding: 2.5vw 4vw; }
            .services-section, .testimonials-section, .portfolio-section, footer { padding: 2rem 3vw; }
            .hero-title { font-size: 2.1rem; }
        }
        @media (max-width: 500px) {
            .loyalty-badge { left: 8px; bottom: 8px; font-size: 0.95rem; padding: 0.5rem 1rem; }
        }
    </style>
</head>
<body>
    <div id="scroll-progress"></div>
    <!-- Floating Loyalty Badge -->
    <div class="loyalty-badge" aria-label="Loyalty Program">
        <i class="fas fa-gem"></i> Loyalty+ Member
    </div>
    <!-- Sticky Header -->
    <header>
        <div class="logo">
            <!-- Custom SVG: Beauty Icon -->
            <svg viewBox="0 0 40 40" fill="none"><ellipse cx="20" cy="20" rx="18" ry="18" fill="#e8b7c3"/><path d="M12 28c2-6 8-6 10 0" stroke="#ffd700" stroke-width="2" stroke-linecap="round"/><ellipse cx="16" cy="17" rx="1.5" ry="2" fill="#fff"/><ellipse cx="24" cy="17" rx="1.5" ry="2" fill="#fff"/></svg>
            Elegance
        </div>
        <nav aria-label="Main Navigation">
            <a href="#home">Home</a>
            <a href="#services">Services</a>
            <a href="#testimonials">Testimonials</a>
            <a href="#portfolio">Gallery</a>
            <a href="#contact">Contact</a>
        </nav>
        <div class="header-icons">
            <button class="icon-btn" aria-label="Search"><i class="fas fa-search"></i></button>
            <button class="icon-btn" aria-label="Cart" id="cartBtn"><i class="fas fa-shopping-cart"></i><span class="cart-count" id="cartCount">0</span></button>
            <div class="menu-toggle" id="menuToggle" aria-label="Open Menu" tabindex="0">
                <span></span><span></span><span></span>
            </div>
        </div>
    </header>
    <div class="mobile-nav" id="mobileNav" aria-label="Mobile Navigation">
        <button class="close-mobile-nav" id="closeMobileNav" aria-label="Close Menu">&times;</button>
        <a href="#home">Home</a>
        <a href="#services">Services</a>
        <a href="#testimonials">Testimonials</a>
        <a href="#portfolio">Gallery</a>
        <a href="#contact">Contact</a>
    </div>
    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="hero-bg">
            <video src="https://www.w3schools.com/howto/rain.mp4" autoplay loop muted playsinline aria-hidden="true"></video>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">Beauty, Reimagined</h1>
            <p class="hero-desc">Step into a world of elegance and transformation. Book premium salon services, discover curated packages, and let your beauty shine with our expert stylists.</p>
            <button class="glass-btn" id="bookNowBtn">Book Your Experience</button>
        </div>
    </section>
    <!-- Services Section -->
    <section class="services-section" id="services">
        <h2 class="services-title">Curated Service Packages</h2>
        <div class="service-grid">
            <div class="service-card">
                <img class="service-img" src="https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=crop&w=600&q=80" alt="Signature Haircut & Styling">
                <div class="service-info">
                    <span class="service-icon"><i class="fas fa-cut"></i></span>
                    <div class="service-name">Signature Haircut & Styling</div>
                    <div class="service-desc">Personalized cuts and styling for every occasion, using premium products and techniques.</div>
                    <div class="service-price">₹1,200</div>
                    <button class="glass-btn add-to-cart">Book Now</button>
                </div>
            </div>
            <div class="service-card">
                <img class="service-img" src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=600&q=80" alt="Luxury Facial Glow">
                <div class="service-info">
                    <span class="service-icon"><i class="fas fa-spa"></i></span>
                    <div class="service-name">Luxury Facial Glow</div>
                    <div class="service-desc">Rejuvenate your skin with our advanced facial treatments for a radiant, healthy glow.</div>
                    <div class="service-price">₹1,500</div>
                    <button class="glass-btn add-to-cart">Book Now</button>
                </div>
            </div>
            <div class="service-card">
                <img class="service-img" src="https://images.unsplash.com/photo-1512436991641-6745cdb1723f?auto=format&fit=crop&w=600&q=80" alt="Artisan Nail Art">
                <div class="service-info">
                    <span class="service-icon"><i class="fas fa-paint-brush"></i></span>
                    <div class="service-name">Artisan Nail Art</div>
                    <div class="service-desc">Creative nail designs and care, from classic to avant-garde, by our nail artists.</div>
                    <div class="service-price">₹900</div>
                    <button class="glass-btn add-to-cart">Book Now</button>
                </div>
            </div>
            <div class="service-card">
                <img class="service-img" src="https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=crop&w=600&q=80" alt="Bridal Makeover">
                <div class="service-info">
                    <span class="service-icon"><i class="fas fa-magic"></i></span>
                    <div class="service-name">Bridal Makeover</div>
                    <div class="service-desc">Stunning bridal looks tailored to your style, for a truly unforgettable day.</div>
                    <div class="service-price">₹5,000</div>
                    <button class="glass-btn add-to-cart">Book Now</button>
                </div>
            </div>
            <div class="service-card">
                <img class="service-img" src="https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=crop&w=600&q=80" alt="Hair Spa Ritual">
                <div class="service-info">
                    <span class="service-icon"><i class="fas fa-water"></i></span>
                    <div class="service-name">Hair Spa Ritual</div>
                    <div class="service-desc">Deep nourishment and relaxation for your hair and scalp, restoring natural shine.</div>
                    <div class="service-price">₹1,800</div>
                    <button class="glass-btn add-to-cart">Book Now</button>
                </div>
            </div>
        </div>
    </section>
    <!-- Testimonials Section -->
    <section class="testimonials-section" id="testimonials">
        <h2 class="testimonials-title">What Our Clients Say</h2>
        <div class="carousel" id="testimonialCarousel">
            <div class="testimonial">
                <div class="quote">“Absolutely loved my new look! The stylists are so talented and friendly.”</div>
                <div class="client">
                    <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="Client" class="client-img">
                    <span class="client-name">Priya S.</span>
                </div>
            </div>
            <div class="testimonial">
                <div class="quote">“The facial left my skin glowing for days. Highly recommend their packages!”</div>
                <div class="client">
                    <img src="https://randomuser.me/api/portraits/women/45.jpg" alt="Client" class="client-img">
                    <span class="client-name">Aarti M.</span>
                </div>
            </div>
            <div class="testimonial">
                <div class="quote">“Best bridal makeover experience. Felt like a queen on my big day!”</div>
                <div class="client">
                    <img src="https://randomuser.me/api/portraits/women/12.jpg" alt="Client" class="client-img">
                    <span class="client-name">Neha K.</span>
                </div>
            </div>
            <div class="testimonial">
                <div class="quote">“The nail art is so creative and lasts long. My go-to salon!”</div>
                <div class="client">
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" alt="Client" class="client-img">
                    <span class="client-name">Ritika D.</span>
                </div>
            </div>
        </div>
    </section>
    <!-- Portfolio Gallery Section -->
    <section class="portfolio-section" id="portfolio">
        <h2 class="portfolio-title">Our Portfolio</h2>
        <div class="gallery" id="gallery">
            <div class="gallery-item" tabindex="0" data-img="https://images.unsplash.com/photo-1630447921919-0b6e769e3e37?auto=format&fit=crop&w=600&q=80">
                <img src="https://images.unsplash.com/photo-1630447921919-0b6e769e3e37?auto=format&fit=crop&w=400&q=80" alt="Hair Styling">
                <div class="overlay">View</div>
            </div>
            <div class="gallery-item" tabindex="0" data-img="https://images.unsplash.com/photo-1570194065650-d99fb4cb6a23?auto=format&fit=crop&w=600&q=80">
                <img src="https://images.unsplash.com/photo-1570194065650-d99fb4cb6a23?auto=format&fit=crop&w=400&q=80" alt="Facial Treatment">
                <div class="overlay">View</div>
            </div>
            <div class="gallery-item" tabindex="0" data-img="https://images.unsplash.com/photo-1634486952081-1e8b3b3f53be?auto=format&fit=crop&w=600&q=80">
                <img src="https://images.unsplash.com/photo-1634486952081-1e8b3b3f53be?auto=format&fit=crop&w=400&q=80" alt="Nail Art">
                <div class="overlay">View</div>
            </div>
            <div class="gallery-item" tabindex="0" data-img="https://images.unsplash.com/photo-1615288707159-5d1e9c2a1a87?auto=format&fit=crop&w=600&q=80">
                <img src="https://images.unsplash.com/photo-1615288707159-5d1e9c2a1a87?auto=format&fit=crop&w=400&q=80" alt="Bridal Makeup">
                <div class="overlay">View</div>
            </div>
            <div class="gallery-item" tabindex="0" data-img="https://images.unsplash.com/photo-1611747717795-0d3e9d5a351f?auto=format&fit=crop&w=600&q=80">
                <img src="https://images.unsplash.com/photo-1611747717795-0d3e9d5a351f?auto=format&fit=crop&w=400&q=80" alt="Hair Spa">
                <div class="overlay">View</div>
            </div>
        </div>
        <!-- Lightbox -->
        <div class="lightbox" id="lightbox" aria-modal="true" role="dialog">
            <span class="close" id="lightboxClose" aria-label="Close">&times;</span>
            <img src="" alt="Gallery Image" id="lightboxImg">
        </div>
    </section>
    <!-- Minimalist Footer -->
    <footer id="contact">
        <div>&copy; 2025 Elegance Salon. Beauty, redefined.</div>
        <div style="margin-top: 0.5rem;">Contact: <a href="mailto:<EMAIL>" style="color:var(--accent);text-decoration:underline;"><EMAIL></a> | +91 98765 43210</div>
        <div class="footer-social">
            <a href="#" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
            <a href="#" aria-label="Facebook"><i class="fab fa-facebook"></i></a>
            <a href="#" aria-label="Twitter"><i class="fab fa-twitter"></i></a>
            <a href="#" aria-label="WhatsApp"><i class="fab fa-whatsapp"></i></a>
        </div>
    </footer>
    <script>
    // Scroll Progress Bar
    window.addEventListener('scroll', function() {
        const scroll = document.documentElement.scrollTop || document.body.scrollTop;
        const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
        const scrolled = (scroll / height) * 100;
        document.getElementById('scroll-progress').style.width = scrolled + '%';
    });
    // Mobile Menu Toggle
    const menuToggle = document.getElementById('menuToggle');
    const mobileNav = document.getElementById('mobileNav');
    menuToggle && menuToggle.addEventListener('click', () => {
        mobileNav.classList.toggle('active');
    });
    // Close mobile nav on link click
    document.querySelectorAll('.mobile-nav a').forEach(link => {
        link.addEventListener('click', () => mobileNav.classList.remove('active'));
    });
    // Book Now scroll to services
    document.getElementById('bookNowBtn').addEventListener('click', () => {
        document.getElementById('services').scrollIntoView({behavior:'smooth'});
    });
    // Cart functionality (demo)
    let cartCount = 0;
    document.querySelectorAll('.add-to-cart').forEach(btn => {
        btn.addEventListener('click', function() {
            cartCount++;
            document.getElementById('cartCount').textContent = cartCount;
            this.textContent = 'Added!';
            this.disabled = true;
            setTimeout(() => {
                this.textContent = 'Book Now';
                this.disabled = false;
            }, 1200);
        });
    });
    // Testimonials carousel (auto-scroll)
    const carousel = document.getElementById('testimonialCarousel');
    let scrollAmount = 0;
    setInterval(() => {
        if (carousel.scrollWidth - carousel.clientWidth - scrollAmount < 10) {
            scrollAmount = 0;
        } else {
            scrollAmount += 340;
        }
        carousel.scrollTo({left: scrollAmount, behavior: 'smooth'});
    }, 3500);
    // Portfolio Lightbox
    const gallery = document.getElementById('gallery');
    const lightbox = document.getElementById('lightbox');
    const lightboxImg = document.getElementById('lightboxImg');
    const lightboxClose = document.getElementById('lightboxClose');
    gallery.querySelectorAll('.gallery-item').forEach(item => {
        item.addEventListener('click', function() {
            lightboxImg.src = this.getAttribute('data-img');
            lightbox.classList.add('active');
        });
        item.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                lightboxImg.src = this.getAttribute('data-img');
                lightbox.classList.add('active');
            }
        });
    });
    lightboxClose.addEventListener('click', () => {
        lightbox.classList.remove('active');
        lightboxImg.src = '';
    });
    lightbox.addEventListener('click', (e) => {
        if (e.target === lightbox) {
            lightbox.classList.remove('active');
            lightboxImg.src = '';
        }
    });
    // Accessibility: close lightbox with Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            lightbox.classList.remove('active');
            lightboxImg.src = '';
        }
    });
    // Burger menu close feature
    const closeMobileNav = document.getElementById('closeMobileNav');
    if (closeMobileNav) {
        closeMobileNav.addEventListener('click', () => {
            mobileNav.classList.remove('active');
        });
    }
    // Also close menu when clicking outside the menu
    document.addEventListener('click', function(e) {
        if (mobileNav.classList.contains('active') && !mobileNav.contains(e.target) && e.target !== menuToggle) {
            mobileNav.classList.remove('active');
        }
    });
    </script>
</body>
</html>
