<!-- Redesigned Kirana Store Theme: Vibrant, Playful, Indian Market Inspired -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apna <PERSON><PERSON> - Sab <PERSON>ch <PERSON>!</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baloo+2:wght@600;800&family=Inter:wght@400;600;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
:root {
    --primary: #ff6f00;
    --secondary: #00897b;
    --accent: #ffd600;
    --bg: #fff8e1;
    --card: #fff;
    --text: #2d2d2d;
    --radius: 22px;
    --shadow: 0 6px 24px rgba(255,111,0,0.08);
    --nav-bg: #fffde7;
}
body {
    background: var(--bg);
    color: var(--text);
    font-family: 'Inter', 'Baloo 2', sans-serif;
    margin: 0;
    overflow-x: hidden;
}
.kirana-header {
    background: var(--primary);
    color: #fff;
    position: sticky;
    top: 0;
    z-index: 1000;
    box-shadow: var(--shadow);
    border-bottom-left-radius: 0 0 32px 32px;
    border-bottom-right-radius: 0 0 32px 32px;
}
.kirana-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.1rem 1.2rem 0.7rem 1.2rem;
}
.kirana-logo {
    font-family: 'Baloo 2', cursive;
    font-weight: 800;
    font-size: 2rem;
    color: #fff;
    letter-spacing: 1px;
    text-shadow: 0 2px 8px #ffb30044;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.kirana-logo .logo-basket {
    font-size: 1.5rem;
    color: var(--accent);
    margin-right: 0.2rem;
}
.header-actions {
    display: flex;
    align-items: center;
    gap: 1.2rem;
}
.cart-btn {
    background: var(--accent);
    color: var(--primary);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    position: relative;
    box-shadow: 0 4px 16px #ffd60033;
    cursor: pointer;
    transition: box-shadow 0.2s, background 0.2s;
}
.cart-btn:hover { background: #fffde7; }
.cart-count {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--secondary);
    color: #fff;
    border-radius: 50%;
    font-size: 0.9rem;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    box-shadow: 0 2px 8px #00897b33;
}
.menu-toggle {
    background: none;
    border: none;
    font-size: 2rem;
    color: #fff;
    display: none;
    cursor: pointer;
}
/* Bottom Nav for Mobile */
.kirana-bottom-nav {
    display: none;
}
@media (max-width: 800px) {
    .menu-toggle { display: block; }
    .kirana-bottom-nav {
        display: flex;
        position: fixed;
        bottom: 0; left: 0; right: 0;
        background: var(--nav-bg);
        box-shadow: 0 -2px 16px #ffb30022;
        z-index: 1200;
        justify-content: space-around;
        align-items: center;
        padding: 0.5rem 0;
        border-top-left-radius: 18px;
        border-top-right-radius: 18px;
    }
    .kirana-bottom-nav a {
        color: var(--primary);
        font-size: 1.3rem;
        text-decoration: none;
        display: flex;
        flex-direction: column;
        align-items: center;
        font-weight: 700;
        padding: 0.2rem 0.5rem;
        transition: color 0.2s;
    }
    .kirana-bottom-nav a.active, .kirana-bottom-nav a:hover {
        color: var(--secondary);
    }
}
/* Sidebar Nav for Desktop */
.kirana-sidebar {
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 80px;
    left: 0;
    background: var(--nav-bg);
    width: 70px;
    height: 70vh;
    border-radius: 0 24px 24px 0;
    box-shadow: 2px 0 16px #ffd60022;
    z-index: 900;
    align-items: center;
    gap: 1.5rem;
    padding: 1.2rem 0;
}
.kirana-sidebar a {
    color: var(--primary);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    text-decoration: none;
    transition: color 0.2s;
}
.kirana-sidebar a.active, .kirana-sidebar a:hover {
    color: var(--secondary);
}
@media (max-width: 800px) {
    .kirana-sidebar { display: none; }
}

/* Hero Section */
.kirana-hero {
    background: linear-gradient(120deg, #fffde7 60%, #ffd600 100%);
    text-align: center;
    padding: 2.5rem 1rem 1.5rem 1rem;
    border-bottom-left-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}
.kirana-hero-svg {
    position: absolute;
    top: -30px;
    right: -40px;
    width: 180px;
    opacity: 0.18;
    z-index: 0;
}
.kirana-hero h1 {
    font-size: 2.3rem;
    font-family: 'Baloo 2', cursive;
    font-weight: 800;
    margin-bottom: 0.5rem;
    color: var(--primary);
    z-index: 1;
    position: relative;
}
.kirana-gradient {
    background: linear-gradient(90deg, var(--secondary), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
}
.hero-sub {
    color: var(--text);
    font-size: 1.15rem;
    margin-bottom: 1.2rem;
    z-index: 1;
    position: relative;
}
.hero-search {
    display: flex;
    justify-content: center;
    margin-bottom: 1.2rem;
    gap: 0.5rem;
    z-index: 1;
    position: relative;
}
.hero-search input {
    border: 2px solid #ffe082;
    border-radius: 20px;
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    width: 180px;
    transition: border 0.2s;
    background: #fffde7;
}
.hero-search input:focus {
    outline: none;
    border-color: var(--secondary);
}
.hero-search button {
    background: var(--secondary);
    color: #fff;
    border: none;
    border-radius: 20px;
    padding: 0.7rem 1.2rem;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s;
}
.hero-search button:hover {
    background: var(--primary);
}
.hero-categories {
    display: flex;
    gap: 0.7rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 0.5rem;
    z-index: 1;
    position: relative;
}
.hero-cat {
    background: var(--card);
    color: var(--primary);
    border-radius: 20px;
    padding: 0.5rem 1.2rem;
    font-weight: 700;
    box-shadow: var(--shadow);
    border: 2px solid var(--secondary);
    cursor: pointer;
    white-space: nowrap;
    font-size: 1.05rem;
    transition: background 0.2s, color 0.2s, border 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    animation: popIn 0.7s cubic-bezier(.68,-0.55,.27,1.55) both;
}
.hero-cat:hover {
    background: var(--secondary);
    color: #fff;
    border-color: var(--primary);
}
@keyframes popIn {
    0% { transform: scale(0.7) translateY(20px); opacity: 0; }
    100% { transform: scale(1) translateY(0); opacity: 1; }
}

/* Offer Banner */
.kirana-offer-banner {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0 0 0;
    z-index: 2;
    position: relative;
}
.offer-badge {
    background: linear-gradient(90deg, var(--secondary), var(--accent));
    color: #fff;
    padding: 0.9rem 2.2rem;
    border-radius: 22px;
    font-size: 1.15rem;
    font-weight: 800;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 0.7rem;
    letter-spacing: 1px;
    animation: bounceIn 1s cubic-bezier(.68,-0.55,.27,1.55) both;
}
@keyframes bounceIn {
    0% { transform: scale(0.7); opacity: 0; }
    60% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); }
}

/* Category Scroll */
.kirana-section {
    max-width: 1100px;
    margin: 0 auto;
    padding: 2.5rem 1rem 1.5rem 1rem;
}
.section-title {
    font-size: 1.6rem;
    font-family: 'Baloo 2', cursive;
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: var(--primary);
    text-align: center;
}
.category-scroll {
    display: flex;
    gap: 1.2rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    scrollbar-width: thin;
    scrollbar-color: var(--accent) #fffde7;
}
.category-card {
    background: var(--card);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 1.2rem 1rem;
    min-width: 120px;
    flex: 0 0 120px;
    text-align: center;
    border: 2px solid var(--primary);
    transition: transform 0.2s, box-shadow 0.2s, background 0.2s;
    color: var(--secondary);
    font-weight: 800;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    cursor: pointer;
    position: relative;
}
.category-card:hover {
    background: var(--primary);
    color: #fff;
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 8px 24px #ff6f0033;
}

/* Product Grid */
.product-list {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.3rem;
    margin-bottom: 1.5rem;
}
@media (min-width: 900px) {
    .product-list {
        grid-template-columns: repeat(4, 1fr);
    }
}
.product-card {
    background: var(--card);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 1.1rem 1rem 1.3rem 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
    border: 2px solid var(--accent);
    position: relative;
    overflow: visible;
    min-height: 260px;
}
.product-card:hover {
    transform: translateY(-6px) scale(1.04) rotate(-1deg);
    box-shadow: 0 12px 32px #ffd60033;
}
.product-badge {
    position: absolute;
    top: -16px;
    left: 16px;
    background: var(--secondary);
    color: #fff;
    font-size: 0.9rem;
    font-weight: 700;
    padding: 0.3rem 0.9rem;
    border-radius: 16px;
    box-shadow: 0 2px 8px #00897b33;
    z-index: 2;
    animation: popIn 0.7s cubic-bezier(.68,-0.55,.27,1.55) both;
}
.product-img {
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius: 50%;
    margin-bottom: 0.7rem;
    background: #fffde7;
    background-size: cover;
    background-position: center;
    box-shadow: 0 2px 8px #ffd60033;
}
.product-name {
    font-size: 1.15rem;
    font-weight: 800;
    margin-bottom: 0.3rem;
    font-family: 'Baloo 2', cursive;
}
.product-price {
    color: var(--primary);
    font-weight: 800;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}
.old-price {
    color: #aaa;
    text-decoration: line-through;
    font-size: 0.95rem;
    margin-left: 0.5rem;
}
.add-btn {
    background: var(--secondary);
    color: #fff;
    border: none;
    border-radius: 20px;
    padding: 0.5rem 1.3rem;
    font-weight: 800;
    cursor: pointer;
    font-size: 1.05rem;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 8px #00897b33;
}
.add-btn:hover { background: var(--primary); color: #fff; }

/* Testimonial Carousel */
.kirana-testimonials {
    background: var(--card);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-top: 2.5rem;
    padding: 2rem 1rem 1.5rem 1rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}
.testimonial-carousel {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.2rem;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    padding-bottom: 0.5rem;
}
.testimonial-bubble {
    background: #fffde7;
    border-radius: 30px 30px 30px 0;
    box-shadow: var(--shadow);
    padding: 1.2rem 1.5rem;
    min-width: 220px;
    max-width: 320px;
    font-size: 1.05rem;
    color: var(--primary);
    position: relative;
    border: 2px solid var(--accent);
    font-family: 'Inter', sans-serif;
    scroll-snap-align: start;
    margin-bottom: 0.5rem;
}
.testimonial-bubble:after {
    content: '';
    position: absolute;
    left: 24px;
    bottom: -18px;
    width: 24px;
    height: 24px;
    background: #fffde7;
    border-bottom-left-radius: 50%;
    border: 2px solid var(--accent);
    border-top: none;
    border-right: none;
    transform: rotate(-20deg);
}
.testimonial-avatar {
    width: 38px;
    height: 38px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--secondary);
    margin-bottom: 0.5rem;
}
.testimonial-bubble span {
    display: block;
    margin-top: 10px;
    font-size: 1rem;
    color: var(--secondary);
    font-weight: 700;
}

/* Footer */
.kirana-footer {
    background: var(--secondary);
    color: #fff;
    padding: 2.2rem 1rem 1.5rem 1rem;
    text-align: center;
    font-size: 1.1rem;
    margin-top: 2.5rem;
    border-top: 4px solid var(--accent);
    border-radius: 24px 24px 0 0;
    position: relative;
    overflow: hidden;
}
.footer-wave {
    position: absolute;
    top: -60px;
    left: 0;
    width: 100%;
    z-index: 0;
}
.kirana-footer p {
    position: relative;
    z-index: 1;
}
.kirana-footer a {
    color: var(--accent);
    text-decoration: none;
    font-weight: 700;
}

@media (max-width: 700px) {
    .kirana-header-row {
        flex-direction: row;
        gap: 10px;
        padding: 0.7rem 0.5rem;
    }
    .product-list {
        grid-template-columns: 1fr;
    }
    .hero-categories {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    .kirana-section { padding: 1.5rem 0.5rem 1rem 0.5rem; }
}
    </style>
</head>
<body>
    <!-- Header -->
    <header class="kirana-header">
        <div class="kirana-header-row">
            <button class="menu-toggle" aria-label="Open Menu"><i class="fas fa-bars"></i></button>
            <a href="#" class="kirana-logo"><span class="logo-basket"><i class="fas fa-shopping-basket"></i></span>ApnaKirana</a>
            <div class="header-actions">
                <button class="cart-btn" id="cartBtn"><i class="fas fa-shopping-cart"></i><span class="cart-count">0</span></button>
            </div>
        </div>
    </header>
    <!-- Sidebar Nav (Desktop) -->
    <nav class="kirana-sidebar">
        <a href="#home" class="active" title="Home"><i class="fas fa-home"></i></a>
        <a href="#categories" title="Categories"><i class="fas fa-th-large"></i></a>
        <a href="#offers" title="Offers"><i class="fas fa-bolt"></i></a>
        <a href="#contact" title="Contact"><i class="fas fa-phone"></i></a>
    </nav>
    <!-- Bottom Nav (Mobile) -->
    <nav class="kirana-bottom-nav">
        <a href="#home" class="active"><i class="fas fa-home"></i><span style="font-size:0.8rem;">Home</span></a>
        <a href="#categories"><i class="fas fa-th-large"></i><span style="font-size:0.8rem;">Categories</span></a>
        <a href="#offers"><i class="fas fa-bolt"></i><span style="font-size:0.8rem;">Offers</span></a>
        <a href="#contact"><i class="fas fa-phone"></i><span style="font-size:0.8rem;">Contact</span></a>
    </nav>
    <!-- Hero Section -->
    <section id="home" class="kirana-hero">
        <svg class="kirana-hero-svg" viewBox="0 0 200 120" fill="none"><ellipse cx="100" cy="60" rx="100" ry="60" fill="#ffd600"/></svg>
        <div class="hero-content">
            <h1><span class="kirana-gradient">Sab Kuch Yahin Milega!</span></h1>
            <p class="hero-sub">Fresh groceries, daily essentials, and more—delivered to your doorstep.</p>
            <form class="hero-search" autocomplete="off">
                <input type="text" placeholder="Search for products..." aria-label="Search">
                <button type="submit"><i class="fas fa-search"></i></button>
            </form>
            <div class="hero-categories">
                <button class="hero-cat"><i class="fas fa-carrot"></i> Veggies</button>
                <button class="hero-cat"><i class="fas fa-apple-alt"></i> Fruits</button>
                <button class="hero-cat"><i class="fas fa-cookie-bite"></i> Snacks</button>
                <button class="hero-cat"><i class="fas fa-bottle-water"></i> Beverages</button>
                <button class="hero-cat"><i class="fas fa-cheese"></i> Dairy</button>
                <button class="hero-cat"><i class="fas fa-pump-soap"></i> Household</button>
            </div>
        </div>
    </section>
    <!-- Offers Banner -->
    <section id="offers" class="kirana-offer-banner">
        <div class="offer-badge"><i class="fas fa-bolt"></i> 10% OFF on first order! Use code: <b>KIRANA10</b></div>
    </section>
    <!-- Categories Section -->
    <section id="categories" class="kirana-section kirana-categories">
        <h2 class="section-title">Shop by Category</h2>
        <div class="category-scroll">
            <div class="category-card"><i class="fas fa-carrot"></i><span>Vegetables</span></div>
            <div class="category-card"><i class="fas fa-apple-alt"></i><span>Fruits</span></div>
            <div class="category-card"><i class="fas fa-cheese"></i><span>Dairy</span></div>
            <div class="category-card"><i class="fas fa-cookie-bite"></i><span>Snacks</span></div>
            <div class="category-card"><i class="fas fa-bottle-water"></i><span>Beverages</span></div>
            <div class="category-card"><i class="fas fa-pump-soap"></i><span>Household</span></div>
        </div>
    </section>
    <!-- Products Section -->
    <section class="kirana-section kirana-products">
        <h2 class="section-title">Featured Products</h2>
        <div class="product-list">
            <div class="product-card">
                <div class="product-badge">Fresh</div>
                <div class="product-img" style="background-image:url('https://images.unsplash.com/photo-1561138299-323242b41c09?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Organic Tomatoes</div>
                    <div class="product-price">₹50 <span class="old-price">₹60</span></div>
                    <button class="add-btn"><i class="fas fa-plus"></i> Add</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-badge">Best Seller</div>
                <div class="product-img" style="background-image:url('https://images.unsplash.com/photo-**********-48a3c822c501?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Fresh Milk (1L)</div>
                    <div class="product-price">₹65</div>
                    <button class="add-btn"><i class="fas fa-plus"></i> Add</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-badge">Healthy</div>
                <div class="product-img" style="background-image:url('https://images.unsplash.com/photo-1584776296944-ab6fb57b0bdd?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Whole Wheat Bread</div>
                    <div class="product-price">₹45</div>
                    <button class="add-btn"><i class="fas fa-plus"></i> Add</button>
                </div>
            </div>
            <div class="product-card">
                <div class="product-badge">Popular</div>
                <div class="product-img" style="background-image:url('https://images.unsplash.com/photo-1586201375822-52c6739c22a7?auto=format&fit=crop&w=400&q=80');"></div>
                <div class="product-info">
                    <div class="product-name">Basmati Rice (1kg)</div>
                    <div class="product-price">₹120</div>
                    <button class="add-btn"><i class="fas fa-plus"></i> Add</button>
                </div>
            </div>
        </div>
    </section>
    <!-- Testimonials Section -->
    <section class="kirana-section kirana-testimonials">
        <h2 class="section-title">Happy Customers</h2>
        <div class="testimonial-carousel">
            <div class="testimonial-bubble">
                <img class="testimonial-avatar" src="https://randomuser.me/api/portraits/women/68.jpg" alt="Meena J.">
                <p>"Super fast delivery and fresh products!"</p>
                <span>- Meena J.</span>
            </div>
            <div class="testimonial-bubble">
                <img class="testimonial-avatar" src="https://randomuser.me/api/portraits/men/45.jpg" alt="Rakesh S.">
                <p>"Best kirana store in the area. Highly recommended."</p>
                <span>- Rakesh S.</span>
            </div>
            <div class="testimonial-bubble">
                <img class="testimonial-avatar" src="https://randomuser.me/api/portraits/women/32.jpg" alt="Priya D.">
                <p>"Great offers and friendly staff."</p>
                <span>- Priya D.</span>
            </div>
        </div>
    </section>
    <!-- Footer -->
    <footer class="kirana-footer">
        <div class="footer-wave">
            <svg width="100%" height="60" viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M0,30 Q720,60 1440,30 Q720,0 0,30 Z" fill="#ff6f00"/></svg>
        </div>
        <p>&copy; 2025 Apna Kirana. Your trusted neighborhood store.</p>
        <p style="margin-top: 8px;">Contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
    </footer>
    <script>
    // Mobile nav toggle
    document.querySelectorAll('.menu-toggle').forEach(btn => {
        btn.addEventListener('click', () => {
            document.querySelector('.kirana-bottom-nav').classList.toggle('open');
        });
    });
    // Active nav highlight
    document.querySelectorAll('.kirana-bottom-nav a, .kirana-sidebar a').forEach(link => {
        link.addEventListener('click', function() {
            document.querySelectorAll('.kirana-bottom-nav a, .kirana-sidebar a').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    // Add to cart (demo only)
    let cartCount = 0;
    document.querySelectorAll('.add-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            cartCount++;
            document.querySelector('.cart-count').textContent = cartCount;
            btn.textContent = 'Added!';
            setTimeout(() => { btn.innerHTML = '<i class="fas fa-plus"></i> Add'; }, 1200);
        });
    });
    </script>
</body>
</html>
