<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweet Palace - Traditional Mithai Shop</title>
    <link href="https://fonts.googleapis.com/css2?family=Kalam:wght@300;400;700&family=Mukti:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Mukti', sans-serif;
            line-height: 1.6;
            color: #4a2c2a;
            background: linear-gradient(135deg, #fff8dc 0%, #ffeaa7 100%);
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #d63031 0%, #e17055 100%);
            color: #fff8dc;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(214, 48, 49, 0.3);
            border-bottom: 3px solid #fdcb6e;
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-family: 'Kalam', cursive;
            font-size: 22px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .festival-badge {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: #4a2c2a;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 11px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        /* Desktop Header */
        .desktop-header {
            display: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(214, 48, 49, 0.8), rgba(225, 112, 85, 0.8)), url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            color: #fff8dc;
            padding: 90px 0 50px;
            margin-top: 75px;
            text-align: center;
            position: relative;
            min-height: 65vh;
            display: flex;
            align-items: center;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at center, rgba(253, 203, 110, 0.2) 0%, transparent 70%);
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(253, 203, 110, 0.3);
            border: 2px solid #fdcb6e;
            color: #fdcb6e;
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 700;
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .hero-title {
            font-family: 'Kalam', cursive;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 20px;
            line-height: 1.2;
            text-shadow: 3px 3px 6px rgba(0,0,0,0.4);
        }

        .hero-subtitle {
            font-size: 16px;
            margin-bottom: 32px;
            opacity: 0.95;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .hero-button {
            background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
            color: #4a2c2a;
            padding: 16px 32px;
            border-radius: 30px;
            text-decoration: none;
            font-weight: 700;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 6px 20px rgba(253, 203, 110, 0.4);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .hero-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(253, 203, 110, 0.5);
            border-color: #fff8dc;
        }

        /* Sweet Categories */
        .sweet-categories {
            padding: 50px 0;
            background: white;
            position: relative;
        }

        .sweet-categories::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #d63031, #fdcb6e, #e17055, #d63031);
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-badge {
            display: inline-block;
            background: linear-gradient(135deg, #d63031 0%, #e17055 100%);
            color: white;
            padding: 8px 20px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(214, 48, 49, 0.3);
        }

        .section-title {
            font-family: 'Kalam', cursive;
            font-size: 32px;
            font-weight: 700;
            color: #4a2c2a;
            margin-bottom: 16px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .section-subtitle {
            font-size: 16px;
            color: #8b4513;
            max-width: 500px;
            margin: 0 auto;
        }

        .categories-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }

        .category-card {
            background: linear-gradient(135deg, #fff8dc 0%, #ffeaa7 100%);
            border-radius: 20px;
            padding: 20px;
            text-align: center;
            text-decoration: none;
            color: #4a2c2a;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(214, 48, 49, 0.1);
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(253, 203, 110, 0.1) 0%, transparent 70%);
            transform: rotate(45deg);
            transition: all 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-8px) scale(1.02);
            border-color: #d63031;
            box-shadow: 0 12px 30px rgba(214, 48, 49, 0.2);
        }

        .category-card:hover::before {
            transform: rotate(45deg) scale(1.2);
        }

        .category-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
            color: #d63031;
            position: relative;
            z-index: 2;
        }

        .category-name {
            font-family: 'Kalam', cursive;
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 6px;
            position: relative;
            z-index: 2;
        }

        .category-count {
            font-size: 12px;
            color: #8b4513;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        /* Featured Sweets */
        .featured-sweets {
            padding: 50px 0;
            background: linear-gradient(135deg, #fff8dc 0%, #ffeaa7 100%);
        }

        .sweets-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .sweet-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(214, 48, 49, 0.15);
            transition: all 0.3s ease;
            border: 2px solid #fdcb6e;
            position: relative;
        }

        .sweet-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #d63031, #fdcb6e, #e17055);
        }

        .sweet-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 15px 35px rgba(214, 48, 49, 0.2);
        }

        .sweet-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .sweet-content {
            padding: 20px;
        }

        .sweet-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .sweet-title {
            font-family: 'Kalam', cursive;
            font-size: 18px;
            font-weight: 700;
            color: #4a2c2a;
            margin-bottom: 6px;
        }

        .sweet-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #fdcb6e;
            font-weight: 700;
        }

        .sweet-price {
            font-family: 'Kalam', cursive;
            font-size: 20px;
            font-weight: 700;
            color: #d63031;
        }

        .sweet-description {
            font-size: 14px;
            color: #8b4513;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .sweet-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sweet-tags {
            display: flex;
            gap: 6px;
        }

        .tag {
            background: #fff8dc;
            color: #d63031;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 700;
            border: 1px solid #fdcb6e;
        }

        .tag.fresh {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }

        .tag.special {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }

        .order-btn {
            background: linear-gradient(135deg, #d63031 0%, #e17055 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(214, 48, 49, 0.3);
        }

        .order-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(214, 48, 49, 0.4);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 3px solid #fdcb6e;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -4px 20px rgba(214, 48, 49, 0.1);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            text-decoration: none;
            color: #8b4513;
            transition: color 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: #d63031;
        }

        .nav-item i {
            font-size: 18px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 700;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 999;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Desktop Styles */
        @media (min-width: 768px) {
            body {
                padding-bottom: 0;
            }

            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: block;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                border-bottom: 3px solid #fdcb6e;
                box-shadow: 0 2px 15px rgba(214, 48, 49, 0.1);
            }

            .desktop-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .desktop-logo {
                font-family: 'Kalam', cursive;
                font-size: 28px;
                font-weight: 700;
                color: #d63031;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 12px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            }

            .desktop-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .desktop-menu a {
                text-decoration: none;
                color: #4a2c2a;
                font-weight: 600;
                transition: color 0.3s ease;
            }

            .desktop-menu a:hover {
                color: #d63031;
            }

            .hero {
                margin-top: 85px;
                padding: 100px 0 60px;
                min-height: 75vh;
            }

            .hero-title {
                font-size: 52px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .categories-grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 20px;
            }

            .sweets-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 24px;
            }

            .bottom-nav {
                display: none;
            }

            .whatsapp-chat {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-crown"></i>
                Sweet Palace
            </div>
            <div class="festival-badge">
                <i class="fas fa-gift"></i>
                <span>Festival Special</span>
            </div>
        </div>
    </header>

    <!-- Desktop Header -->
    <header class="desktop-header">
        <nav class="desktop-nav">
            <a href="#" class="desktop-logo">
                <i class="fas fa-crown"></i>
                Sweet Palace
            </a>
            <ul class="desktop-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#sweets">मिठाई</a></li>
                <li><a href="#festival">त्योहार स्पेशल</a></li>
                <li><a href="#about">हमारे बारे में</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    Traditional Since 1985
                </div>
                <h1 class="hero-title">
                    मिठास का<br>
                    राजमहल
                </h1>
                <p class="hero-subtitle">
                    पारंपरिक तरीके से बनी शुद्ध देसी मिठाइयां। 
                    हर त्योहार को बनाएं और भी मीठा।
                </p>
                <a href="#sweets" class="hero-button">
                    <i class="fas fa-candy-cane"></i>
                    मिठाई देखें
                </a>
            </div>
        </div>
    </section>

    <!-- Sweet Categories -->
    <section class="sweet-categories">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Traditional Collection</div>
                <h2 class="section-title">हमारी मिठाई रेंज</h2>
                <p class="section-subtitle">
                    हर त्योहार और खुशी के मौके के लिए खास मिठाइयां
                </p>
            </div>

            <div class="categories-grid">
                <a href="#sweets" class="category-card">
                    <i class="fas fa-cookie category-icon"></i>
                    <div class="category-name">गुलाब जामुन</div>
                    <div class="category-count">5 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-birthday-cake category-icon"></i>
                    <div class="category-name">रसगुल्ला</div>
                    <div class="category-count">4 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-candy-cane category-icon"></i>
                    <div class="category-name">बर्फी</div>
                    <div class="category-count">8 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-star category-icon"></i>
                    <div class="category-name">हलवा</div>
                    <div class="category-count">6 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-heart category-icon"></i>
                    <div class="category-name">लड्डू</div>
                    <div class="category-count">7 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-gem category-icon"></i>
                    <div class="category-name">कलाकंद</div>
                    <div class="category-count">3 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-crown category-icon"></i>
                    <div class="category-name">रसमलाई</div>
                    <div class="category-count">2 वैरायटी</div>
                </a>

                <a href="#sweets" class="category-card">
                    <i class="fas fa-gift category-icon"></i>
                    <div class="category-name">त्योहार स्पेशल</div>
                    <div class="category-count">10+ आइटम्स</div>
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Sweets -->
    <section class="featured-sweets" id="sweets">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Chef's Special</div>
                <h2 class="section-title">आज के स्पेशल</h2>
                <p class="section-subtitle">
                    हलवाई जी की खास रेसिपी से बनी मिठाइयां
                </p>
            </div>

            <div class="sweets-grid">
                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Gulab Jamun" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">गुलाब जामुन</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>5.0</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹40/पीस</div>
                        </div>
                        <p class="sweet-description">
                            मुंह में घुल जाने वाले गुलाब जामुन, गर्म चाशनी के साथ। खोया और मावा से बने।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag fresh">ताज़ा बना</span>
                                <span class="tag">क्लासिक</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('गुलाब जामुन', '40/पीस')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1606491956689-2ea866880c84?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Rasgulla" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">रसगुल्ला</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹35/पीस</div>
                        </div>
                        <p class="sweet-description">
                            बंगाली स्टाइल स्पंजी रसगुल्ला, मीठी चाशनी में डूबा हुआ। छेना से बना।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag fresh">ताज़ा बना</span>
                                <span class="tag">बंगाली स्पेशल</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('रसगुल्ला', '35/पीस')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Kaju Barfi" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">काजू बर्फी</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹80/पीस</div>
                        </div>
                        <p class="sweet-description">
                            प्रीमियम काजू से बनी मुंह में घुल जाने वाली बर्फी। चांदी के वर्क के साथ।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag special">प्रीमियम</span>
                                <span class="tag">चांदी वर्क</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('काजू बर्फी', '80/पीस')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1488900128323-21503983a07e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Motichoor Laddu" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">मोतीचूर लड्डू</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.7</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹50/पीस</div>
                        </div>
                        <p class="sweet-description">
                            बेसन के छोटे-छोटे दानों से बना मोतीचूर लड्डू। त्योहारों का खास स्वाद।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag">त्योहार स्पेशल</span>
                                <span class="tag fresh">ताज़ा बना</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('मोतीचूर लड्डू', '50/पीस')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1504113888839-1c8eb50233d3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Ras Malai" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">रसमलाई</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.9</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹60/पीस</div>
                        </div>
                        <p class="sweet-description">
                            मलाईदार रसमलाई, पिस्ता और बादाम के टुकड़ों के साथ। ठंडा सर्व किया जाता है।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag special">स्पेशल</span>
                                <span class="tag">कोल्ड सर्व</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('रसमलाई', '60/पीस')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="sweet-card">
                    <img src="https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Gajar Halwa" class="sweet-image">
                    <div class="sweet-content">
                        <div class="sweet-header">
                            <div>
                                <h3 class="sweet-title">गाजर का हलवा</h3>
                                <div class="sweet-rating">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <span>4.8</span>
                                </div>
                            </div>
                            <div class="sweet-price">₹45/100g</div>
                        </div>
                        <p class="sweet-description">
                            देसी घी में बना गाजर का हलवा, खोया और ड्राई फ्रूट्स के साथ। सर्दियों का स्पेशल।
                        </p>
                        <div class="sweet-footer">
                            <div class="sweet-tags">
                                <span class="tag">सर्दी स्पेशल</span>
                                <span class="tag fresh">ताज़ा बना</span>
                            </div>
                            <button class="order-btn" onclick="orderSweet('गाजर का हलवा', '45/100g')">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>होम</span>
            </a>
            <a href="#sweets" class="nav-item">
                <i class="fas fa-candy-cane"></i>
                <span>मिठाई</span>
            </a>
            <a href="#festival" class="nav-item">
                <i class="fas fa-gift"></i>
                <span>त्योहार</span>
            </a>
            <a href="#about" class="nav-item">
                <i class="fas fa-info-circle"></i>
                <span>हमारे बारे में</span>
            </a>
            <a href="#contact" class="nav-item">
                <i class="fas fa-phone"></i>
                <span>संपर्क</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं Sweet Palace से मिठाई ऑर्डर करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Order sweet function
        function orderSweet(sweetName, price) {
            const message = `🍬 *Sweet Palace - मिठाई ऑर्डर*

🧁 *मिठाई:* ${sweetName}
💰 *प्राइस:* ₹${price}

कृपया इस ऑर्डर को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showFestiveNotification(`${sweetName} ऑर्डर में जोड़ा गया! 🍬`);
        }

        // Show festive notification
        function showFestiveNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 95px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #d63031, #e17055);
                color: #fff8dc;
                padding: 16px 32px;
                border-radius: 30px;
                font-family: 'Kalam', cursive;
                font-weight: 700;
                font-size: 14px;
                z-index: 2000;
                box-shadow: 0 8px 30px rgba(214, 48, 49, 0.4);
                border: 3px solid #fdcb6e;
                animation: festiveBounce 0.6s ease;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes festiveBounce {
                    0% {
                        transform: translateX(-50%) translateY(-40px) scale(0.7) rotate(-10deg);
                        opacity: 0;
                    }
                    30% {
                        transform: translateX(-50%) translateY(10px) scale(1.1) rotate(5deg);
                        opacity: 1;
                    }
                    60% {
                        transform: translateX(-50%) translateY(-5px) scale(0.95) rotate(-2deg);
                        opacity: 1;
                    }
                    100% {
                        transform: translateX(-50%) translateY(0) scale(1) rotate(0deg);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'festiveBounce 0.6s ease reverse';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 600);
                }
            }, 3500);
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to section
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 75;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 75;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add festive hover effects
        document.querySelectorAll('.sweet-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) scale(1.02) rotate(1deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1) rotate(0deg)';
            });
        });

        // Category card festive effects
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.05) rotate(-2deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1) rotate(0deg)';
            });
        });

        // Add sparkle effect on page load
        function createSparkle() {
            const sparkle = document.createElement('div');
            sparkle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: #fdcb6e;
                border-radius: 50%;
                pointer-events: none;
                z-index: 1001;
                animation: sparkleFloat 3s linear infinite;
            `;

            sparkle.style.left = Math.random() * window.innerWidth + 'px';
            sparkle.style.top = window.innerHeight + 'px';

            document.body.appendChild(sparkle);

            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 3000);
        }

        // Add sparkle animation
        const sparkleStyle = document.createElement('style');
        sparkleStyle.textContent = `
            @keyframes sparkleFloat {
                0% {
                    transform: translateY(0) rotate(0deg);
                    opacity: 1;
                }
                100% {
                    transform: translateY(-100vh) rotate(360deg);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(sparkleStyle);

        // Create sparkles periodically
        setInterval(createSparkle, 2000);
    </script>
</body>
</html>
