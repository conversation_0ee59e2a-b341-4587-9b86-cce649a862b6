<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Photography Services Theme - Dukkan</title>
    <style>
/* --- Redesigned Photography Services Theme --- */
body {
    background: linear-gradient(135deg, #f0f2f5 0%, #e6ecf3 100%);
    color: #1f2c38;
    font-family: 'Lato', sans-serif;
    overflow-x: hidden;
}
.photo-header {
    background: #1f2c38;
    background: linear-gradient(90deg, #1f2c38 0%, #2c3e50 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 32px;
    box-shadow: 0 4px 18px rgba(31, 44, 56, 0.18);
    border-bottom: 4px solid #3498db;
    position: sticky;
    top: 0;
    z-index: 1000;
}
.lens-gradient-text {
    background: linear-gradient(90deg, #3498db 0%, #8e54e9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    letter-spacing: 2px;
    font-size: 2rem;
}
.photo-nav {
    display: flex;
    gap: 18px;
}
.nav-btn {
    background: #232f3e;
    border: none;
    border-radius: 20px;
    padding: 8px 22px;
    font-size: 1.1rem;
    color: #fff;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.10);
}
.nav-btn.active, .nav-btn:hover {
    background: #3498db;
    color: #fff;
    transform: translateY(-2px) scale(1.05);
}
.photo-header .nav-icons {
    display: flex;
    gap: 18px;
}
.photo-header .nav-icons i {
    font-size: 1.5rem;
    color: #8e54e9;
    transition: color 0.2s, transform 0.2s;
    cursor: pointer;
}
.photo-header .nav-icons i:hover {
    color: #3498db;
    transform: scale(1.2) rotate(-8deg);
}
.cart-icon {
    position: relative;
}
.cart-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #3498db;
    color: #fff;
    border-radius: 50%;
    padding: 4px 8px;
    font-size: 0.8rem;
    font-weight: bold;
    border: 2px solid #1f2c38;
}
.photo-hero {
    position: relative;
    min-height: 340px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    background: url('https://images.unsplash.com/photo-1516724562728-afc824a36e84?auto=format&fit=crop&w=1200&q=80') no-repeat center center/cover;
    overflow: visible;
    border-bottom: 4px solid #3498db;
}
.hero-overlay {
    position: absolute;
    top: 0; left: 0; width: 100%; height: 100%;
    background: linear-gradient(180deg, rgba(31,44,56,0.7) 0%, rgba(52,152,219,0.2) 100%);
    z-index: 0;
}
.hero-content {
    position: relative;
    z-index: 1;
    text-align: center;
    padding: 60px 0 0 0;
}
.photo-hero h1 {
    font-size: 3rem;
    margin-bottom: 10px;
}
.photo-hero p {
    font-size: 1.3rem;
    background: rgba(31,44,56,0.6);
    display: inline-block;
    padding: 6px 18px;
    border-radius: 20px;
    margin-bottom: 18px;
    color: #fff;
}
.hero-btn {
    margin-top: 18px;
    background: linear-gradient(90deg, #3498db 0%, #8e54e9 100%);
    color: #fff;
    border: none;
    padding: 14px 32px;
    border-radius: 16px;
    font-size: 1.2rem;
    font-weight: 700;
    cursor: pointer;
    transition: box-shadow 0.3s, transform 0.3s, background 0.2s;
    box-shadow: 0 6px 18px rgba(52, 152, 219, 0.25);
    text-transform: uppercase;
    letter-spacing: 1px;
}
.hero-btn:hover {
    background: linear-gradient(90deg, #8e54e9 0%, #3498db 100%);
    color: #fff;
    box-shadow: 0 10px 28px rgba(52, 152, 219, 0.35);
    transform: translateY(-3px) scale(1.04);
}
.hero-gallery {
    display: flex;
    gap: 18px;
    justify-content: center;
    align-items: flex-end;
    margin-top: 32px;
    z-index: 2;
    position: relative;
}
.hero-thumb {
    width: 90px;
    height: 90px;
    object-fit: cover;
    border-radius: 18px;
    border: 3px solid #3498db;
    box-shadow: 0 4px 16px rgba(52, 152, 219, 0.18);
    margin-bottom: 0;
    transition: transform 0.2s, box-shadow 0.2s;
    background: #fff;
    cursor: pointer;
}
.hero-thumb:hover {
    transform: scale(1.08) rotate(-4deg);
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.28);
}
.photo-timeline {
    background: #fff;
    margin: 32px 0 0 0;
    border-radius: 18px;
    box-shadow: 0 4px 18px rgba(52, 152, 219, 0.10);
    border: 2px dashed #8e54e9;
    padding: 28px 0 18px 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}
.photo-timeline h2 {
    font-size: 2rem;
    margin-bottom: 18px;
    text-align: center;
}
.timeline-steps {
    display: flex;
    justify-content: center;
    gap: 32px;
    flex-wrap: wrap;
}
.timeline-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}
.step-icon {
    background: linear-gradient(90deg, #3498db 0%, #8e54e9 100%);
    color: #fff;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.6rem;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.15);
    margin-bottom: 4px;
}
.step-label {
    font-size: 1rem;
    font-weight: 700;
    color: #232f3e;
}
.photo-portfolio {
    background: none;
    margin: 40px 0 0 0;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
}
.photo-portfolio h2 {
    font-size: 2rem;
    margin-bottom: 18px;
    text-align: center;
}
.portfolio-masonry {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 18px;
}
.portfolio-img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 18px;
    border: 3px solid #8e54e9;
    box-shadow: 0 4px 16px rgba(52, 152, 219, 0.18);
    transition: transform 0.2s, box-shadow 0.2s;
    background: #fff;
    cursor: pointer;
}
.portfolio-img:hover {
    transform: scale(1.04) rotate(-2deg);
    box-shadow: 0 8px 32px rgba(52, 152, 219, 0.28);
}
.photo-packages {
    background: #f7faff;
    margin: 40px 0 0 0;
    border-radius: 18px;
    box-shadow: 0 4px 18px rgba(52, 152, 219, 0.10);
    border: 2px solid #e1f0fa;
    padding: 28px 0 18px 0;
    max-width: 1100px;
    margin-left: auto;
    margin-right: auto;
}
.photo-packages h2 {
    font-size: 2rem;
    margin-bottom: 18px;
    text-align: center;
}
.package-scroll {
    display: flex;
    gap: 24px;
    overflow-x: auto;
    padding-bottom: 8px;
    scrollbar-width: none;
    justify-content: center;
}
.package-scroll::-webkit-scrollbar {
    display: none;
}
.package-card {
    background: linear-gradient(135deg, #fff, #e1f0fa 100%);
    border-radius: 18px;
    min-width: 260px;
    max-width: 320px;
    box-shadow: 0 4px 18px rgba(52, 152, 219, 0.10);
    border: 2px solid #8e54e9;
    padding: 24px 18px 18px 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
}
.package-card:hover {
    transform: translateY(-6px) scale(1.03);
    box-shadow: 0 10px 28px rgba(52, 152, 219, 0.18);
}
.package-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #8e54e9;
    margin-bottom: 8px;
}
.package-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #3498db;
    margin-bottom: 12px;
}
.package-card ul {
    list-style: none;
    margin-bottom: 14px;
    padding: 0;
    color: #232f3e;
    font-size: 1rem;
}
.package-card li {
    margin-bottom: 4px;
}
.package-btn {
    background: linear-gradient(90deg, #3498db 0%, #8e54e9 100%);
    color: #fff;
    border: none;
    padding: 10px 0;
    border-radius: 12px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 700;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    width: 100%;
    align-self: center;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.18);
    text-transform: uppercase;
    letter-spacing: 1px;
}
.package-btn:hover {
    background: linear-gradient(90deg, #8e54e9 0%, #3498db 100%);
    color: #fff;
    transform: translateY(-2px) scale(1.04);
}
.photo-testimonials {
    background: #fff;
    margin: 40px 0 0 0;
    border-radius: 18px;
    box-shadow: 0 4px 18px rgba(52, 152, 219, 0.10);
    border: 2px solid #e1f0fa;
    padding: 28px 0 18px 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}
.photo-testimonials h2 {
    font-size: 2rem;
    margin-bottom: 18px;
    text-align: center;
}
.testimonial-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 24px;
}
.testimonial-card {
    background: linear-gradient(135deg, #fff, #e1f0fa 100%);
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.10);
    padding: 22px 18px;
    max-width: 320px;
    font-size: 1.1rem;
    color: #232f3e;
    position: relative;
    border: 2px solid #8e54e9;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 16px;
}
.testimonial-img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    border: 2px solid #3498db;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.10);
}
.testimonial-content p {
    margin: 0 0 6px 0;
    font-style: italic;
    color: #232f3e;
}
.testimonial-content span {
    font-size: 1rem;
    color: #8e54e9;
    font-weight: 700;
}
.cart-section {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #fff;
    box-shadow: 0 -4px 18px rgba(52, 152, 219, 0.15);
    padding: 24px 0 18px 0;
    transform: translateY(100%);
    transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 1000;
    border-top: 4px solid #3498db;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
}
.cart-section.active {
    transform: translateY(0);
    display: block;
}
.cart-section h2 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #232f3e;
    font-weight: 700;
    text-align: center;
}
.cart-items {
    max-height: 220px;
    overflow-y: auto;
    margin-bottom: 15px;
    padding-right: 5px;
    border: 1px dashed #8e54e9;
    padding: 10px;
    background: rgba(230, 236, 243, 0.5);
    border-radius: 10px;
}
.cart-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px dashed #8e54e9;
}
.cart-item-name {
    font-weight: 500;
    color: #232f3e;
    font-size: 1.2rem;
}
.cart-item-price {
    font-weight: 700;
    color: #3498db;
    font-size: 1.2rem;
}
.cart-total {
    font-size: 1.5rem;
    font-weight: 800;
    margin-bottom: 15px;
    text-align: right;
    color: #232f3e;
    background: rgba(52, 152, 219, 0.08);
    padding: 5px 10px;
    border-radius: 5px;
}
.checkout-btn {
    background: linear-gradient(90deg, #3498db 0%, #8e54e9 100%);
    color: #fff;
    border: none;
    padding: 15px 0;
    border-radius: 14px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: 700;
    width: 100%;
    transition: background 0.2s, color 0.2s, transform 0.2s;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.18);
    text-transform: uppercase;
    letter-spacing: 2px;
}
.checkout-btn:hover {
    background: linear-gradient(90deg, #8e54e9 0%, #3498db 100%);
    color: #fff;
    transform: translateY(-3px) scale(1.03);
    box-shadow: 0 8px 20px rgba(52, 152, 219, 0.28);
}
.photo-footer {
    background: #1f2c38;
    color: #fff;
    padding: 32px 20px 0 20px;
    text-align: center;
    font-size: 1rem;
    margin-bottom: 80px;
    position: relative;
    border-top: 4px solid #3498db;
    border-radius: 24px 24px 0 0;
    overflow: hidden;
}
.footer-lens {
    position: absolute;
    top: -60px;
    left: 0;
    width: 100%;
    z-index: 0;
}
.photo-footer p {
    position: relative;
    z-index: 1;
}
@media (max-width: 700px) {
    .photo-header {
        flex-direction: column;
        gap: 10px;
        padding: 12px 8px;
    }
    .photo-nav {
        gap: 8px;
    }
    .portfolio-masonry {
        grid-template-columns: 1fr;
    }
    .hero-thumb {
        width: 60px;
        height: 60px;
    }
}
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="photo-header">
        <div class="logo lens-gradient-text">LensCraft</div>
        <nav class="photo-nav">
            <button class="nav-btn active">Home</button>
            <button class="nav-btn">Portfolio</button>
            <button class="nav-btn">Services</button>
            <button class="nav-btn">Contact</button>
        </nav>
        <div class="nav-icons">
            <i class="fas fa-search"></i>
            <div class="cart-icon">
                <i class="fas fa-shopping-cart" onclick="toggleCart()"></i>
                <span class="cart-count">0</span>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="photo-hero">
        <div class="hero-overlay"></div>
        <div class="hero-content">
            <h1 class="lens-gradient-text">Frame Your Story</h1>
            <p>Artistic, modern, and timeless photography for every moment.</p>
            <button class="hero-btn">Book a Session</button>
        </div>
        <div class="hero-gallery">
            <img src="https://images.unsplash.com/photo-1516724562728-afc824a36e84?auto=format&fit=crop&w=400&q=80" alt="Wedding" class="hero-thumb">
            <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?auto=format&fit=crop&w=400&q=80" alt="Portrait" class="hero-thumb">
            <img src="https://images.unsplash.com/photo-1515187029135-18ee286d815b?auto=format&fit=crop&w=400&q=80" alt="Event" class="hero-thumb">
            <img src="https://images.unsplash.com/photo-1604754742629-0e5722a9e9c1?auto=format&fit=crop&w=400&q=80" alt="Product" class="hero-thumb">
        </div>
    </section>

    <!-- Services Timeline -->
    <section class="photo-timeline">
        <h2 class="lens-gradient-text">How We Work</h2>
        <div class="timeline-steps">
            <div class="timeline-step">
                <span class="step-icon"><i class="fas fa-calendar-check"></i></span>
                <span class="step-label">Book</span>
            </div>
            <div class="timeline-step">
                <span class="step-icon"><i class="fas fa-comments"></i></span>
                <span class="step-label">Consult</span>
            </div>
            <div class="timeline-step">
                <span class="step-icon"><i class="fas fa-camera-retro"></i></span>
                <span class="step-label">Shoot</span>
            </div>
            <div class="timeline-step">
                <span class="step-icon"><i class="fas fa-magic"></i></span>
                <span class="step-label">Edit</span>
            </div>
            <div class="timeline-step">
                <span class="step-icon"><i class="fas fa-gift"></i></span>
                <span class="step-label">Deliver</span>
            </div>
        </div>
    </section>

    <!-- Portfolio Section -->
    <section class="photo-portfolio">
        <h2 class="lens-gradient-text">Portfolio</h2>
        <div class="portfolio-masonry">
            <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80" alt="Portfolio 1" class="portfolio-img">
            <img src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=400&q=80" alt="Portfolio 2" class="portfolio-img">
            <img src="https://images.unsplash.com/photo-1465101178521-c1a9136a3b99?auto=format&fit=crop&w=400&q=80" alt="Portfolio 3" class="portfolio-img">
            <img src="https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=crop&w=400&q=80" alt="Portfolio 4" class="portfolio-img">
            <img src="https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=crop&w=400&q=80" alt="Portfolio 5" class="portfolio-img">
        </div>
    </section>

    <!-- Packages Section -->
    <section class="photo-packages">
        <h2 class="lens-gradient-text">Popular Packages</h2>
        <div class="package-scroll">
            <div class="package-card">
                <div class="package-title">Wedding Gold</div>
                <div class="package-price">₹35,000</div>
                <ul>
                    <li>Full Day Coverage</li>
                    <li>Photo Album</li>
                    <li>Online Gallery</li>
                </ul>
                <button class="package-btn">Book Now</button>
            </div>
            <div class="package-card">
                <div class="package-title">Portrait Pro</div>
                <div class="package-price">₹7,000</div>
                <ul>
                    <li>2 Hour Session</li>
                    <li>10 Edited Photos</li>
                    <li>Prints Included</li>
                </ul>
                <button class="package-btn">Book Now</button>
            </div>
            <div class="package-card">
                <div class="package-title">Event Basic</div>
                <div class="package-price">₹12,000</div>
                <ul>
                    <li>3 Hour Coverage</li>
                    <li>All Digital Photos</li>
                    <li>Express Delivery</li>
                </ul>
                <button class="package-btn">Book Now</button>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="photo-testimonials">
        <h2 class="lens-gradient-text">Client Stories</h2>
        <div class="testimonial-cards">
            <div class="testimonial-card">
                <div class="testimonial-img" style="background-image:url('https://randomuser.me/api/portraits/women/44.jpg');"></div>
                <div class="testimonial-content">
                    <p>"The photos were breathtaking! The team made us feel so comfortable."</p>
                    <span>- Aarti S.</span>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-img" style="background-image:url('https://randomuser.me/api/portraits/men/32.jpg');"></div>
                <div class="testimonial-content">
                    <p>"Professional, creative, and always on time. Highly recommended!"</p>
                    <span>- Rohan M.</span>
                </div>
            </div>
            <div class="testimonial-card">
                <div class="testimonial-img" style="background-image:url('https://randomuser.me/api/portraits/women/65.jpg');"></div>
                <div class="testimonial-content">
                    <p>"Captured our event perfectly. Will book again!"</p>
                    <span>- Priya K.</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Cart Section (Slide-Up) -->
    <section class="cart-section" id="cartSection">
        <h2>Your Bookings</h2>
        <div class="cart-items">
            <p style="text-align: center; color: #777; font-style: italic;">Your cart is empty. Start booking!</p>
        </div>
        <div class="cart-total">Total: ₹0</div>
        <button class="checkout-btn">Confirm Booking</button>
    </section>

    <!-- Footer -->
    <footer class="photo-footer">
        <div class="footer-lens">
            <svg width="100%" height="60" viewBox="0 0 1440 60" fill="none" xmlns="http://www.w3.org/2000/svg"><ellipse cx="720" cy="30" rx="720" ry="30" fill="#1f2c38"/></svg>
        </div>
        <p>&copy; 2025 LensCraft. Capturing your best moments.</p>
        <p style="margin-top: 8px;">Contact: +91 98765 43210 | <EMAIL></p>
    </footer>

    <script>
        function toggleCart() {
            const cartSection = document.getElementById('cartSection');
            cartSection.classList.toggle('active');
        }

        // Basic cart functionality for demo
        let cartCount = 0;
        let cartTotal = 0;
        document.querySelectorAll('.add-to-cart').forEach(button => {
            button.addEventListener('click', function() {
                const productCard = this.closest('.product-card');
                const productName = productCard.querySelector('.product-name').textContent;
                const productPriceStr = productCard.querySelector('.product-price').textContent;
                const productPrice = parseFloat(productPriceStr.replace('₹', '').replace(',', ''));
                
                // Update cart count
                cartCount++;
                document.querySelector('.cart-count').textContent = cartCount;
                
                // Update cart total
                cartTotal += productPrice;
                document.querySelector('.cart-total').textContent = `Total: ₹${cartTotal.toLocaleString('en-IN')}`;
                
                // Add item to cart UI
                const cartItems = document.querySelector('.cart-items');
                if (cartItems.querySelector('p')) {
                    cartItems.innerHTML = '';
                }
                const cartItem = document.createElement('div');
                cartItem.classList.add('cart-item');
                cartItem.innerHTML = `
                    <span class="cart-item-name">${productName}</span>
                    <span class="cart-item-price">${productPriceStr}</span>
                `;
                cartItems.appendChild(cartItem);
                
                // Show notification
                alert(`${productName} added to bookings!`);
            });
        });
    </script>
</body>
</html>
