<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cafe Corner - Your Cozy Coffee Place</title>
    <link href="https://fonts.googleapis.com/css2?family=Comfortaa:wght@300;400;500;600;700&family=Open+Sans:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Open Sans', sans-serif;
            line-height: 1.6;
            color: #3c2415;
            background: #f7f3f0;
            padding-bottom: 80px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Mobile Header */
        .mobile-header {
            background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
            color: #f7f3f0;
            padding: 16px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            box-shadow: 0 2px 15px rgba(139, 69, 19, 0.3);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            font-family: 'Comfortaa', cursive;
            font-size: 20px;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .order-status {
            background: rgba(247, 243, 240, 0.2);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        /* Desktop Header */
        .desktop-header {
            display: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(rgba(139, 69, 19, 0.7), rgba(160, 82, 45, 0.7)), url('https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80') center/cover;
            color: #f7f3f0;
            padding: 90px 0 50px;
            margin-top: 70px;
            text-align: center;
            position: relative;
            min-height: 60vh;
            display: flex;
            align-items: center;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(247, 243, 240, 0.2);
            border: 1px solid rgba(247, 243, 240, 0.3);
            color: #f7f3f0;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-family: 'Comfortaa', cursive;
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 16px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 16px;
            margin-bottom: 32px;
            opacity: 0.9;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-button {
            background: linear-gradient(135deg, #d2691e 0%, #cd853f 100%);
            color: white;
            padding: 16px 32px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 700;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 6px 20px rgba(210, 105, 30, 0.3);
            transition: all 0.3s ease;
        }

        .hero-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(210, 105, 30, 0.4);
        }

        /* Coffee Categories */
        .coffee-categories {
            padding: 50px 0;
            background: white;
        }

        .section-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .section-badge {
            display: inline-block;
            background: #8b4513;
            color: white;
            padding: 6px 16px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 16px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section-title {
            font-family: 'Comfortaa', cursive;
            font-size: 28px;
            font-weight: 700;
            color: #3c2415;
            margin-bottom: 12px;
        }

        .section-subtitle {
            font-size: 16px;
            color: #8b7355;
            max-width: 500px;
            margin: 0 auto;
        }

        .categories-scroll {
            display: flex;
            gap: 16px;
            overflow-x: auto;
            padding: 0 16px 20px;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .categories-scroll::-webkit-scrollbar {
            display: none;
        }

        .category-card {
            min-width: 140px;
            background: linear-gradient(135deg, #f4e4bc 0%, #e6d3a3 100%);
            border-radius: 20px;
            padding: 24px 16px;
            text-align: center;
            text-decoration: none;
            color: #3c2415;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
            border: 2px solid transparent;
        }

        .category-card:hover {
            transform: translateY(-6px);
            border-color: #8b4513;
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.2);
        }

        .category-icon {
            font-size: 28px;
            margin-bottom: 12px;
            display: block;
            color: #8b4513;
        }

        .category-name {
            font-family: 'Comfortaa', cursive;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .category-count {
            font-size: 12px;
            color: #8b7355;
        }

        /* Menu Items */
        .menu-items {
            padding: 50px 0;
            background: #f7f3f0;
        }

        .items-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .item-card {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.1);
            transition: all 0.3s ease;
            border: 1px solid #f0e6d2;
        }

        .item-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 30px rgba(139, 69, 19, 0.15);
        }

        .item-image {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .item-content {
            padding: 20px;
        }

        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .item-title {
            font-family: 'Comfortaa', cursive;
            font-size: 18px;
            font-weight: 600;
            color: #3c2415;
            margin-bottom: 6px;
        }

        .item-rating {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: #d2691e;
            font-weight: 600;
        }

        .item-price {
            font-family: 'Comfortaa', cursive;
            font-size: 20px;
            font-weight: 700;
            color: #8b4513;
        }

        .item-description {
            font-size: 14px;
            color: #8b7355;
            margin-bottom: 16px;
            line-height: 1.5;
        }

        .item-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .item-tags {
            display: flex;
            gap: 6px;
        }

        .tag {
            background: #f4e4bc;
            color: #8b4513;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
        }

        .tag.hot {
            background: #ffcccb;
            color: #d2691e;
        }

        .tag.cold {
            background: #e0f6ff;
            color: #4682b4;
        }

        .order-btn {
            background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .order-btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #f0e6d2;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 15px rgba(139, 69, 19, 0.1);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            text-decoration: none;
            color: #8b7355;
            transition: color 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: #8b4513;
        }

        .nav-item i {
            font-size: 18px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 600;
        }

        /* WhatsApp Chat Button */
        .whatsapp-chat {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: #25d366;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.3);
            z-index: 999;
            transition: transform 0.2s ease;
        }

        .whatsapp-chat:hover {
            transform: scale(1.1);
        }

        /* Desktop Styles */
        @media (min-width: 768px) {
            body {
                padding-bottom: 0;
            }

            .mobile-header {
                display: none;
            }

            .desktop-header {
                display: block;
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(20px);
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                border-bottom: 1px solid #f0e6d2;
                box-shadow: 0 2px 15px rgba(139, 69, 19, 0.1);
            }

            .desktop-nav {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 16px;
                max-width: 1200px;
                margin: 0 auto;
            }

            .desktop-logo {
                font-family: 'Comfortaa', cursive;
                font-size: 24px;
                font-weight: 700;
                color: #8b4513;
                text-decoration: none;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .desktop-menu {
                display: flex;
                gap: 32px;
                list-style: none;
            }

            .desktop-menu a {
                text-decoration: none;
                color: #3c2415;
                font-weight: 500;
                transition: color 0.3s ease;
            }

            .desktop-menu a:hover {
                color: #8b4513;
            }

            .hero {
                margin-top: 80px;
                padding: 100px 0 60px;
                min-height: 70vh;
            }

            .hero-title {
                font-size: 48px;
            }

            .hero-subtitle {
                font-size: 18px;
            }

            .categories-scroll {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
                gap: 20px;
                padding: 0;
                overflow: visible;
            }

            .items-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 24px;
            }

            .bottom-nav {
                display: none;
            }

            .whatsapp-chat {
                bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Mobile Header -->
    <header class="mobile-header">
        <div class="header-content">
            <div class="logo">
                <i class="fas fa-coffee"></i>
                Cafe Corner
            </div>
            <div class="order-status">
                <i class="fas fa-clock"></i>
                <span>Open</span>
            </div>
        </div>
    </header>

    <!-- Desktop Header -->
    <header class="desktop-header">
        <nav class="desktop-nav">
            <a href="#" class="desktop-logo">
                <i class="fas fa-coffee"></i>
                Cafe Corner
            </a>
            <ul class="desktop-menu">
                <li><a href="#home">होम</a></li>
                <li><a href="#menu">मेन्यू</a></li>
                <li><a href="#about">हमारे बारे में</a></li>
                <li><a href="#events">इवेंट्स</a></li>
                <li><a href="#contact">संपर्क</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-heart"></i>
                    Fresh Brewed Daily
                </div>
                <h1 class="hero-title">
                    Your Cozy<br>
                    Coffee Corner
                </h1>
                <p class="hero-subtitle">
                    ताज़ी पिसी कॉफी, गर्म माहौल, और दोस्तों के साथ बिताने के लिए 
                    परफेक्ट जगह। आइए, कॉफी का मजा लें।
                </p>
                <a href="#menu" class="hero-button">
                    <i class="fas fa-mug-hot"></i>
                    कॉफी मेन्यू देखें
                </a>
            </div>
        </div>
    </section>

    <!-- Coffee Categories -->
    <section class="coffee-categories">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Coffee Collection</div>
                <h2 class="section-title">हमारी कॉफी रेंज</h2>
                <p class="section-subtitle">
                    दुनिया भर की बेहतरीन कॉफी बीन्स से बनी स्पेशल कॉफी
                </p>
            </div>

            <div class="categories-scroll">
                <a href="#menu" class="category-card">
                    <i class="fas fa-mug-hot category-icon"></i>
                    <div class="category-name">हॉट कॉफी</div>
                    <div class="category-count">12 वैरायटी</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-glass-whiskey category-icon"></i>
                    <div class="category-name">कोल्ड कॉफी</div>
                    <div class="category-count">8 वैरायटी</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-blender category-icon"></i>
                    <div class="category-name">फ्रैप्पे</div>
                    <div class="category-count">6 वैरायटी</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-cookie-bite category-icon"></i>
                    <div class="category-name">स्नैक्स</div>
                    <div class="category-count">15 आइटम्स</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-birthday-cake category-icon"></i>
                    <div class="category-name">डेजर्ट</div>
                    <div class="category-count">10 आइटम्स</div>
                </a>

                <a href="#menu" class="category-card">
                    <i class="fas fa-leaf category-icon"></i>
                    <div class="category-name">हर्बल टी</div>
                    <div class="category-count">7 वैरायटी</div>
                </a>
            </div>
        </div>
    </section>

    <!-- Menu Items -->
    <section class="menu-items" id="menu">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">Today's Special</div>
                <h2 class="section-title">आज के स्पेशल</h2>
                <p class="section-subtitle">
                    बैरिस्टा की पसंदीदा कॉफी और स्नैक्स
                </p>
            </div>

            <div class="items-grid">
                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1509042239860-f550ce710b93?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Cappuccino" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">क्लासिक कैप्पुचीनो</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.8 (95)</span>
                                </div>
                            </div>
                            <div class="item-price">₹120</div>
                        </div>
                        <p class="item-description">
                            रिच एस्प्रेसो, स्टीम्ड मिल्क और वेल्वेटी फोम का परफेक्ट बैलेंस
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag hot">हॉट</span>
                                <span class="tag">क्लासिक</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('क्लासिक कैप्पुचीनो', 120)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1461023058943-07fcbe16d735?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Latte" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">वनीला लट्टे</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (120)</span>
                                </div>
                            </div>
                            <div class="item-price">₹140</div>
                        </div>
                        <p class="item-description">
                            क्रीमी मिल्क, एस्प्रेसो और वनीला सिरप का मीठा कॉम्बिनेशन
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag hot">हॉट</span>
                                <span class="tag">स्वीट</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('वनीला लट्टे', 140)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1517701604599-bb29b565090c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Cold Coffee" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">आइस्ड कैरामेल कॉफी</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.7 (85)</span>
                                </div>
                            </div>
                            <div class="item-price">₹160</div>
                        </div>
                        <p class="item-description">
                            ठंडी कॉफी, आइस क्यूब्स और कैरामेल सिरप के साथ रिफ्रेशिंग ड्रिंक
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag cold">कोल्ड</span>
                                <span class="tag">रिफ्रेशिंग</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('आइस्ड कैरामेल कॉफी', 160)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1572442388796-11668a67e53d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Croissant" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">बटर क्रॉइसैंट</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.6 (70)</span>
                                </div>
                            </div>
                            <div class="item-price">₹80</div>
                        </div>
                        <p class="item-description">
                            फ्रेश बेक्ड, फ्लेकी और बटरी क्रॉइसैंट - कॉफी के साथ परफेक्ट
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag">फ्रेश बेक्ड</span>
                                <span class="tag">बटरी</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('बटर क्रॉइसैंट', 80)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Cheesecake" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">न्यूयॉर्क चीज़केक</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.9 (110)</span>
                                </div>
                            </div>
                            <div class="item-price">₹180</div>
                        </div>
                        <p class="item-description">
                            क्रीमी, रिच चीज़केक ब्लूबेरी टॉपिंग के साथ - डेजर्ट लवर्स के लिए
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag">डेजर्ट</span>
                                <span class="tag">क्रीमी</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('न्यूयॉर्क चीज़केक', 180)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>

                <div class="item-card">
                    <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="Green Tea" class="item-image">
                    <div class="item-content">
                        <div class="item-header">
                            <div>
                                <h3 class="item-title">हनी ग्रीन टी</h3>
                                <div class="item-rating">
                                    <i class="fas fa-star"></i>
                                    <span>4.5 (60)</span>
                                </div>
                            </div>
                            <div class="item-price">₹90</div>
                        </div>
                        <p class="item-description">
                            ऑर्गेनिक ग्रीन टी, नेचुरल हनी और लेमन के साथ हेल्दी ऑप्शन
                        </p>
                        <div class="item-footer">
                            <div class="item-tags">
                                <span class="tag">हेल्दी</span>
                                <span class="tag">ऑर्गेनिक</span>
                            </div>
                            <button class="order-btn" onclick="orderCoffee('हनी ग्रीन टी', 90)">
                                ऑर्डर करें
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#home" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>होम</span>
            </a>
            <a href="#menu" class="nav-item">
                <i class="fas fa-coffee"></i>
                <span>मेन्यू</span>
            </a>
            <a href="#about" class="nav-item">
                <i class="fas fa-info-circle"></i>
                <span>हमारे बारे में</span>
            </a>
            <a href="#events" class="nav-item">
                <i class="fas fa-calendar-alt"></i>
                <span>इवेंट्स</span>
            </a>
            <a href="#contact" class="nav-item">
                <i class="fas fa-phone"></i>
                <span>संपर्क</span>
            </a>
        </div>
    </div>

    <!-- WhatsApp Chat Button -->
    <a href="https://wa.me/919876543210?text=नमस्ते! मैं Cafe Corner से कॉफी ऑर्डर करना चाहता हूं।" class="whatsapp-chat" target="_blank">
        <i class="fab fa-whatsapp"></i>
    </a>

    <script>
        // Order coffee function
        function orderCoffee(itemName, price) {
            const message = `☕ *Cafe Corner - कॉफी ऑर्डर*

🍵 *आइटम:* ${itemName}
💰 *प्राइस:* ₹${price}

कृपया इस ऑर्डर को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showCozyNotification(`${itemName} ऑर्डर में जोड़ा गया! ☕`);
        }

        // Show cozy notification
        function showCozyNotification(message) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 90px;
                left: 50%;
                transform: translateX(-50%);
                background: linear-gradient(135deg, #8b4513, #a0522d);
                color: #f7f3f0;
                padding: 14px 28px;
                border-radius: 25px;
                font-family: 'Comfortaa', cursive;
                font-weight: 600;
                font-size: 14px;
                z-index: 2000;
                box-shadow: 0 6px 25px rgba(139, 69, 19, 0.3);
                border: 2px solid #d2691e;
                animation: cozyBounce 0.5s ease;
            `;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes cozyBounce {
                    0% {
                        transform: translateX(-50%) translateY(-30px) scale(0.8);
                        opacity: 0;
                    }
                    50% {
                        transform: translateX(-50%) translateY(5px) scale(1.05);
                        opacity: 1;
                    }
                    100% {
                        transform: translateX(-50%) translateY(0) scale(1);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'cozyBounce 0.5s ease reverse';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 500);
                }
            }, 3000);
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Smooth scroll to section
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Smooth scrolling for all anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 70;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add cozy hover effects
        document.querySelectorAll('.item-card').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-6px) scale(1.02)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Category card hover effects
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-8px) rotate(2deg)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) rotate(0deg)';
            });
        });
    </script>
</body>
</html>
