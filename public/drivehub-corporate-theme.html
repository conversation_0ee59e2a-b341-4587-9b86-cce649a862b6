<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DriveHub - Corporate Car Rental Solutions</title>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #1e40af;
            --secondary-color: #0f172a;
            --accent-color: #059669;
            --warning-color: #d97706;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-light: #64748b;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #e2e8f0;
            --border-color: #e2e8f0;
            --border-dark: #cbd5e1;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'IBM Plex Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-weight: 400;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-md);
            border-bottom-color: var(--border-dark);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .logo i {
            font-size: 1.75rem;
            color: var(--primary-color);
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.2s ease;
            letter-spacing: 0.025em;
            padding: 0.5rem 0;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-cta {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            letter-spacing: 0.025em;
        }

        .nav-cta:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.25rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            padding: 8rem 0 6rem;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
            position: relative;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(30,64,175,0.05)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,117.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .hero-text {
            max-width: 500px;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 2rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 3.5rem);
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
            letter-spacing: -0.025em;
        }

        .hero-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: 2.5rem;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 0.375rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-dark);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-color);
        }

        .hero-image {
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .hero-visual {
            width: 100%;
            max-width: 500px;
            height: 300px;
            background: linear-gradient(135deg, var(--primary-color), #3b82f6);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            box-shadow: var(--shadow-xl);
        }

        /* Stats Section */
        .stats {
            padding: 4rem 0;
            background: var(--primary-color);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            padding: 1rem;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 0.875rem;
            opacity: 0.9;
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: var(--bg-secondary);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-badge {
            display: inline-block;
            background: var(--accent-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 1rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
            letter-spacing: -0.025em;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 0.75rem;
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            border-color: var(--border-dark);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: var(--bg-secondary);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            font-size: 0.875rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                padding: 6rem 0 4rem;
            }

            .hero-content {
                grid-template-columns: 1fr;
                gap: 2rem;
                text-align: center;
            }

            .hero-buttons {
                justify-content: center;
            }

            .btn {
                justify-content: center;
                min-width: 200px;
            }

            .features {
                padding: 4rem 0;
            }

            .features-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card {
                padding: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 0.75rem;
            }

            .nav-container {
                padding: 1rem 0;
            }

            .hero-title {
                font-size: 2rem;
            }

            .section-title {
                font-size: 1.75rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="logo">
                    <i class="fas fa-building"></i>
                    DriveHub
                </a>
                
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">होम</a></li>
                    <li><a href="#services" class="nav-link">सर्विसेज</a></li>
                    <li><a href="#solutions" class="nav-link">सॉल्यूशन्स</a></li>
                    <li><a href="#about" class="nav-link">कंपनी</a></li>
                    <li><a href="#contact" class="nav-link">संपर्क</a></li>
                </ul>
                
                <a href="#booking" class="nav-cta">कॉर्पोरेट बुकिंग</a>
                
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <div class="hero-badge">
                        <i class="fas fa-award"></i>
                        एंटरप्राइज सॉल्यूशन
                    </div>
                    
                    <h1 class="hero-title">
                        कॉर्पोरेट मोबिलिटी<br>
                        सॉल्यूशन्स
                    </h1>
                    
                    <p class="hero-subtitle">
                        बिजनेस के लिए प्रोफेशनल कार रेंटल सर्विस। 
                        एंटरप्राइज ग्रेड सिक्योरिटी, फ्लीट मैनेजमेंट, और 24/7 सपोर्ट।
                    </p>
                    
                    <div class="hero-buttons">
                        <a href="#booking" class="btn btn-primary">
                            <i class="fas fa-briefcase"></i>
                            कॉर्पोरेट प्लान देखें
                        </a>
                        <a href="#solutions" class="btn btn-secondary">
                            <i class="fas fa-chart-line"></i>
                            केस स्टडी
                        </a>
                    </div>
                </div>
                
                <div class="hero-image">
                    <div class="hero-visual">
                        <i class="fas fa-car-side"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">कॉर्पोरेट क्लाइंट्स</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50K+</span>
                    <span class="stat-label">मंथली ट्रिप्स</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.8%</span>
                    <span class="stat-label">अपटाइम गारंटी</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">डेडिकेटेड सपोर्ट</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">एंटरप्राइज फीचर्स</div>
                <h2 class="section-title">बिजनेस के लिए बनाया गया</h2>
                <p class="section-subtitle">
                    कॉर्पोरेट जरूरतों को ध्यान में रखकर डिजाइन किए गए फीचर्स
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">एंटरप्राइज सिक्योरिटी</h3>
                    <p class="feature-description">
                        ISO 27001 सर्टिफाइड सिक्योरिटी, एंड-टू-एंड एन्क्रिप्शन, और कॉम्प्लायंस गारंटी।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3 class="feature-title">रियल-टाइम एनालिटिक्स</h3>
                    <p class="feature-description">
                        डिटेल्ड रिपोर्टिंग, कॉस्ट एनालिसिस, और परफॉर्मेंस मेट्रिक्स के साथ डैशबोर्ड।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <h3 class="feature-title">टीम मैनेजमेंट</h3>
                    <p class="feature-description">
                        एम्प्लॉई एक्सेस कंट्रोल, अप्रूवल वर्कफ्लो, और सेंट्रलाइज्ड बिलिंग सिस्टम।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <h3 class="feature-title">API इंटीग्रेशन</h3>
                    <p class="feature-description">
                        आपके एक्जिस्टिंग सिस्टम्स के साथ सीमलेस इंटीग्रेशन। ERP, HRMS, और अकाउंटिंग सॉफ्टवेयर सपोर्ट।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">डेडिकेटेड सपोर्ट</h3>
                    <p class="feature-description">
                        24/7 प्राइऑरिटी सपोर्ट, डेडिकेटेड अकाउंट मैनेजर, और इमरजेंसी असिस्टेंस।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <h3 class="feature-title">कॉस्ट ऑप्टिमाइजेशन</h3>
                    <p class="feature-description">
                        स्मार्ट रूटिंग, फ्यूल एफिशिएंसी ट्रैकिंग, और ऑटोमेटेड कॉस्ट कंट्रोल सिस्टम।
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services" style="padding: 6rem 0; background: white;">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">कॉर्पोरेट सर्विसेज</div>
                <h2 class="section-title">बिजनेस सॉल्यूशन्स</h2>
                <p class="section-subtitle">
                    हर साइज की कंपनी के लिए कस्टमाइज्ड मोबिलिटी सॉल्यूशन्स
                </p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem;">
                <div style="background: white; border: 2px solid var(--border-color); padding: 2.5rem; border-radius: 0.75rem; transition: all 0.2s ease; position: relative;">
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--primary-color); border-radius: 0.75rem 0.75rem 0 0;"></div>
                    <div style="width: 3rem; height: 3rem; background: var(--bg-secondary); border-radius: 0.75rem; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.25rem; color: var(--primary-color);">
                        <i class="fas fa-building"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-primary);">स्टार्टअप पैकेज</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6; font-size: 0.875rem;">छोटी कंपनियों के लिए बेसिक मोबिलिटी सॉल्यूशन</p>
                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">₹15,000/महीना</div>
                    <ul style="list-style: none; margin-bottom: 2rem;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> 5 कारें तक</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> बेसिक रिपोर्टिंग</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> ईमेल सपोर्ट</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> मंथली बिलिंग</li>
                    </ul>
                    <button style="background: var(--primary-color); color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.375rem; font-weight: 600; cursor: pointer; width: 100%; transition: all 0.2s ease;">प्लान चुनें</button>
                </div>

                <div style="background: white; border: 2px solid var(--primary-color); padding: 2.5rem; border-radius: 0.75rem; transition: all 0.2s ease; position: relative; box-shadow: var(--shadow-lg);">
                    <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%); background: var(--primary-color); color: white; padding: 0.25rem 1rem; border-radius: 1rem; font-size: 0.75rem; font-weight: 600;">पॉपुलर</div>
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--primary-color); border-radius: 0.75rem 0.75rem 0 0;"></div>
                    <div style="width: 3rem; height: 3rem; background: var(--primary-color); border-radius: 0.75rem; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.25rem; color: white;">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-primary);">ग्रोथ पैकेज</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6; font-size: 0.875rem;">मीडियम साइज कंपनियों के लिए एडवांस्ड फीचर्स</p>
                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">₹35,000/महीना</div>
                    <ul style="list-style: none; margin-bottom: 2rem;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> 20 कारें तक</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> एडवांस्ड एनालिटिक्स</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> 24/7 फोन सपोर्ट</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> API एक्सेस</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> कस्टम रिपोर्ट्स</li>
                    </ul>
                    <button style="background: var(--primary-color); color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.375rem; font-weight: 600; cursor: pointer; width: 100%; transition: all 0.2s ease;">प्लान चुनें</button>
                </div>

                <div style="background: white; border: 2px solid var(--border-color); padding: 2.5rem; border-radius: 0.75rem; transition: all 0.2s ease; position: relative;">
                    <div style="position: absolute; top: 0; left: 0; right: 0; height: 4px; background: var(--warning-color); border-radius: 0.75rem 0.75rem 0 0;"></div>
                    <div style="width: 3rem; height: 3rem; background: var(--bg-secondary); border-radius: 0.75rem; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.25rem; color: var(--warning-color);">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 style="font-size: 1.25rem; font-weight: 600; margin-bottom: 1rem; color: var(--text-primary);">एंटरप्राइज पैकेज</h3>
                    <p style="color: var(--text-secondary); margin-bottom: 1.5rem; line-height: 1.6; font-size: 0.875rem;">बड़ी कंपनियों के लिए कम्प्लीट सॉल्यूशन</p>
                    <div style="font-size: 1.5rem; font-weight: 700; color: var(--warning-color); margin-bottom: 1rem;">कस्टम प्राइसिंग</div>
                    <ul style="list-style: none; margin-bottom: 2rem;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> अनलिमिटेड कारें</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> डेडिकेटेड अकाउंट मैनेजर</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> कस्टम इंटीग्रेशन</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> SLA गारंटी</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem; color: var(--text-secondary); font-size: 0.875rem;"><i class="fas fa-check" style="color: var(--accent-color); font-size: 0.75rem;"></i> ऑन-साइट सपोर्ट</li>
                    </ul>
                    <button style="background: var(--warning-color); color: white; padding: 0.75rem 1.5rem; border: none; border-radius: 0.375rem; font-weight: 600; cursor: pointer; width: 100%; transition: all 0.2s ease;">संपर्क करें</button>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking" style="padding: 6rem 0; background: var(--bg-secondary);">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">कॉर्पोरेट बुकिंग</div>
                <h2 class="section-title">बिजनेस रिक्वेस्ट सबमिट करें</h2>
                <p class="section-subtitle">
                    हमारे एक्सपर्ट्स आपकी कंपनी की जरूरतों के हिसाब से कस्टम सॉल्यूशन तैयार करेंगे
                </p>
            </div>

            <div style="background: white; padding: 3rem; border-radius: 0.75rem; border: 1px solid var(--border-color); max-width: 800px; margin: 0 auto; box-shadow: var(--shadow-lg);">
                <form id="corporateForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">कंपनी का नाम *</label>
                            <input type="text" id="companyName" name="companyName" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="आपकी कंपनी का नाम" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">कॉन्टैक्ट पर्सन *</label>
                            <input type="text" id="contactPerson" name="contactPerson" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="आपका पूरा नाम" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">ईमेल एड्रेस *</label>
                            <input type="email" id="email" name="email" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="<EMAIL>" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">फोन नंबर *</label>
                            <input type="tel" id="phone" name="phone" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="+91 98765 43210" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">कंपनी साइज *</label>
                            <select id="companySize" name="companySize" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                                <option value="">कंपनी साइज चुनें</option>
                                <option value="startup">स्टार्टअप (1-50 एम्प्लॉईज)</option>
                                <option value="medium">मीडियम (51-500 एम्प्लॉईज)</option>
                                <option value="large">लार्ज (500+ एम्प्लॉईज)</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">मंथली कार रिक्वायरमेंट</label>
                            <select id="carRequirement" name="carRequirement" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;">
                                <option value="">अप्रॉक्सिमेट नंबर</option>
                                <option value="1-5">1-5 कारें</option>
                                <option value="6-20">6-20 कारें</option>
                                <option value="21-50">21-50 कारें</option>
                                <option value="50+">50+ कारें</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">इंडस्ट्री</label>
                            <select id="industry" name="industry" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;">
                                <option value="">इंडस्ट्री चुनें</option>
                                <option value="it">IT/Software</option>
                                <option value="finance">Finance/Banking</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="consulting">Consulting</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">प्रेफर्ड स्टार्ट डेट</label>
                            <input type="date" id="startDate" name="startDate" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;">
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; margin-bottom: 2rem;">
                        <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">स्पेसिफिक रिक्वायरमेंट्स</label>
                        <textarea id="requirements" name="requirements" rows="4" style="padding: 0.75rem; border: 1px solid var(--border-dark); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease; resize: vertical;" placeholder="आपकी कंपनी की स्पेसिफिक जरूरतें, बजट, या कोई खास रिक्वायरमेंट्स..."></textarea>
                    </div>

                    <button type="submit" style="background: var(--primary-color); color: white; padding: 1rem 2rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease; width: 100%; letter-spacing: 0.025em;">
                        <i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>
                        कॉर्पोरेट रिक्वेस्ट सबमिट करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--secondary-color); color: white; padding: 3rem 0 2rem;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div>
                    <h4 style="font-size: 1.5rem; font-weight: 700; margin-bottom: 1rem; color: white;">DriveHub</h4>
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6; font-size: 0.875rem;">
                        एंटरप्राइज ग्रेड कार रेंटल सॉल्यूशन्स। बिजनेस की जरूरतों के लिए डिजाइन किया गया।
                    </p>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem; color: rgba(255, 255, 255, 0.9);">सॉल्यूशन्स</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">फ्लीट मैनेजमेंट</a>
                        <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">एम्प्लॉई ट्रांसपोर्ट</a>
                        <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">क्लाइंट विजिट्स</a>
                        <a href="#" style="color: rgba(255, 255, 255, 0.7); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">इवेंट ट्रांसपोर्ट</a>
                    </div>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem; color: rgba(255, 255, 255, 0.9);">सपोर्ट</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem;">📞 +91 98765 43210</span>
                        <span style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem;">📧 <EMAIL></span>
                        <span style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem;">🕒 24/7 सपोर्ट</span>
                        <span style="color: rgba(255, 255, 255, 0.7); font-size: 0.875rem;">📍 पैन इंडिया सर्विस</span>
                    </div>
                </div>
            </div>

            <div style="text-align: center; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.2); color: rgba(255, 255, 255, 0.6); font-size: 0.875rem;">
                <p>&copy; 2024 DriveHub Corporate Solutions. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <button onclick="openCorporateChat()" style="position: fixed; bottom: 2rem; right: 2rem; width: 3.5rem; height: 3.5rem; background: var(--primary-color); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; cursor: pointer; box-shadow: var(--shadow-lg); transition: all 0.2s ease; z-index: 1000; border: none;">
        <i class="fas fa-headset"></i>
    </button>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const startDate = document.getElementById('startDate');

            if (startDate) startDate.min = today;

            // Add professional focus styles
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = 'var(--primary-color)';
                    this.style.outline = 'none';
                    this.style.boxShadow = '0 0 0 3px rgba(30, 64, 175, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.style.borderColor = 'var(--border-dark)';
                    this.style.boxShadow = 'none';
                });
            });
        });

        // Corporate form submission
        document.getElementById('corporateForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.companyName || !data.contactPerson || !data.email || !data.phone || !data.companySize) {
                showProfessionalAlert('कृपया सभी आवश्यक फील्ड भरें।', 'warning');
                return;
            }

            const message = `🏢 *DriveHub - कॉर्पोरेट इंक्वायरी*

🏢 *कंपनी:* ${data.companyName}
👤 *कॉन्टैक्ट पर्सन:* ${data.contactPerson}
📧 *ईमेल:* ${data.email}
📱 *फोन:* ${data.phone}

📊 *कंपनी साइज:* ${data.companySize}
🚗 *कार रिक्वायरमेंट:* ${data.carRequirement || 'नहीं बताया'}
🏭 *इंडस्ट्री:* ${data.industry || 'नहीं बताया'}
📅 *स्टार्ट डेट:* ${data.startDate || 'फ्लेक्सिबल'}

📝 *रिक्वायरमेंट्स:* ${data.requirements || 'कोई स्पेसिफिक रिक्वायरमेंट नहीं'}

कृपया इस कॉर्पोरेट इंक्वायरी को प्राइऑरिटी दें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showProfessionalAlert('आपकी कॉर्पोरेट रिक्वेस्ट सफलतापूर्वक सबमिट हो गई है। हमारी टीम 24 घंटे में आपसे संपर्क करेगी।', 'success');
            this.reset();
        });

        function openCorporateChat() {
            const message = '🏢 नमस्ते! मैं DriveHub के कॉर्पोरेट मोबिलिटी सॉल्यूशन्स के बारे में जानकारी चाहता/चाहती हूँ।';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function showProfessionalAlert(message, type = 'info') {
            const colors = {
                'success': 'var(--accent-color)',
                'warning': 'var(--warning-color)',
                'info': 'var(--primary-color)'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.375rem;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                font-weight: 600;
                max-width: 400px;
                font-size: 0.875rem;
                border-left: 4px solid rgba(255, 255, 255, 0.3);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Professional button hover effects
        document.querySelectorAll('button, .btn').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
