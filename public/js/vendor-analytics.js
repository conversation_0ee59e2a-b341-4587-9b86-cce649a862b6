// Vendor Analytics JavaScript
console.log('Loading vendor analytics...');

// Define the analytics data and methods
window.vendorAnalyticsComponent = function() {
    return {
        loading: true,
        timeRange: 'day',
        chartMetric: 'revenue',
        analyticsData: {
            revenue: 245680,
            revenueChange: '+15.2%',
            orders: 1234,
            ordersChange: '+8.5%',
            customers: 89,
            customersChange: '+12.3%',
            conversionRate: 3.2,
            conversionChange: '-0.5%'
        },
        revenueChart: null,
        chartData: {},

        init() {
            console.log('=== ANALYTICS INITIALIZATION ===');
            console.log('Chart.js available:', typeof Chart !== 'undefined');
            
            this.generateChartData();
            this.loading = false;
            
            this.$nextTick(() => {
                setTimeout(() => {
                    console.log('Attempting to render charts...');
                    this.renderRevenueChart();
                }, 300);
            });
        },

        setTimeRange(range) {
            this.timeRange = range;
            document.querySelectorAll('.date-range-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            this.fetchAnalyticsData();
        },

        setChartMetric(metric) {
            this.chartMetric = metric;
            setTimeout(() => {
                this.renderRevenueChart();
            }, 50);
        },

        getChartTitle() {
            const titles = {
                revenue: 'Revenue Trends',
                orders: 'Orders Trends', 
                customers: 'Customer Trends'
            };
            return titles[this.chartMetric] || 'Revenue Trends';
        },

        fetchAnalyticsData() {
            this.loading = true;
            setTimeout(() => {
                this.generateChartData();
                this.loading = false;
                this.$nextTick(() => {
                    this.renderRevenueChart();
                });
            }, 500);
        },

        generateChartData() {
            console.log('Generating chart data for timeRange:', this.timeRange);
            
            switch(this.timeRange) {
                case 'day':
                    this.chartData = this.generateHourlyData();
                    break;
                case 'week':
                    this.chartData = this.generateWeeklyData();
                    break;
                case 'month':
                    this.chartData = this.generateMonthlyData();
                    break;
                case 'quarter':
                    this.chartData = this.generateQuarterlyData();
                    break;
                case 'year':
                    this.chartData = this.generateYearlyData();
                    break;
                default:
                    this.chartData = this.generateMonthlyData();
            }
            
            console.log('Generated chart data:', this.chartData);
        },

        generateHourlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];
            
            for (let i = 0; i < 24; i++) {
                labels.push(`${i.toString().padStart(2, '0')}:00`);
                
                let baseRevenue = 5000;
                if (i >= 9 && i <= 17) baseRevenue = 15000;
                if (i >= 19 && i <= 22) baseRevenue = 12000;
                
                revenueData.push(baseRevenue + Math.random() * 8000);
                ordersData.push(Math.floor((baseRevenue / 500) + Math.random() * 20));
                customersData.push(Math.floor((baseRevenue / 1000) + Math.random() * 10));
            }
            
            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateWeeklyData() {
            const labels = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];
            
            labels.forEach((day, index) => {
                let baseRevenue = 45000;
                if (index === 0 || index === 6) baseRevenue = 35000;
                if (index === 5) baseRevenue = 55000;
                
                revenueData.push(baseRevenue + Math.random() * 20000);
                ordersData.push(Math.floor((baseRevenue / 300) + Math.random() * 50));
                customersData.push(Math.floor((baseRevenue / 800) + Math.random() * 30));
            });
            
            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateMonthlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];
            
            for (let i = 1; i <= 30; i++) {
                labels.push(`${i}`);
                
                let baseRevenue = 35000;
                if (i <= 5 || i >= 25) baseRevenue = 45000;
                if (i >= 15 && i <= 20) baseRevenue = 30000;
                
                revenueData.push(baseRevenue + Math.random() * 25000);
                ordersData.push(Math.floor((baseRevenue / 250) + Math.random() * 60));
                customersData.push(Math.floor((baseRevenue / 700) + Math.random() * 40));
            }
            
            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateQuarterlyData() {
            const labels = [];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];
            
            for (let i = 1; i <= 13; i++) {
                labels.push(`Week ${i}`);
                
                let baseRevenue = 280000;
                if (i <= 3) baseRevenue = 320000;
                if (i >= 11) baseRevenue = 350000;
                
                revenueData.push(baseRevenue + Math.random() * 100000);
                ordersData.push(Math.floor((baseRevenue / 200) + Math.random() * 200));
                customersData.push(Math.floor((baseRevenue / 600) + Math.random() * 150));
            }
            
            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        generateYearlyData() {
            const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const revenueData = [];
            const ordersData = [];
            const customersData = [];
            
            labels.forEach((month, index) => {
                let baseRevenue = 1200000;
                if (index >= 10) baseRevenue = 1500000;
                if (index >= 3 && index <= 5) baseRevenue = 1350000;
                if (index >= 6 && index <= 8) baseRevenue = 1100000;
                
                revenueData.push(baseRevenue + Math.random() * 400000);
                ordersData.push(Math.floor((baseRevenue / 800) + Math.random() * 500));
                customersData.push(Math.floor((baseRevenue / 2000) + Math.random() * 300));
            });
            
            return {
                labels,
                datasets: {
                    revenue: revenueData,
                    orders: ordersData,
                    customers: customersData
                }
            };
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 0
            }).format(amount);
        },

        renderRevenueChart() {
            console.log('=== CHART RENDERING DEBUG ===');
            
            const ctx = document.getElementById('revenueChart');
            if (!ctx) {
                console.error('Chart canvas not found!');
                return;
            }

            if (typeof Chart === 'undefined') {
                console.error('Chart.js is not loaded!');
                return;
            }

            if (this.revenueChart) {
                this.revenueChart.destroy();
                this.revenueChart = null;
            }

            const metricConfig = this.getMetricConfig();
            const chartData = this.chartData.datasets?.[this.chartMetric] || [];
            
            if (!chartData.length) {
                console.warn('No chart data available');
                return;
            }

            try {
                this.revenueChart = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.chartData.labels || [],
                        datasets: [{
                            label: metricConfig.label,
                            data: chartData,
                            borderColor: metricConfig.color,
                            backgroundColor: metricConfig.color + '20',
                            fill: true,
                            tension: 0.4,
                            borderWidth: 3,
                            pointBackgroundColor: metricConfig.color,
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 6
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: { color: 'rgba(0, 0, 0, 0.05)' }
                            },
                            x: {
                                grid: { display: false }
                            }
                        }
                    }
                });
                
                console.log('Chart created successfully');
            } catch (error) {
                console.error('Error creating chart:', error);
            }
        },

        getMetricConfig() {
            const configs = {
                revenue: {
                    label: 'Revenue',
                    color: '#66BB6A'
                },
                orders: {
                    label: 'Orders',
                    color: '#3B82F6'
                },
                customers: {
                    label: 'Customers',
                    color: '#F59E0B'
                }
            };
            return configs[this.chartMetric] || configs.revenue;
        },


    };
};

console.log('Vendor analytics component loaded');
