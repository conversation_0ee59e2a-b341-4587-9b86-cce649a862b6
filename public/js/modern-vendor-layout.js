/**
 * Modern Vendor Layout JavaScript
 * Handles all interactive functionality for the modern vendor dashboard
 */

// Global vendor app state
window.vendorApp = {
    isLoading: true,
    darkMode: localStorage.getItem('vendor-dark-mode') === 'true',
    sidebarCollapsed: localStorage.getItem('vendor-sidebar-collapsed') === 'true',
    mobileSidebarOpen: false,
    notifications: [],
    user: null
};

/**
 * Modern Vendor App Alpine.js Component
 */
function modernVendorApp() {
    return {
        // State Management
        isLoading: true,
        darkMode: localStorage.getItem('vendor-dark-mode') === 'true',
        sidebarCollapsed: localStorage.getItem('vendor-sidebar-collapsed') === 'true',
        mobileSidebarOpen: false,

        // Initialization
        initializeApp() {
            console.log('🚀 Initializing Modern Vendor Dashboard...');
            
            // Apply theme
            this.applyTheme();

            // Initialize responsive behavior
            this.initializeResponsive();

            // Initialize animations
            this.initializeAnimations();

            // Initialize keyboard shortcuts
            this.initializeKeyboardShortcuts();

            // Initialize tooltips
            this.initializeTooltips();

            // Hide loading screen after initialization
            setTimeout(() => {
                this.isLoading = false;
                console.log('✅ Modern Vendor Dashboard initialized successfully');
            }, 1200);

            // Update global state
            window.vendorApp = { ...window.vendorApp, ...this };
        },

        // Theme Management
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('vendor-dark-mode', this.darkMode);
            this.applyTheme();
            
            // Dispatch theme change event
            window.dispatchEvent(new CustomEvent('vendor-theme-changed', {
                detail: { darkMode: this.darkMode }
            }));
        },

        applyTheme() {
            const html = document.documentElement;
            const body = document.body;
            
            if (this.darkMode) {
                html.classList.add('dark');
                body.classList.add('dark-mode');
            } else {
                html.classList.remove('dark');
                body.classList.remove('dark-mode');
            }

            // Update meta theme color
            const metaTheme = document.querySelector('meta[name="theme-color"]');
            if (metaTheme) {
                metaTheme.content = this.darkMode ? '#1F2937' : '#7ED957';
            }
        },

        // Sidebar Management
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
            localStorage.setItem('vendor-sidebar-collapsed', this.sidebarCollapsed);
            
            // Dispatch sidebar change event
            window.dispatchEvent(new CustomEvent('vendor-sidebar-changed', {
                detail: { collapsed: this.sidebarCollapsed }
            }));
        },

        toggleMobileSidebar() {
            this.mobileSidebarOpen = !this.mobileSidebarOpen;
            
            // Prevent body scroll when mobile sidebar is open
            if (this.mobileSidebarOpen) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        },

        closeMobileSidebar() {
            this.mobileSidebarOpen = false;
            document.body.style.overflow = '';
        },

        // Window Resize Handler
        handleResize() {
            const width = window.innerWidth;
            
            // Auto-close mobile sidebar on desktop
            if (width > 1024 && this.mobileSidebarOpen) {
                this.closeMobileSidebar();
            }
            
            // Auto-collapse sidebar on tablet
            if (width <= 1024 && width > 768 && !this.sidebarCollapsed) {
                this.sidebarCollapsed = true;
                localStorage.setItem('vendor-sidebar-collapsed', this.sidebarCollapsed);
            }
        },

        // Responsive Initialization
        initializeResponsive() {
            // Set initial responsive state
            this.handleResize();
            
            // Add resize listener with debounce
            let resizeTimeout;
            window.addEventListener('resize', () => {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(() => {
                    this.handleResize();
                }, 150);
            });
        },

        // Animation Initialization
        initializeAnimations() {
            // Check if GSAP is available
            if (typeof gsap === 'undefined') {
                console.warn('GSAP not loaded, skipping animations');
                return;
            }

            // Animate sidebar navigation items
            gsap.from('.nav-item', {
                duration: 0.6,
                y: 20,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out',
                delay: 0.5
            });

            // Animate header elements
            gsap.from('.header-content > *', {
                duration: 0.8,
                y: -20,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out',
                delay: 0.3
            });

            // Animate page content
            gsap.from('.page-content', {
                duration: 1,
                y: 30,
                opacity: 0,
                ease: 'power2.out',
                delay: 0.8
            });
        },

        // Keyboard Shortcuts
        initializeKeyboardShortcuts() {
            document.addEventListener('keydown', (e) => {
                // Ctrl/Cmd + B: Toggle sidebar
                if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                    e.preventDefault();
                    this.toggleSidebar();
                }
                
                // Ctrl/Cmd + D: Toggle dark mode
                if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
                    e.preventDefault();
                    this.toggleDarkMode();
                }
                
                // Escape: Close mobile sidebar
                if (e.key === 'Escape' && this.mobileSidebarOpen) {
                    this.closeMobileSidebar();
                }
            });
        },

        // Tooltip Initialization
        initializeTooltips() {
            // Simple tooltip implementation
            const tooltipElements = document.querySelectorAll('[title]');
            
            tooltipElements.forEach(element => {
                const title = element.getAttribute('title');
                element.removeAttribute('title');
                element.setAttribute('data-tooltip', title);
                
                element.addEventListener('mouseenter', (e) => {
                    this.showTooltip(e.target, title);
                });
                
                element.addEventListener('mouseleave', () => {
                    this.hideTooltip();
                });
            });
        },

        // Tooltip Methods
        showTooltip(element, text) {
            const tooltip = document.createElement('div');
            tooltip.className = 'vendor-tooltip';
            tooltip.textContent = text;
            tooltip.id = 'vendor-tooltip';
            
            document.body.appendChild(tooltip);
            
            const rect = element.getBoundingClientRect();
            const tooltipRect = tooltip.getBoundingClientRect();
            
            tooltip.style.left = `${rect.left + (rect.width / 2) - (tooltipRect.width / 2)}px`;
            tooltip.style.top = `${rect.top - tooltipRect.height - 8}px`;
            
            // Animate in
            if (typeof gsap !== 'undefined') {
                gsap.fromTo(tooltip, 
                    { opacity: 0, y: 10 },
                    { opacity: 1, y: 0, duration: 0.2 }
                );
            }
        },

        hideTooltip() {
            const tooltip = document.getElementById('vendor-tooltip');
            if (tooltip) {
                if (typeof gsap !== 'undefined') {
                    gsap.to(tooltip, {
                        opacity: 0,
                        y: 10,
                        duration: 0.2,
                        onComplete: () => tooltip.remove()
                    });
                } else {
                    tooltip.remove();
                }
            }
        },

        // Utility Methods
        showNotification(message, type = 'info', duration = 5000) {
            const notification = {
                id: Date.now(),
                message,
                type,
                timestamp: new Date()
            };
            
            this.notifications.unshift(notification);
            
            // Auto-remove after duration
            setTimeout(() => {
                this.removeNotification(notification.id);
            }, duration);
            
            return notification.id;
        },

        removeNotification(id) {
            this.notifications = this.notifications.filter(n => n.id !== id);
        },

        // Navigation Helper
        navigateTo(url) {
            window.location.href = url;
        },

        // Search Functionality
        performSearch(query) {
            console.log('Searching for:', query);
            // Implement search logic here
            return [];
        }
    };
}

// Global notification system
window.showVendorNotification = function(message, type = 'info', duration = 5000) {
    if (window.vendorApp && window.vendorApp.showNotification) {
        return window.vendorApp.showNotification(message, type, duration);
    }
    
    // Fallback notification
    console.log(`[${type.toUpperCase()}] ${message}`);
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 DOM loaded, preparing vendor dashboard...');
    
    // Add smooth scrolling to anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add loading states to forms
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading...';
            }
        });
    });
});

// Add CSS for tooltips
const tooltipStyles = `
.vendor-tooltip {
    position: absolute;
    background: var(--vendor-gray-900);
    color: var(--vendor-white);
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-md);
    font-size: var(--vendor-text-xs);
    font-weight: var(--vendor-font-medium);
    z-index: var(--vendor-z-tooltip);
    pointer-events: none;
    white-space: nowrap;
    box-shadow: var(--vendor-shadow-lg);
}

.vendor-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: var(--vendor-gray-900);
}
`;

// Inject tooltip styles
const styleSheet = document.createElement('style');
styleSheet.textContent = tooltipStyles;
document.head.appendChild(styleSheet);
