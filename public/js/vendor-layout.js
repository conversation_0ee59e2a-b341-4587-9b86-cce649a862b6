/* ===== VENDOR LAYOUT JAVASCRIPT ===== */

// Vendor Layout Alpine.js Component
function vendorLayout() {
    return {
        // State
        isLoading: true,
        isSidebarCollapsed: false,
        isMobileSidebarOpen: false,
        isDarkMode: false,
        
        // Initialize
        init() {
            // Hide loading screen after a short delay
            setTimeout(() => {
                this.isLoading = false;
            }, 800);
            
            // Check for saved preferences
            this.loadPreferences();
            
            // Handle window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth >= 1024) {
                    this.isMobileSidebarOpen = false;
                }
            });
            
            // Handle escape key for mobile sidebar
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.isMobileSidebarOpen) {
                    this.closeMobileSidebar();
                }
            });
        },
        
        // Sidebar Methods
        toggleSidebar() {
            if (window.innerWidth < 1024) {
                this.toggleMobileSidebar();
            } else {
                this.toggleDesktopSidebar();
            }
        },
        
        toggleDesktopSidebar() {
            this.isSidebarCollapsed = !this.isSidebarCollapsed;
            this.savePreferences();

            // Add smooth transition effect
            this.animateSidebarToggle();
        },

        toggleSidebarCollapse() {
            this.toggleDesktopSidebar();
        },

        animateSidebarToggle() {
            // Add animation class for smooth transition
            const sidebar = document.querySelector('.vendor-sidebar');
            const mainWrapper = document.querySelector('.vendor-main-wrapper');

            if (sidebar && mainWrapper) {
                sidebar.style.transition = 'width 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                mainWrapper.style.transition = 'margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1)';

                // Remove transition after animation completes
                setTimeout(() => {
                    sidebar.style.transition = '';
                    mainWrapper.style.transition = '';
                }, 300);
            }
        },
        
        toggleMobileSidebar() {
            this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
        },
        
        openMobileSidebar() {
            this.isMobileSidebarOpen = true;
        },
        
        closeMobileSidebar() {
            this.isMobileSidebarOpen = false;
        },
        
        // Theme Methods
        toggleDarkMode() {
            this.isDarkMode = !this.isDarkMode;
            this.savePreferences();
        },
        
        // Preferences
        loadPreferences() {
            const preferences = localStorage.getItem('vendor-layout-preferences');
            if (preferences) {
                const parsed = JSON.parse(preferences);
                this.isSidebarCollapsed = parsed.sidebarCollapsed || false;
                this.isDarkMode = parsed.darkMode || false;
            }
        },
        
        savePreferences() {
            const preferences = {
                sidebarCollapsed: this.isSidebarCollapsed,
                darkMode: this.isDarkMode
            };
            localStorage.setItem('vendor-layout-preferences', JSON.stringify(preferences));
        },
        
        // Utility Methods
        isMobile() {
            return window.innerWidth < 1024;
        },
        
        isTablet() {
            return window.innerWidth >= 768 && window.innerWidth < 1024;
        },
        
        isDesktop() {
            return window.innerWidth >= 1024;
        }
    };
}

// Mobile Menu Toggle Function
function toggleMobileMenu() {
    const sidebar = document.querySelector('.vendor-sidebar');
    const overlay = document.querySelector('.vendor-mobile-overlay');
    
    if (sidebar && overlay) {
        sidebar.classList.toggle('mobile-open');
        overlay.style.display = sidebar.classList.contains('mobile-open') ? 'block' : 'none';
    }
}

// Close Mobile Menu Function
function closeMobileMenu() {
    const sidebar = document.querySelector('.vendor-sidebar');
    const overlay = document.querySelector('.vendor-mobile-overlay');
    
    if (sidebar && overlay) {
        sidebar.classList.remove('mobile-open');
        overlay.style.display = 'none';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for mobile menu
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const overlay = document.querySelector('.vendor-mobile-overlay');
    
    if (mobileToggle) {
        mobileToggle.addEventListener('click', toggleMobileMenu);
    }
    
    if (overlay) {
        overlay.addEventListener('click', closeMobileMenu);
    }
    
    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 1024) {
            closeMobileMenu();
        }
    });
    
    // Handle escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMobileMenu();
        }
    });
});

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { vendorLayout, toggleMobileMenu, closeMobileMenu };
}
