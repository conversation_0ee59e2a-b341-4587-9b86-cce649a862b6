/**
 * Mobile Authentication Enhancement Script
 * Enhances the mobile authentication experience with native app-like features
 */

(function() {
    'use strict';

    // Mobile detection
    const isMobile = window.innerWidth <= 768;
    
    // Initialize mobile enhancements
    function initMobileEnhancements() {
        if (!isMobile) return;
        
        // PWA-like features
        initPWAFeatures();
        
        // Touch enhancements
        initTouchEnhancements();
        
        // Input enhancements
        initInputEnhancements();
        
        // Loading states
        initLoadingStates();
        
        // Keyboard handling
        initKeyboardHandling();
    }

    // PWA-like features
    function initPWAFeatures() {
        // Add to home screen prompt
        let deferredPrompt;
        
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            
            // Show install banner after delay
            setTimeout(() => {
                if (deferredPrompt) {
                    showInstallBanner();
                }
            }, 10000);
        });
        
        // Status bar styling
        updateStatusBar();
    }

    // Touch enhancements
    function initTouchEnhancements() {
        // Add haptic feedback to buttons
        const buttons = document.querySelectorAll('.mobile-primary-button, .mobile-social-button, .mobile-password-toggle');
        
        buttons.forEach(button => {
            button.addEventListener('touchstart', handleTouchStart, { passive: false });
            button.addEventListener('touchend', handleTouchEnd, { passive: false });
            button.addEventListener('touchcancel', handleTouchEnd, { passive: false });
        });
        
        // Prevent double-tap zoom
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    }

    // Input enhancements
    function initInputEnhancements() {
        const inputs = document.querySelectorAll('.mobile-form-input');
        
        inputs.forEach(input => {
            // Auto-capitalize names
            if (input.name === 'name') {
                input.addEventListener('input', function() {
                    this.value = this.value.replace(/\b\w/g, l => l.toUpperCase());
                });
            }
            
            // Email validation
            if (input.type === 'email') {
                input.addEventListener('blur', validateEmail);
            }
            
            // Password strength
            if (input.type === 'password' && input.name === 'password') {
                input.addEventListener('input', checkPasswordStrength);
            }
            
            // Prevent zoom on focus
            input.addEventListener('focus', preventZoom);
            input.addEventListener('blur', restoreZoom);
        });
    }

    // Loading states
    function initLoadingStates() {
        const forms = document.querySelectorAll('.mobile-auth-form');
        
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                const submitButton = this.querySelector('.mobile-primary-button');
                
                if (submitButton) {
                    // Store original text
                    const originalText = submitButton.textContent;
                    
                    // Show loading state
                    submitButton.textContent = submitButton.textContent.includes('Sign In') ? 
                        'Signing In...' : 'Creating Account...';
                    submitButton.disabled = true;
                    submitButton.classList.add('mobile-loading');
                    
                    // Add spinner
                    const spinner = document.createElement('div');
                    spinner.className = 'mobile-spinner';
                    submitButton.prepend(spinner);
                    
                    // Timeout fallback
                    setTimeout(() => {
                        if (submitButton.disabled) {
                            submitButton.textContent = originalText;
                            submitButton.disabled = false;
                            submitButton.classList.remove('mobile-loading');
                            spinner.remove();
                        }
                    }, 30000);
                }
            });
        });
    }

    // Keyboard handling
    function initKeyboardHandling() {
        let initialViewportHeight = window.innerHeight;
        
        window.addEventListener('resize', function() {
            const currentHeight = window.innerHeight;
            const heightDifference = initialViewportHeight - currentHeight;
            
            // Keyboard is likely open if height decreased significantly
            if (heightDifference > 150) {
                document.body.classList.add('keyboard-open');
                adjustViewportForKeyboard();
            } else {
                document.body.classList.remove('keyboard-open');
                restoreViewport();
            }
        });
    }

    // Helper functions
    function handleTouchStart(e) {
        this.style.transform = 'scale(0.98)';
        this.style.opacity = '0.8';
        
        // Haptic feedback (if supported)
        if ('vibrate' in navigator) {
            navigator.vibrate(10);
        }
    }

    function handleTouchEnd(e) {
        this.style.transform = 'scale(1)';
        this.style.opacity = '1';
    }

    function validateEmail() {
        const email = this.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        
        if (email && !emailRegex.test(email)) {
            this.setCustomValidity('Please enter a valid email address');
            this.classList.add('error');
        } else {
            this.setCustomValidity('');
            this.classList.remove('error');
        }
    }

    function checkPasswordStrength() {
        const password = this.value;
        let strength = 0;
        
        if (password.length >= 8) strength++;
        if (password.match(/[a-z]/)) strength++;
        if (password.match(/[A-Z]/)) strength++;
        if (password.match(/[0-9]/)) strength++;
        if (password.match(/[^a-zA-Z0-9]/)) strength++;
        
        // Add visual feedback
        const strengthIndicator = document.getElementById('password-strength');
        if (strengthIndicator) {
            strengthIndicator.className = `strength-${strength}`;
        }
    }

    function preventZoom() {
        const viewport = document.querySelector('meta[name=viewport]');
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
    }

    function restoreZoom() {
        const viewport = document.querySelector('meta[name=viewport]');
        viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
    }

    function adjustViewportForKeyboard() {
        const formContainer = document.querySelector('.mobile-form-container');
        if (formContainer) {
            formContainer.style.paddingBottom = '20px';
        }
    }

    function restoreViewport() {
        const formContainer = document.querySelector('.mobile-form-container');
        if (formContainer) {
            formContainer.style.paddingBottom = '24px';
        }
    }

    function updateStatusBar() {
        // Update theme color based on scroll position
        const themeColor = document.querySelector('meta[name=theme-color]');
        
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            
            // Adjust theme color opacity
            if (themeColor) {
                const opacity = Math.max(0.8, 1 - scrolled / 200);
                themeColor.setAttribute('content', `rgba(37, 211, 102, ${opacity})`);
            }
        });
    }

    function showInstallBanner() {
        const banner = document.createElement('div');
        banner.className = 'install-banner';
        banner.innerHTML = `
            <div class="install-content">
                <div class="install-icon">📱</div>
                <div class="install-text">
                    <div class="install-title">Install WhaMart</div>
                    <div class="install-subtitle">Add to your home screen for better experience</div>
                </div>
                <button class="install-button">Install</button>
                <button class="install-close">&times;</button>
            </div>
        `;
        
        document.body.appendChild(banner);
        
        // Add styles
        const style = document.createElement('style');
        style.textContent = `
            .install-banner {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: white;
                border-radius: 16px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.12);
                z-index: 1000;
                animation: slideUp 0.3s ease-out;
            }
            
            .install-content {
                display: flex;
                align-items: center;
                padding: 16px;
                gap: 12px;
            }
            
            .install-icon {
                font-size: 24px;
            }
            
            .install-text {
                flex: 1;
            }
            
            .install-title {
                font-weight: 600;
                font-size: 14px;
                color: #1a1a1a;
            }
            
            .install-subtitle {
                font-size: 12px;
                color: #666;
                margin-top: 2px;
            }
            
            .install-button {
                background: #25D366;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
            }
            
            .install-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #666;
                padding: 4px;
                margin-left: 8px;
            }
            
            @keyframes slideUp {
                from { transform: translateY(100%); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // Handle actions
        banner.querySelector('.install-button').addEventListener('click', () => {
            deferredPrompt.prompt();
            deferredPrompt.userChoice.then((choiceResult) => {
                deferredPrompt = null;
                banner.remove();
            });
        });
        
        banner.querySelector('.install-close').addEventListener('click', () => {
            banner.remove();
        });
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            if (banner.parentNode) {
                banner.remove();
            }
        }, 10000);
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMobileEnhancements);
    } else {
        initMobileEnhancements();
    }

})();
