/* ===== MOBILE ORDERS INTERACTIONS ===== */

// Toast notification system for mobile
window.showToast = function(message, type = 'info') {
    // Remove existing toast
    const existingToast = document.querySelector('.mobile-toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `mobile-toast mobile-toast-${type}`;
    toast.innerHTML = `
        <div class="mobile-toast-content">
            <i class="mobile-toast-icon fas ${getToastIcon(type)}"></i>
            <span class="mobile-toast-message">${message}</span>
        </div>
    `;
    
    // Add toast styles
    Object.assign(toast.style, {
        position: 'fixed',
        top: '80px',
        left: '50%',
        transform: 'translateX(-50%)',
        background: getToastColor(type),
        color: 'white',
        padding: '12px 20px',
        borderRadius: '12px',
        boxShadow: '0 8px 32px rgba(0,0,0,0.2)',
        zIndex: '9999',
        fontSize: '0.875rem',
        fontWeight: '600',
        maxWidth: '300px',
        opacity: '0',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    });
    
    // Add to document
    document.body.appendChild(toast);
    
    // Animate in
    requestAnimationFrame(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(-50%) translateY(0)';
    });
    
    // Remove after delay
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateX(-50%) translateY(-10px)';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
};

function getToastIcon(type) {
    const icons = {
        'success': 'fa-check-circle',
        'error': 'fa-exclamation-circle',
        'warning': 'fa-exclamation-triangle',
        'info': 'fa-info-circle'
    };
    return icons[type] || icons.info;
}

function getToastColor(type) {
    const colors = {
        'success': 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
        'error': 'linear-gradient(135deg, #F44336 0%, #d32f2f 100%)',
        'warning': 'linear-gradient(135deg, #FF9800 0%, #f57c00 100%)',
        'info': 'linear-gradient(135deg, #2196F3 0%, #1976d2 100%)'
    };
    return colors[type] || colors.info;
}

// Mobile haptic feedback
window.mobileHaptic = function(type = 'light') {
    if ('vibrate' in navigator) {
        const patterns = {
            'light': 10,
            'medium': 20,
            'heavy': 50
        };
        navigator.vibrate(patterns[type] || patterns.light);
    }
};

// Mobile swipe detection
window.addSwipeListener = function(element, callback) {
    let startX = 0;
    let startY = 0;
    let currentX = 0;
    let currentY = 0;
    
    element.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    }, { passive: true });
    
    element.addEventListener('touchmove', (e) => {
        currentX = e.touches[0].clientX;
        currentY = e.touches[0].clientY;
    }, { passive: true });
    
    element.addEventListener('touchend', () => {
        const diffX = currentX - startX;
        const diffY = currentY - startY;
        
        // Detect swipe direction
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            const direction = diffX > 0 ? 'right' : 'left';
            callback(direction, Math.abs(diffX));
        }
        
        startX = 0;
        startY = 0;
        currentX = 0;
        currentY = 0;
    }, { passive: true });
};

// Initialize mobile-specific features
document.addEventListener('DOMContentLoaded', function() {
    // Add touch-friendly interactions
    if (window.innerWidth <= 768) {
        // Add ripple effect to mobile buttons
        document.addEventListener('touchstart', function(e) {
            const target = e.target.closest('.mobile-action-btn, .mobile-filter-chip, .mobile-header-btn');
            if (target) {
                addRippleEffect(target, e.touches[0]);
                window.mobileHaptic('light');
            }
        });
    }
});

function addRippleEffect(element, touch) {
    const rect = element.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    
    const ripple = document.createElement('div');
    ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        pointer-events: none;
        transform: translate(-50%, -50%) scale(0);
        animation: ripple 0.6s linear;
        left: ${x}px;
        top: ${y}px;
        width: 20px;
        height: 20px;
    `;
    
    // Add ripple animation keyframes if not exists
    if (!document.querySelector('#ripple-styles')) {
        const style = document.createElement('style');
        style.id = 'ripple-styles';
        style.textContent = `
            @keyframes ripple {
                to {
                    transform: translate(-50%, -50%) scale(4);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
        if (ripple.parentNode) {
            ripple.parentNode.removeChild(ripple);
        }
    }, 600);
}
