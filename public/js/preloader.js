/**
 * ===== DUKKAN GLOBAL PRELOADER ===== 
 * Modern & Responsive Preloader Controller
 */

class DukkanPreloader {
    constructor(options = {}) {
        this.options = {
            minDuration: 800,        // Minimum loading time in ms
            maxDuration: 3000,       // Maximum loading time in ms
            fadeOutDuration: 500,    // Fade out animation duration
            showProgress: true,      // Show progress bar
            autoHide: true,          // Auto hide when page loads
            ...options
        };
        
        this.startTime = Date.now();
        this.preloader = null;
        this.progressBar = null;
        this.isHidden = false;
        
        this.init();
    }
    
    init() {
        // Create preloader if it doesn't exist
        if (!document.querySelector('.dukkan-preloader')) {
            this.createPreloader();
        }
        
        this.preloader = document.querySelector('.dukkan-preloader');
        this.progressBar = document.querySelector('.preloader-progress-bar');
        
        // Show preloader
        this.show();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Start progress animation if enabled
        if (this.options.showProgress && this.progressBar) {
            this.animateProgress();
        }
        
        // Auto hide when page is loaded
        if (this.options.autoHide) {
            this.setupAutoHide();
        }
    }
    
    createPreloader() {
        const preloaderHTML = `
            <div class="dukkan-preloader" id="dukkan-preloader">
                <div class="preloader-content">
                    <div class="preloader-icon">
                        <i class="fas fa-store" style="display: none;"></i>
                    </div>
                    <div class="preloader-tagline">दुकान ऑनलाइन है</div>
                    <div class="preloader-loading">
                        Loading<span class="loading-dots"></span>
                    </div>
                    <div class="preloader-progress">
                        <div class="preloader-progress-bar"></div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('afterbegin', preloaderHTML);
        
        // Show Font Awesome icon if available, otherwise use emoji
        this.setupIcon();
    }
    
    setupIcon() {
        const iconElement = document.querySelector('.preloader-icon i');
        
        // Check if Font Awesome is loaded
        if (window.FontAwesome || document.querySelector('link[href*="font-awesome"]') || document.querySelector('link[href*="fontawesome"]')) {
            iconElement.style.display = 'block';
        } else {
            // Use emoji fallback
            iconElement.style.display = 'none';
        }
    }
    
    setupEventListeners() {
        // Handle page load
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.checkReadyToHide();
            });
        }
        
        // Handle all resources loaded
        window.addEventListener('load', () => {
            this.checkReadyToHide();
        });
        
        // Handle navigation events (for SPAs)
        if (window.history && window.history.pushState) {
            const originalPushState = window.history.pushState;
            window.history.pushState = (...args) => {
                this.show();
                originalPushState.apply(window.history, args);
                setTimeout(() => this.checkReadyToHide(), 100);
            };
        }
    }
    
    setupAutoHide() {
        // Minimum duration timer
        setTimeout(() => {
            this.checkReadyToHide();
        }, this.options.minDuration);
        
        // Maximum duration failsafe
        setTimeout(() => {
            this.hide();
        }, this.options.maxDuration);
    }
    
    checkReadyToHide() {
        const elapsedTime = Date.now() - this.startTime;
        
        if (elapsedTime >= this.options.minDuration && 
            (document.readyState === 'complete' || document.readyState === 'interactive')) {
            this.hide();
        }
    }
    
    animateProgress() {
        if (!this.progressBar) return;
        
        let progress = 0;
        const duration = this.options.minDuration;
        const increment = 100 / (duration / 50); // Update every 50ms
        
        const progressInterval = setInterval(() => {
            progress += increment;
            
            if (progress >= 100) {
                progress = 100;
                clearInterval(progressInterval);
            }
            
            this.progressBar.style.width = `${progress}%`;
        }, 50);
    }
    
    show() {
        if (this.preloader && this.isHidden) {
            this.preloader.classList.remove('hidden');
            this.isHidden = false;
            document.body.style.overflow = 'hidden';
        }
    }
    
    hide() {
        if (this.preloader && !this.isHidden) {
            this.preloader.classList.add('hidden');
            this.isHidden = true;
            
            // Restore body scroll
            setTimeout(() => {
                document.body.style.overflow = '';
                
                // Trigger custom event
                const event = new CustomEvent('preloaderHidden', {
                    detail: { duration: Date.now() - this.startTime }
                });
                document.dispatchEvent(event);
            }, this.options.fadeOutDuration);
        }
    }
    
    // Public methods
    forceHide() {
        this.hide();
    }
    
    forceShow() {
        this.show();
    }
    
    updateText(text) {
        const loadingElement = document.querySelector('.preloader-loading');
        if (loadingElement) {
            loadingElement.innerHTML = text + '<span class="loading-dots"></span>';
        }
    }
    
    updateTagline(tagline) {
        const taglineElement = document.querySelector('.preloader-tagline');
        if (taglineElement) {
            taglineElement.textContent = tagline;
        }
    }
    
    setProgress(percentage) {
        if (this.progressBar) {
            this.progressBar.style.width = `${Math.min(100, Math.max(0, percentage))}%`;
        }
    }
    
    destroy() {
        if (this.preloader) {
            this.preloader.remove();
        }
        document.body.style.overflow = '';
    }
}

// Auto-initialize preloader
document.addEventListener('DOMContentLoaded', () => {
    // Check if preloader should be disabled (for development)
    const urlParams = new URLSearchParams(window.location.search);
    const disablePreloader = urlParams.get('no-preloader') === 'true' || 
                           localStorage.getItem('dukkan-disable-preloader') === 'true';
    
    if (!disablePreloader) {
        window.dukkanPreloader = new DukkanPreloader({
            minDuration: 1000,
            showProgress: true
        });
    }
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DukkanPreloader;
}

// Global utility functions
window.DukkanPreloader = DukkanPreloader;

// Utility functions for easy access
window.showPreloader = () => {
    if (window.dukkanPreloader) {
        window.dukkanPreloader.forceShow();
    }
};

window.hidePreloader = () => {
    if (window.dukkanPreloader) {
        window.dukkanPreloader.forceHide();
    }
};

window.updatePreloaderText = (text) => {
    if (window.dukkanPreloader) {
        window.dukkanPreloader.updateText(text);
    }
};

// Development helper
console.log('🏪 Dukkan Preloader loaded successfully!');
console.log('💡 Use hidePreloader() to hide manually or add ?no-preloader=true to disable');
