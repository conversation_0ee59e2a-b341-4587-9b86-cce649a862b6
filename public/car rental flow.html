<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CarGo Rentals Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: #ffffff;
        }
        
        /* Chat Header */
        .chat-header {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: #ffffff;
            border-bottom: 1px solid #e9edef;
            height: 60px;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #54656f;
            cursor: pointer;
            padding: 8px;
            margin-right: 8px;
            border-radius: 50%;
            transition: background 0.2s;
        }
        
        .back-btn:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        
        .store-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #00a884;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-right: 12px;
        }
        
        .store-info {
            flex: 1;
        }
        
        .store-name {
            color: #111b21;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.2;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .verified-badge {
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .verified-icon {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .store-status {
            color: #667781;
            font-size: 13px;
            margin-top: 2px;
        }
        
        /* Chat Body */
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 12px;
            background: #efeae2;
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4d4d4' fill-opacity='0.08'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        /* Hide Scrollbar */
        .chat-body::-webkit-scrollbar {
            display: none;
        }
        
        .chat-body {
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
            scrollbar-width: none;  /* Firefox */
        }
        
        /* Messages */
        .message {
            display: flex;
            margin-bottom: 8px;
            animation: messageSlide 0.3s ease-out;
        }
        
        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message.outgoing {
            justify-content: flex-end;
        }
        
        .message-bubble {
            max-width: 85%;
            padding: 6px 7px 8px 9px;
            border-radius: 7.5px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .message-bubble.incoming {
            background: #ffffff;
            color: #111b21;
            border-bottom-left-radius: 2px;
            width: 100%;
        }
        
        .message-bubble.outgoing {
            background: #d9fdd3;
            color: #111b21;
            border-bottom-right-radius: 2px;
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
        }
        
        .message-time {
            font-size: 11px;
            color: rgba(0, 0, 0, 0.45);
            text-align: right;
            margin-top: 2px;
        }
        
        /* Quick Reply Buttons */
        .quick-replies {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }
        
        .quick-reply-btn {
            background: transparent;
            border: 1px solid #00a884;
            color: #00a884;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .quick-reply-btn:hover {
            background: #00a884;
            color: #fff;
        }
        
        /* Product Cards */
        .product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            margin: 8px 0;
            max-width: 250px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
            position: relative;
            overflow: hidden;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            color: #111b21;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .product-price {
            color: #00a884;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .product-description {
            color: #667781;
            font-size: 12px;
            line-height: 1.3;
        }
        
        /* Offer Badge */
        .offer-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff5722;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            z-index: 1;
        }
        
        .offer-badge.bestseller {
            background: #00a884;
        }
        
        .offer-badge.new {
            background: #4caf50;
        }
        
        .offer-badge.limited {
            background: #ff9800;
        }
        
        /* Chat Input */
        .chat-input {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: transparent;
            gap: 8px;
        }
        
        .input-container {
            flex: 1;
            display: flex;
            align-items: center;
            background: #f0f2f5;
            border-radius: 21px;
            padding: 8px 16px;
            min-height: 42px;
        }
        
        .message-input {
            flex: 1;
            background: none;
            border: none;
            outline: none;
            color: #111b21;
            font-size: 15px;
            line-height: 20px;
            padding: 6px 0;
            resize: none;
            max-height: 100px;
            font-family: inherit;
        }
        
        .message-input::placeholder {
            color: #667781;
        }
        
        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #00a884;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
            flex-shrink: 0;
        }
        
        .send-btn:hover {
            background: #06a678;
        }
        
        .send-btn:disabled {
            background: #bbb;
            cursor: not-allowed;
        }
        
        /* Typing Indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 8px 0;
            margin-bottom: 8px;
        }
        
        .typing-bubble {
            background: #ffffff;
            border-radius: 7.5px;
            border-bottom-left-radius: 2px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667781;
            border-radius: 50%;
            animation: typingDot 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typingDot {
            0%, 60%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            30% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .chat-container {
                max-width: 100%;
                height: 100vh;
            }
            
            .message-bubble {
                max-width: 85%;
            }
            
            .product-card {
                max-width: 220px;
            }
        }

        /* Customer Form Styles */
        .customer-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 4px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e9edef;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            color: #111b21;
        }

        .form-input:focus {
            outline: none;
            border-color: #00a884;
            box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.1);
        }

        /* QR Code Payment Styles */
        .qr-payment {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: #ffffff;
            border: 2px solid #e9edef;
            border-radius: 8px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #667781;
        }

        .payment-amount {
            font-size: 18px;
            font-weight: 700;
            color: #00a884;
            margin: 8px 0;
        }

        .payment-info {
            font-size: 12px;
            color: #667781;
            margin-bottom: 12px;
        }

        /* Product Details Styles */
        .product-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .product-image-gallery {
            margin-bottom: 12px;
        }

        .product-detail-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .product-detail-image:hover {
            transform: scale(1.02);
        }

        .image-thumbnails {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .thumbnail-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .thumbnail-image:hover {
            border-color: #00a884;
            transform: scale(1.05);
        }

        .thumbnail-image.active {
            border-color: #00a884;
            box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
        }

        .product-detail-name {
            font-size: 16px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 8px;
        }

        .product-detail-price {
            font-size: 18px;
            font-weight: 700;
            color: #00a884;
            margin-bottom: 12px;
        }

        .product-options {
            margin: 12px 0;
        }

        .option-group {
            margin-bottom: 12px;
        }

        .option-label {
            font-size: 13px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 6px;
            display: block;
        }

        .option-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .option-btn {
            background: #ffffff;
            border: 1px solid #e9edef;
            color: #111b21;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .option-btn:hover, .option-btn.selected {
            background: #00a884;
            color: #ffffff;
            border-color: #00a884;
        }

        /* Product Carousel Styles */
        .product-carousel {
            margin: 12px 0;
            position: relative;
        }

        .carousel-container {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            gap: 12px;
            padding: 8px 4px;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .carousel-container::-webkit-scrollbar {
            display: none;
        }

        .carousel-product-card {
            flex: none;
            width: 180px;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e9edef;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
            position: relative;
            transition: all 0.3s ease;
        }

        .carousel-product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.12);
        }

        .carousel-product-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 6px 6px 0 0;
        }

        .carousel-product-info {
            padding: 10px;
        }

        .carousel-product-name {
            color: #111b21;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            height: 32px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
        }

        .original-price {
            color: #888;
            font-size: 11px;
            text-decoration: line-through;
            font-weight: 400;
        }

        .carousel-product-description {
            color: #667781;
            font-size: 11px;
            line-height: 1.2;
            margin-bottom: 8px;
            height: 26px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .carousel-product-btn {
            width: 100%;
            background: #00a884;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .carousel-product-btn:hover {
            background: #018c70;
            transform: scale(1.02);
        }

        .carousel-product-btn:active {
            transform: scale(0.98);
        }

        .carousel-navigation {
            text-align: center;
            margin: 8px 0 4px 0;
        }

        .scroll-indicator {
            font-size: 11px;
            color: #667781;
            font-style: italic;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* Carousel Offer Badge */
        .carousel-product-card .offer-badge {
            position: absolute;
            top: 6px;
            right: 6px;
            background: #ff5722;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 600;
            z-index: 2;
            text-transform: uppercase;
        }

        .carousel-product-card .offer-badge.bestseller {
            background: #00a884;
        }

        .carousel-product-card .offer-badge.new {
            background: #4caf50;
        }

        .carousel-product-card .offer-badge.limited {
            background: #ff9800;
        }

        /* Mobile Responsiveness for Carousel */
        @media (max-width: 768px) {
            .carousel-product-card {
                width: 160px;
            }
            
            .carousel-product-image {
                height: 90px;
            }
            
            .carousel-container {
                gap: 10px;
                padding: 6px 2px;
            }
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }

        /* Date Separator Styles */
        .chat-date-separator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 16px 0 8px 0;
        }
        .date-label {
            background: #e9edef;
            color: #54656f;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 16px;
            border-radius: 8px;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.04);
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }

        /* Date Separator Styles */
        .chat-date-separator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 16px 0 8px 0;
        }
        .date-label {
            background: #e9edef;
            color: #54656f;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 16px;
            border-radius: 8px;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.04);
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }

        /* Date Separator Styles */
        .chat-date-separator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 16px 0 8px 0;
        }
        .date-label {
            background: #e9edef;
            color: #54656f;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 16px;
            border-radius: 8px;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.04);
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }

        /* Chat specific styles */
        .message {
            display: flex;
            margin-bottom: 8px;
            animation: messageSlide 0.3s ease-out;
        }
        
        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message.outgoing {
            justify-content: flex-end;
        }
        
        .message-bubble {
            max-width: 85%;
            padding: 6px 7px 8px 9px;
            border-radius: 7.5px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .message-bubble.incoming {
            background: #ffffff;
            color: #111b21;
            border-bottom-left-radius: 2px;
            width: 100%;
        }
        
        .message-bubble.outgoing {
            background: #d9fdd3;
            color: #111b21;
            border-bottom-right-radius: 2px;
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
        }
        
        .message-time {
            font-size: 11px;
            color: rgba(0, 0, 0, 0.45);
            text-align: right;
            margin-top: 2px;
        }
        
        /* Typing Indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 8px 0;
            margin-bottom: 8px;
        }
        
        .typing-bubble {
            background: #ffffff;
            border-radius: 7.5px;
            border-bottom-left-radius: 2px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667781;
            border-radius: 50%;
            animation: typingDot 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typingDot {
            0%, 60%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            30% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* Car specific styles */
        .customer-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 4px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e9edef;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            color: #111b21;
        }

        .form-input:focus {
            outline: none;
            border-color: #00a884;
            box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.1);
        }

        /* Hide scrollbar for carousels */
        .product-carousel::-webkit-scrollbar {
            display: none;
        }
        
        .product-carousel {
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
            scrollbar-width: none;  /* Firefox */
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="store-avatar">CR</div>
            <div class="store-info">
                <div class="store-name">
                    CarGo Rentals
                    <div class="verified-badge">
                        <img src="verified-badge.webp" alt="Verified" class="verified-icon">
                    </div>
                </div>
                <div class="store-status">Online · Fastest Car Rentals</div>
            </div>
        </div>
        <!-- Chat Body -->
        <div class="chat-body">
            <div class="chat-content" id="chatContent">
                <!-- Disclaimer Message -->
                <div class="message incoming" style="justify-content: center;">
                    <div class="message-bubble incoming" style="background: #e9f5ff; color: #1976d2; text-align: center; font-size: 13px; width: 100%;">
                        <span style="font-weight: 600;">This business uses CarGo Rentals' secure platform.</span><br>
                        We do not share your personal data or chat conversation with any third party.
                    </div>
                </div>
                <!-- Date Separator -->
                <div class="chat-date-separator">
                    <span class="date-label">Today</span>
                </div>
            </div>
            <!-- Typing Indicator -->
            <div class="typing-indicator" style="display: none;">
                <div class="typing-bubble">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>
        <!-- Chat Input -->
        <div class="chat-input">
            <div class="input-container">
                <textarea class="message-input" id="messageInput" placeholder="Type a message" rows="1"></textarea>
            </div>
            <button class="send-btn" id="sendBtn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>
    <script>
        // Car rental chat flow logic with UI identical to clean-whatsapp-ui.html
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendBtn');
        const chatContent = document.getElementById('chatContent');
        const typingIndicator = document.querySelector('.typing-indicator');
        const chatBody = document.querySelector('.chat-body');

        let chatState = {
            step: 0,
            selectedService: null,
            selectedType: null,
            selectedCar: null,
            rentalDetails: {}
        };

        // Car data (sample)
        const carTypes = [
            { type: 'Hatchback', cars: [
                { name: 'Maruti Swift', price: 1200, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400', badge: 'Popular' },
                { name: 'Hyundai i10', price: 1100, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400' }
            ]},
            { type: 'Sedan', cars: [
                { name: 'Honda City', price: 1800, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400', badge: 'Best Seller' },
                { name: 'Hyundai Verna', price: 1750, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400' }
            ]},
            { type: 'SUV', cars: [
                { name: 'Mahindra XUV', price: 2500, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400', badge: 'New' },
                { name: 'Hyundai Creta', price: 2400, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400' }
            ]},
            { type: 'Luxury', cars: [
                { name: 'BMW 5 Series', price: 7000, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400', badge: 'Luxury' },
                { name: 'Audi A6', price: 7200, img: 'https://images.pexels.com/photos/358070/pexels-photo-358070.jpeg?auto=compress&w=400' }
            ]}
        ];

        // Utility functions for UI
        function addMessage(html, type = 'incoming') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = html;
            chatContent.appendChild(messageDiv);
            scrollToBottom();
        }
        function showTyping() {
            typingIndicator.style.display = 'flex';
            scrollToBottom();
        }
        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
        function scrollToBottom() {
            chatBody.scrollTop = chatBody.scrollHeight;
        }
        function getCurrentTime() {
            return new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        // Chat flow steps
        function startChat() {
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="message-text">👋 Welcome to CarGo Rentals! How can we help you today?</div>
                    <div class="quick-replies">
                        <button class="quick-reply-btn" data-action="self-drive">🚗 Self-drive</button>
                        <button class="quick-reply-btn" data-action="chauffeur">🧑‍✈️ Chauffeur</button>
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 1;
        }
        function askCarType() {
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="message-text">Please select a car type:</div>
                    <div class="quick-replies">
                        ${carTypes.map(ct => `<button class="quick-reply-btn" data-type="${ct.type}">${ct.type}</button>`).join('')}
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 2;
        }
        function showCarOptions() {
            const cars = carTypes.find(ct => ct.type === chatState.selectedType).cars;
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="message-text">Here are our ${chatState.selectedType} options:</div>
                    <div class="product-carousel">
                        <div class="carousel-container">
                            ${cars.map((car, idx) => `
                                <div class="carousel-product-card">
                                    ${car.badge ? `<div class='carousel-product-badge'>${car.badge}</div>` : ''}
                                    <img src="${car.img}" class="carousel-product-image" alt="${car.name}" />
                                    <div class="carousel-product-info">
                                        <div class="carousel-product-name">${car.name}</div>
                                        <div class="carousel-product-price">₹${car.price}/day</div>
                                        <button class="carousel-btn" data-car-idx="${idx}">Select</button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="carousel-navigation">
                        <div class="scroll-indicator">← Swipe to see more cars →</div>
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 3;
        }
        function askRentalDetails() {
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="message-text">Please enter your rental details:</div>
                    <div class="customer-form">
                        <div class="form-group">
                            <label class="form-label">Pickup Date</label>
                            <input type="date" class="form-input" id="pickupDate" required />
                        </div>
                        <div class="form-group">
                            <label class="form-label">Drop-off Date</label>
                            <input type="date" class="form-input" id="dropDate" required />
                        </div>
                        <div class="form-group">
                            <label class="form-label">Pickup Location</label>
                            <input type="text" class="form-input" id="pickupLoc" required placeholder="e.g. Andheri, Mumbai" />
                        </div>
                        <div class="form-group">
                            <label class="form-label">Drop-off Location</label>
                            <input type="text" class="form-input" id="dropLoc" required placeholder="e.g. Bandra, Mumbai" />
                        </div>
                        <button class="quick-reply-btn" id="submitRentalDetails" style="width: 100%; margin-top: 8px;">Submit</button>
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 4;
        }
        function showPriceAndConfirm() {
            const days = Math.max(1, Math.ceil((new Date(chatState.rentalDetails.dropDate) - new Date(chatState.rentalDetails.pickupDate)) / (1000*60*60*24)));
            const price = chatState.selectedCar.price * days;
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="template-message">
                        <div class="template-content">
                            <b>Rental Summary:</b><br>
                            ${chatState.selectedCar.name} (${chatState.selectedType})<br>
                            Service: ${chatState.selectedService}<br>
                            Duration: ${days} day(s)<br>
                            Pickup: ${chatState.rentalDetails.pickupLoc}<br>
                            Drop: ${chatState.rentalDetails.dropLoc}<br>
                            <b>Total: ₹${price}</b>
                        </div>
                        <div class="template-buttons">
                            <div class="template-button-row">
                                <button class="template-btn primary" id="confirmBookingBtn">Confirm Booking</button>
                                <button class="template-btn" id="cancelBookingBtn">Cancel</button>
                            </div>
                        </div>
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 5;
        }
        function showBookingConfirmation() {
            addMessage(`
                <div class="message-bubble incoming">
                    <div class="template-message">
                        <div class="template-content">
                            ✅ <strong>Your booking is confirmed!</strong><br><br>
                            Thank you for choosing CarGo Rentals.<br>
                            <span class="template-variable">Booking ID: #CR${Date.now()}</span><br>
                            Car: ${chatState.selectedCar.name}<br>
                            Service: ${chatState.selectedService}<br>
                            Pickup: ${chatState.rentalDetails.pickupLoc} (${chatState.rentalDetails.pickupDate})<br>
                            Drop: ${chatState.rentalDetails.dropLoc} (${chatState.rentalDetails.dropDate})<br>
                            <br>Our team will contact you soon.<br>
                            For questions, type below or call us!
                        </div>
                    </div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `);
            chatState.step = 6;
        }
        function restartFlow() {
            chatContent.innerHTML = '';
            // Re-add disclaimer and date separator
            chatContent.innerHTML = `
                <div class="message incoming" style="justify-content: center;">
                    <div class="message-bubble incoming" style="background: #e9f5ff; color: #1976d2; text-align: center; font-size: 13px; width: 100%;">
                        <span style="font-weight: 600;">This business uses CarGo Rentals' secure platform.</span><br>
                        We do not share your personal data or chat conversation with any third party.
                    </div>
                </div>
                <div class="chat-date-separator">
                    <span class="date-label">Today</span>
                </div>
            `;
            chatState = { step: 0, selectedService: null, selectedType: null, selectedCar: null, rentalDetails: {} };
            setTimeout(() => startChat(), 400);
        }

        // Event delegation for all chat actions
        document.addEventListener('click', function(e) {
            // Service selection
            if (e.target.classList.contains('quick-reply-btn') && e.target.dataset.action) {
                chatState.selectedService = e.target.dataset.action === 'self-drive' ? 'Self-drive' : 'Chauffeur';
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">${e.target.textContent}</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); askCarType(); }, 700);
                return;
            }
            // Car type selection
            if (e.target.classList.contains('quick-reply-btn') && e.target.dataset.type) {
                chatState.selectedType = e.target.dataset.type;
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">${e.target.textContent}</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); showCarOptions(); }, 700);
                return;
            }
            // Car selection from carousel
            if (e.target.classList.contains('carousel-btn') && e.target.dataset.carIdx !== undefined) {
                const cars = carTypes.find(ct => ct.type === chatState.selectedType).cars;
                chatState.selectedCar = cars[parseInt(e.target.dataset.carIdx)];
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">${chatState.selectedCar.name}</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); askRentalDetails(); }, 700);
                return;
            }
            // Rental details form submit
            if (e.target.id === 'submitRentalDetails') {
                const pickupDate = document.getElementById('pickupDate').value;
                const dropDate = document.getElementById('dropDate').value;
                const pickupLoc = document.getElementById('pickupLoc').value;
                const dropLoc = document.getElementById('dropLoc').value;
                if (!pickupDate || !dropDate || !pickupLoc || !dropLoc) {
                    alert('Please fill all details!');
                    return;
                }
                chatState.rentalDetails = { pickupDate, dropDate, pickupLoc, dropLoc };
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">Pickup: ${pickupDate}, Drop: ${dropDate}\nFrom: ${pickupLoc} To: ${dropLoc}</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); showPriceAndConfirm(); }, 700);
                return;
            }
            // Confirm/cancel booking
            if (e.target.id === 'confirmBookingBtn') {
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">Confirm Booking</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); showBookingConfirmation(); }, 700);
                return;
            }
            if (e.target.id === 'cancelBookingBtn') {
                addMessage(`<div class="message-bubble outgoing"><div class="message-text">Cancel</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
                showTyping();
                setTimeout(() => { hideTyping(); restartFlow(); }, 700);
                return;
            }
        });

        // User input (optional, for free text)
        sendButton.addEventListener('click', function() {
            const val = messageInput.value.trim();
            if (!val) return;
            addMessage(`<div class="message-bubble outgoing"><div class="message-text">${val}</div><div class="message-time">${getCurrentTime()}</div></div>`, 'outgoing');
            messageInput.value = '';
            showTyping();
            setTimeout(() => { hideTyping(); addMessage(`<div class="message-bubble incoming"><div class="message-text">Thank you for your message! Our team will reply soon.</div><div class="message-time">${getCurrentTime()}</div></div>`); }, 1200);
        });
        messageInput.addEventListener('input', function() {
            sendButton.style.opacity = this.value.trim() ? '1' : '0.5';
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendButton.click();
            }
        });

        // Start the chat flow
        startChat();
    </script>
</body>
</html>
