<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Glamour Studio - Luxury Beauty Salon</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            overflow-x: hidden;
        }

        .luxury-header {
            background: linear-gradient(45deg, #000000, #1a1a1a);
            padding: 30px 20px;
            text-align: center;
            position: relative;
            border-bottom: 3px solid #d4af37;
        }

        .luxury-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="%23d4af37" opacity="0.1"/></svg>') repeat;
            pointer-events: none;
        }

        .salon-logo {
            font-size: 2.8rem;
            font-weight: bold;
            color: #d4af37;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 10px;
            letter-spacing: 3px;
        }

        .salon-tagline {
            font-size: 1.1rem;
            color: #cccccc;
            font-style: italic;
            letter-spacing: 1px;
        }

        .vertical-cards-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
            padding: 40px 20px;
            max-width: 500px;
            margin: 0 auto;
        }

        .service-card {
            background: linear-gradient(135deg, #2a2a2a, #1f1f1f);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid #d4af37;
            box-shadow: 0 10px 30px rgba(212, 175, 55, 0.2);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(212, 175, 55, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .service-card:hover::before {
            opacity: 1;
            animation: shimmer 1.5s ease-in-out;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .service-card:hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 15px 40px rgba(212, 175, 55, 0.3);
            border-color: #f4d03f;
        }

        .service-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .service-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.5rem;
        }

        .service-title {
            font-size: 1.4rem;
            font-weight: bold;
            color: #d4af37;
            flex: 1;
        }

        .service-price {
            font-size: 1.2rem;
            color: #f4d03f;
            font-weight: bold;
        }

        .service-description {
            color: #cccccc;
            line-height: 1.6;
            margin-bottom: 15px;
            font-size: 0.95rem;
        }

        .service-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 20px;
        }

        .feature-tag {
            background: rgba(212, 175, 55, 0.2);
            color: #d4af37;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            border: 1px solid rgba(212, 175, 55, 0.3);
        }

        .book-btn {
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: #000;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            font-size: 1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .book-btn:hover {
            background: linear-gradient(135deg, #f4d03f, #d4af37);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        .luxury-footer {
            background: #000;
            padding: 30px 20px;
            text-align: center;
            border-top: 3px solid #d4af37;
            margin-top: 40px;
        }

        .contact-info {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #cccccc;
        }

        .contact-icon {
            color: #d4af37;
            font-size: 1.2rem;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 20px;
        }

        .social-link {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            transform: translateY(-3px) rotate(360deg);
            box-shadow: 0 5px 15px rgba(212, 175, 55, 0.4);
        }

        @media (max-width: 768px) {
            .salon-logo {
                font-size: 2.2rem;
            }
            
            .vertical-cards-container {
                padding: 20px 15px;
            }
            
            .service-card {
                padding: 20px;
            }
            
            .contact-info {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <header class="luxury-header">
        <h1 class="salon-logo">✨ GLAMOUR STUDIO ✨</h1>
        <p class="salon-tagline">Where Luxury Meets Beauty</p>
    </header>

    <div class="vertical-cards-container">
        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">💇‍♀️</div>
                <div class="service-title">Premium Hair Styling</div>
                <div class="service-price">₹2,500</div>
            </div>
            <p class="service-description">
                Luxurious hair transformation with premium products and expert stylists. Includes consultation, wash, cut, style, and finishing.
            </p>
            <div class="service-features">
                <span class="feature-tag">Olaplex Treatment</span>
                <span class="feature-tag">Keratin Infusion</span>
                <span class="feature-tag">Scalp Massage</span>
            </div>
            <button class="book-btn">Book Appointment</button>
        </div>

        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">✨</div>
                <div class="service-title">Luxury Facial Treatment</div>
                <div class="service-price">₹3,200</div>
            </div>
            <p class="service-description">
                Rejuvenating facial with gold-infused serums, diamond microdermabrasion, and LED light therapy for radiant skin.
            </p>
            <div class="service-features">
                <span class="feature-tag">24K Gold Mask</span>
                <span class="feature-tag">Diamond Polish</span>
                <span class="feature-tag">LED Therapy</span>
            </div>
            <button class="book-btn">Book Appointment</button>
        </div>

        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">💅</div>
                <div class="service-title">Signature Manicure</div>
                <div class="service-price">₹1,800</div>
            </div>
            <p class="service-description">
                Elegant nail artistry with premium gel polish, cuticle care, and hand massage using luxury moisturizers.
            </p>
            <div class="service-features">
                <span class="feature-tag">Gel Polish</span>
                <span class="feature-tag">Nail Art</span>
                <span class="feature-tag">Hand Spa</span>
            </div>
            <button class="book-btn">Book Appointment</button>
        </div>

        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">👁️</div>
                <div class="service-title">Eyebrow Sculpting</div>
                <div class="service-price">₹1,200</div>
            </div>
            <p class="service-description">
                Perfect eyebrow shaping with threading, tinting, and microblading touch-ups for defined, natural-looking brows.
            </p>
            <div class="service-features">
                <span class="feature-tag">Threading</span>
                <span class="feature-tag">Tinting</span>
                <span class="feature-tag">Shaping</span>
            </div>
            <button class="book-btn">Book Appointment</button>
        </div>

        <div class="service-card">
            <div class="service-header">
                <div class="service-icon">🌟</div>
                <div class="service-title">Bridal Package</div>
                <div class="service-price">₹15,000</div>
            </div>
            <p class="service-description">
                Complete bridal makeover including hair styling, makeup, manicure, pedicure, and pre-wedding skin treatments.
            </p>
            <div class="service-features">
                <span class="feature-tag">Full Makeover</span>
                <span class="feature-tag">Trial Session</span>
                <span class="feature-tag">Touch-ups</span>
            </div>
            <button class="book-btn">Book Appointment</button>
        </div>
    </div>

    <footer class="luxury-footer">
        <div class="contact-info">
            <div class="contact-item">
                <span class="contact-icon">📍</span>
                <span>123 Fashion Street, Mumbai</span>
            </div>
            <div class="contact-item">
                <span class="contact-icon">📞</span>
                <span>+91 98765 43210</span>
            </div>
            <div class="contact-item">
                <span class="contact-icon">⏰</span>
                <span>10 AM - 8 PM</span>
            </div>
        </div>
        
        <div class="social-links">
            <a href="#" class="social-link">📘</a>
            <a href="#" class="social-link">📷</a>
            <a href="#" class="social-link">🐦</a>
            <a href="#" class="social-link">💬</a>
        </div>
        
        <p style="margin-top: 20px; color: #888;">© 2024 Glamour Studio. Luxury Beauty Experience.</p>
    </footer>
</body>
</html>
