<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Serenity Spa - Zen Wellness Sanctuary</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', serif;
            background: radial-gradient(circle at center, #f0f8f0, #e8f5e8);
            color: #2d5016;
            line-height: 1.7;
            overflow-x: hidden;
        }

        .zen-header {
            background: linear-gradient(135deg, #4a7c59, #6b8e23);
            padding: 60px 20px;
            text-align: center;
            position: relative;
            border-radius: 0 0 50% 50% / 0 0 100px 100px;
        }

        .zen-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M20,50 Q50,20 80,50 Q50,80 20,50" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></svg>') repeat;
            pointer-events: none;
        }

        .spa-name {
            font-size: 2.8rem;
            color: #ffffff;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            font-weight: 300;
            letter-spacing: 3px;
        }

        .zen-philosophy {
            font-size: 1.1rem;
            color: #e8f5e8;
            font-style: italic;
            opacity: 0.9;
        }

        .circular-services {
            max-width: 1000px;
            margin: 80px auto;
            padding: 0 20px;
            display: flex;
            flex-direction: column;
            gap: 60px;
        }

        .service-circle {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            min-height: 300px;
        }

        .service-circle:nth-child(even) {
            flex-direction: row-reverse;
        }

        .circle-visual {
            width: 250px;
            height: 250px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            flex-shrink: 0;
            margin: 0 40px;
            transition: all 0.5s ease;
        }

        .service-circle:nth-child(1) .circle-visual {
            background: radial-gradient(circle, #8fbc8f, #556b2f);
        }

        .service-circle:nth-child(2) .circle-visual {
            background: radial-gradient(circle, #98d8c8, #2e8b57);
        }

        .service-circle:nth-child(3) .circle-visual {
            background: radial-gradient(circle, #f7dc6f, #b7950b);
        }

        .service-circle:nth-child(4) .circle-visual {
            background: radial-gradient(circle, #aed6f1, #2874a6);
        }

        .service-circle:nth-child(5) .circle-visual {
            background: radial-gradient(circle, #d7bde2, #7d3c98);
        }

        .circle-visual:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .circle-visual::before {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border-radius: 50%;
            border: 2px solid rgba(255,255,255,0.3);
            animation: ripple 3s infinite;
        }

        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.2);
                opacity: 0;
            }
        }

        .service-icon {
            font-size: 4rem;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            z-index: 1;
        }

        .service-content {
            flex: 1;
            background: rgba(255,255,255,0.9);
            padding: 40px;
            border-radius: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .service-name {
            font-size: 1.8rem;
            color: #2d5016;
            margin-bottom: 15px;
            font-weight: 400;
        }

        .service-desc {
            color: #4a7c59;
            margin-bottom: 20px;
            font-size: 1rem;
        }

        .zen-features {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 25px;
        }

        .zen-tag {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            color: white;
            font-weight: 500;
        }

        .service-circle:nth-child(1) .zen-tag { background: #8fbc8f; }
        .service-circle:nth-child(2) .zen-tag { background: #98d8c8; }
        .service-circle:nth-child(3) .zen-tag { background: #f7dc6f; color: #2d5016; }
        .service-circle:nth-child(4) .zen-tag { background: #aed6f1; color: #2d5016; }
        .service-circle:nth-child(5) .zen-tag { background: #d7bde2; color: #2d5016; }

        .price-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 20px;
        }

        .zen-price {
            font-size: 1.6rem;
            color: #2d5016;
            font-weight: bold;
        }

        .zen-book-btn {
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s ease;
            color: white;
            text-transform: capitalize;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .service-circle:nth-child(1) .zen-book-btn { background: linear-gradient(45deg, #8fbc8f, #556b2f); }
        .service-circle:nth-child(2) .zen-book-btn { background: linear-gradient(45deg, #98d8c8, #2e8b57); }
        .service-circle:nth-child(3) .zen-book-btn { background: linear-gradient(45deg, #f7dc6f, #b7950b); }
        .service-circle:nth-child(4) .zen-book-btn { background: linear-gradient(45deg, #aed6f1, #2874a6); }
        .service-circle:nth-child(5) .zen-book-btn { background: linear-gradient(45deg, #d7bde2, #7d3c98); }

        .zen-book-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .zen-book-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.5s ease;
        }

        .zen-book-btn:hover::before {
            width: 300px;
            height: 300px;
        }

        .organic-footer {
            background: linear-gradient(135deg, #2d5016, #4a7c59);
            margin-top: 80px;
            padding: 60px 20px;
            position: relative;
            border-radius: 50% 50% 0 0 / 100px 100px 0 0;
        }

        .organic-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="15" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/><circle cx="75" cy="75" r="20" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></svg>') repeat;
            pointer-events: none;
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
            color: #e8f5e8;
            position: relative;
            z-index: 1;
        }

        .footer-title {
            font-size: 2.2rem;
            margin-bottom: 30px;
            color: #ffffff;
            font-weight: 300;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h4 {
            color: #98d8c8;
            margin-bottom: 15px;
            font-size: 1.2rem;
            font-weight: 400;
        }

        .footer-section p {
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .zen-social {
            display: flex;
            justify-content: center;
            gap: 25px;
            margin-top: 40px;
        }

        .social-zen {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 1.5rem;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .social-zen:nth-child(1) { background: radial-gradient(circle, #8fbc8f, #556b2f); }
        .social-zen:nth-child(2) { background: radial-gradient(circle, #98d8c8, #2e8b57); }
        .social-zen:nth-child(3) { background: radial-gradient(circle, #f7dc6f, #b7950b); }
        .social-zen:nth-child(4) { background: radial-gradient(circle, #aed6f1, #2874a6); }

        .social-zen:hover {
            transform: translateY(-5px) scale(1.1);
            box-shadow: 0 15px 30px rgba(0,0,0,0.3);
        }

        .social-zen::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .social-zen:hover::before {
            opacity: 1;
            animation: zenShine 1s ease-in-out;
        }

        @keyframes zenShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        @media (max-width: 768px) {
            .spa-name {
                font-size: 2.2rem;
            }
            
            .service-circle {
                flex-direction: column !important;
                text-align: center;
                gap: 30px;
            }
            
            .circle-visual {
                width: 200px;
                height: 200px;
                margin: 0;
            }
            
            .service-icon {
                font-size: 3rem;
            }
            
            .price-section {
                flex-direction: column;
                gap: 15px;
            }
            
            .footer-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }
        }
    </style>
</head>
<body>
    <header class="zen-header">
        <h1 class="spa-name">🕉️ Serenity Spa 🕉️</h1>
        <p class="zen-philosophy">Inner Peace Through Outer Beauty</p>
    </header>

    <div class="circular-services">
        <div class="service-circle">
            <div class="circle-visual">
                <div class="service-icon">🌿</div>
            </div>
            <div class="service-content">
                <h3 class="service-name">Herbal Hair Therapy</h3>
                <p class="service-desc">Natural hair treatments using organic herbs and essential oils for healthy, lustrous hair that radiates natural beauty.</p>
                <div class="zen-features">
                    <span class="zen-tag">Organic Herbs</span>
                    <span class="zen-tag">Scalp Massage</span>
                    <span class="zen-tag">Aromatherapy</span>
                </div>
                <div class="price-section">
                    <div class="zen-price">₹2,200</div>
                    <button class="zen-book-btn">Find Peace</button>
                </div>
            </div>
        </div>

        <div class="service-circle">
            <div class="circle-visual">
                <div class="service-icon">🧘‍♀️</div>
            </div>
            <div class="service-content">
                <h3 class="service-name">Mindful Facial Ritual</h3>
                <p class="service-desc">Meditative facial experience combining ancient techniques with modern wellness for inner and outer radiance.</p>
                <div class="zen-features">
                    <span class="zen-tag">Meditation</span>
                    <span class="zen-tag">Crystal Therapy</span>
                    <span class="zen-tag">Natural Masks</span>
                </div>
                <div class="price-section">
                    <div class="zen-price">₹2,800</div>
                    <button class="zen-book-btn">Embrace Calm</button>
                </div>
            </div>
        </div>

        <div class="service-circle">
            <div class="circle-visual">
                <div class="service-icon">🌸</div>
            </div>
            <div class="service-content">
                <h3 class="service-name">Lotus Nail Sanctuary</h3>
                <p class="service-desc">Gentle nail care inspired by lotus purity, using natural products and mindful techniques for beautiful, healthy nails.</p>
                <div class="zen-features">
                    <span class="zen-tag">Natural Polish</span>
                    <span class="zen-tag">Hand Meditation</span>
                    <span class="zen-tag">Flower Essence</span>
                </div>
                <div class="price-section">
                    <div class="zen-price">₹1,600</div>
                    <button class="zen-book-btn">Bloom Beauty</button>
                </div>
            </div>
        </div>

        <div class="service-circle">
            <div class="circle-visual">
                <div class="service-icon">👁️‍🗨️</div>
            </div>
            <div class="service-content">
                <h3 class="service-name">Third Eye Brow Art</h3>
                <p class="service-desc">Intuitive eyebrow shaping that enhances your natural features while promoting inner awareness and confidence.</p>
                <div class="zen-features">
                    <span class="zen-tag">Natural Shaping</span>
                    <span class="zen-tag">Herbal Tinting</span>
                    <span class="zen-tag">Energy Balance</span>
                </div>
                <div class="price-section">
                    <div class="zen-price">₹1,200</div>
                    <button class="zen-book-btn">Awaken Vision</button>
                </div>
            </div>
        </div>

        <div class="service-circle">
            <div class="circle-visual">
                <div class="service-icon">🌺</div>
            </div>
            <div class="service-content">
                <h3 class="service-name">Chakra Makeup Harmony</h3>
                <p class="service-desc">Holistic makeup application that aligns with your energy centers, creating beauty that radiates from within.</p>
                <div class="zen-features">
                    <span class="zen-tag">Energy Alignment</span>
                    <span class="zen-tag">Natural Cosmetics</span>
                    <span class="zen-tag">Color Therapy</span>
                </div>
                <div class="price-section">
                    <div class="zen-price">₹3,500</div>
                    <button class="zen-book-btn">Align Energy</button>
                </div>
            </div>
        </div>
    </div>

    <footer class="organic-footer">
        <div class="footer-content">
            <h2 class="footer-title">Connect with Serenity</h2>
            <div class="footer-grid">
                <div class="footer-section">
                    <h4>Sacred Space</h4>
                    <p>🏛️ 321 Meditation Lane</p>
                    <p>Rishikesh, Uttarakhand</p>
                    <p>249201</p>
                </div>
                <div class="footer-section">
                    <h4>Reach Us</h4>
                    <p>☎️ +91 65432 10987</p>
                    <p>📧 <EMAIL></p>
                    <p>🌐 www.serenityspa.com</p>
                </div>
                <div class="footer-section">
                    <h4>Sanctuary Hours</h4>
                    <p>Daily: 8 AM - 7 PM</p>
                    <p>Meditation: 6 AM - 8 AM</p>
                    <p>Closed on Full Moon</p>
                </div>
            </div>
            <div class="zen-social">
                <a href="#" class="social-zen">📘</a>
                <a href="#" class="social-zen">📷</a>
                <a href="#" class="social-zen">🐦</a>
                <a href="#" class="social-zen">💬</a>
            </div>
            <p style="margin-top: 30px; opacity: 0.7;">© 2024 Serenity Spa. Where Beauty Meets Mindfulness.</p>
        </div>
    </footer>
</body>
</html>
