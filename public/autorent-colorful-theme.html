<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AutoRent - Colorful Car Rental Experience</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #4ecdc4;
            --accent-color: #45b7d1;
            --warning-color: #feca57;
            --success-color: #48cae4;
            --purple-color: #a8e6cf;
            --orange-color: #ff8a80;
            --pink-color: #f8bbd9;
            --text-primary: #2d3436;
            --text-secondary: #636e72;
            --text-light: #b2bec3;
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --shadow-color: rgba(0, 0, 0, 0.1);
            --border-radius: 20px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Nunito', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(78, 205, 196, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(69, 183, 209, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 3px solid transparent;
            background-image: linear-gradient(white, white), linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            background-origin: border-box;
            background-clip: padding-box, border-box;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: 0 8px 32px var(--shadow-color);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.75rem;
            font-weight: 900;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            text-decoration: none;
        }

        .logo i {
            font-size: 2rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2.5rem;
            align-items: center;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-link:hover {
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            transition: width 0.3s ease;
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-cta {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 700;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .nav-cta::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .nav-cta:hover::before {
            left: 100%;
        }

        .nav-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            padding: 8rem 0 6rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,107,107,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,117.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: linear-gradient(135deg, var(--warning-color), var(--orange-color));
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 700;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(254, 202, 87, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            animation: colorShift 3s ease-in-out infinite;
        }

        @keyframes colorShift {
            0%, 100% { filter: hue-rotate(0deg); }
            50% { filter: hue-rotate(30deg); }
        }

        .hero-subtitle {
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            color: var(--text-secondary);
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 25px;
            font-weight: 700;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--accent-color), var(--success-color));
            color: white;
            box-shadow: 0 4px 15px rgba(69, 183, 209, 0.3);
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(69, 183, 209, 0.4);
        }

        /* Floating Elements */
        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--accent-color), var(--success-color));
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--warning-color), var(--orange-color));
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: linear-gradient(135deg, var(--bg-secondary) 0%, rgba(255, 255, 255, 0.8) 100%);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-block;
            background: linear-gradient(135deg, var(--purple-color), var(--pink-color));
            color: var(--text-primary);
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.875rem;
            font-weight: 700;
            margin-bottom: 1rem;
            box-shadow: 0 4px 15px rgba(168, 230, 207, 0.3);
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2.5rem;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px var(--shadow-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--accent-color));
        }

        .feature-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .feature-icon.icon-1 { background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); }
        .feature-icon.icon-2 { background: linear-gradient(135deg, var(--accent-color), var(--success-color)); }
        .feature-icon.icon-3 { background: linear-gradient(135deg, var(--warning-color), var(--orange-color)); }
        .feature-icon.icon-4 { background: linear-gradient(135deg, var(--purple-color), var(--pink-color)); }
        .feature-icon.icon-5 { background: linear-gradient(135deg, var(--secondary-color), var(--accent-color)); }
        .feature-icon.icon-6 { background: linear-gradient(135deg, var(--orange-color), var(--primary-color)); }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 800;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="logo">
                    <i class="fas fa-car-side"></i>
                    AutoRent
                </a>
                
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">होम</a></li>
                    <li><a href="#services" class="nav-link">सेवाएं</a></li>
                    <li><a href="#booking" class="nav-link">बुकिंग</a></li>
                    <li><a href="#gallery" class="nav-link">गैलरी</a></li>
                    <li><a href="#contact" class="nav-link">संपर्क</a></li>
                </ul>
                
                <a href="#booking" class="nav-cta">बुक करें</a>
                
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
        
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    रंगबिरंगी कार रेंटल दुनिया
                </div>
                
                <h1 class="hero-title">
                    जिंदगी में<br>
                    रंग भरें!
                </h1>
                
                <p class="hero-subtitle">
                    हर यात्रा को खुशियों से भरपूर बनाएं। रंग-बिरंगी कारों के साथ 
                    अपने सफर को यादगार और मजेदार बनाएं।
                </p>
                
                <div class="hero-buttons">
                    <a href="#booking" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        मजेदार राइड बुक करें
                    </a>
                    <a href="#services" class="btn btn-secondary">
                        <i class="fas fa-palette"></i>
                        कलरफुल कारें देखें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">क्यों AutoRent</div>
                <h2 class="section-title">खुशियों की गारंटी</h2>
                <p class="section-subtitle">
                    हमारी हर सेवा में खुशी और रंग भरे हुए हैं
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon icon-1">
                        <i class="fas fa-smile"></i>
                    </div>
                    <h3 class="feature-title">हैप्पी राइडिंग</h3>
                    <p class="feature-description">
                        हर कार में खुशी का एहसास। म्यूजिक, कलर्स, और पॉजिटिव वाइब्स के साथ यात्रा करें।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon icon-2">
                        <i class="fas fa-rainbow"></i>
                    </div>
                    <h3 class="feature-title">कलरफुल फ्लीट</h3>
                    <p class="feature-description">
                        लाल, नीली, पीली, हरी - हर रंग की कारें। अपने मूड के हिसाब से कार चुनें।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon icon-3">
                        <i class="fas fa-gift"></i>
                    </div>
                    <h3 class="feature-title">सरप्राइज गिफ्ट्स</h3>
                    <p class="feature-description">
                        हर बुकिंग के साथ छोटे-छोटे सरप्राइज गिफ्ट्स। यात्रा को और भी स्पेशल बनाएं।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon icon-4">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="feature-title">म्यूजिक & एंटरटेनमेंट</h3>
                    <p class="feature-description">
                        प्रीमियम साउंड सिस्टम, स्पॉटिफाई, और गेम्स। बोरियत को अलविदा कहें।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon icon-5">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="feature-title">फैमिली फ्रेंडली</h3>
                    <p class="feature-description">
                        बच्चों के लिए स्पेशल सीट्स, टॉयज, और सेफ्टी फीचर्स। पूरे फैमिली के लिए मजा।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon icon-6">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3 class="feature-title">इंस्टा-रेडी</h3>
                    <p class="feature-description">
                        फोटोजेनिक कारें जो आपकी इंस्टाग्राम फीड को कलरफुल बनाएं। #AutoRentVibes
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services" style="padding: 6rem 0; background: white;">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">हमारी सेवाएं</div>
                <h2 class="section-title">रंग-बिरंगी कारों का खजाना</h2>
                <p class="section-subtitle">
                    हर रंग, हर स्टाइल, हर मूड के लिए परफेक्ट कार
                </p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem;">
                <div style="background: linear-gradient(135deg, #ff6b6b, #ff8a80); padding: 2.5rem; border-radius: 20px; color: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3); transition: all 0.3s ease;">
                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
                    <div style="width: 4rem; height: 4rem; background: rgba(255, 255, 255, 0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">हैप्पी इकॉनमी</h3>
                    <p style="margin-bottom: 1.5rem; opacity: 0.9;">खुशमिजाज इकॉनमी कारें जो आपके चेहरे पर मुस्कान लाएं</p>
                    <div style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">₹15/किमी से शुरू</div>
                    <ul style="list-style: none;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> कलरफुल इंटीरियर</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> हैप्पी म्यूजिक प्लेलिस्ट</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> फ्री स्नैक्स</li>
                    </ul>
                </div>

                <div style="background: linear-gradient(135deg, #4ecdc4, #48cae4); padding: 2.5rem; border-radius: 20px; color: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(78, 205, 196, 0.3); transition: all 0.3s ease;">
                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
                    <div style="width: 4rem; height: 4rem; background: rgba(255, 255, 255, 0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">फैमिली फन SUV</h3>
                    <p style="margin-bottom: 1.5rem; opacity: 0.9;">पूरे परिवार के लिए मजेदार और आरामदायक SUVs</p>
                    <div style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">₹22/किमी से शुरू</div>
                    <ul style="list-style: none;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> 7-8 सीटर स्पेस</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> किड्स एंटरटेनमेंट सिस्टम</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> फैमिली गेम्स</li>
                    </ul>
                </div>

                <div style="background: linear-gradient(135deg, #feca57, #ff8a80); padding: 2.5rem; border-radius: 20px; color: white; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(254, 202, 87, 0.3); transition: all 0.3s ease;">
                    <div style="position: absolute; top: -50px; right: -50px; width: 100px; height: 100px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
                    <div style="width: 4rem; height: 4rem; background: rgba(255, 255, 255, 0.2); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; font-size: 1.5rem;">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">रॉयल लक्जरी</h3>
                    <p style="margin-bottom: 1.5rem; opacity: 0.9;">राजा-महाराजा जैसा एहसास देने वाली लक्जरी कारें</p>
                    <div style="font-size: 1.5rem; font-weight: 800; margin-bottom: 1rem;">₹35/किमी से शुरू</div>
                    <ul style="list-style: none;">
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> गोल्डन इंटीरियर</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> पर्सनल बटलर सर्विस</li>
                        <li style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;"><i class="fas fa-check" style="color: rgba(255, 255, 255, 0.8);"></i> रेड कार्पेट ट्रीटमेंट</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking" style="padding: 6rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1440 320\"><path fill=\"rgba(255,255,255,0.1)\" d=\"M0,32L48,37.3C96,43,192,53,288,80C384,107,480,149,576,154.7C672,160,768,128,864,128C960,128,1056,160,1152,165.3C1248,171,1344,149,1392,138.7L1440,128L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z\"></path></svg>') no-repeat center top; background-size: cover;"></div>

        <div class="container" style="position: relative; z-index: 2;">
            <div class="section-header">
                <div style="display: inline-block; background: linear-gradient(135deg, #feca57, #ff8a80); color: white; padding: 0.5rem 1rem; border-radius: 25px; font-size: 0.875rem; font-weight: 700; margin-bottom: 1rem;">मजेदार बुकिंग</div>
                <h2 style="font-size: clamp(2rem, 4vw, 3rem); font-weight: 900; margin-bottom: 1rem; color: white;">अपनी ड्रीम कार बुक करें</h2>
                <p style="font-size: 1.125rem; color: rgba(255, 255, 255, 0.9); max-width: 600px; margin: 0 auto;">
                    बस कुछ क्लिक्स में अपनी पसंदीदा कलरफुल कार बुक करें
                </p>
            </div>

            <div style="background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(20px); padding: 3rem; border-radius: 20px; border: 1px solid rgba(255, 255, 255, 0.2); max-width: 800px; margin: 0 auto; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);">
                <form id="bookingForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">आपका नाम *</label>
                            <input type="text" id="name" name="name" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" placeholder="अपना पूरा नाम लिखें" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">मोबाइल नंबर *</label>
                            <input type="tel" id="phone" name="phone" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" placeholder="+91 98765 43210" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">कार कैटेगरी *</label>
                            <select id="carType" name="carType" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" required>
                                <option value="" style="color: #333;">कार कैटेगरी चुनें</option>
                                <option value="happy-economy" style="color: #333;">हैप्पी इकॉनमी</option>
                                <option value="family-suv" style="color: #333;">फैमिली फन SUV</option>
                                <option value="royal-luxury" style="color: #333;">रॉयल लक्जरी</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">कार का रंग</label>
                            <select id="carColor" name="carColor" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;">
                                <option value="" style="color: #333;">कोई भी रंग</option>
                                <option value="red" style="color: #333;">🔴 लाल (पावर और एनर्जी)</option>
                                <option value="blue" style="color: #333;">🔵 नीला (शांति और कूलनेस)</option>
                                <option value="yellow" style="color: #333;">🟡 पीला (खुशी और पॉजिटिविटी)</option>
                                <option value="green" style="color: #333;">🟢 हरा (प्रकृति और फ्रेशनेस)</option>
                                <option value="pink" style="color: #333;">🩷 गुलाबी (प्यार और रोमांस)</option>
                                <option value="orange" style="color: #333;">🟠 नारंगी (एडवेंचर और फन)</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">पिकअप डेट *</label>
                            <input type="date" id="pickupDate" name="pickupDate" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">पिकअप टाइम *</label>
                            <input type="time" id="pickupTime" name="pickupTime" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">रिटर्न डेट *</label>
                            <input type="date" id="returnDate" name="returnDate" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">रिटर्न टाइम *</label>
                            <input type="time" id="returnTime" name="returnTime" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease;" required>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; margin-bottom: 2rem;">
                        <label style="margin-bottom: 0.5rem; font-weight: 600; color: rgba(255, 255, 255, 0.9); font-size: 0.875rem;">स्पेशल रिक्वेस्ट (डेकोरेशन, म्यूजिक, etc.)</label>
                        <textarea id="requirements" name="requirements" rows="3" style="padding: 1rem; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 15px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1rem; transition: all 0.3s ease; resize: vertical;" placeholder="जैसे: बर्थडे डेकोरेशन, रोमांटिक सेटअप, पार्टी म्यूजिक..."></textarea>
                    </div>

                    <button type="submit" style="background: linear-gradient(135deg, #feca57, #ff8a80); color: white; padding: 1rem 2rem; border: none; border-radius: 25px; font-size: 1.125rem; font-weight: 700; cursor: pointer; transition: all 0.3s ease; width: 100%; box-shadow: 0 4px 15px rgba(254, 202, 87, 0.3); position: relative; overflow: hidden;">
                        <i class="fas fa-heart" style="margin-right: 0.5rem;"></i>
                        मजेदार राइड बुक करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: linear-gradient(135deg, #2d3436, #636e72); color: white; padding: 3rem 0 2rem;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div>
                    <h4 style="font-size: 1.5rem; font-weight: 900; margin-bottom: 1rem; background: linear-gradient(135deg, #ff6b6b, #4ecdc4); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">AutoRent</h4>
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6;">
                        जिंदगी में रंग भरने वाली कार रेंटल सर्विस। हर यात्रा को खुशियों से भरपूर बनाते हैं।
                    </p>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 700; margin-bottom: 1rem; color: #feca57;">कलरफुल सर्विसेज</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span style="color: rgba(255, 255, 255, 0.8);">🔴 रेड कारें (पावर राइड्स)</span>
                        <span style="color: rgba(255, 255, 255, 0.8);">🔵 ब्लू कारें (कूल राइड्स)</span>
                        <span style="color: rgba(255, 255, 255, 0.8);">🟡 येलो कारें (हैप्पी राइड्स)</span>
                        <span style="color: rgba(255, 255, 255, 0.8);">🟢 ग्रीन कारें (इको राइड्स)</span>
                    </div>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 700; margin-bottom: 1rem; color: #ff8a80;">संपर्क करें</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span style="color: rgba(255, 255, 255, 0.8);">📞 +91 98765 43210</span>
                        <span style="color: rgba(255, 255, 255, 0.8);">📧 <EMAIL></span>
                        <span style="color: rgba(255, 255, 255, 0.8);">📍 रंगबिरंगे शहरों में</span>
                        <span style="color: rgba(255, 255, 255, 0.8);">🌈 #AutoRentVibes</span>
                    </div>
                </div>
            </div>

            <div style="text-align: center; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.2); color: rgba(255, 255, 255, 0.6);">
                <p>&copy; 2024 AutoRent. जिंदगी में रंग भरते रहें! 🌈</p>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <button onclick="openChat()" style="position: fixed; bottom: 2rem; right: 2rem; width: 4rem; height: 4rem; background: linear-gradient(135deg, #ff6b6b, #4ecdc4); border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; cursor: pointer; box-shadow: 0 4px 20px rgba(255, 107, 107, 0.4); transition: all 0.3s ease; z-index: 1000; border: none; animation: bounce 2s infinite;">
        <i class="fab fa-whatsapp"></i>
    </button>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.getElementById('pickupDate');
            const returnDate = document.getElementById('returnDate');

            if (pickupDate) pickupDate.min = today;
            if (returnDate) returnDate.min = today;

            if (pickupDate) {
                pickupDate.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (returnDate) returnDate.min = selectedDate;
                });
            }

            // Add colorful focus effects
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = '#feca57';
                    this.style.boxShadow = '0 0 20px rgba(254, 202, 87, 0.3)';
                });

                input.addEventListener('blur', function() {
                    this.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    this.style.boxShadow = 'none';
                });
            });
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.name || !data.phone || !data.carType || !data.pickupDate || !data.pickupTime || !data.returnDate || !data.returnTime) {
                showColorfulAlert('कृपया सभी आवश्यक फील्ड भरें! 🌈', 'warning');
                return;
            }

            const colorEmoji = {
                'red': '🔴',
                'blue': '🔵',
                'yellow': '🟡',
                'green': '🟢',
                'pink': '🩷',
                'orange': '🟠'
            };

            const message = `🌈 *AutoRent - कलरफुल कार बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार कैटेगरी:* ${data.carType}
🎨 *पसंदीदा रंग:* ${data.carColor ? colorEmoji[data.carColor] + ' ' + data.carColor : 'कोई भी रंग'}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
🏁 *रिटर्न:* ${data.returnDate} ${data.returnTime}

🎉 *स्पेशल रिक्वेस्ट:* ${data.requirements || 'कोई नहीं'}

आइए आपकी यात्रा को रंगबिरंगा बनाते हैं! 🌈✨`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showColorfulAlert('🎉 आपकी कलरफुल राइड बुक हो गई है! जल्दी ही आपसे संपर्क करेंगे! 🌈', 'success');
            this.reset();
        });

        function openChat() {
            const message = '🌈 नमस्ते! मैं AutoRent की कलरफुल कार रेंटल सर्विस के बारे में जानकारी चाहता/चाहती हूँ। 🚗✨';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function showColorfulAlert(message, type = 'info') {
            const colors = {
                'success': 'linear-gradient(135deg, #48cae4, #4ecdc4)',
                'warning': 'linear-gradient(135deg, #feca57, #ff8a80)',
                'info': 'linear-gradient(135deg, #ff6b6b, #4ecdc4)'
            };

            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 20px;
                box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
                z-index: 10000;
                font-weight: 700;
                max-width: 400px;
                animation: slideInBounce 0.5s ease-out;
            `;
            notification.textContent = message;

            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInBounce {
                    0% { transform: translateX(100%) scale(0.8); opacity: 0; }
                    50% { transform: translateX(-10px) scale(1.05); opacity: 1; }
                    100% { transform: translateX(0) scale(1); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideInBounce 0.5s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 4000);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add hover effects to service cards
        document.addEventListener('DOMContentLoaded', function() {
            const serviceCards = document.querySelectorAll('[style*="background: linear-gradient"]');
            serviceCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                    this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.2)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = this.style.boxShadow.replace('0 20px 40px rgba(0, 0, 0, 0.2)', '');
                });
            });
        });
    </script>
</body>
</html>
