<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CarGo - Minimalist <PERSON> Rental</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #000000;
            --secondary-color: #6366f1;
            --accent-color: #10b981;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-tertiary: #f3f4f6;
            --border-light: #e5e7eb;
            --border-medium: #d1d5db;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            font-weight: 400;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-light);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-md);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1.5rem 0;
        }

        .logo {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--primary-color);
            text-decoration: none;
            letter-spacing: -0.025em;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 3rem;
            align-items: center;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: color 0.2s ease;
            letter-spacing: 0.025em;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-cta {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.375rem;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            letter-spacing: 0.025em;
        }

        .nav-cta:hover {
            background: #374151;
            transform: translateY(-1px);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.25rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            padding: 8rem 0 6rem;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-content {
            max-width: 600px;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--bg-secondary);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: 2rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .hero-title {
            font-size: clamp(3rem, 8vw, 5rem);
            font-weight: 800;
            line-height: 1;
            margin-bottom: 1.5rem;
            color: var(--primary-color);
            letter-spacing: -0.05em;
        }

        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            line-height: 1.6;
            font-weight: 400;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 0.375rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.2s ease;
            border: none;
            cursor: pointer;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: #374151;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text-primary);
            border: 1px solid var(--border-medium);
        }

        .btn-secondary:hover {
            background: var(--bg-secondary);
            border-color: var(--primary-color);
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: var(--bg-secondary);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .section-badge {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-bottom: 1rem;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 2.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--primary-color);
            letter-spacing: -0.025em;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 0.75rem;
            border: 1px solid var(--border-light);
            transition: all 0.2s ease;
        }

        .feature-card:hover {
            border-color: var(--border-medium);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .feature-icon {
            width: 3rem;
            height: 3rem;
            background: var(--bg-secondary);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 1.125rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--primary-color);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            font-size: 0.875rem;
        }

        /* Services Section */
        .services {
            padding: 6rem 0;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: white;
            border: 1px solid var(--border-light);
            border-radius: 0.75rem;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .service-card:hover {
            border-color: var(--border-medium);
            box-shadow: var(--shadow-lg);
            transform: translateY(-2px);
        }

        .service-header {
            padding: 2rem 2rem 1rem;
            border-bottom: 1px solid var(--border-light);
        }

        .service-icon {
            width: 3rem;
            height: 3rem;
            background: var(--bg-secondary);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.25rem;
            color: var(--primary-color);
        }

        .service-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .service-price {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--secondary-color);
        }

        .service-body {
            padding: 1rem 2rem 2rem;
        }

        .service-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
            font-size: 0.875rem;
        }

        .service-features {
            list-style: none;
        }

        .service-features li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .service-features li i {
            color: var(--accent-color);
            font-size: 0.75rem;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                padding: 6rem 0 4rem;
                min-height: 80vh;
            }

            .hero-buttons {
                flex-direction: column;
            }

            .btn {
                justify-content: center;
            }

            .features,
            .services {
                padding: 4rem 0;
            }

            .features-grid,
            .services-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .feature-card,
            .service-card {
                padding: 1.5rem;
            }

            .service-header {
                padding: 1.5rem 1.5rem 1rem;
            }

            .service-body {
                padding: 1rem 1.5rem 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 0.75rem;
            }

            .nav-container {
                padding: 1rem 0;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .section-title {
                font-size: 1.75rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="logo">CarGo</a>
                
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">होम</a></li>
                    <li><a href="#services" class="nav-link">सेवाएं</a></li>
                    <li><a href="#booking" class="nav-link">बुकिंग</a></li>
                    <li><a href="#about" class="nav-link">हमारे बारे में</a></li>
                    <li><a href="#contact" class="nav-link">संपर्क</a></li>
                </ul>
                
                <a href="#booking" class="nav-cta">बुक करें</a>
                
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-award"></i>
                    सिंपल • फास्ट • रिलायबल
                </div>
                
                <h1 class="hero-title">
                    सिंपल<br>
                    कार रेंटल
                </h1>
                
                <p class="hero-subtitle">
                    बिना किसी झंझट के, सिर्फ 2 मिनट में कार बुक करें। 
                    साफ-सुथरी कारें, फिक्स्ड प्राइसिंग, और ट्रांसपैरेंट सर्विस।
                </p>
                
                <div class="hero-buttons">
                    <a href="#booking" class="btn btn-primary">
                        <i class="fas fa-arrow-right"></i>
                        अभी बुक करें
                    </a>
                    <a href="#services" class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        और जानें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">क्यों CarGo</div>
                <h2 class="section-title">सिंपल है, बेहतर है</h2>
                <p class="section-subtitle">
                    हमने कार रेंटल को आसान बनाया है। कोई कॉम्प्लिकेशन नहीं, सिर्फ क्वालिटी सर्विस।
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">2 मिनट बुकिंग</h3>
                    <p class="feature-description">
                        सिंपल फॉर्म भरें, कार चुनें, बुक करें। बस इतना ही। कोई लंबी प्रोसेस नहीं।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <h3 class="feature-title">फिक्स्ड प्राइसिंग</h3>
                    <p class="feature-description">
                        जो प्राइस दिखाया गया है, वही पे करना है। कोई हिडन चार्जेस या सरप्राइज नहीं।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="feature-title">क्लीन कारें</h3>
                    <p class="feature-description">
                        हर कार को प्रोफेशनली साफ किया जाता है। अंदर-बाहर स्पॉटलेस और फ्रेश।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <h3 class="feature-title">24/7 सपोर्ट</h3>
                    <p class="feature-description">
                        कभी भी कोई प्रॉब्लम हो, हमारी टीम तुरंत हेल्प करने के लिए तैयार है।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">फुल इंश्योरेंस</h3>
                    <p class="feature-description">
                        सभी कारें कॉम्प्रिहेंसिव इंश्योरेंस के साथ। आपकी टेंशन हमारी जिम्मेदारी।
                    </p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">मोबाइल फ्रेंडली</h3>
                    <p class="feature-description">
                        फोन से आसानी से बुक करें। सिंपल इंटरफेस, फास्ट लोडिंग, स्मूथ एक्सपीरियंस।
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">हमारी सेवाएं</div>
                <h2 class="section-title">सिंपल प्राइसिंग</h2>
                <p class="section-subtitle">
                    कोई कन्फ्यूजन नहीं। साफ प्राइसिंग, क्वालिटी कारें।
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <h3 class="service-title">इकॉनमी</h3>
                        <div class="service-price">₹12/किमी</div>
                    </div>
                    <div class="service-body">
                        <p class="service-description">
                            डेली कम्यूट और शहर में घूमने के लिए परफेक्ट। फ्यूल एफिशिएंट और कम्फर्टेबल।
                        </p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> AC और म्यूजिक सिस्टम</li>
                            <li><i class="fas fa-check"></i> 4-5 सीटर</li>
                            <li><i class="fas fa-check"></i> फ्री इंश्योरेंस</li>
                            <li><i class="fas fa-check"></i> 24/7 रोडसाइड असिस्टेंस</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <h3 class="service-title">SUV</h3>
                        <div class="service-price">₹18/किमी</div>
                    </div>
                    <div class="service-body">
                        <p class="service-description">
                            फैमिली ट्रिप्स और ग्रुप ट्रैवल के लिए आइडियल। ज्यादा स्पेस और कम्फर्ट।
                        </p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> 7-8 सीटर कैपेसिटी</li>
                            <li><i class="fas fa-check"></i> बड़ा लगेज स्पेस</li>
                            <li><i class="fas fa-check"></i> पावरफुल इंजन</li>
                            <li><i class="fas fa-check"></i> GPS नेवीगेशन</li>
                        </ul>
                    </div>
                </div>

                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon">
                            <i class="fas fa-gem"></i>
                        </div>
                        <h3 class="service-title">प्रीमियम</h3>
                        <div class="service-price">₹25/किमी</div>
                    </div>
                    <div class="service-body">
                        <p class="service-description">
                            स्पेशल ऑकेजन्स और बिजनेस मीटिंग्स के लिए। लक्जरी और स्टाइल।
                        </p>
                        <ul class="service-features">
                            <li><i class="fas fa-check"></i> लक्जरी इंटीरियर</li>
                            <li><i class="fas fa-check"></i> प्रीमियम साउंड सिस्टम</li>
                            <li><i class="fas fa-check"></i> लेदर सीट्स</li>
                            <li><i class="fas fa-check"></i> प्रोफेशनल ड्राइवर</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking" style="padding: 6rem 0; background: var(--bg-secondary);">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">बुकिंग</div>
                <h2 class="section-title">अभी बुक करें</h2>
                <p class="section-subtitle">
                    सिंपल फॉर्म भरें और तुरंत कन्फर्मेशन पाएं।
                </p>
            </div>

            <div style="background: white; padding: 3rem; border-radius: 0.75rem; border: 1px solid var(--border-light); max-width: 800px; margin: 0 auto;">
                <form id="bookingForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">आपका नाम *</label>
                            <input type="text" id="name" name="name" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="अपना पूरा नाम लिखें" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">मोबाइल नंबर *</label>
                            <input type="tel" id="phone" name="phone" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="+91 98765 43210" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">कार टाइप *</label>
                            <select id="carType" name="carType" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                                <option value="">कार टाइप चुनें</option>
                                <option value="economy">इकॉनमी (₹12/किमी)</option>
                                <option value="suv">SUV (₹18/किमी)</option>
                                <option value="premium">प्रीमियम (₹25/किमी)</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">रेंटल टाइप *</label>
                            <select id="rentalType" name="rentalType" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                                <option value="">रेंटल टाइप चुनें</option>
                                <option value="hourly">घंटे के हिसाब</option>
                                <option value="daily">दैनिक</option>
                                <option value="weekly">साप्ताहिक</option>
                            </select>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">पिकअप डेट *</label>
                            <input type="date" id="pickupDate" name="pickupDate" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">पिकअप टाइम *</label>
                            <input type="time" id="pickupTime" name="pickupTime" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">रिटर्न डेट *</label>
                            <input type="date" id="returnDate" name="returnDate" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">रिटर्न टाइम *</label>
                            <input type="time" id="returnTime" name="returnTime" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" required>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 1.5rem;">
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">ड्राइवर चाहिए?</label>
                            <select id="needDriver" name="needDriver" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;">
                                <option value="no">नहीं</option>
                                <option value="yes">हां</option>
                            </select>
                        </div>
                        <div style="display: flex; flex-direction: column;">
                            <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">पिकअप लोकेशन</label>
                            <input type="text" id="pickupLocation" name="pickupLocation" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease;" placeholder="पिकअप का पता">
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; margin-bottom: 2rem;">
                        <label style="margin-bottom: 0.5rem; font-weight: 600; color: var(--text-primary); font-size: 0.875rem;">कोई स्पेशल रिक्वेस्ट?</label>
                        <textarea id="requirements" name="requirements" rows="3" style="padding: 0.75rem; border: 1px solid var(--border-medium); border-radius: 0.375rem; font-size: 0.875rem; transition: border-color 0.2s ease; resize: vertical;" placeholder="कोई विशेष जरूरत हो तो बताएं..."></textarea>
                    </div>

                    <button type="submit" style="background: var(--primary-color); color: white; padding: 1rem 2rem; border: none; border-radius: 0.375rem; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease; width: 100%; letter-spacing: 0.025em;">
                        <i class="fas fa-paper-plane" style="margin-right: 0.5rem;"></i>
                        बुकिंग कन्फर्म करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer style="background: var(--primary-color); color: white; padding: 3rem 0 2rem;">
        <div class="container">
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                <div>
                    <h4 style="font-size: 1.25rem; font-weight: 700; margin-bottom: 1rem;">CarGo</h4>
                    <p style="color: rgba(255, 255, 255, 0.8); line-height: 1.6; font-size: 0.875rem;">
                        सिंपल और रिलायबल कार रेंटल सर्विस। हमारा मिशन है आपकी यात्रा को आसान बनाना।
                    </p>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">सर्विसेज</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="#services" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">इकॉनमी कार</a>
                        <a href="#services" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">SUV रेंटल</a>
                        <a href="#services" style="color: rgba(255, 255, 255, 0.8); text-decoration: none; font-size: 0.875rem; transition: color 0.2s ease;">प्रीमियम कार</a>
                    </div>
                </div>

                <div>
                    <h4 style="font-size: 1rem; font-weight: 600; margin-bottom: 1rem;">संपर्क</h4>
                    <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <span style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem;">📞 +91 98765 43210</span>
                        <span style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem;">📧 <EMAIL></span>
                        <span style="color: rgba(255, 255, 255, 0.8); font-size: 0.875rem;">📍 मुंबई, दिल्ली, बेंगलुरु</span>
                    </div>
                </div>
            </div>

            <div style="text-align: center; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.2); color: rgba(255, 255, 255, 0.6); font-size: 0.875rem;">
                <p>&copy; 2024 CarGo. सभी अधिकार सुरक्षित।</p>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <button onclick="openChat()" style="position: fixed; bottom: 2rem; right: 2rem; width: 3.5rem; height: 3.5rem; background: #25d366; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem; cursor: pointer; box-shadow: var(--shadow-lg); transition: all 0.2s ease; z-index: 1000; border: none;">
        <i class="fab fa-whatsapp"></i>
    </button>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.getElementById('pickupDate');
            const returnDate = document.getElementById('returnDate');

            if (pickupDate) pickupDate.min = today;
            if (returnDate) returnDate.min = today;

            if (pickupDate) {
                pickupDate.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (returnDate) returnDate.min = selectedDate;
                });
            }

            // Add focus styles to form inputs
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.style.borderColor = 'var(--primary-color)';
                    this.style.outline = 'none';
                    this.style.boxShadow = '0 0 0 3px rgba(0, 0, 0, 0.1)';
                });

                input.addEventListener('blur', function() {
                    this.style.borderColor = 'var(--border-medium)';
                    this.style.boxShadow = 'none';
                });
            });
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.name || !data.phone || !data.carType || !data.rentalType || !data.pickupDate || !data.pickupTime || !data.returnDate || !data.returnTime) {
                alert('कृपया सभी आवश्यक फील्ड भरें।');
                return;
            }

            const message = `🚗 *CarGo - सिंपल कार बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार टाइप:* ${data.carType}
📅 *रेंटल टाइप:* ${data.rentalType}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
🏁 *रिटर्न:* ${data.returnDate} ${data.returnTime}
👨‍✈️ *ड्राइवर:* ${data.needDriver === 'yes' ? 'हां' : 'नहीं'}
📍 *पिकअप लोकेशन:* ${data.pickupLocation || 'नहीं बताया'}

📝 *स्पेशल रिक्वेस्ट:* ${data.requirements || 'कोई नहीं'}

कृपया इस बुकिंग को कन्फर्म करें। धन्यवाद!`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            // Simple notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--accent-color);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.375rem;
                box-shadow: var(--shadow-lg);
                z-index: 10000;
                font-weight: 600;
                font-size: 0.875rem;
            `;
            notification.textContent = '✅ बुकिंग रिक्वेस्ट भेज दी गई है!';

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);

            this.reset();
        });

        function openChat() {
            const message = '🚗 नमस्ते! मैं CarGo की सिंपल कार रेंटल सर्विस के बारे में जानकारी चाहता/चाहती हूँ।';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Button hover effects
        document.querySelectorAll('.btn, button[type="submit"]').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
