<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Store Chat</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: #ffffff;
        }
        
        /* Chat Header */
        .chat-header {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: #ffffff;
            border-bottom: 1px solid #e9edef;
            height: 60px;
        }
        
        .back-btn {
            background: none;
            border: none;
            color: #54656f;
            cursor: pointer;
            padding: 8px;
            margin-right: 8px;
            border-radius: 50%;
            transition: background 0.2s;
        }
        
        .back-btn:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        
        .store-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #00a884;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: 600;
            color: white;
            margin-right: 12px;
        }
        
        .store-info {
            flex: 1;
        }
        
        .store-name {
            color: #111b21;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.2;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .verified-badge {
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        
        .verified-icon {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .store-status {
            color: #667781;
            font-size: 13px;
            margin-top: 2px;
        }
        
        .header-actions {
            display: flex;
            gap: 4px;
        }
        
        .header-action-btn {
            background: none;
            border: none;
            color: #54656f;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.2s;
        }
        
        .header-action-btn:hover {
            background: rgba(0, 0, 0, 0.05);
        }
        
        /* Chat Body */
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 12px;
            background: #efeae2;
            background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d4d4d4' fill-opacity='0.08'%3E%3Ccircle cx='20' cy='20' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        /* Hide Scrollbar */
        .chat-body::-webkit-scrollbar {
            display: none;
        }
        
        .chat-body {
            -ms-overflow-style: none;  /* Internet Explorer 10+ */
            scrollbar-width: none;  /* Firefox */
        }
        
        /* Messages */
        .message {
            display: flex;
            margin-bottom: 8px;
            animation: messageSlide 0.3s ease-out;
        }
        
        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .message.outgoing {
            justify-content: flex-end;
        }
        
        .message-bubble {
            max-width: 85%;
            padding: 6px 7px 8px 9px;
            border-radius: 7.5px;
            position: relative;
            word-wrap: break-word;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .message-bubble.incoming {
            background: #ffffff;
            color: #111b21;
            border-bottom-left-radius: 2px;
            width: 100%;
        }
        
        .message-bubble.outgoing {
            background: #d9fdd3;
            color: #111b21;
            border-bottom-right-radius: 2px;
        }
        
        .message-text {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 4px;
        }
        
        .message-time {
            font-size: 11px;
            color: rgba(0, 0, 0, 0.45);
            text-align: right;
            margin-top: 2px;
        }
        
        /* Quick Reply Buttons */
        .quick-replies {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-top: 8px;
        }
        
        .quick-reply-btn {
            background: transparent;
            border: 1px solid #00a884;
            color: #00a884;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s;
            white-space: nowrap;
        }
        
        .quick-reply-btn:hover {
            background: #00a884;
            color: #fff;
        }
        
        /* Product Cards */
        .product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            margin: 8px 0;
            max-width: 250px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            width: 100%;
            height: 140px;
            object-fit: cover;
            position: relative;
            overflow: hidden;
        }
        
        .product-info {
            padding: 12px;
        }
        
        .product-name {
            color: #111b21;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .product-price {
            color: #00a884;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 6px;
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .product-description {
            color: #667781;
            font-size: 12px;
            line-height: 1.3;
        }
        
        /* Offer Badge */
        .offer-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff5722;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            z-index: 1;
        }
        
        .offer-badge.bestseller {
            background: #00a884;
        }
        
        .offer-badge.new {
            background: #4caf50;
        }
        
        .offer-badge.limited {
            background: #ff9800;
        }
        
        /* Chat Input */
        .chat-input {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background: transparent;
            gap: 8px;
        }
        
        .input-container {
            flex: 1;
            display: flex;
            align-items: center;
            background: #f0f2f5;
            border-radius: 21px;
            padding: 8px 16px;
            min-height: 42px;
        }
        
        .message-input {
            flex: 1;
            background: none;
            border: none;
            outline: none;
            color: #111b21;
            font-size: 15px;
            line-height: 20px;
            padding: 6px 0;
            resize: none;
            max-height: 100px;
            font-family: inherit;
        }
        
        .message-input::placeholder {
            color: #667781;
        }
        
        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #00a884;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
            flex-shrink: 0;
        }
        
        .send-btn:hover {
            background: #06a678;
        }
        
        .send-btn:disabled {
            background: #bbb;
            cursor: not-allowed;
        }
        
        /* Typing Indicator */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 8px 0;
            margin-bottom: 8px;
        }
        
        .typing-bubble {
            background: #ffffff;
            border-radius: 7.5px;
            border-bottom-left-radius: 2px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13);
        }
        
        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667781;
            border-radius: 50%;
            animation: typingDot 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) {
            animation-delay: 0.2s;
        }
        
        .typing-dot:nth-child(3) {
            animation-delay: 0.4s;
        }
        
        @keyframes typingDot {
            0%, 60%, 100% {
                opacity: 0.3;
                transform: scale(1);
            }
            30% {
                opacity: 1;
                transform: scale(1.2);
            }
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .chat-container {
                max-width: 100%;
                height: 100vh;
            }
            
            .message-bubble {
                max-width: 85%;
            }
            
            .product-card {
                max-width: 220px;
            }
        }

        /* Customer Form Styles */
        .customer-form {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-label {
            display: block;
            font-size: 13px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 4px;
        }

        .form-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e9edef;
            border-radius: 6px;
            font-size: 14px;
            background: #ffffff;
            color: #111b21;
        }

        .form-input:focus {
            outline: none;
            border-color: #00a884;
            box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.1);
        }

        /* QR Code Payment Styles */
        .qr-payment {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .qr-code {
            width: 150px;
            height: 150px;
            background: #ffffff;
            border: 2px solid #e9edef;
            border-radius: 8px;
            margin: 0 auto 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #667781;
        }

        .payment-amount {
            font-size: 18px;
            font-weight: 700;
            color: #00a884;
            margin: 8px 0;
        }

        .payment-info {
            font-size: 12px;
            color: #667781;
            margin-bottom: 12px;
        }

        /* Product Details Styles */
        .product-details {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }

        .product-image-gallery {
            margin-bottom: 12px;
        }

        .product-detail-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .product-detail-image:hover {
            transform: scale(1.02);
        }

        .image-thumbnails {
            display: flex;
            gap: 6px;
            justify-content: center;
        }

        .thumbnail-image {
            width: 50px;
            height: 50px;
            object-fit: cover;
            border-radius: 6px;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .thumbnail-image:hover {
            border-color: #00a884;
            transform: scale(1.05);
        }

        .thumbnail-image.active {
            border-color: #00a884;
            box-shadow: 0 0 0 2px rgba(0, 168, 132, 0.2);
        }

        .product-detail-name {
            font-size: 16px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 8px;
        }

        .product-detail-price {
            font-size: 18px;
            font-weight: 700;
            color: #00a884;
            margin-bottom: 12px;
        }

        .product-options {
            margin: 12px 0;
        }

        .option-group {
            margin-bottom: 12px;
        }

        .option-label {
            font-size: 13px;
            font-weight: 600;
            color: #111b21;
            margin-bottom: 6px;
            display: block;
        }

        .option-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
        }

        .option-btn {
            background: #ffffff;
            border: 1px solid #e9edef;
            color: #111b21;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .option-btn:hover, .option-btn.selected {
            background: #00a884;
            color: #ffffff;
            border-color: #00a884;
        }

        /* Product Carousel Styles */
        .product-carousel {
            margin: 12px 0;
            position: relative;
        }

        .carousel-container {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            gap: 12px;
            padding: 8px 4px;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .carousel-container::-webkit-scrollbar {
            display: none;
        }

        .carousel-product-card {
            flex: none;
            width: 180px;
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #e9edef;
            box-shadow: 0 2px 6px rgba(0,0,0,0.08);
            position: relative;
            transition: all 0.3s ease;
        }

        .carousel-product-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.12);
        }

        .carousel-product-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
            border-radius: 6px 6px 0 0;
        }

        .carousel-product-info {
            padding: 10px;
        }

        .carousel-product-name {
            color: #111b21;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            height: 32px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 700;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 4px;
            flex-wrap: wrap;
        }

        .original-price {
            color: #888;
            font-size: 11px;
            text-decoration: line-through;
            font-weight: 400;
        }

        .carousel-product-description {
            color: #667781;
            font-size: 11px;
            line-height: 1.2;
            margin-bottom: 8px;
            height: 26px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .carousel-product-btn {
            width: 100%;
            background: #00a884;
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 6px;
            font-size: 11px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .carousel-product-btn:hover {
            background: #018c70;
            transform: scale(1.02);
        }

        .carousel-product-btn:active {
            transform: scale(0.98);
        }

        .carousel-navigation {
            text-align: center;
            margin: 8px 0 4px 0;
        }

        .scroll-indicator {
            font-size: 11px;
            color: #667781;
            font-style: italic;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.7; }
            50% { opacity: 1; }
        }

        /* Carousel Offer Badge */
        .carousel-product-card .offer-badge {
            position: absolute;
            top: 6px;
            right: 6px;
            background: #ff5722;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 9px;
            font-weight: 600;
            z-index: 2;
            text-transform: uppercase;
        }

        .carousel-product-card .offer-badge.bestseller {
            background: #00a884;
        }

        .carousel-product-card .offer-badge.new {
            background: #4caf50;
        }

        .carousel-product-card .offer-badge.limited {
            background: #ff9800;
        }

        /* Mobile Responsiveness for Carousel */
        @media (max-width: 768px) {
            .carousel-product-card {
                width: 160px;
            }
            
            .carousel-product-image {
                height: 90px;
            }
            
            .carousel-container {
                gap: 10px;
                padding: 6px 2px;
            }
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }

        /* Date Separator Styles */
        .chat-date-separator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 16px 0 8px 0;
        }
        .date-label {
            background: #e9edef;
            color: #54656f;
            font-size: 12px;
            font-weight: 500;
            padding: 4px 16px;
            border-radius: 8px;
            letter-spacing: 0.5px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.04);
        }
        
        /* Message Template Styles */
        .template-message {
            background: transparent;
            border: none;
            border-radius: 0;
            padding: 0;
            margin: 0;
            position: relative;
            box-shadow: none;
        }
        
        .template-content {
            color: #111b21;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .template-variable {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 13px;
        }
        
        .template-buttons {
            border-top: 1px solid #e9edef;
            background: #f8f9fa;
            margin: 8px 0 0 0;
            border-radius: 0 0 5px 5px;
        }
        
        .template-button-row {
            display: flex;
            border-bottom: 1px solid #e9edef;
        }
        
        .template-button-row:last-child {
            border-bottom: none;
        }
        
        .template-btn {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 12px;
            color: #1976d2;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s;
            text-align: center;
            border-right: 1px solid #e9edef;
        }
        
        .template-btn:last-child {
            border-right: none;
        }
        
        .template-btn:hover {
            background: rgba(25, 118, 210, 0.04);
        }
        
        .template-btn:active {
            background: rgba(25, 118, 210, 0.08);
        }
        
        .template-btn.primary {
            color: #00a884;
            font-weight: 600;
        }
        
        .template-btn.primary:hover {
            background: rgba(0, 168, 132, 0.04);
        }
        
        .btn-icon {
            margin-right: 4px;
            font-size: 14px;
        }
        
        /* Enhanced Product Card Styles */
        .product-card {
            position: relative;
            transition: transform 0.2s ease;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .product-image {
            position: relative;
            overflow: hidden;
        }
        
        .product-name {
            font-weight: 600;
        }
        
        .product-price {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        /* Category Summary Styling */
        .category-summary {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            border-radius: 8px;
            padding: 12px;
            margin: 8px 0;
            border: 1px solid #e9edef;
        }
        
        .category-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            border-bottom: 1px solid rgba(0,0,0,0.05);
        }
        
        .category-item:last-child {
            border-bottom: none;
        }
        
        .category-name {
            font-weight: 500;
            color: #111b21;
        }
        
        .category-count {
            color: #667781;
            font-size: 12px;
        }
        
        /* Product Carousel */
        .product-carousel {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 8px 0 12px 0;
            margin: 8px 0;
            scroll-snap-type: x mandatory;
            -webkit-overflow-scrolling: touch;
        }
        
        .product-carousel::-webkit-scrollbar {
            height: 4px;
        }
        
        .product-carousel::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 2px;
        }
        
        .product-carousel::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }
        
        .carousel-product-card {
            background: #ffffff;
            border-radius: 8px;
            overflow: hidden;
            min-width: 180px;
            max-width: 180px;
            border: 1px solid #e9edef;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            flex-shrink: 0;
            scroll-snap-align: start;
            transition: transform 0.2s ease;
        }
        
        .carousel-product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .carousel-product-image {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .carousel-product-info {
            padding: 8px;
        }
        
        .carousel-product-name {
            color: #111b21;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            line-height: 1.2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .carousel-product-price {
            color: #00a884;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge {
            background: #00a884;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 8px;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 4px;
        }
        
        .carousel-product-badge.sale {
            background: #ff5722;
        }
        
        .carousel-product-badge.new {
            background: #4caf50;
        }
        
        .carousel-btn {
            background: #00a884;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-top: 4px;
            transition: background 0.2s;
        }
        
        .carousel-btn:hover {
            background: #06a678;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Chat Header -->
        <div class="chat-header">
            <div class="store-avatar">FS</div>
            <div class="store-info">
                <div class="store-name">
                    Fashion Store
                    <div class="verified-badge">
                        <img src="verified-badge.webp" alt="Verified" class="verified-icon">
                    </div>
                </div>
                <div class="store-status">online</div>
            </div>
        </div>
        
        <!-- Chat Body -->
        <div class="chat-body">
            <div class="chat-content">
                <!-- Disclaimer Message -->
                <div class="message incoming" style="justify-content: center;">
                    <div class="message-bubble incoming" style="background: #e9f5ff; color: #1976d2; text-align: center; font-size: 13px; width: 100%;">
                        <span style="font-weight: 600;">This business uses WhaMart's secure platform.</span><br>
                        We do not share your personal data or chat conversation with any third party.
                    </div>
                </div>
                <!-- Date Separator -->
                <div class="chat-date-separator">
                    <span class="date-label">Today</span>
                </div>
                <!-- Welcome Message -->
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="message-text">नमस्ते! Fashion Store में आपका स्वागत है। आज मैं आपकी कैसे मदद कर सकता हूं? 👋</div>
                        <div class="quick-replies">
                            <button class="quick-reply-btn" data-action="view-products">🛒 View Products</button>
                            <button class="quick-reply-btn" data-action="contact-us">📞 Contact Us</button>
                            <button class="quick-reply-btn" data-action="offers">🎉 Offers</button>
                        </div>
                        <div class="message-time">10:00</div>
                    </div>
                </div>
            </div>
            
            <!-- Typing Indicator -->
            <div class="typing-indicator" style="display: none;">
                <div class="typing-bubble">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="chat-input">
            <div class="input-container">
                <textarea class="message-input" placeholder="Type a message" rows="1"></textarea>
            </div>
            <button class="send-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>
    
    <script>
        // Complete WhatsApp Store Chat Flow
        const messageInput = document.querySelector('.message-input');
        const sendButton = document.querySelector('.send-btn');
        const chatContent = document.querySelector('.chat-content');
        const typingIndicator = document.querySelector('.typing-indicator');
        const chatBody = document.querySelector('.chat-body');
        
        // Chat flow state
        let currentFlow = 'welcome';
        let selectedCategory = '';
        let selectedProduct = {};
        let customerData = {};
        let orderTotal = 0;
        
        // Product data for different categories
        const productCategories = {
            'men': {
                name: '👔 Men\'s Fashion',
                products: [
                    {
                        id: 1,
                        name: 'Premium Formal Shirt',
                        price: 1299,
                        originalPrice: 1799,
                        image: 'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1596755094514-f87e34085b2c?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1607345366928-199ea26cfe3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: '100% Cotton, Wrinkle-free, Available in 5 colors. Perfect for office wear.',
                        colors: ['White', 'Blue', 'Black', 'Light Blue', 'Pink'],
                        sizes: ['S', 'M', 'L', 'XL', 'XXL']
                    },
                    {
                        id: 2,
                        name: 'Casual Denim Jeans',
                        price: 1599,
                        image: 'https://images.unsplash.com/photo-1542272604-787c3835535d?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1542272604-787c3835535d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1506629905522-b2bb20949a15?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1582552938357-32b906df40cb?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1541099649105-f69ad21f3246?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Stretchable denim, comfort fit, fade-resistant. Perfect for casual outings.',
                        colors: ['Blue', 'Black', 'Gray'],
                        sizes: ['28', '30', '32', '34', '36', '38']
                    },
                    {
                        id: 3,
                        name: 'Cotton T-Shirt',
                        price: 799,
                        image: 'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1622445275576-721325763afe?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1562157873-818bc0726f68?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1583743814966-8936f37f4ec2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Premium cotton, comfortable fit, breathable fabric.',
                        colors: ['White', 'Black', 'Navy', 'Gray', 'Red'],
                        sizes: ['S', 'M', 'L', 'XL']
                    }
                ]
            },
            'women': {
                name: '👗 Women\'s Collection',
                products: [
                    {
                        id: 4,
                        name: 'Floral Summer Dress',
                        price: 899,
                        image: 'https://images.unsplash.com/photo-1595777457583-95e059d581b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1595777457583-95e059d581b8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1572804013309-59a88b7e92f1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1509631179647-0177331693ae?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Light fabric, perfect for summer. Beautiful floral print, comfortable fit.',
                        colors: ['Pink', 'Blue', 'Yellow', 'White'],
                        sizes: ['XS', 'S', 'M', 'L', 'XL']
                    },
                    {
                        id: 5,
                        name: 'Designer Handbag',
                        price: 2199,
                        image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1584917865442-de89df76afd3?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1548036328-c9fa89d128fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Premium leather, multiple compartments, elegant design.',
                        colors: ['Black', 'Brown', 'Tan', 'Red'],
                        sizes: ['One Size']
                    }
                ]
            },
            'footwear': {
                name: '👟 Footwear',
                products: [
                    {
                        id: 6,
                        name: 'Athletic Running Shoes',
                        price: 2499,
                        image: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1608231387042-66d1773070a5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1605348532760-6753d2c43329?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Lightweight, breathable mesh, excellent grip. Perfect for running and gym.',
                        colors: ['White', 'Black', 'Blue', 'Red'],
                        sizes: ['6', '7', '8', '9', '10', '11']
                    }
                ]
            },
            'electronics': {
                name: '📱 Electronics',
                products: [
                    {
                        id: 7,
                        name: 'Wireless Bluetooth Earbuds',
                        price: 1299,
                        image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1590658268037-6bf12165a8df?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1606220588913-b3aebf87e85e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1583394838336-acd977736f90?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: '25Hr battery, noise cancellation, IPX7 waterproof. High-quality sound.',
                        colors: ['Black', 'White', 'Blue'],
                        sizes: ['One Size']
                    },
                    {
                        id: 8,
                        name: 'Protective Phone Case',
                        price: 399,
                        originalPrice: 499,
                        image: 'https://images.unsplash.com/photo-1601593346740-925612772716?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1601593346740-925612772716?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1556656793-08538906a9f8?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1563203369-26f2e4a5ccf7?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1574944985070-8f3ebc6b79d2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Shockproof, clear design, perfect fit for your phone.',
                        colors: ['Clear', 'Black', 'Blue'],
                        sizes: ['One Size']
                    }
                ]
            },
            'accessories': {
                name: '💼 Accessories',
                products: [
                    {
                        id: 9,
                        name: 'Premium Watch Combo',
                        price: 1999,
                        originalPrice: 3500,
                        image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80',
                        images: [
                            'https://images.unsplash.com/photo-1523275335684-37898b6baf30?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1524592094714-0f0654e20314?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1434056886845-dac89ffe9b56?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                            'https://images.unsplash.com/photo-1611078489935-0cb964de46d6?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                        ],
                        description: 'Watch + Wallet + Sunglasses combo. Perfect gift set with premium packaging.',
                        colors: ['Silver', 'Gold', 'Black'],
                        sizes: ['One Size']
                    }
                ]
            }
        };
        
        // Initial setup
        sendButton.style.opacity = '0.5';
        
        messageInput.addEventListener('input', function() {
            sendButton.style.opacity = this.value.trim() ? '1' : '0.5';
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 100) + 'px';
        });
        
        sendButton.addEventListener('click', function() {
            const message = messageInput.value.trim();
            if (message) {
                addMessage(message, 'outgoing');
                messageInput.value = '';
                messageInput.style.height = 'auto';
                sendButton.style.opacity = '0.5';
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    addMessage('धन्यवाद आपके message के लिए! हम जल्दी ही आपको reply करेंगे 😊', 'incoming');
                }, 1500);
            }
        });
        
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendButton.click();
            }
        });
        
        // Main click handler for all interactions
        document.addEventListener('click', function(e) {
            
            // Welcome buttons
            if (e.target.classList.contains('quick-reply-btn') && e.target.dataset.action) {
                const action = e.target.dataset.action;
                const text = e.target.textContent;
                addMessage(text, 'outgoing');
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    
                    if (action === 'view-products') {
                        showCategorySelection();
                        currentFlow = 'category-selection';
                    } else if (action === 'contact-us') {
                        addMessage('📞 हमारी support team 24/7 उपलब्ध है।\n\nWhatsApp: +91-9876543210\nEmail: <EMAIL>\n\nकोई भी सवाल हो तो बेझिझक पूछें! 😊', 'incoming');
                    } else if (action === 'offers') {
                        addMessage('🎉 आज के Special Offers:\n\n🔥 50% OFF on Men\'s Shirts\n👗 Buy 2 Get 1 Free on Dresses\n👟 Flat ₹500 off on Footwear\n📱 20% OFF on Electronics\n\n⏰ Limited time offer! अभी order करें!', 'incoming');
                    }
                }, 1000);
                return;
            }
            
            // Category selection
            if (e.target.classList.contains('quick-reply-btn') && currentFlow === 'category-selection') {
                const text = e.target.textContent;
                addMessage(text, 'outgoing');
                
                // Determine category
                if (text.includes('Men')) selectedCategory = 'men';
                else if (text.includes('Women')) selectedCategory = 'women';
                else if (text.includes('Footwear')) selectedCategory = 'footwear';
                else if (text.includes('Electronics')) selectedCategory = 'electronics';
                else if (text.includes('Accessories')) selectedCategory = 'accessories';
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    showProductCarousel(selectedCategory);
                    currentFlow = 'product-selection';
                }, 1000);
                return;
            }
            
            // Product selection from carousel
            if (e.target.classList.contains('carousel-product-btn')) {
                const productCard = e.target.closest('.carousel-product-card');
                const productName = productCard.querySelector('.carousel-product-name').textContent;
                
                addMessage(`🛒 ${productName}`, 'outgoing');
                
                // Find selected product
                for (let category in productCategories) {
                    const product = productCategories[category].products.find(p => p.name === productName);
                    if (product) {
                        selectedProduct = product;
                        break;
                    }
                }
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    showProductDetails(selectedProduct);
                    currentFlow = 'product-details';
                }, 1000);
                return;
            }
            
            // Product details buttons
            if (e.target.classList.contains('quick-reply-btn') && currentFlow === 'product-details') {
                const text = e.target.textContent;
                addMessage(text, 'outgoing');
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    
                    if (text.includes('Back')) {
                        showProductCarousel(selectedCategory);
                        currentFlow = 'product-selection';
                    } else if (text.includes('Continue')) {
                        showCustomerForm();
                        currentFlow = 'customer-form';
                    }
                }, 800);
                return;
            }
            
            // Option buttons (size, color)
            if (e.target.classList.contains('option-btn')) {
                // Toggle selection
                const group = e.target.closest('.option-group');
                group.querySelectorAll('.option-btn').forEach(btn => btn.classList.remove('selected'));
                e.target.classList.add('selected');
                return;
            }
            
            // Thumbnail image click
            if (e.target.classList.contains('thumbnail-image')) {
                changeMainImage(e.target.src);
                return;
            }
            
            // Form submission
            if (e.target.id === 'submit-customer-form') {
                const name = document.getElementById('customer-name').value;
                const mobile = document.getElementById('customer-mobile').value;
                const address = document.getElementById('customer-address').value;
                
                if (!name || !mobile || !address) {
                    alert('कृपया सभी fields भरें!');
                    return;
                }
                
                customerData = { name, mobile, address };
                orderTotal = selectedProduct.price;
                
                addMessage(`📝 Customer Details Submitted:\n\nName: ${name}\nMobile: ${mobile}\nAddress: ${address}`, 'outgoing');
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    showPaymentQR();
                    currentFlow = 'payment';
                }, 1000);
                return;
            }
            
            // Payment done
            if (e.target.id === 'payment-done-btn') {
                addMessage('💳 Payment Done', 'outgoing');
                
                showTyping();
                setTimeout(() => {
                    hideTyping();
                    showThankYouMessage();
                    currentFlow = 'completed';
                }, 1000);
                return;
            }
        });
        
        function showCategorySelection() {
            const categoriesHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="message-text">Perfect! यहाँ हमारी product categories हैं। कृपया अपनी पसंदीदा category select करें: 👇</div>
                        <div class="quick-replies">
                            <button class="quick-reply-btn">👔 Men's Fashion</button>
                            <button class="quick-reply-btn">👗 Women's Collection</button>
                            <button class="quick-reply-btn">👟 Footwear</button>
                            <button class="quick-reply-btn">📱 Electronics</button>
                            <button class="quick-reply-btn">💼 Accessories</button>
                        </div>
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', categoriesHtml);
            scrollToBottom();
        }
        
        function showProductCarousel(category) {
            const categoryData = productCategories[category];
            let productsHtml = '';
            
            categoryData.products.forEach(product => {
                const originalPriceHtml = product.originalPrice ? 
                    `<span class="original-price">₹${product.originalPrice}</span>` : '';
                
                productsHtml += `
                    <div class="carousel-product-card">
                        <img src="${product.image}" alt="${product.name}" class="carousel-product-image">
                        <div class="carousel-product-info">
                            <div class="carousel-product-name">${product.name}</div>
                            <div class="carousel-product-price">
                                ₹${product.price} ${originalPriceHtml}
                            </div>
                            <div class="carousel-product-description">${product.description.substring(0, 50)}...</div>
                            <button class="carousel-product-btn">🛒 Select Product</button>
                        </div>
                    </div>
                `;
            });
            
            const carouselHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="message-text">यहाँ ${categoryData.name} की हमारी collection है: 👇</div>
                        <div class="product-carousel">
                            <div class="carousel-container">
                                ${productsHtml}
                            </div>
                        </div>
                        <div class="carousel-navigation">
                            <div class="scroll-indicator">← Swipe to see more products →</div>
                        </div>
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', carouselHtml);
            scrollToBottom();
        }
        
        function showProductDetails(product) {
            const originalPriceHtml = product.originalPrice ? 
                `<span style="text-decoration: line-through; color: #888; margin-left: 8px;">₹${product.originalPrice}</span>` : '';
            
            const colorOptions = product.colors.map(color => 
                `<button class="option-btn">${color}</button>`
            ).join('');
            
            const sizeOptions = product.sizes.map(size => 
                `<button class="option-btn">${size}</button>`
            ).join('');
            
            // Generate thumbnail images (max 3 additional images)
            const thumbnailImages = product.images && product.images.length > 1 ? 
                product.images.slice(1, 4).map((img, index) => 
                    `<img src="${img}" alt="View ${index + 2}" class="thumbnail-image" onclick="changeMainImage('${img}')">`
                ).join('') : '';
            
            const mainImage = product.images && product.images.length > 0 ? product.images[0] : product.image;
            
            const detailsHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="product-details">
                            <div class="product-image-gallery">
                                <img src="${mainImage}" alt="${product.name}" class="product-detail-image" id="main-product-image">
                                ${thumbnailImages ? `
                                    <div class="image-thumbnails">
                                        ${thumbnailImages}
                                    </div>
                                ` : ''}
                            </div>
                            <div class="product-detail-name">${product.name}</div>
                            <div class="product-detail-price">₹${product.price} ${originalPriceHtml}</div>
                            <div class="product-description">${product.description}</div>
                            
                            <div class="product-options">
                                <div class="option-group">
                                    <label class="option-label">Color:</label>
                                    <div class="option-buttons">
                                        ${colorOptions}
                                    </div>
                                </div>
                                
                                <div class="option-group">
                                    <label class="option-label">Size:</label>
                                    <div class="option-buttons">
                                        ${sizeOptions}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="quick-replies">
                            <button class="quick-reply-btn">← Back</button>
                            <button class="quick-reply-btn">Continue →</button>
                        </div>
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', detailsHtml);
            scrollToBottom();
        }
        
        function showCustomerForm() {
            const formHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="message-text">Perfect! अब कृपया अपनी details भरें: 📝</div>
                        
                        <div class="customer-form">
                            <div class="form-group">
                                <label class="form-label">Customer Name:</label>
                                <input type="text" id="customer-name" class="form-input" placeholder="अपना नाम लिखें">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Mobile Number:</label>
                                <input type="tel" id="customer-mobile" class="form-input" placeholder="Mobile number">
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">Shipping Address:</label>
                                <textarea id="customer-address" class="form-input" rows="3" placeholder="Complete address"></textarea>
                            </div>
                            
                            <button id="submit-customer-form" class="quick-reply-btn" style="width: 100%; margin-top: 8px;">
                                📋 Submit Details
                            </button>
                        </div>
                        
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', formHtml);
            scrollToBottom();
        }
        
        function showPaymentQR() {
            const qrHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="message-text">Order confirmed! Payment के लिए QR code scan करें: 📱</div>
                        
                        <div class="qr-payment">
                            <div class="qr-code">
                                📱 QR Code<br>Scan to Pay
                            </div>
                            <div class="payment-amount">Total: ₹${orderTotal}</div>
                            <div class="payment-info">UPI ID: fashionstore@paytm<br>या QR code scan करें</div>
                            
                            <button id="payment-done-btn" class="quick-reply-btn" style="width: 100%;">
                                ✅ Payment Done
                            </button>
                        </div>
                        
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', qrHtml);
            scrollToBottom();
        }
        
        function showThankYouMessage() {
            const thankYouHtml = `
                <div class="message incoming">
                    <div class="message-bubble incoming">
                        <div class="template-message">
                            <div class="template-content">
                                🎉 <strong>Thank You ${customerData.name}!</strong>
                                <br><br>
                                आपका order successfully placed हो गया है।
                                <br><br>
                                📦 Order ID: <span class="template-variable">#ORD${Date.now()}</span>
                                <br>🛍️ Product: ${selectedProduct.name}
                                <br>💰 Amount: ₹${orderTotal}
                                <br>📱 Mobile: ${customerData.mobile}
                                <br>📍 Address: ${customerData.address}
                                <br><br>
                                🚚 आपका order 2-3 दिन में deliver हो जाएगा।
                                <br>📱 SMS notification भी आएगा।
                                <br><br>
                                Shopping के लिए धन्यवाद! 😊
                            </div>
                        </div>
                        <div class="message-time">${getCurrentTime()}</div>
                    </div>
                </div>
            `;
            chatContent.insertAdjacentHTML('beforeend', thankYouHtml);
            scrollToBottom();
        }
        
        function addMessage(text, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `
                <div class="message-bubble ${type}">
                    <div class="message-text">${text}</div>
                    <div class="message-time">${getCurrentTime()}</div>
                </div>
            `;
            chatContent.appendChild(messageDiv);
            scrollToBottom();
        }
        
        function showTyping() {
            typingIndicator.style.display = 'flex';
            scrollToBottom();
        }
        
        function hideTyping() {
            typingIndicator.style.display = 'none';
        }
        
        function scrollToBottom() {
            chatBody.scrollTop = chatBody.scrollHeight;
        }
        
        function getCurrentTime() {
            return new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }
        
        // Function to change main product image
        function changeMainImage(imageSrc) {
            const mainImage = document.getElementById('main-product-image');
            if (mainImage) {
                mainImage.src = imageSrc;
                
                // Update active thumbnail
                document.querySelectorAll('.thumbnail-image').forEach(thumb => {
                    thumb.classList.remove('active');
                    if (thumb.src === imageSrc) {
                        thumb.classList.add('active');
                    }
                });
            }
        }
    </script>
</body>
</html>
