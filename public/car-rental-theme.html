<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RideEasy Car Rentals - आसान कार रेंटल सेवा</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 600"><path d="M0,300 Q300,200 600,300 T1200,300 L1200,600 L0,600 Z" fill="rgba(255,255,255,0.1)"/></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .tagline {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .cta-button {
            display: inline-block;
            background: #ff6b6b;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        .cta-button:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
        }

        /* Services Section */
        .services {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 20px;
            color: #333;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 50px;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }

        .service-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-5px);
        }

        .service-icon {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .service-title {
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #333;
        }

        .service-description {
            color: #666;
            margin-bottom: 20px;
        }

        .service-price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #667eea;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .hero {
                padding: 60px 0;
            }
            
            .logo {
                font-size: 2rem;
            }
            
            .tagline {
                font-size: 1rem;
            }
            
            .cta-button {
                padding: 12px 25px;
                font-size: 1rem;
            }

            .services {
                padding: 60px 0;
            }

            .section-title {
                font-size: 2rem;
            }

            .services-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .service-card {
                padding: 25px;
            }
        }

        /* Booking Section */
        .booking {
            padding: 80px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .booking-form {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 8px;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.9);
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
        }

        .form-group input::placeholder,
        .form-group textarea::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #ff6b6b;
            background: rgba(255, 255, 255, 0.2);
        }

        .submit-btn {
            background: #ff6b6b;
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .booking {
                padding: 60px 0;
            }

            .booking-form {
                padding: 30px 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        /* Testimonials Section */
        .testimonials {
            padding: 80px 0;
            background: #f8f9fa;
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .testimonial-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 4rem;
            color: #667eea;
            opacity: 0.3;
        }

        .testimonial-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: #555;
            margin-bottom: 20px;
            font-style: italic;
        }

        .testimonial-author {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .author-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .author-info h4 {
            margin: 0;
            color: #333;
            font-size: 1.1rem;
        }

        .author-info p {
            margin: 5px 0 0 0;
            color: #666;
            font-size: 0.9rem;
        }

        .rating {
            color: #ffc107;
            font-size: 1.2rem;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .testimonials {
                padding: 60px 0;
            }

            .testimonials-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .testimonial-card {
                padding: 25px;
            }
        }

        /* Contact Section */
        .contact {
            padding: 80px 0;
            background: #333;
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 40px;
            margin-top: 50px;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .contact-info h3 {
            color: #ff6b6b;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }

        .contact-icon {
            font-size: 1.5rem;
            color: #667eea;
        }

        .contact-text {
            flex: 1;
        }

        .contact-text strong {
            display: block;
            margin-bottom: 5px;
        }

        .map-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
        }

        .map-placeholder {
            background: #667eea;
            height: 200px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            margin-bottom: 15px;
        }

        /* Floating Chat Button */
        .floating-chat {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .floating-chat:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(102, 126, 234, 0.6);
        }

        .floating-chat::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: #ff6b6b;
            border-radius: 50%;
            top: -5px;
            right: -5px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.2); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Footer */
        .footer {
            background: #222;
            color: white;
            text-align: center;
            padding: 30px 0;
        }

        .footer p {
            margin: 0;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .contact {
                padding: 60px 0;
            }

            .contact-grid {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .contact-info {
                padding: 25px;
            }

            .floating-chat {
                bottom: 20px;
                right: 20px;
                width: 50px;
                height: 50px;
                font-size: 1.3rem;
            }
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="logo">🚗 RideEasy</div>
                <p class="tagline">विश्वसनीय कार रेंटल सेवा - आपकी यात्रा, हमारी जिम्मेदारी</p>
                <a href="#booking" class="cta-button">अभी बुक करें</a>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="container">
            <h2 class="section-title">हमारी सेवाएं</h2>
            <p class="section-subtitle">आपकी हर जरूरत के लिए बेहतरीन कार रेंटल सेवा</p>
            
            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">🚗</div>
                    <h3 class="service-title">इकॉनमी कार</h3>
                    <p class="service-description">शहर में घूमने के लिए किफायती और आरामदायक कारें</p>
                    <div class="service-price">₹12/किमी से शुरू</div>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🚙</div>
                    <h3 class="service-title">SUV/MUV</h3>
                    <p class="service-description">परिवार और दोस्तों के साथ यात्रा के लिए विशाल कारें</p>
                    <div class="service-price">₹18/किमी से शुरू</div>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🏎️</div>
                    <h3 class="service-title">प्रीमियम कार</h3>
                    <p class="service-description">खास मौकों के लिए लक्जरी और स्टाइलिश कारें</p>
                    <div class="service-price">₹25/किमी से शुरू</div>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">👨‍✈️</div>
                    <h3 class="service-title">ड्राइवर के साथ</h3>
                    <p class="service-description">अनुभवी ड्राइवर के साथ आरामदायक यात्रा</p>
                    <div class="service-price">₹15/किमी से शुरू</div>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">🕐</div>
                    <h3 class="service-title">घंटे के हिसाब</h3>
                    <p class="service-description">कम समय के लिए फ्लेक्सिबल रेंटल सेवा</p>
                    <div class="service-price">₹200/घंटा से शुरू</div>
                </div>
                
                <div class="service-card">
                    <div class="service-icon">📅</div>
                    <h3 class="service-title">दैनिक पैकेज</h3>
                    <p class="service-description">पूरे दिन के लिए बेस्ट डील और सुविधा</p>
                    <div class="service-price">₹2500/दिन से शुरू</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking">
        <div class="container">
            <h2 class="section-title">अभी बुक करें</h2>
            <p class="section-subtitle">आसान बुकिंग प्रोसेस - केवल 2 मिनट में</p>
            
            <div class="booking-form">
                <form id="bookingForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="name">आपका नाम *</label>
                            <input type="text" id="name" name="name" placeholder="अपना पूरा नाम लिखें" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">मोबाइल नंबर *</label>
                            <input type="tel" id="phone" name="phone" placeholder="+91 98765 43210" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="carType">कार का प्रकार *</label>
                            <select id="carType" name="carType" required>
                                <option value="">कार का प्रकार चुनें</option>
                                <option value="economy">इकॉनमी कार</option>
                                <option value="suv">SUV/MUV</option>
                                <option value="premium">प्रीमियम कार</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="rentalType">रेंटल प्रकार *</label>
                            <select id="rentalType" name="rentalType" required>
                                <option value="">रेंटल प्रकार चुनें</option>
                                <option value="hourly">घंटे के हिसाब</option>
                                <option value="daily">दैनिक</option>
                                <option value="weekly">साप्ताहिक</option>
                                <option value="monthly">मासिक</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="pickupDate">पिकअप दिनांक *</label>
                            <input type="date" id="pickupDate" name="pickupDate" required>
                        </div>
                        <div class="form-group">
                            <label for="pickupTime">पिकअप समय *</label>
                            <input type="time" id="pickupTime" name="pickupTime" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="returnDate">वापसी दिनांक *</label>
                            <input type="date" id="returnDate" name="returnDate" required>
                        </div>
                        <div class="form-group">
                            <label for="returnTime">वापसी समय *</label>
                            <input type="time" id="returnTime" name="returnTime" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="needDriver">क्या आपको ड्राइवर चाहिए?</label>
                            <select id="needDriver" name="needDriver">
                                <option value="no">नहीं</option>
                                <option value="yes">हां</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="pickupLocation">पिकअप लोकेशन</label>
                            <input type="text" id="pickupLocation" name="pickupLocation" placeholder="पिकअप का पता">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="requirements">विशेष आवश्यकताएं</label>
                        <textarea id="requirements" name="requirements" rows="3" placeholder="कोई विशेष जरूरत हो तो बताएं..."></textarea>
                    </div>
                    
                    <button type="submit" class="submit-btn">बुकिंग कन्फर्म करें</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2 class="section-title">ग्राहकों के रिव्यू</h2>
            <p class="section-subtitle">हमारे खुश ग्राहकों के अनुभव</p>
            
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <p class="testimonial-text">बहुत ही अच्छी सेवा! कार बिल्कुल साफ और अच्छी हालत में मिली। ड्राइवर भी बहुत विनम्र और अनुभवी था। पूरे परिवार के साथ गोआ की यात्रा बहुत आरामदायक रही।</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">R</div>
                        <div class="author-info">
                            <h4>राहुल शर्मा</h4>
                            <p>बिजनेसमैन, मुंबई</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <p class="testimonial-text">आपातकालीन यात्रा के लिए तुरंत कार मिल गई। बुकिंग प्रोसेस बहुत आसान है और कीमत भी बहुत रीजनेबल है। आगे भी इसी सेवा का इस्तेमाल करूंगा।</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">P</div>
                        <div class="author-info">
                            <h4>प्रिया गुप्ता</h4>
                            <p>डॉक्टर, दिल्ली</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <p class="testimonial-text">शादी के लिए लक्जरी कार बुक की थी। कार बिल्कुल नई जैसी थी और समय पर मिल गई। सभी ने कार की तारीफ की। RideEasy की सेवा वाकई बेहतरीन है!</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">A</div>
                        <div class="author-info">
                            <h4>अमित वर्मा</h4>
                            <p>इंजीनियर, बेंगलुरु</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                </div>
                
                <div class="testimonial-card">
                    <p class="testimonial-text">बिजनेस ट्रिप के लिए रेगुलर इस्तेमाल करती हूँ। हमेशा समय पर और प्रोफेशनल सेवा मिलती है। कस्टमर सपोर्ट भी बहुत अच्छा है। मैं इन्हें सभी को रिकमेंड करती हूँ।</p>
                    <div class="testimonial-author">
                        <div class="author-avatar">S</div>
                        <div class="author-info">
                            <h4>सुनीता सिंह</h4>
                            <p>कंसल्टेंट, पुणे</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact">
        <div class="container">
            <h2 class="section-title">हमसे संपर्क करें</h2>
            <p class="section-subtitle">24/7 कस्टमर सपोर्ट और तुरंत सेवा</p>
            
            <div class="contact-grid">
                <div class="contact-info">
                    <h3>संपर्क जानकारी</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-text">
                            <strong>फोन नंबर</strong>
                            +91 98765 43210<br>
                            +91 87654 32109
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-text">
                            <strong>ईमेल</strong>
                            <EMAIL><br>
                            <EMAIL>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-text">
                            <strong>WhatsApp</strong>
                            +91 98765 43210
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🕐</div>
                        <div class="contact-text">
                            <strong>खुलने का समय</strong>
                            सोमवार - रविवार<br>
                            सुबह 6:00 - रात 11:00
                        </div>
                    </div>
                </div>
                
                <div class="contact-info">
                    <h3>हमारा कार्यालय</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-text">
                            <strong>पता</strong>
                            123, मेइन रोड,<br>
                            कॉमर्शियल कॉम्प्लेक्स,<br>
                            मुंबई - 400001
                        </div>
                    </div>
                    
                    <div class="map-container">
                        <div class="map-placeholder">
                            🗺️ गूगल मैप यहाँ होगा
                        </div>
                        <p>क्लिक करके दिशा देखें</p>
                    </div>
                </div>
                
                <div class="contact-info">
                    <h3>तुरंत बुकिंग</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">⚡</div>
                        <div class="contact-text">
                            <strong>आपातकालीन सेवा</strong>
                            24/7 उपलब्ध
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🚗</div>
                        <div class="contact-text">
                            <strong>तुरंत कार उपलब्ध</strong>
                            30 मिनट में
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">💳</div>
                        <div class="contact-text">
                            <strong>पेमेंट</strong>
                            कैश, UPI, कार्ड<br>
                            सभी विकल्प उपलब्ध
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 RideEasy Car Rentals. सभी अधिकार सुरक्षित। | आपकी यात्रा, हमारी जिम्मेदारी</p>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <div class="floating-chat" onclick="openChat()">
        💬
    </div>

    <script>
        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('pickupDate').min = today;
        document.getElementById('returnDate').min = today;

        // Update return date when pickup date changes
        document.getElementById('pickupDate').addEventListener('change', function() {
            const pickupDate = this.value;
            document.getElementById('returnDate').min = pickupDate;
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // Create WhatsApp message
            const message = `नमस्ते! मैं RideEasy से कार बुक करना चाहता/चाहती हूँ:\n\nनाम: ${data.name}\nमोबाइल: ${data.phone}\nकार का प्रकार: ${data.carType}\nरेंटल प्रकार: ${data.rentalType}\nपिकअप: ${data.pickupDate} ${data.pickupTime}\nवापसी: ${data.returnDate} ${data.returnTime}\nड्राइवर: ${data.needDriver === 'yes' ? 'हां' : 'नहीं'}\nपिकअप लोकेशन: ${data.pickupLocation || 'नहीं बताया'}\nविशेष आवश्यकता: ${data.requirements || 'कोई नहीं'}`;
            
            // Open WhatsApp
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
            
            // Show success message
            alert('आपकी बुकिंग रिक्वेस्ट WhatsApp पर भेज दी गई है! हम जल्दी आपसे संपर्क करेंगे।');
        });

        // Floating chat function
        function openChat() {
            const message = 'नमस्ते! मैं RideEasy की सेवाओं के बारे में जानकारी चाहता/चाहती हूँ।';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

</body>
</html>