<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DriveMax - Premium Car Rental Service</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #f59e0b;
            --accent-color: #10b981;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --text-light: #9ca3af;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --bg-dark: #111827;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-accent: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background-color: var(--bg-primary);
            overflow-x: hidden;
        }

        .container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(255, 255, 255, 0.98);
            box-shadow: var(--shadow-md);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--primary-color);
            text-decoration: none;
        }

        .logo i {
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--text-primary);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--primary-color);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-cta {
            background: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-cta:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-primary);
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            background: var(--gradient-primary);
            color: white;
            padding: 8rem 0 6rem;
            position: relative;
            overflow: hidden;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,117.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 2rem;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: clamp(2.5rem, 5vw, 4rem);
            font-weight: 800;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .hero-subtitle {
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            margin-bottom: 3rem;
            opacity: 0.9;
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 0.75rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
            background: var(--bg-secondary);
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            background: var(--gradient-accent);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            color: white;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Services Section */
        .services {
            padding: 6rem 0;
            background: white;
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .service-card {
            background: var(--bg-secondary);
            padding: 2.5rem;
            border-radius: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-primary);
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-xl);
            background: white;
        }

        .service-icon {
            width: 5rem;
            height: 5rem;
            background: var(--gradient-secondary);
            border-radius: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .service-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .service-features {
            list-style: none;
            text-align: left;
        }

        .service-features li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .service-features li i {
            color: var(--accent-color);
            font-size: 0.75rem;
        }

        /* Booking Section */
        .booking {
            padding: 6rem 0;
            background: var(--gradient-primary);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .booking::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" d="M0,32L48,37.3C96,43,192,53,288,80C384,107,480,149,576,154.7C672,160,768,128,864,128C960,128,1056,160,1152,165.3C1248,171,1344,149,1392,138.7L1440,128L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"></path></svg>') no-repeat center top;
            background-size: cover;
        }

        .booking-container {
            position: relative;
            z-index: 2;
        }

        .booking-form {
            background: rgba(255, 255, 255, 0.1);
            padding: 3rem;
            border-radius: 2rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 800px;
            margin: 0 auto;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.875rem;
        }

        .form-input {
            padding: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--secondary-color);
            background: rgba(255, 255, 255, 0.2);
            box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .submit-btn {
            background: var(--secondary-color);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 0.75rem;
            font-size: 1.125rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            background: #d97706;
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        /* Testimonials Section */
        .testimonials {
            padding: 6rem 0;
            background: var(--bg-secondary);
        }

        .testimonials-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
        }

        .testimonial-card {
            background: white;
            padding: 2.5rem;
            border-radius: 1.5rem;
            box-shadow: var(--shadow-md);
            position: relative;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .testimonial-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .testimonial-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--gradient-accent);
            border-radius: 1.5rem 1.5rem 0 0;
        }

        .testimonial-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .author-avatar {
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
            font-size: 1.25rem;
        }

        .author-info h4 {
            margin: 0;
            color: var(--text-primary);
            font-size: 1.125rem;
            font-weight: 700;
        }

        .author-info p {
            margin: 0.25rem 0 0 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .rating {
            color: var(--secondary-color);
            font-size: 1rem;
            margin-top: 0.25rem;
        }

        .testimonial-text {
            font-size: 1rem;
            line-height: 1.7;
            color: var(--text-secondary);
            font-style: italic;
        }

        .quote-icon {
            position: absolute;
            top: 1.5rem;
            right: 1.5rem;
            font-size: 2rem;
            color: var(--primary-color);
            opacity: 0.2;
        }

        /* Stats Section */
        .stats {
            padding: 4rem 0;
            background: var(--bg-dark);
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            text-align: center;
        }

        .stat-item {
            padding: 1.5rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            display: block;
        }

        .stat-label {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
        }

        /* Contact Section */
        .contact {
            padding: 6rem 0;
            background: var(--bg-dark);
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
        }

        .contact-info {
            background: rgba(255, 255, 255, 0.05);
            padding: 2.5rem;
            border-radius: 1.5rem;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .contact-info h3 {
            color: var(--secondary-color);
            margin-bottom: 2rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .contact-icon {
            width: 3rem;
            height: 3rem;
            background: var(--gradient-accent);
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
            flex-shrink: 0;
        }

        .contact-text {
            flex: 1;
        }

        .contact-text strong {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: white;
        }

        .contact-text span {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* Floating Chat Button */
        .floating-chat {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 4rem;
            height: 4rem;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s ease;
            z-index: 1000;
            border: none;
        }

        .floating-chat:hover {
            transform: scale(1.1);
            box-shadow: 0 8px 30px rgba(37, 99, 235, 0.4);
        }

        .floating-chat::after {
            content: '';
            position: absolute;
            width: 1rem;
            height: 1rem;
            background: var(--secondary-color);
            border-radius: 50%;
            top: -0.25rem;
            right: -0.25rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.3); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        /* Footer */
        .footer {
            background: var(--bg-dark);
            color: white;
            padding: 3rem 0 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .footer-section h4 {
            color: var(--secondary-color);
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .footer-section p,
        .footer-section a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            line-height: 1.6;
            margin-bottom: 0.5rem;
            display: block;
        }

        .footer-section a:hover {
            color: var(--secondary-color);
        }

        .footer-bottom {
            text-align: center;
            padding-top: 2rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.6);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                padding: 6rem 0 4rem;
                min-height: 80vh;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .features,
            .services,
            .booking,
            .testimonials,
            .contact {
                padding: 4rem 0;
            }

            .features-grid,
            .services-grid,
            .testimonials-grid,
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .booking-form {
                padding: 2rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .floating-chat {
                bottom: 1.5rem;
                right: 1.5rem;
                width: 3.5rem;
                height: 3.5rem;
                font-size: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 0.75rem;
            }

            .hero-title {
                font-size: 2rem;
            }

            .section-title {
                font-size: 1.75rem;
            }

            .feature-card,
            .service-card,
            .testimonial-card,
            .contact-info {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="logo">
                    <i class="fas fa-car"></i>
                    DriveMax
                </a>

                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">होम</a></li>
                    <li><a href="#services" class="nav-link">सेवाएं</a></li>
                    <li><a href="#booking" class="nav-link">बुकिंग</a></li>
                    <li><a href="#testimonials" class="nav-link">रिव्यू</a></li>
                    <li><a href="#contact" class="nav-link">संपर्क</a></li>
                </ul>

                <a href="#booking" class="nav-cta">अभी बुक करें</a>

                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    भारत की #1 कार रेंटल सेवा
                </div>

                <h1 class="hero-title">
                    आपकी यात्रा का<br>
                    <span style="background: linear-gradient(135deg, #f59e0b, #d97706); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">सबसे बेहतर साथी</span>
                </h1>

                <p class="hero-subtitle">
                    24/7 उपलब्ध, सुरक्षित और किफायती कार रेंटल सेवा।
                    अब आपकी मंजिल सिर्फ एक क्लिक दूर है।
                </p>

                <div class="hero-buttons">
                    <a href="#booking" class="btn btn-primary">
                        <i class="fas fa-calendar-check"></i>
                        अभी बुक करें
                    </a>
                    <a href="#services" class="btn btn-secondary">
                        <i class="fas fa-info-circle"></i>
                        सेवाएं देखें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">क्यों चुनें DriveMax</div>
                <h2 class="section-title">हमारी विशेषताएं</h2>
                <p class="section-subtitle">
                    हम आपको सबसे बेहतरीन कार रेंटल अनुभव प्रदान करते हैं
                </p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">100% सुरक्षित</h3>
                    <p class="feature-description">
                        सभी कारें पूरी तरह से इंश्योर्ड हैं और नियमित सर्विसिंग के साथ मेंटेन की जाती हैं।
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="feature-title">24/7 सपोर्ट</h3>
                    <p class="feature-description">
                        किसी भी समय, कहीं भी समस्या हो तो हमारी टीम आपकी मदद के लिए तैयार है।
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-rupee-sign"></i>
                    </div>
                    <h3 class="feature-title">बेस्ट प्राइस</h3>
                    <p class="feature-description">
                        मार्केट में सबसे कम दरों पर प्रीमियम कार रेंटल सेवा का लाभ उठाएं।
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">आसान बुकिंग</h3>
                    <p class="feature-description">
                        सिर्फ 2 मिनट में ऑनलाइन बुकिंग करें और तुरंत कन्फर्मेशन पाएं।
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="feature-title">नई कारें</h3>
                    <p class="feature-description">
                        हमारे पास सभी लेटेस्ट मॉडल की साफ-सुथरी और अच्छी कंडीशन की कारें हैं।
                    </p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3 class="feature-title">GPS ट्रैकिंग</h3>
                    <p class="feature-description">
                        सभी कारों में GPS ट्रैकिंग सिस्टम है जो आपकी सुरक्षा को बढ़ाता है।
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">हमारी सेवाएं</div>
                <h2 class="section-title">कार रेंटल पैकेज</h2>
                <p class="section-subtitle">
                    आपकी हर जरूरत के लिए बेहतरीन कार रेंटल सेवा
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="service-title">इकॉनमी कार</h3>
                    <p class="service-description">शहर में घूमने के लिए किफायती और आरामदायक कारें</p>
                    <div class="service-price">₹12/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> AC और म्यूजिक सिस्टम</li>
                        <li><i class="fas fa-check"></i> फ्री इंश्योरेंस</li>
                        <li><i class="fas fa-check"></i> 24/7 रोडसाइड असिस्टेंस</li>
                    </ul>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3 class="service-title">SUV/MUV</h3>
                    <p class="service-description">परिवार और दोस्तों के साथ यात्रा के लिए विशाल कारें</p>
                    <div class="service-price">₹18/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> 7-8 सीटर कैपेसिटी</li>
                        <li><i class="fas fa-check"></i> बड़ा लगेज स्पेस</li>
                        <li><i class="fas fa-check"></i> GPS नेवीगेशन</li>
                    </ul>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="service-title">प्रीमियम कार</h3>
                    <p class="service-description">खास मौकों के लिए लक्जरी और स्टाइलिश कारें</p>
                    <div class="service-price">₹25/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> लक्जरी इंटीरियर</li>
                        <li><i class="fas fa-check"></i> प्रीमियम साउंड सिस्टम</li>
                        <li><i class="fas fa-check"></i> चमड़े की सीटें</li>
                    </ul>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="service-title">ड्राइवर के साथ</h3>
                    <p class="service-description">अनुभवी ड्राइवर के साथ आरामदायक यात्रा</p>
                    <div class="service-price">₹15/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> अनुभवी ड्राइवर</li>
                        <li><i class="fas fa-check"></i> लोकल एरिया नॉलेज</li>
                        <li><i class="fas fa-check"></i> सेफ ड्राइविंग</li>
                    </ul>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="service-title">घंटे के हिसाब</h3>
                    <p class="service-description">कम समय के लिए फ्लेक्सिबल रेंटल सेवा</p>
                    <div class="service-price">₹200/घंटा से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> मिनिमम 4 घंटे</li>
                        <li><i class="fas fa-check"></i> फ्री वेटिंग टाइम</li>
                        <li><i class="fas fa-check"></i> फ्लेक्सिबल टाइमिंग</li>
                    </ul>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h3 class="service-title">दैनिक पैकेज</h3>
                    <p class="service-description">पूरे दिन के लिए बेस्ट डील और सुविधा</p>
                    <div class="service-price">₹2500/दिन से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> 12 घंटे/300 किमी</li>
                        <li><i class="fas fa-check"></i> फ्री फ्यूल</li>
                        <li><i class="fas fa-check"></i> ड्राइवर अलाउंस इंक्लूडेड</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">50K+</span>
                    <span class="stat-label">खुश ग्राहक</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">कारों का फ्लीट</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">25+</span>
                    <span class="stat-label">शहरों में सेवा</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">कस्टमर सपोर्ट</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking">
        <div class="container booking-container">
            <div class="section-header">
                <div class="section-badge">तुरंत बुकिंग</div>
                <h2 class="section-title">अभी बुक करें</h2>
                <p class="section-subtitle">आसान बुकिंग प्रोसेस - केवल 2 मिनट में</p>
            </div>

            <div class="booking-form">
                <form id="bookingForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="name">आपका नाम *</label>
                            <input type="text" id="name" name="name" class="form-input" placeholder="अपना पूरा नाम लिखें" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="phone">मोबाइल नंबर *</label>
                            <input type="tel" id="phone" name="phone" class="form-input" placeholder="+91 98765 43210" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="carType">कार का प्रकार *</label>
                            <select id="carType" name="carType" class="form-input" required>
                                <option value="">कार का प्रकार चुनें</option>
                                <option value="economy">इकॉनमी कार</option>
                                <option value="suv">SUV/MUV</option>
                                <option value="premium">प्रीमियम कार</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="rentalType">रेंटल प्रकार *</label>
                            <select id="rentalType" name="rentalType" class="form-input" required>
                                <option value="">रेंटल प्रकार चुनें</option>
                                <option value="hourly">घंटे के हिसाब</option>
                                <option value="daily">दैनिक</option>
                                <option value="weekly">साप्ताहिक</option>
                                <option value="monthly">मासिक</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="pickupDate">पिकअप दिनांक *</label>
                            <input type="date" id="pickupDate" name="pickupDate" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="pickupTime">पिकअप समय *</label>
                            <input type="time" id="pickupTime" name="pickupTime" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="returnDate">वापसी दिनांक *</label>
                            <input type="date" id="returnDate" name="returnDate" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="returnTime">वापसी समय *</label>
                            <input type="time" id="returnTime" name="returnTime" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="needDriver">क्या आपको ड्राइवर चाहिए?</label>
                            <select id="needDriver" name="needDriver" class="form-input">
                                <option value="no">नहीं</option>
                                <option value="yes">हां</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="pickupLocation">पिकअप लोकेशन</label>
                            <input type="text" id="pickupLocation" name="pickupLocation" class="form-input" placeholder="पिकअप का पता">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="requirements">विशेष आवश्यकताएं</label>
                        <textarea id="requirements" name="requirements" rows="3" class="form-input form-textarea" placeholder="कोई विशेष जरूरत हो तो बताएं..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-paper-plane"></i>
                        बुकिंग कन्फर्म करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials" id="testimonials">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">ग्राहकों के रिव्यू</div>
                <h2 class="section-title">हमारे खुश ग्राहकों के अनुभव</h2>
                <p class="section-subtitle">
                    हजारों संतुष्ट ग्राहकों का भरोसा और प्यार
                </p>
            </div>

            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <i class="fas fa-quote-right quote-icon"></i>
                    <div class="testimonial-header">
                        <div class="author-avatar">R</div>
                        <div class="author-info">
                            <h4>राहुल शर्मा</h4>
                            <p>बिजनेसमैन, मुंबई</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                    <p class="testimonial-text">
                        बहुत ही अच्छी सेवा! कार बिल्कुल साफ और अच्छी हालत में मिली। ड्राइवर भी बहुत विनम्र और अनुभवी था। पूरे परिवार के साथ गोआ की यात्रा बहुत आरामदायक रही।
                    </p>
                </div>

                <div class="testimonial-card">
                    <i class="fas fa-quote-right quote-icon"></i>
                    <div class="testimonial-header">
                        <div class="author-avatar">P</div>
                        <div class="author-info">
                            <h4>प्रिया गुप्ता</h4>
                            <p>डॉक्टर, दिल्ली</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                    <p class="testimonial-text">
                        आपातकालीन यात्रा के लिए तुरंत कार मिल गई। बुकिंग प्रोसेस बहुत आसान है और कीमत भी बहुत रीजनेबल है। आगे भी इसी सेवा का इस्तेमाल करूंगा।
                    </p>
                </div>

                <div class="testimonial-card">
                    <i class="fas fa-quote-right quote-icon"></i>
                    <div class="testimonial-header">
                        <div class="author-avatar">A</div>
                        <div class="author-info">
                            <h4>अमित वर्मा</h4>
                            <p>इंजीनियर, बेंगलुरु</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                    <p class="testimonial-text">
                        शादी के लिए लक्जरी कार बुक की थी। कार बिल्कुल नई जैसी थी और समय पर मिल गई। सभी ने कार की तारीफ की। DriveMax की सेवा वाकई बेहतरीन है!
                    </p>
                </div>

                <div class="testimonial-card">
                    <i class="fas fa-quote-right quote-icon"></i>
                    <div class="testimonial-header">
                        <div class="author-avatar">S</div>
                        <div class="author-info">
                            <h4>सुनीता सिंह</h4>
                            <p>कंसल्टेंट, पुणे</p>
                            <div class="rating">★★★★★</div>
                        </div>
                    </div>
                    <p class="testimonial-text">
                        बिजनेस ट्रिप के लिए रेगुलर इस्तेमाल करती हूँ। हमेशा समय पर और प्रोफेशनल सेवा मिलती है। कस्टमर सपोर्ट भी बहुत अच्छा है। मैं इन्हें सभी को रिकमेंड करती हूँ।
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">संपर्क करें</div>
                <h2 class="section-title">हमसे जुड़ें</h2>
                <p class="section-subtitle">
                    24/7 कस्टमर सपोर्ट और तुरंत सेवा के लिए संपर्क करें
                </p>
            </div>

            <div class="contact-grid">
                <div class="contact-info">
                    <h3>संपर्क जानकारी</h3>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-text">
                            <strong>फोन नंबर</strong>
                            <span>+91 98765 43210<br>+91 87654 32109</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-text">
                            <strong>ईमेल</strong>
                            <span><EMAIL><br><EMAIL></span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fab fa-whatsapp"></i>
                        </div>
                        <div class="contact-text">
                            <strong>WhatsApp</strong>
                            <span>+91 98765 43210</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-text">
                            <strong>खुलने का समय</strong>
                            <span>सोमवार - रविवार<br>सुबह 6:00 - रात 11:00</span>
                        </div>
                    </div>
                </div>

                <div class="contact-info">
                    <h3>हमारा कार्यालय</h3>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-text">
                            <strong>पता</strong>
                            <span>123, मेइन रोड,<br>कॉमर्शियल कॉम्प्लेक्स,<br>मुंबई - 400001</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-city"></i>
                        </div>
                        <div class="contact-text">
                            <strong>सेवा क्षेत्र</strong>
                            <span>मुंबई, दिल्ली, बेंगलुरु,<br>पुणे, हैदराबाद, चेन्नई</span>
                        </div>
                    </div>
                </div>

                <div class="contact-info">
                    <h3>तुरंत बुकिंग</h3>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-bolt"></i>
                        </div>
                        <div class="contact-text">
                            <strong>आपातकालीन सेवा</strong>
                            <span>24/7 उपलब्ध</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="contact-text">
                            <strong>तुरंत कार उपलब्ध</strong>
                            <span>30 मिनट में</span>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-credit-card"></i>
                        </div>
                        <div class="contact-text">
                            <strong>पेमेंट</strong>
                            <span>कैश, UPI, कार्ड<br>सभी विकल्प उपलब्ध</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>DriveMax</h4>
                    <p>भारत की सबसे भरोसेमंद कार रेंटल सेवा। हमारा मिशन है आपकी यात्रा को सुरक्षित, आरामदायक और किफायती बनाना।</p>
                </div>

                <div class="footer-section">
                    <h4>सेवाएं</h4>
                    <a href="#services">इकॉनमी कार</a>
                    <a href="#services">SUV/MUV</a>
                    <a href="#services">प्रीमियम कार</a>
                    <a href="#services">ड्राइवर के साथ</a>
                </div>

                <div class="footer-section">
                    <h4>कंपनी</h4>
                    <a href="#about">हमारे बारे में</a>
                    <a href="#contact">संपर्क करें</a>
                    <a href="#careers">करियर</a>
                    <a href="#privacy">प्राइवेसी पॉलिसी</a>
                </div>

                <div class="footer-section">
                    <h4>सपोर्ट</h4>
                    <a href="#help">हेल्प सेंटर</a>
                    <a href="#faq">FAQ</a>
                    <a href="#terms">नियम और शर्तें</a>
                    <a href="#feedback">फीडबैक</a>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2024 DriveMax Car Rentals. सभी अधिकार सुरक्षित। | आपकी यात्रा, हमारी जिम्मेदारी</p>
            </div>
        </div>
    </footer>

    <!-- Floating Chat Button -->
    <button class="floating-chat" onclick="openChat()">
        <i class="fab fa-whatsapp"></i>
    </button>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.getElementById('pickupDate');
            const returnDate = document.getElementById('returnDate');

            if (pickupDate) pickupDate.min = today;
            if (returnDate) returnDate.min = today;

            // Update return date when pickup date changes
            if (pickupDate) {
                pickupDate.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (returnDate) returnDate.min = selectedDate;
                });
            }
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Validate required fields
            if (!data.name || !data.phone || !data.carType || !data.rentalType || !data.pickupDate || !data.pickupTime || !data.returnDate || !data.returnTime) {
                alert('कृपया सभी आवश्यक फील्ड भरें।');
                return;
            }

            // Create WhatsApp message
            const message = `🚗 *DriveMax Car Rental - नई बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार का प्रकार:* ${data.carType}
📅 *रेंटल प्रकार:* ${data.rentalType}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
🏁 *वापसी:* ${data.returnDate} ${data.returnTime}
👨‍✈️ *ड्राइवर:* ${data.needDriver === 'yes' ? 'हां' : 'नहीं'}
📍 *पिकअप लोकेशन:* ${data.pickupLocation || 'नहीं बताया'}

📝 *विशेष आवश्यकता:* ${data.requirements || 'कोई नहीं'}

कृपया इस बुकिंग को कन्फर्म करें। धन्यवाद!`;

            // Open WhatsApp
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            // Show success message with better styling
            showNotification('✅ आपकी बुकिंग रिक्वेस्ट WhatsApp पर भेज दी गई है! हम जल्दी आपसे संपर्क करेंगे।', 'success');

            // Reset form
            this.reset();
        });

        // Floating chat function
        function openChat() {
            const message = '🚗 नमस्ते! मैं DriveMax की सेवाओं के बारे में जानकारी चाहता/चाहती हूँ।';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        // Notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : '#3b82f6'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 0.5rem;
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                z-index: 10000;
                max-width: 400px;
                font-weight: 500;
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;

            // Add animation styles
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            document.body.appendChild(notification);

            // Remove notification after 5 seconds
            setTimeout(() => {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add loading animation to buttons
        document.querySelectorAll('.btn, .submit-btn').forEach(button => {
            button.addEventListener('click', function() {
                if (this.type !== 'submit') return;

                const originalText = this.innerHTML;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> प्रोसेसिंग...';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        });

        // Intersection Observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for animation
        document.addEventListener('DOMContentLoaded', function() {
            const animatedElements = document.querySelectorAll('.feature-card, .service-card, .testimonial-card');
            animatedElements.forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        });
    </script>

</body>
</html>