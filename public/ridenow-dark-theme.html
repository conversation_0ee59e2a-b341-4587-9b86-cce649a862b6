<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RideNow - Premium Car Rental Service</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #00f5ff;
            --secondary-color: #ff0080;
            --accent-color: #7c3aed;
            --neon-blue: #00f5ff;
            --neon-pink: #ff0080;
            --neon-purple: #8b5cf6;
            --bg-dark: #0a0a0a;
            --bg-darker: #050505;
            --bg-card: rgba(255, 255, 255, 0.05);
            --text-primary: #ffffff;
            --text-secondary: #a1a1aa;
            --text-muted: #71717a;
            --border-color: rgba(255, 255, 255, 0.1);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: var(--bg-dark);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 0, 128, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(124, 58, 237, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        /* Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(10, 10, 10, 0.8);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            background: rgba(10, 10, 10, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--neon-blue);
            text-decoration: none;
            text-shadow: 0 0 20px var(--neon-blue);
        }

        .logo i {
            font-size: 1.8rem;
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px var(--neon-blue); }
            to { text-shadow: 0 0 30px var(--neon-blue), 0 0 40px var(--neon-blue); }
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-link {
            color: var(--text-secondary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            color: var(--neon-blue);
            text-shadow: 0 0 10px var(--neon-blue);
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--neon-blue), var(--neon-pink));
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .nav-cta {
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink));
            color: var(--bg-dark);
            padding: 0.75rem 1.5rem;
            border-radius: 2rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }

        .nav-cta:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            color: var(--text-primary);
            font-size: 1.5rem;
            cursor: pointer;
        }

        /* Hero Section */
        .hero {
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(0,245,255,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,117.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 2rem;
            color: var(--neon-blue);
        }

        .hero-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem);
            font-weight: 900;
            margin-bottom: 1.5rem;
            line-height: 1.1;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink), var(--neon-purple));
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            color: transparent;
            text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        .hero-subtitle {
            font-size: clamp(1.125rem, 2vw, 1.25rem);
            margin-bottom: 3rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 2rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink));
            color: var(--bg-dark);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        .btn-secondary {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            color: var(--text-primary);
            border: 1px solid var(--glass-border);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            border-color: var(--neon-blue);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.2);
        }

        /* Glass Card Base */
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 1.5rem;
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--neon-blue);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            transform: translateY(-4px);
        }

        /* Features Section */
        .features {
            padding: 6rem 0;
        }

        .section-header {
            text-align: center;
            margin-bottom: 4rem;
        }

        .section-badge {
            display: inline-block;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink));
            color: var(--bg-dark);
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .section-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            max-width: 600px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            padding: 2rem;
            text-align: center;
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink));
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 1.5rem;
            color: var(--bg-dark);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--text-primary);
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Services Section */
        .services {
            padding: 6rem 0;
            background: var(--bg-darker);
        }

        .services-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
        }

        .service-card {
            padding: 2.5rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--neon-blue), var(--neon-pink));
        }

        .service-icon {
            width: 5rem;
            height: 5rem;
            background: linear-gradient(135deg, var(--neon-purple), var(--neon-pink));
            border-radius: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            font-size: 2rem;
            color: white;
            box-shadow: 0 0 30px rgba(139, 92, 246, 0.4);
        }

        .service-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .service-description {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .service-price {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--neon-blue);
            margin-bottom: 1rem;
            text-shadow: 0 0 10px var(--neon-blue);
        }

        .service-features {
            list-style: none;
            text-align: left;
        }

        .service-features li {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .service-features li i {
            color: var(--neon-blue);
            font-size: 0.75rem;
        }

        /* Booking Section */
        .booking {
            padding: 6rem 0;
            position: relative;
        }

        .booking::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 70%, rgba(0, 245, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 0, 128, 0.1) 0%, transparent 50%);
        }

        .booking-container {
            position: relative;
            z-index: 2;
        }

        .booking-form {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            padding: 3rem;
            border-radius: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .form-input {
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 0.75rem;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--neon-blue);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.2);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .submit-btn {
            background: linear-gradient(135deg, var(--neon-blue), var(--neon-pink));
            color: var(--bg-dark);
            padding: 1rem 2rem;
            border: none;
            border-radius: 2rem;
            font-size: 1.125rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 1rem;
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .mobile-menu-btn {
                display: block;
            }

            .hero {
                padding: 6rem 0 4rem;
                min-height: 80vh;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }

            .features,
            .services,
            .booking {
                padding: 4rem 0;
            }

            .features-grid,
            .services-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .booking-form {
                padding: 2rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 0 0.75rem;
            }

            .hero-title {
                font-size: 2rem;
            }

            .section-title {
                font-size: 1.75rem;
            }

            .feature-card,
            .service-card {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="logo">
                    <i class="fas fa-bolt"></i>
                    RideNow
                </a>
                
                <ul class="nav-menu">
                    <li><a href="#home" class="nav-link">होम</a></li>
                    <li><a href="#services" class="nav-link">सेवाएं</a></li>
                    <li><a href="#booking" class="nav-link">बुकिंग</a></li>
                    <li><a href="#reviews" class="nav-link">रिव्यू</a></li>
                    <li><a href="#contact" class="nav-link">संपर्क</a></li>
                </ul>
                
                <a href="#booking" class="nav-cta">अभी बुक करें</a>
                
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    प्रीमियम कार रेंटल एक्सपीरियंस
                </div>
                
                <h1 class="hero-title">
                    रात की रफ्तार<br>
                    दिन का भरोसा
                </h1>
                
                <p class="hero-subtitle">
                    अत्याधुनिक तकनीक के साथ सबसे तेज़ और सुरक्षित कार रेंटल सेवा। 
                    24/7 उपलब्ध, तुरंत बुकिंग, प्रीमियम अनुभव।
                </p>
                
                <div class="hero-buttons">
                    <a href="#booking" class="btn btn-primary">
                        <i class="fas fa-rocket"></i>
                        तुरंत बुक करें
                    </a>
                    <a href="#services" class="btn btn-secondary">
                        <i class="fas fa-play"></i>
                        डेमो देखें
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">क्यों चुनें RideNow</div>
                <h2 class="section-title">फ्यूचर ऑफ मोबिलिटी</h2>
                <p class="section-subtitle">
                    अत्याधुनिक तकनीक और प्रीमियम सेवा का संयोजन
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <h3 class="feature-title">इंस्टेंट बुकिंग</h3>
                    <p class="feature-description">
                        AI-powered सिस्टम के साथ 30 सेकंड में कार बुक करें। रियल-टाइम availability और instant confirmation।
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <h3 class="feature-title">एडवांस्ड सिक्योरिटी</h3>
                    <p class="feature-description">
                        GPS ट्रैकिंग, बायोमेट्रिक लॉक्स, और 24/7 मॉनिटरिंग के साथ पूर्ण सुरक्षा गारंटी।
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="feature-title">AI असिस्टेंट</h3>
                    <p class="feature-description">
                        स्मार्ट AI असिस्टेंट आपकी हर जरूरत को समझता है और बेस्ट रूट suggest करता है।
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 class="feature-title">स्मार्ट ऐप</h3>
                    <p class="feature-description">
                        रिमोट कंट्रोल, प्री-कूलिंग, फ्यूल मॉनिटरिंग - सब कुछ आपके फोन में।
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="feature-title">इको-फ्रेंडली</h3>
                    <p class="feature-description">
                        हाइब्रिड और इलेक्ट्रिक कारों का बड़ा फ्लीट। पर्यावरण के साथ जिम्मेदार यात्रा।
                    </p>
                </div>
                
                <div class="feature-card glass-card">
                    <div class="feature-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="feature-title">प्रीमियम एक्सपीरियंस</h3>
                    <p class="feature-description">
                        लक्जरी इंटीरियर, प्रीमियम साउंड सिस्टम, और VIP ट्रीटमेंट हर राइड में।
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services" id="services">
        <div class="container">
            <div class="section-header">
                <div class="section-badge">हमारी सेवाएं</div>
                <h2 class="section-title">प्रीमियम कार कलेक्शन</h2>
                <p class="section-subtitle">
                    हर मूड और जरूरत के लिए परफेक्ट कार
                </p>
            </div>

            <div class="services-grid">
                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-car"></i>
                    </div>
                    <h3 class="service-title">स्मार्ट इकॉनमी</h3>
                    <p class="service-description">AI-enabled इकॉनमी कारें जो फ्यूल एफिशिएंसी और कम्फर्ट का परफेक्ट बैलेंस देती हैं</p>
                    <div class="service-price">₹15/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> स्मार्ट नेवीगेशन सिस्टम</li>
                        <li><i class="fas fa-check"></i> ऑटो क्लाइमेट कंट्रोल</li>
                        <li><i class="fas fa-check"></i> वायरलेस चार्जिंग</li>
                        <li><i class="fas fa-check"></i> AI वॉयस असिस्टेंट</li>
                    </ul>
                </div>

                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3 class="service-title">लक्जरी SUV</h3>
                    <p class="service-description">प्रीमियम SUVs जो स्पेस, पावर और लक्जरी का अनुभव देती हैं</p>
                    <div class="service-price">₹25/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> 7-8 सीटर कैपेसिटी</li>
                        <li><i class="fas fa-check"></i> पैनोरामिक सनरूफ</li>
                        <li><i class="fas fa-check"></i> प्रीमियम साउंड सिस्टम</li>
                        <li><i class="fas fa-check"></i> एडवांस्ड सेफ्टी फीचर्स</li>
                    </ul>
                </div>

                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="service-title">अल्ट्रा प्रीमियम</h3>
                    <p class="service-description">टॉप-ऑफ-द-लाइन लक्जरी कारें जो रॉयल एक्सपीरियंस देती हैं</p>
                    <div class="service-price">₹40/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> हैंडक्राफ्टेड इंटीरियर</li>
                        <li><i class="fas fa-check"></i> मसाज सीट्स</li>
                        <li><i class="fas fa-check"></i> एंबिएंट लाइटिंग</li>
                        <li><i class="fas fa-check"></i> पर्सनल कॉन्सियर्ज</li>
                    </ul>
                </div>

                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="service-title">इको-इलेक्ट्रिक</h3>
                    <p class="service-description">100% इलेक्ट्रिक कारें जो पर्यावरण के साथ-साथ आपके बजट को भी बचाती हैं</p>
                    <div class="service-price">₹18/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> जीरो एमिशन</li>
                        <li><i class="fas fa-check"></i> फास्ट चार्जिंग सपोर्ट</li>
                        <li><i class="fas fa-check"></i> 400+ किमी रेंज</li>
                        <li><i class="fas fa-check"></i> साइलेंट ऑपरेशन</li>
                    </ul>
                </div>

                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="service-title">प्रो ड्राइवर</h3>
                    <p class="service-description">ट्रेंड और एक्सपर्ट ड्राइवर्स जो आपकी यात्रा को मेमोरेबल बनाते हैं</p>
                    <div class="service-price">₹20/किमी से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> वेरिफाइड प्रोफेशनल्स</li>
                        <li><i class="fas fa-check"></i> मल्टी-लैंग्वेज सपोर्ट</li>
                        <li><i class="fas fa-check"></i> लोकल एक्सपर्टीज</li>
                        <li><i class="fas fa-check"></i> 5-स्टार रेटेड</li>
                    </ul>
                </div>

                <div class="service-card glass-card">
                    <div class="service-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="service-title">फ्लेक्स टाइम</h3>
                    <p class="service-description">घंटे, दिन या महीने के हिसाब से फ्लेक्सिबल रेंटल प्लान्स</p>
                    <div class="service-price">₹300/घंटा से शुरू</div>
                    <ul class="service-features">
                        <li><i class="fas fa-check"></i> मिनिमम 2 घंटे</li>
                        <li><i class="fas fa-check"></i> फ्री वेटिंग टाइम</li>
                        <li><i class="fas fa-check"></i> एक्सटेंड ऑप्शन</li>
                        <li><i class="fas fa-check"></i> नो हिडन चार्जेस</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Booking Section -->
    <section class="booking" id="booking">
        <div class="container booking-container">
            <div class="section-header">
                <div class="section-badge">इंस्टेंट बुकिंग</div>
                <h2 class="section-title">अभी बुक करें</h2>
                <p class="section-subtitle">30 सेकंड में कार बुक करें, AI की मदद से</p>
            </div>

            <div class="booking-form">
                <form id="bookingForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="name">आपका नाम *</label>
                            <input type="text" id="name" name="name" class="form-input" placeholder="अपना पूरा नाम लिखें" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="phone">मोबाइल नंबर *</label>
                            <input type="tel" id="phone" name="phone" class="form-input" placeholder="+91 98765 43210" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="carType">कार कैटेगरी *</label>
                            <select id="carType" name="carType" class="form-input" required>
                                <option value="">कार कैटेगरी चुनें</option>
                                <option value="smart-economy">स्मार्ट इकॉनमी</option>
                                <option value="luxury-suv">लक्जरी SUV</option>
                                <option value="ultra-premium">अल्ट्रा प्रीमियम</option>
                                <option value="eco-electric">इको-इलेक्ट्रिक</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="rentalType">रेंटल टाइप *</label>
                            <select id="rentalType" name="rentalType" class="form-input" required>
                                <option value="">रेंटल टाइप चुनें</option>
                                <option value="hourly">घंटे के हिसाब</option>
                                <option value="daily">दैनिक</option>
                                <option value="weekly">साप्ताहिक</option>
                                <option value="monthly">मासिक</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="pickupDate">पिकअप डेट *</label>
                            <input type="date" id="pickupDate" name="pickupDate" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="pickupTime">पिकअप टाइम *</label>
                            <input type="time" id="pickupTime" name="pickupTime" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="returnDate">रिटर्न डेट *</label>
                            <input type="date" id="returnDate" name="returnDate" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="returnTime">रिटर्न टाइम *</label>
                            <input type="time" id="returnTime" name="returnTime" class="form-input" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="needDriver">प्रो ड्राइवर चाहिए?</label>
                            <select id="needDriver" name="needDriver" class="form-input">
                                <option value="no">नहीं, सेल्फ ड्राइव</option>
                                <option value="yes">हां, प्रो ड्राइवर चाहिए</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label" for="pickupLocation">पिकअप लोकेशन</label>
                            <input type="text" id="pickupLocation" name="pickupLocation" class="form-input" placeholder="पिकअप का पता">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="requirements">स्पेशल रिक्वेस्ट</label>
                        <textarea id="requirements" name="requirements" rows="3" class="form-input form-textarea" placeholder="कोई विशेष जरूरत हो तो बताएं..."></textarea>
                    </div>

                    <button type="submit" class="submit-btn">
                        <i class="fas fa-rocket"></i>
                        इंस्टेंट बुकिंग करें
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Floating Chat Button -->
    <button class="floating-chat" onclick="openChat()">
        <i class="fab fa-whatsapp"></i>
    </button>

    <script>
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.getElementById('navbar');
            if (window.scrollY > 100) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Set minimum date to today
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.getElementById('pickupDate');
            const returnDate = document.getElementById('returnDate');

            if (pickupDate) pickupDate.min = today;
            if (returnDate) returnDate.min = today;

            if (pickupDate) {
                pickupDate.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (returnDate) returnDate.min = selectedDate;
                });
            }
        });

        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.name || !data.phone || !data.carType || !data.rentalType || !data.pickupDate || !data.pickupTime || !data.returnDate || !data.returnTime) {
                alert('कृपया सभी आवश्यक फील्ड भरें।');
                return;
            }

            const message = `⚡ *RideNow - प्रीमियम कार बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार कैटेगरी:* ${data.carType}
📅 *रेंटल टाइप:* ${data.rentalType}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
🏁 *रिटर्न:* ${data.returnDate} ${data.returnTime}
👨‍✈️ *प्रो ड्राइवर:* ${data.needDriver === 'yes' ? 'हां' : 'नहीं'}
📍 *पिकअप लोकेशन:* ${data.pickupLocation || 'नहीं बताया'}

📝 *स्पेशल रिक्वेस्ट:* ${data.requirements || 'कोई नहीं'}

कृपया इस प्रीमियम बुकिंग को कन्फर्म करें। धन्यवाद! ⚡`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showNotification('⚡ आपकी प्रीमियम बुकिंग रिक्वेस्ट भेज दी गई है!', 'success');
            this.reset();
        });

        function openChat() {
            const message = '⚡ नमस्ते! मैं RideNow की प्रीमियम सेवाओं के बारे में जानकारी चाहता/चाहती हूँ।';
            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? 'linear-gradient(135deg, #00f5ff, #ff0080)' : 'linear-gradient(135deg, #7c3aed, #8b5cf6)'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 1rem;
                box-shadow: 0 0 30px rgba(0, 245, 255, 0.3);
                z-index: 10000;
                max-width: 400px;
                font-weight: 600;
                backdrop-filter: blur(20px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    </script>

    <style>
        .floating-chat {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, #25d366, #128c7e);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            box-shadow: 0 0 20px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
            border: none;
        }

        .floating-chat:hover {
            transform: scale(1.1);
            box-shadow: 0 0 30px rgba(37, 211, 102, 0.6);
        }

        .floating-chat::after {
            content: '';
            position: absolute;
            width: 1rem;
            height: 1rem;
            background: var(--neon-pink);
            border-radius: 50%;
            top: -0.25rem;
            right: -0.25rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.3); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }
    </style>
</body>
</html>
