<svg width="600" height="400" viewBox="0 0 600 400" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="#9CA3AF" />
        </marker>
    </defs>

    <!-- Styles -->
    <style>
        .node-rect {
            fill: #ffffff;
            stroke: #E5E7EB;
            stroke-width: 1.5;
            rx: 8;
            ry: 8;
        }
        .node-title {
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: bold;
            fill: #1F2937;
        }
        .node-text {
            font-family: Arial, sans-serif;
            font-size: 12px;
            fill: #4B5563;
        }
        .connector-line {
            stroke: #9CA3AF;
            stroke-width: 2;
            fill: none;
            marker-end: url(#arrowhead);
        }
    </style>

    <!-- Node 1: Welcome Message -->
    <g transform="translate(50, 50)">
        <rect class="node-rect" width="200" height="80" />
        <text x="15" y="25" class="node-title">👋 Welcome Message</text>
        <text x="15" y="45" class="node-text">Hi there! How can I help?</text>
        <text x="15" y="60" class="node-text">1. View Products</text>
    </g>

    <!-- Node 2: Product Catalog -->
    <g transform="translate(350, 150)">
        <rect class="node-rect" width="200" height="80" />
        <text x="15" y="25" class="node-title">🛍️ Product Catalog</text>
        <text x="15" y="45" class="node-text">Here are our top products.</text>
        <text x="15" y="60" class="node-text">- T-Shirt</text>
    </g>

    <!-- Node 3: Support -->
    <g transform="translate(50, 250)">
        <rect class="node-rect" width="200" height="80" />
        <text x="15" y="25" class="node-title">📞 Contact Support</text>
        <text x="15" y="45" class="node-text">Let me connect you to an agent.</text>
    </g>

    <!-- Connectors -->
    <path class="connector-line" d="M 250,90 Q 300,90 350,170" />
    <path class="connector-line" d="M 150,130 Q 150,190 150,250" />

</svg>
