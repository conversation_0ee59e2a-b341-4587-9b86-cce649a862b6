<!DOCTYPE html>
<html lang="hi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>QuickCars - Mobile Car Rental App</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary-color: #007AFF;
            --secondary-color: #34C759;
            --accent-color: #FF9500;
            --danger-color: #FF3B30;
            --warning-color: #FFCC00;
            --text-primary: #000000;
            --text-secondary: #8E8E93;
            --text-tertiary: #C7C7CC;
            --bg-primary: #FFFFFF;
            --bg-secondary: #F2F2F7;
            --bg-tertiary: #E5E5EA;
            --bg-card: #FFFFFF;
            --border-color: #C6C6C8;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --ios-blue: #007AFF;
            --ios-green: #34C759;
            --ios-orange: #FF9500;
            --ios-red: #FF3B30;
            --ios-gray: #8E8E93;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }

        body {
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.4;
            font-size: 16px;
            overflow-x: hidden;
            padding-bottom: 80px; /* Space for bottom navigation */
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 0 16px;
        }

        /* Top Navigation Bar */
        .top-nav {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            z-index: 1000;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 60px;
        }

        .nav-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ios-blue);
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-icon:active {
            transform: scale(0.95);
            background: var(--bg-tertiary);
        }

        /* Main Content */
        .main-content {
            margin-top: 60px;
            padding-bottom: 20px;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--ios-blue), #5AC8FA);
            color: white;
            padding: 32px 16px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="rgba(255,255,255,0.1)" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,138.7C960,139,1056,117,1152,117.3C1248,117,1344,139,1392,149.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat center bottom;
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-badge {
            display: inline-block;
            background: rgba(255, 255, 255, 0.2);
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 16px;
            backdrop-filter: blur(10px);
        }

        .hero-title {
            font-size: 28px;
            font-weight: 900;
            margin-bottom: 8px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 24px;
            line-height: 1.4;
        }

        .hero-button {
            background: white;
            color: var(--ios-blue);
            padding: 14px 32px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 700;
            font-size: 16px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .hero-button:active {
            transform: scale(0.98);
        }

        /* Quick Actions */
        .quick-actions {
            padding: 20px 16px;
            background: var(--bg-primary);
            margin: 16px;
            border-radius: 16px;
            box-shadow: 0 2px 10px var(--shadow-color);
        }

        .quick-actions-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .action-card {
            background: var(--bg-secondary);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.2s ease;
            border: 1px solid var(--border-color);
        }

        .action-card:active {
            transform: scale(0.98);
            background: var(--bg-tertiary);
        }

        .action-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: white;
        }

        .action-icon.blue { background: var(--ios-blue); }
        .action-icon.green { background: var(--ios-green); }
        .action-icon.orange { background: var(--ios-orange); }
        .action-icon.red { background: var(--ios-red); }

        .action-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .action-subtitle {
            font-size: 12px;
            color: var(--text-secondary);
        }

        /* Services Section */
        .services {
            padding: 0 16px 20px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 16px;
            color: var(--text-primary);
        }

        .services-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .service-card {
            background: var(--bg-card);
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 2px 10px var(--shadow-color);
            border: 1px solid var(--border-color);
            transition: all 0.2s ease;
        }

        .service-card:active {
            transform: scale(0.99);
        }

        .service-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;
        }

        .service-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .service-info {
            flex: 1;
        }

        .service-title {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 4px;
            color: var(--text-primary);
        }

        .service-price {
            font-size: 18px;
            font-weight: 900;
            color: var(--ios-blue);
        }

        .service-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .service-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .feature-tag {
            background: var(--bg-secondary);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 12px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Bottom Navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--bg-primary);
            border-top: 1px solid var(--border-color);
            padding: 8px 0;
            z-index: 1000;
            backdrop-filter: blur(20px);
        }

        .nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 8px;
            text-decoration: none;
            color: var(--text-secondary);
            transition: all 0.2s ease;
            min-width: 60px;
        }

        .nav-item.active {
            color: var(--ios-blue);
        }

        .nav-item:active {
            transform: scale(0.95);
        }

        .nav-item i {
            font-size: 20px;
        }

        .nav-item span {
            font-size: 10px;
            font-weight: 600;
        }

        .nav-badge {
            position: absolute;
            top: 4px;
            right: 8px;
            background: var(--ios-red);
            color: white;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: 700;
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: 90px;
            right: 20px;
            width: 56px;
            height: 56px;
            background: var(--ios-blue);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
            border: none;
            cursor: pointer;
            transition: all 0.2s ease;
            z-index: 999;
        }

        .fab:active {
            transform: scale(0.95);
        }

        /* Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: flex-end;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: 16px 16px 0 0;
            padding: 20px;
            width: 100%;
            max-height: 80vh;
            overflow-y: auto;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }

        .modal.show .modal-content {
            transform: translateY(0);
        }

        .modal-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--text-primary);
        }

        .modal-close {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-secondary);
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            cursor: pointer;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: 16px;
            background: var(--bg-primary);
            color: var(--text-primary);
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ios-blue);
        }

        .form-button {
            width: 100%;
            background: var(--ios-blue);
            color: white;
            padding: 16px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .form-button:active {
            transform: scale(0.98);
        }

        /* Responsive Design */
        @media (max-width: 375px) {
            .container {
                padding: 0 12px;
            }
            
            .hero {
                padding: 24px 12px;
            }
            
            .quick-actions {
                margin: 12px;
                padding: 16px 12px;
            }
            
            .services {
                padding: 0 12px 20px;
            }
        }

        @media (min-width: 768px) {
            body {
                max-width: 414px;
                margin: 0 auto;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            }
        }
    </style>
</head>
<body>
    <!-- Top Navigation -->
    <div class="top-nav">
        <div class="nav-icon">
            <i class="fas fa-bars"></i>
        </div>
        <div class="nav-title">QuickCars</div>
        <div class="nav-icon">
            <i class="fas fa-user-circle"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Hero Section -->
        <section class="hero">
            <div class="hero-content">
                <div class="hero-badge">
                    <i class="fas fa-star"></i>
                    तुरंत कार, तुरंत सफर
                </div>
                
                <h1 class="hero-title">
                    2 मिनट में<br>
                    कार बुक करें
                </h1>
                
                <p class="hero-subtitle">
                    सबसे तेज़ और आसान कार रेंटल ऐप। 
                    कहीं भी, कभी भी, तुरंत कार।
                </p>
                
                <a href="#" class="hero-button" onclick="openBookingModal()">
                    <i class="fas fa-rocket"></i>
                    अभी बुक करें
                </a>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions">
            <h2 class="quick-actions-title">क्विक एक्शन्स</h2>
            <div class="actions-grid">
                <a href="#" class="action-card" onclick="openBookingModal()">
                    <div class="action-icon blue">
                        <i class="fas fa-car"></i>
                    </div>
                    <div class="action-title">तुरंत बुक करें</div>
                    <div class="action-subtitle">30 सेकंड में</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon green">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="action-title">नियर मी</div>
                    <div class="action-subtitle">पास की कारें</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon orange">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="action-title">मेरी ट्रिप्स</div>
                    <div class="action-subtitle">हिस्ट्री देखें</div>
                </a>
                
                <a href="#" class="action-card">
                    <div class="action-icon red">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="action-title">हेल्प</div>
                    <div class="action-subtitle">24/7 सपोर्ट</div>
                </a>
            </div>
        </section>

        <!-- Services -->
        <section class="services">
            <h2 class="section-title">कार कैटेगरीज</h2>
            <div class="services-list">
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon blue">
                            <i class="fas fa-car"></i>
                        </div>
                        <div class="service-info">
                            <div class="service-title">इकॉनमी</div>
                            <div class="service-price">₹12/किमी</div>
                        </div>
                    </div>
                    <div class="service-description">
                        शहर में घूमने के लिए परफेक्ट। फ्यूल एफिशिएंट और कम्फर्टेबल।
                    </div>
                    <div class="service-features">
                        <span class="feature-tag">AC</span>
                        <span class="feature-tag">4 सीटर</span>
                        <span class="feature-tag">म्यूजिक</span>
                        <span class="feature-tag">GPS</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon green">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="service-info">
                            <div class="service-title">SUV</div>
                            <div class="service-price">₹18/किमी</div>
                        </div>
                    </div>
                    <div class="service-description">
                        फैमिली ट्रिप्स के लिए आइडियल। ज्यादा स्पेस और कम्फर्ट।
                    </div>
                    <div class="service-features">
                        <span class="feature-tag">7 सीटर</span>
                        <span class="feature-tag">बड़ा बूट</span>
                        <span class="feature-tag">पावरफुल</span>
                        <span class="feature-tag">सेफ</span>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="service-header">
                        <div class="service-icon orange">
                            <i class="fas fa-gem"></i>
                        </div>
                        <div class="service-info">
                            <div class="service-title">प्रीमियम</div>
                            <div class="service-price">₹25/किमी</div>
                        </div>
                    </div>
                    <div class="service-description">
                        स्पेशल ऑकेजन्स के लिए। लक्जरी और स्टाइल का कॉम्बो।
                    </div>
                    <div class="service-features">
                        <span class="feature-tag">लक्जरी</span>
                        <span class="feature-tag">लेदर सीट्स</span>
                        <span class="feature-tag">साउंड सिस्टम</span>
                        <span class="feature-tag">ड्राइवर</span>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- Bottom Navigation -->
    <div class="bottom-nav">
        <div class="nav-items">
            <a href="#" class="nav-item active">
                <i class="fas fa-home"></i>
                <span>होम</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-search"></i>
                <span>सर्च</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-calendar"></i>
                <span>बुकिंग</span>
                <div class="nav-badge">2</div>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-heart"></i>
                <span>फेवरिट</span>
            </a>
            <a href="#" class="nav-item">
                <i class="fas fa-user"></i>
                <span>प्रोफाइल</span>
            </a>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button class="fab" onclick="openBookingModal()">
        <i class="fas fa-plus"></i>
    </button>

    <!-- Booking Modal -->
    <div class="modal" id="bookingModal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">क्विक बुकिंग</div>
                <button class="modal-close" onclick="closeBookingModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="quickBookingForm">
                <div class="form-group">
                    <label class="form-label">आपका नाम</label>
                    <input type="text" class="form-input" id="name" name="name" placeholder="अपना नाम लिखें" required>
                </div>

                <div class="form-group">
                    <label class="form-label">मोबाइल नंबर</label>
                    <input type="tel" class="form-input" id="phone" name="phone" placeholder="+91 98765 43210" required>
                </div>

                <div class="form-group">
                    <label class="form-label">कार टाइप</label>
                    <select class="form-input" id="carType" name="carType" required>
                        <option value="">कार टाइप चुनें</option>
                        <option value="economy">इकॉनमी (₹12/किमी)</option>
                        <option value="suv">SUV (₹18/किमी)</option>
                        <option value="premium">प्रीमियम (₹25/किमी)</option>
                    </select>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <div class="form-group">
                        <label class="form-label">पिकअप डेट</label>
                        <input type="date" class="form-input" id="pickupDate" name="pickupDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">पिकअप टाइम</label>
                        <input type="time" class="form-input" id="pickupTime" name="pickupTime" required>
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
                    <div class="form-group">
                        <label class="form-label">रिटर्न डेट</label>
                        <input type="date" class="form-input" id="returnDate" name="returnDate" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">रिटर्न टाइम</label>
                        <input type="time" class="form-input" id="returnTime" name="returnTime" required>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">पिकअप लोकेशन</label>
                    <input type="text" class="form-input" id="pickupLocation" name="pickupLocation" placeholder="पिकअप का पता">
                </div>

                <div class="form-group">
                    <label class="form-label">स्पेशल रिक्वेस्ट</label>
                    <textarea class="form-input" id="requirements" name="requirements" rows="3" placeholder="कोई विशेष जरूरत हो तो बताएं..." style="resize: vertical; min-height: 80px;"></textarea>
                </div>

                <button type="submit" class="form-button">
                    <i class="fas fa-paper-plane" style="margin-right: 8px;"></i>
                    तुरंत बुक करें
                </button>
            </form>
        </div>
    </div>

    <script>
        // Mobile app-like interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Set minimum date to today
            const today = new Date().toISOString().split('T')[0];
            const pickupDate = document.getElementById('pickupDate');
            const returnDate = document.getElementById('returnDate');

            if (pickupDate) pickupDate.min = today;
            if (returnDate) returnDate.min = today;

            if (pickupDate) {
                pickupDate.addEventListener('change', function() {
                    const selectedDate = this.value;
                    if (returnDate) returnDate.min = selectedDate;
                });
            }

            // Add iOS-style haptic feedback simulation
            const buttons = document.querySelectorAll('button, .action-card, .nav-item, .service-card');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    if (navigator.vibrate) {
                        navigator.vibrate(10); // Light haptic feedback
                    }
                });
            });

            // Smooth scroll behavior
            document.documentElement.style.scrollBehavior = 'smooth';
        });

        // Modal functions
        function openBookingModal() {
            const modal = document.getElementById('bookingModal');
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }

        function closeBookingModal() {
            const modal = document.getElementById('bookingModal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto';
        }

        // Close modal on backdrop click
        document.getElementById('bookingModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookingModal();
            }
        });

        // Form submission
        document.getElementById('quickBookingForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            if (!data.name || !data.phone || !data.carType || !data.pickupDate || !data.pickupTime || !data.returnDate || !data.returnTime) {
                showMobileAlert('कृपया सभी आवश्यक फील्ड भरें! 📱', 'warning');
                return;
            }

            const message = `📱 *QuickCars - मोबाइल बुकिंग*

👤 *नाम:* ${data.name}
📱 *मोबाइल:* ${data.phone}
🚙 *कार टाइप:* ${data.carType}

📍 *पिकअप:* ${data.pickupDate} ${data.pickupTime}
🏁 *रिटर्न:* ${data.returnDate} ${data.returnTime}
📍 *पिकअप लोकेशन:* ${data.pickupLocation || 'नहीं बताया'}

📝 *स्पेशल रिक्वेस्ट:* ${data.requirements || 'कोई नहीं'}

QuickCars ऐप से बुक किया गया। तुरंत कन्फर्म करें! 🚗⚡`;

            const whatsappUrl = `https://wa.me/919876543210?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');

            showMobileAlert('🎉 आपकी राइड बुक हो गई! जल्दी ही ड्राइवर आपसे संपर्क करेगा! 📱', 'success');
            this.reset();
            closeBookingModal();
        });

        // Mobile-style alert
        function showMobileAlert(message, type = 'info') {
            const colors = {
                'success': 'var(--ios-green)',
                'warning': 'var(--ios-orange)',
                'info': 'var(--ios-blue)'
            };

            const alert = document.createElement('div');
            alert.style.cssText = `
                position: fixed;
                top: 80px;
                left: 16px;
                right: 16px;
                background: ${colors[type]};
                color: white;
                padding: 16px 20px;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                z-index: 3000;
                font-weight: 600;
                font-size: 14px;
                transform: translateY(-100px);
                opacity: 0;
                transition: all 0.3s ease;
            `;
            alert.textContent = message;

            document.body.appendChild(alert);

            // Animate in
            setTimeout(() => {
                alert.style.transform = 'translateY(0)';
                alert.style.opacity = '1';
            }, 100);

            // Animate out
            setTimeout(() => {
                alert.style.transform = 'translateY(-100px)';
                alert.style.opacity = '0';
                setTimeout(() => {
                    if (alert.parentNode) {
                        alert.parentNode.removeChild(alert);
                    }
                }, 300);
            }, 3000);

            // Haptic feedback
            if (navigator.vibrate) {
                navigator.vibrate(type === 'success' ? [100, 50, 100] : [200]);
            }
        }

        // Bottom navigation active state
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all items
                document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));

                // Add active class to clicked item
                this.classList.add('active');

                // Haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate(10);
                }
            });
        });

        // Swipe gestures for mobile
        let startY = 0;
        let startX = 0;

        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const endX = e.changedTouches[0].clientX;
            const diffY = startY - endY;
            const diffX = startX - endX;

            // Swipe up to open booking modal
            if (diffY > 50 && Math.abs(diffX) < 100) {
                openBookingModal();
            }
        });

        // Prevent zoom on double tap
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function(event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);

        // Service worker registration for PWA-like experience
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                // Service worker would be registered here in a real app
                console.log('QuickCars - Mobile App Ready');
            });
        }

        // Add to home screen prompt
        let deferredPrompt;
        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;

            // Show install button or banner
            showMobileAlert('📱 QuickCars को होम स्क्रीन पर Add करें!', 'info');
        });

        // Handle install
        function installApp() {
            if (deferredPrompt) {
                deferredPrompt.prompt();
                deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                        showMobileAlert('🎉 QuickCars इंस्टॉल हो गया!', 'success');
                    }
                    deferredPrompt = null;
                });
            }
        }

        // Pull to refresh simulation
        let startYRefresh = 0;
        let isRefreshing = false;

        document.addEventListener('touchstart', function(e) {
            if (window.scrollY === 0) {
                startYRefresh = e.touches[0].clientY;
            }
        });

        document.addEventListener('touchmove', function(e) {
            if (window.scrollY === 0 && !isRefreshing) {
                const currentY = e.touches[0].clientY;
                const diff = currentY - startYRefresh;

                if (diff > 100) {
                    isRefreshing = true;
                    showMobileAlert('🔄 रिफ्रेश हो रहा है...', 'info');

                    setTimeout(() => {
                        showMobileAlert('✅ रिफ्रेश कम्प्लीट!', 'success');
                        isRefreshing = false;
                    }, 1500);
                }
            }
        });
    </script>
</body>
</html>
