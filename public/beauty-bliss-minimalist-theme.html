<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Beauty Bliss - Modern Minimalist Salon</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
            color: #2c3e50;
            line-height: 1.6;
        }

        .minimalist-header {
            background: #ffffff;
            padding: 40px 20px;
            text-align: center;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            position: relative;
        }

        .header-shape {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b9d, #c44569, #6c5ce7, #74b9ff);
        }

        .salon-name {
            font-size: 2.5rem;
            font-weight: 300;
            color: #2c3e50;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }

        .salon-subtitle {
            font-size: 1rem;
            color: #7f8c8d;
            font-weight: 400;
        }

        .services-container {
            max-width: 900px;
            margin: 50px auto;
            padding: 0 20px;
        }

        .service-strip {
            background: #ffffff;
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            min-height: 120px;
        }

        .service-strip:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 35px rgba(0,0,0,0.12);
        }

        .service-strip:nth-child(odd) {
            flex-direction: row;
        }

        .service-strip:nth-child(even) {
            flex-direction: row-reverse;
        }

        .service-color-bar {
            width: 8px;
            height: 100%;
            min-height: 120px;
        }

        .service-strip:nth-child(1) .service-color-bar { background: #ff6b9d; }
        .service-strip:nth-child(2) .service-color-bar { background: #c44569; }
        .service-strip:nth-child(3) .service-color-bar { background: #6c5ce7; }
        .service-strip:nth-child(4) .service-color-bar { background: #74b9ff; }
        .service-strip:nth-child(5) .service-color-bar { background: #00b894; }

        .service-content {
            flex: 1;
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 25px;
        }

        .service-icon-circle {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            flex-shrink: 0;
        }

        .service-strip:nth-child(1) .service-icon-circle { background: rgba(255, 107, 157, 0.1); color: #ff6b9d; }
        .service-strip:nth-child(2) .service-icon-circle { background: rgba(196, 69, 105, 0.1); color: #c44569; }
        .service-strip:nth-child(3) .service-icon-circle { background: rgba(108, 92, 231, 0.1); color: #6c5ce7; }
        .service-strip:nth-child(4) .service-icon-circle { background: rgba(116, 185, 255, 0.1); color: #74b9ff; }
        .service-strip:nth-child(5) .service-icon-circle { background: rgba(0, 184, 148, 0.1); color: #00b894; }

        .service-details {
            flex: 1;
        }

        .service-name {
            font-size: 1.4rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .service-desc {
            color: #7f8c8d;
            font-size: 0.95rem;
            margin-bottom: 12px;
        }

        .service-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tag {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .service-strip:nth-child(1) .tag { background: rgba(255, 107, 157, 0.1); color: #ff6b9d; }
        .service-strip:nth-child(2) .tag { background: rgba(196, 69, 105, 0.1); color: #c44569; }
        .service-strip:nth-child(3) .tag { background: rgba(108, 92, 231, 0.1); color: #6c5ce7; }
        .service-strip:nth-child(4) .tag { background: rgba(116, 185, 255, 0.1); color: #74b9ff; }
        .service-strip:nth-child(5) .tag { background: rgba(0, 184, 148, 0.1); color: #00b894; }

        .service-price-section {
            text-align: center;
            padding: 0 25px;
        }

        .service-price {
            font-size: 1.6rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .book-button {
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: white;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .service-strip:nth-child(1) .book-button { background: #ff6b9d; }
        .service-strip:nth-child(2) .book-button { background: #c44569; }
        .service-strip:nth-child(3) .book-button { background: #6c5ce7; }
        .service-strip:nth-child(4) .book-button { background: #74b9ff; }
        .service-strip:nth-child(5) .book-button { background: #00b894; }

        .book-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .geometric-footer {
            background: #2c3e50;
            padding: 40px 20px;
            margin-top: 60px;
            position: relative;
            overflow: hidden;
        }

        .geometric-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #ff6b9d, #c44569, #6c5ce7, #74b9ff, #00b894);
        }

        .footer-content {
            max-width: 800px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            color: #ecf0f1;
        }

        .footer-section h3 {
            color: #74b9ff;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .footer-section p, .footer-section div {
            margin-bottom: 8px;
            color: #bdc3c7;
        }

        .social-icons {
            display: flex;
            gap: 15px;
            margin-top: 15px;
        }

        .social-icon {
            width: 35px;
            height: 35px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-icon:nth-child(1) { background: #ff6b9d; }
        .social-icon:nth-child(2) { background: #c44569; }
        .social-icon:nth-child(3) { background: #6c5ce7; }
        .social-icon:nth-child(4) { background: #74b9ff; }

        .social-icon:hover {
            transform: translateY(-3px) rotate(5deg);
        }

        @media (max-width: 768px) {
            .service-strip {
                flex-direction: column !important;
                text-align: center;
                min-height: auto;
            }
            
            .service-strip:nth-child(even) {
                flex-direction: column !important;
            }
            
            .service-color-bar {
                width: 100%;
                height: 4px;
                min-height: 4px;
            }
            
            .service-content {
                flex-direction: column;
                gap: 15px;
            }
            
            .salon-name {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <header class="minimalist-header">
        <div class="header-shape"></div>
        <h1 class="salon-name">Beauty Bliss</h1>
        <p class="salon-subtitle">Modern • Minimalist • Elegant</p>
    </header>

    <div class="services-container">
        <div class="service-strip">
            <div class="service-color-bar"></div>
            <div class="service-content">
                <div class="service-icon-circle">💇‍♀️</div>
                <div class="service-details">
                    <h3 class="service-name">Hair Styling & Cut</h3>
                    <p class="service-desc">Professional hair cutting and styling with modern techniques</p>
                    <div class="service-tags">
                        <span class="tag">Wash & Cut</span>
                        <span class="tag">Styling</span>
                        <span class="tag">Consultation</span>
                    </div>
                </div>
            </div>
            <div class="service-price-section">
                <div class="service-price">₹1,500</div>
                <button class="book-button">Book Now</button>
            </div>
        </div>

        <div class="service-strip">
            <div class="service-color-bar"></div>
            <div class="service-content">
                <div class="service-icon-circle">✨</div>
                <div class="service-details">
                    <h3 class="service-name">Facial Treatment</h3>
                    <p class="service-desc">Rejuvenating facial treatments for glowing, healthy skin</p>
                    <div class="service-tags">
                        <span class="tag">Deep Cleansing</span>
                        <span class="tag">Hydrating</span>
                        <span class="tag">Anti-aging</span>
                    </div>
                </div>
            </div>
            <div class="service-price-section">
                <div class="service-price">₹2,200</div>
                <button class="book-button">Book Now</button>
            </div>
        </div>

        <div class="service-strip">
            <div class="service-color-bar"></div>
            <div class="service-content">
                <div class="service-icon-circle">💅</div>
                <div class="service-details">
                    <h3 class="service-name">Nail Care</h3>
                    <p class="service-desc">Complete manicure and pedicure services with nail art</p>
                    <div class="service-tags">
                        <span class="tag">Manicure</span>
                        <span class="tag">Pedicure</span>
                        <span class="tag">Nail Art</span>
                    </div>
                </div>
            </div>
            <div class="service-price-section">
                <div class="service-price">₹1,200</div>
                <button class="book-button">Book Now</button>
            </div>
        </div>

        <div class="service-strip">
            <div class="service-color-bar"></div>
            <div class="service-content">
                <div class="service-icon-circle">👁️</div>
                <div class="service-details">
                    <h3 class="service-name">Eyebrow & Lashes</h3>
                    <p class="service-desc">Perfect eyebrow shaping and eyelash extensions</p>
                    <div class="service-tags">
                        <span class="tag">Threading</span>
                        <span class="tag">Tinting</span>
                        <span class="tag">Extensions</span>
                    </div>
                </div>
            </div>
            <div class="service-price-section">
                <div class="service-price">₹800</div>
                <button class="book-button">Book Now</button>
            </div>
        </div>

        <div class="service-strip">
            <div class="service-color-bar"></div>
            <div class="service-content">
                <div class="service-icon-circle">💄</div>
                <div class="service-details">
                    <h3 class="service-name">Makeup Services</h3>
                    <p class="service-desc">Professional makeup for all occasions and events</p>
                    <div class="service-tags">
                        <span class="tag">Party Makeup</span>
                        <span class="tag">Bridal</span>
                        <span class="tag">HD Makeup</span>
                    </div>
                </div>
            </div>
            <div class="service-price-section">
                <div class="service-price">₹3,500</div>
                <button class="book-button">Book Now</button>
            </div>
        </div>
    </div>

    <footer class="geometric-footer">
        <div class="footer-content">
            <div class="footer-section">
                <h3>Contact Info</h3>
                <p>📍 456 Beauty Lane, Delhi</p>
                <p>📞 +91 87654 32109</p>
                <p>✉️ <EMAIL></p>
            </div>
            <div class="footer-section">
                <h3>Opening Hours</h3>
                <p>Monday - Saturday: 9 AM - 7 PM</p>
                <p>Sunday: 10 AM - 6 PM</p>
                <p>Appointments recommended</p>
            </div>
            <div class="footer-section">
                <h3>Follow Us</h3>
                <p>Stay updated with latest trends</p>
                <div class="social-icons">
                    <a href="#" class="social-icon">📘</a>
                    <a href="#" class="social-icon">📷</a>
                    <a href="#" class="social-icon">🐦</a>
                    <a href="#" class="social-icon">💬</a>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
