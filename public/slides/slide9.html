<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 8: Timeline & Readiness</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
        }
        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: flex-start;
        }
        .section h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }
        .milestones-list li {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        .milestones-list i {
            color: var(--primary);
            margin-right: 1rem;
            font-size: 1.2rem;
        }
        .timeline {
            position: relative;
            padding-left: 2rem;
            border-left: 3px solid #e9ecef;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 2rem;
        }
        .timeline-item:last-child { margin-bottom: 0; }
        .timeline-dot {
            position: absolute;
            left: -2.6rem;
            top: 0.2rem;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: var(--primary);
            border: 4px solid var(--white);
        }
        .timeline-period { font-weight: 600; margin-bottom: 0.25rem; }
        .timeline-activity { color: var(--gray); }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">09</div>
            </div>
        </div>
        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                <p class="subtitle" data-translate-key="slide_title">Timeline & Readiness</p>
                <h1 class="title" data-translate-key="main_heading">Ready to Launch</h1>
            </div>
            <div class="timeline-content-grid" style="animation-delay: 0.3s; display: flex; flex-wrap: wrap; gap: 2rem; justify-content: center; align-items: flex-start;">
                <div class="milestones-cards-grid">
                    <h3 class="milestones-cards-title" data-translate-key="milestones_title">Milestones Achieved</h3>
                    <div class="milestones-cards">
                        <div class="milestone-card" style="background: linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%);">
                            <div class="milestone-icon" style="background:#25D366;"><i class="fas fa-cogs"></i></div>
                            <span class="milestone-label" data-translate-key="milestone_1">Product development complete</span>
                        </div>
                        <div class="milestone-card" style="background: linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);">
                            <div class="milestone-icon" style="background:#fdc500;"><i class="fas fa-gift"></i></div>
                            <span class="milestone-label" data-translate-key="milestone_2">7-day free trial funnel live</span>
                        </div>
                        <div class="milestone-card" style="background: linear-gradient(120deg,#e6e6ff 70%,#ffe6fa 100%);">
                            <div class="milestone-icon" style="background:#6F42C1;"><i class="fas fa-users"></i></div>
                            <span class="milestone-label" data-translate-key="milestone_3">Influencer list ready</span>
                        </div>
                        <div class="milestone-card" style="background: linear-gradient(120deg,#f0fdf6 70%,#c5f7fa 100%);">
                            <div class="milestone-icon" style="background:#34B7F1;"><i class="fas fa-film"></i></div>
                            <span class="milestone-label" data-translate-key="milestone_4">Meta & video ad templates ready</span>
                        </div>
                        <div class="milestone-card" style="background: linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%);">
                            <div class="milestone-icon" style="background:#C2185B;"><i class="fas fa-users-cog"></i></div>
                            <span class="milestone-label" data-translate-key="milestone_5">Team structure in place</span>
                        </div>
                    </div>
                </div>
                <div class="timeline-section-cards">
                    <h3 class="timeline-cards-title" data-translate-key="timeline_title">Launch Timeline</h3>
                    <div class="timeline-cards">
                        <div class="timeline-step-card" style="background: linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%);">
                            <div class="timeline-step-icon" style="background:#25D366;"><i class="fas fa-bullhorn"></i></div>
                            <div class="timeline-step-info">
                                <span class="timeline-step-period" data-translate-key="timeline_period_1">Week 1</span>
                                <span class="timeline-step-activity" data-translate-key="timeline_activity_1">Paid marketing begins</span>
                            </div>
                        </div>
                        <div class="timeline-step-card" style="background: linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);">
                            <div class="timeline-step-icon" style="background:#fdc500;"><i class="fas fa-user-plus"></i></div>
                            <div class="timeline-step-info">
                                <span class="timeline-step-period" data-translate-key="timeline_period_2">Weeks 2-4</span>
                                <span class="timeline-step-activity" data-translate-key="timeline_activity_2">5,000+ user onboarding target</span>
                            </div>
                        </div>
                        <div class="timeline-step-card" style="background: linear-gradient(120deg,#e6e6ff 70%,#ffe6fa 100%);">
                            <div class="timeline-step-icon" style="background:#6F42C1;"><i class="fas fa-handshake"></i></div>
                            <div class="timeline-step-info">
                                <span class="timeline-step-period" data-translate-key="timeline_period_3">Months 2-3</span>
                                <span class="timeline-step-activity" data-translate-key="timeline_activity_3">Scale ads & partnerships</span>
                            </div>
                        </div>
                        <div class="timeline-step-card" style="background: linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%);">
                            <div class="timeline-step-icon" style="background:#C2185B;"><i class="fas fa-layer-group"></i></div>
                            <div class="timeline-step-info">
                                <span class="timeline-step-period" data-translate-key="timeline_period_4">Months 4-6</span>
                                <span class="timeline-step-activity" data-translate-key="timeline_activity_4">Feature expansion & upselling</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                .timeline-content-grid {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 2rem;
                    justify-content: center;
                    align-items: flex-start;
                }
                .milestones-cards-grid {
                    flex: 1 1 320px;
                    display: flex;
                    flex-direction: column;
                    gap: 1.1rem;
                }
                .milestones-cards-title {
                    font-size: 1.1rem;
                    font-weight: 700;
                    color: #075E54;
                    margin-bottom: 0.4rem;
                }
                .milestones-cards {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
                .milestone-card {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    background: var(--light-gray);
                    border-radius: 14px;
                    box-shadow: 0 2px 10px rgba(7,94,84,0.08);
                    padding: 0.7rem 1rem;
                    min-width: 0;
                    animation: slideUp 0.6s ease-out forwards;
                    opacity: 0;
                    font-size: 1rem;
                }
                .milestone-icon {
                    width: 38px;
                    height: 38px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.18rem;
                    color: #fff;
                    flex-shrink: 0;
                    box-shadow: 0 1px 6px rgba(7,94,84,0.09);
                }
                .milestone-label {
                    font-weight: 600;
                    color: #075E54;
                    font-size: 0.97rem;
                }
                .timeline-section-cards {
                    flex: 1.5 1 420px;
                    display: flex;
                    flex-direction: column;
                    gap: 1.1rem;
                }
                .timeline-cards-title {
                    font-size: 1.1rem;
                    font-weight: 700;
                    color: #128C7E;
                    margin-bottom: 0.4rem;
                }
                .timeline-cards {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 1rem;
                }
                .timeline-step-card {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    background: var(--light-gray);
                    border-radius: 14px;
                    box-shadow: 0 2px 10px rgba(7,94,84,0.08);
                    padding: 0.7rem 1rem;
                    min-width: 0;
                    animation: slideUp 0.6s ease-out forwards;
                    opacity: 0;
                    font-size: 1rem;
                }
                .timeline-step-icon {
                    width: 38px;
                    height: 38px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.18rem;
                    color: #fff;
                    flex-shrink: 0;
                    box-shadow: 0 1px 6px rgba(7,94,84,0.09);
                }
                .timeline-step-info {
                    display: flex;
                    flex-direction: column;
                    gap: 0.2rem;
                }
                .timeline-step-period {
                    font-weight: 600;
                    color: #075E54;
                    font-size: 0.97rem;
                }
                .timeline-step-activity {
                    font-weight: 500;
                    color: #128C7E;
                    font-size: 0.97rem;
                }
                @media (max-width: 900px) {
                    .main-content {
                        padding: 1.2rem 0.5rem;
                    }
                    .timeline-content-grid {
                        flex-direction: column;
                        gap: 1.2rem;
                    }
                }
            </style>
        </div>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Timeline & Readiness",
                main_heading: "Ready to Launch",
                milestones_title: "Milestones Achieved",
                milestone_1: "Product development complete",
                milestone_2: "7-day free trial funnel live",
                milestone_3: "Influencer list ready",
                milestone_4: "Meta & video ad templates ready",
                milestone_5: "Team structure in place",
                timeline_title: "Launch Timeline",
                timeline_period_1: "Week 1",
                timeline_activity_1: "Paid marketing begins",
                timeline_period_2: "Weeks 2-4",
                timeline_activity_2: "5,000+ user onboarding target",
                timeline_period_3: "Months 2-3",
                timeline_activity_3: "Scale ads & partnerships",
                timeline_period_4: "Months 4-6",
                timeline_activity_4: "Feature expansion & upselling"
            },
            hi: {
                investor_deck: "निवेशक डेक",
                slide_title: "टाइमलाइन और रेडीनेस",
                main_heading: "लॉन्च के लिए तैयार",
                milestones_title: "क्या-क्या पूरा हुआ",
                milestone_1: "प्रोडक्ट बनकर तैयार",
                milestone_2: "7 दिन का फ्री ट्रायल लाइव",
                milestone_3: "इन्फ्लुएंसर लिस्ट तैयार",
                milestone_4: "मेटा और वीडियो ऐड टेम्पलेट्स रेडी",
                milestone_5: "टीम स्ट्रक्चर सेट",
                timeline_title: "लॉन्च टाइमलाइन",
                timeline_period_1: "हफ्ता 1",
                timeline_activity_1: "पेड मार्केटिंग शुरू",
                timeline_period_2: "हफ्ते 2-4",
                timeline_activity_2: "5,000+ यूजर ऑनबोर्डिंग टारगेट",
                timeline_period_3: "महीना 2-3",
                timeline_activity_3: "ऐड और पार्टनरशिप्स बढ़ाना",
                timeline_period_4: "महीना 4-6",
                timeline_activity_4: "फीचर बढ़ाना और अपसेलिंग"
            },
            gu: {
                investor_deck: "રોકાણકાર ડેક",
                slide_title: "ટાઈમલાઈન અને તૈયારીઓ",
                main_heading: "લોન્ચ માટે તૈયાર",
                milestones_title: "શું પૂર્ણ થયું",
                milestone_1: "પ્રોડક્ટ તૈયાર",
                milestone_2: "7 દિવસનું ફ્રી ટ્રાયલ લાઇવ",
                milestone_3: "ઇન્ફ્લુએન્સર લિસ્ટ તૈયાર",
                milestone_4: "મેટા અને વિડિયો એડ ટેમ્પલેટ્સ તૈયાર",
                milestone_5: "ટીમ સ્ટ્રક્ચર તૈયાર",
                timeline_title: "લોન્ચ ટાઈમલાઈન",
                timeline_period_1: "અઠવાડિયું 1",
                timeline_activity_1: "પેડ માર્કેટિંગ શરૂ",
                timeline_period_2: "અઠવાડિયા 2-4",
                timeline_activity_2: "5,000+ યુઝર ઓનબોર્ડિંગ ટાર્ગેટ",
                timeline_period_3: "મહિનો 2-3",
                timeline_activity_3: "એડ્સ અને પાર્ટનરશિપ્સ વધારવી",
                timeline_period_4: "મહિનો 4-6",
                timeline_activity_4: "ફીચર્સ વધારવા અને અપસેલિંગ"
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });

        function nextSlide() {
            window.location.href = 'slide10.html';
        }
        function previousSlide() {
            window.location.href = 'slide8.html';
        }
    </script>
</body>
</html>
