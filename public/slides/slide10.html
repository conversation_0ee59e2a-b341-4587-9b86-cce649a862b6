<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 9: Investment Offer</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
        }
        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }
        .content-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 2rem;
            align-items: flex-start;
        }
        .section h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
        }
        .info-cards {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        .info-card {
            background-color: var(--light-gray);
            padding: 1.5rem;
            border-radius: 12px;
        }
        .info-card .label { font-size: 1rem; color: var(--gray); margin-bottom: 0.5rem; }
        .info-card .value { font-size: 1.75rem; font-weight: 700; color: var(--accent); }
        .benefits-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
            font-size: 1rem;
        }
        .benefits-list i {
            color: var(--primary);
            margin-right: 1rem;
            font-size: 1.2rem;
            margin-top: 0.2rem;
        }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">10</div>
            </div>
        </div>
        <div class="main-content unique-investment-slide">
            <div class="hero-stats-row" style="animation-delay: 0.2s;">
                <div class="hero-stat-card glass" style="--stat-card-bg:linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%);">
                    <span class="hero-label" data-translate-key="raising_label">We Are Raising</span>
                    <span class="hero-value" data-translate-key="raising_value">₹10,00,000</span>
                </div>
                <div class="hero-stat-card glass" style="--stat-card-bg:linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);">
                    <span class="hero-label" data-translate-key="entry_label">Minimum Entry</span>
                    <span class="hero-value" data-translate-key="entry_value">₹5,00,000</span>
                </div>
                <div class="hero-stat-card glass" style="--stat-card-bg:linear-gradient(120deg,#e6e6ff 70%,#ffe6fa 100%);">
                    <span class="hero-label" data-translate-key="lockin_label">Lock-In Period</span>
                    <span class="hero-value" data-translate-key="lockin_value">3 Months</span>
                </div>
                <div class="hero-stat-card glass" style="--stat-card-bg:linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%);">
                    <span class="hero-label" data-translate-key="roi_label">Expected ROI</span>
                    <span class="hero-value">30-40%</span>
                </div>
            </div>
            <div class="projections-benefits-flex" style="animation-delay: 0.4s;">
                <div class="vertical-stepper glass">
                    <h3 class="glass-title" data-translate-key="projections_title">Revenue Projections (6 Months)</h3>
                    <div class="step">
                        <div class="step-icon" style="--step-icon-bg:linear-gradient(135deg,#25D366 60%,#34B7F1 100%);"><i class="fas fa-users"></i></div>
                        <div class="step-content"><span data-translate-key="projection_1">10,000 users x ₹1500 = ₹1.5 Cr revenue</span></div>
                    </div>
                    <div class="step">
                        <div class="step-icon" style="--step-icon-bg:linear-gradient(135deg,#fdc500 60%,#ff6f61 100%);"><i class="fas fa-wallet"></i></div>
                        <div class="step-content"><span data-translate-key="projection_2">Expenses (marketing, ops): ₹30–40L</span></div>
                    </div>
                    <div class="step">
                        <div class="step-icon" style="--step-icon-bg:linear-gradient(135deg,#6F42C1 60%,#34B7F1 100%);"><i class="fas fa-chart-pie"></i></div>
                        <div class="step-content"><span data-translate-key="projection_3">Estimated Net Profit: ₹1.1 Cr</span></div>
                    </div>
                </div>
                <div class="benefits-cards-row">
                    <h3 class="glass-title" data-translate-key="benefits_title">Investor Benefits</h3>
                    <div class="benefit-card glass" style="--benefit-card-bg:linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);">
                        <div class="benefit-icon" style="--benefit-icon-bg:linear-gradient(135deg,#fdc500 60%,#ff6f61 100%);"><i class="fas fa-file-invoice-dollar"></i></div>
                        <div class="benefit-desc" data-translate-key="benefit_1">Monthly progress reports</div>
                    </div>
                    <div class="benefit-card glass" style="--benefit-card-bg:linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%);">
                        <div class="benefit-icon" style="--benefit-icon-bg:linear-gradient(135deg,#25D366 60%,#34B7F1 100%);"><i class="fas fa-eye"></i></div>
                        <div class="benefit-desc" data-translate-key="benefit_2">Transparent, performance-linked model</div>
                    </div>
                    <div class="benefit-card glass" style="--benefit-card-bg:linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%);">
                        <div class="benefit-icon" style="--benefit-icon-bg:linear-gradient(135deg,#6F42C1 60%,#34B7F1 100%);"><i class="fas fa-redo"></i></div>
                        <div class="benefit-desc" data-translate-key="benefit_3">Option to reinvest or exit with profits</div>
                    </div>
                </div>
            </div>
            <style>
                .unique-investment-slide {
                    background: #fff;
                    min-height: 100%;
                    border-radius: 1.5rem;
                    box-shadow: 0 4px 32px rgba(0,0,0,0.07);
                    padding: 1.5rem 1rem 1rem 1rem;
                    display: flex;
                    flex-direction: column;
                    justify-content: flex-start;
                    align-items: center;
                    overflow: hidden;
                }
                .hero-stats-row {
                    display: flex;
                    gap: 1.1rem;
                    width: 100%;
                    justify-content: center;
                    margin-bottom: 1.2rem;
                    flex-wrap: wrap;
                }
                .hero-stat-card {
                    min-width: 210px;
                    padding: 1.7rem 2.2rem;
                    border-radius: 1.2rem;
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    justify-content: center;
                    margin-bottom: 0.8rem;
                    box-shadow: 0 2px 14px rgba(37,211,102,0.10);
                    background: var(--stat-card-bg, linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%));
                    position: relative;
                }
                .hero-label {
                    font-size: 1.06rem;
                    color: #128C7E;
                    font-weight: 600;
                    margin-bottom: 0.18rem;
                }
                .hero-value {
                    font-size: 2.1rem;
                    font-weight: 800;
                    color: #212529;
                    letter-spacing: 0.6px;
                }
                .projections-benefits-flex {
                    display: flex;
                    gap: 1.2rem;
                    width: 100%;
                    justify-content: space-between;
                    align-items: stretch;
                    margin-top: 0.3rem;
                    flex-wrap: wrap;
                }
                .vertical-stepper {
                    background: linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);
                    border-radius: 1.2rem;
                    padding: 1.4rem 1.2rem;
                    min-width: 280px;
                    max-width: 340px;
                    display: flex;
                    flex-direction: column;
                    gap: 1.3rem;
                    box-shadow: 0 2px 14px rgba(253,197,0,0.10);
                }
                .glass-title {
                    color: #128C7E;
                    font-size: 1rem;
                    font-weight: 700;
                    margin-bottom: 0.6rem;
                    letter-spacing: 0.3px;
                }
                .step {
                    display: flex;
                    align-items: flex-start;
                    gap: 0.7rem;
                }
                .step-icon {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background: var(--step-icon-bg, linear-gradient(135deg, #25D366 60%, #128C7E 100%));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    font-size: 1.3rem;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                }
                .step-content {
                    color: #212529;
                    font-size: 0.97rem;
                    font-weight: 500;
                    margin-top: 0.1rem;
                }
                .benefits-cards-row {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 1.1rem;
                    min-width: 220px;
                    max-width: 320px;
                }
                .benefit-card {
                    display: flex;
                    align-items: center;
                    gap: 1.1rem;
                    padding: 1.2rem 1.5rem;
                    border-radius: 1.1rem;
                    background: var(--benefit-card-bg, linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%));
                    box-shadow: 0 2px 12px rgba(194,24,91,0.09);
                }
                .benefit-icon {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: var(--benefit-icon-bg, linear-gradient(135deg, #fdc500 60%, #ff6f61 100%));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: #fff;
                    font-size: 1.1rem;
                }
                .benefit-desc {
                    color: #212529;
                    font-size: 0.93rem;
                    font-weight: 500;
                }
                .glass {
                    backdrop-filter: blur(4px);
                    background: rgba(255,255,255,0.92) !important;
                    box-shadow: 0 2px 12px rgba(0,0,0,0.07);
                }
                @media (max-width: 1100px) {
                    .hero-stats-row { flex-wrap: wrap; gap: 1.1rem; }
                    .projections-benefits-flex { flex-direction: column; gap: 1.1rem; align-items: center; }
                    .vertical-stepper, .benefits-cards-row { max-width: 100%; min-width: 160px; }
                }
                @media (max-width: 700px) {
                    .unique-investment-slide { padding: 0.7rem 0.1rem; }
                    .hero-stat-card { padding: 0.9rem 0.7rem; min-width: 100px; }
                }
            </style>
        </div>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Investment Opportunity",
                main_heading: "Join Our Growth Journey",
                raising_label: "We Are Raising",
                raising_value: "₹10,00,000",
                entry_label: "Minimum Entry",
                entry_value: "₹5,00,000",
                lockin_label: "Lock-In Period",
                lockin_value: "3 Months",
                roi_label: "Expected ROI",
                projections_title: "Revenue Projections (6 Months)",
                projection_1: "10,000 users x ₹1500 = ₹1.5 Cr revenue",
                projection_2: "Expenses (marketing, ops): ₹30–40L",
                projection_3: "Estimated Net Profit: ₹1.1 Cr",
                benefits_title: "Investor Benefits",
                benefit_1: "Monthly progress reports",
                benefit_2: "Transparent, performance-linked model",
                benefit_3: "Option to reinvest or exit with profits"
            },
            hi: {
                investor_deck: "निवेशक डेक",
                slide_title: "इन्वेस्टमेंट का मौका",
                main_heading: "हमारी ग्रोथ जर्नी में जुड़ें",
                raising_label: "हम पैसे जुटा रहे हैं",
                raising_value: "₹10,00,000",
                entry_label: "कम से कम निवेश",
                entry_value: "₹5,00,000",
                lockin_label: "लॉक-इन पीरियड",
                lockin_value: "3 महीने",
                roi_label: "अपेक्षित रिटर्न",
                projections_title: "कमाई का अनुमान (6 महीने)",
                projection_1: "10,000 यूजर x ₹1500 = ₹1.5 करोड़ कमाई",
                projection_2: "खर्चे (मार्केटिंग, ऑप्स): ₹30-40 लाख",
                projection_3: "अनुमानित नेट प्रॉफिट: ₹1.1 करोड़",
                benefits_title: "निवेशक फायदे",
                benefit_1: "हर महीने प्रगति रिपोर्ट",
                benefit_2: "पारदर्शी, परफॉर्मेंस से जुड़ा मॉडल",
                benefit_3: "मुनाफे के साथ दोबारा निवेश या बाहर निकलने का ऑप्शन"
            },
            gu: {
                investor_deck: "રોકાણકાર ડેક",
                slide_title: "ઇન્વેસ્ટમેન્ટની તક",
                main_heading: "અમારી ગ્રોથ યાત્રામાં જોડાઓ",
                raising_label: "અમે પૈસા ભેગા કરી રહ્યા છીએ",
                raising_value: "₹10,00,000",
                entry_label: "ઓછામાં ઓછું રોકાણ",
                entry_value: "₹5,00,000",
                lockin_label: "લોક-ઇન પિરિયડ",
                lockin_value: "3 મહિના",
                roi_label: "અપેક્ષિત રિટર્ન",
                projections_title: "કમાણીનું અંદાજ (6 મહિના)",
                projection_1: "10,000 યુઝર x ₹1500 = ₹1.5 કરોડ કમાણી",
                projection_2: "ખર્ચ (માર્કેટિંગ, ઓપરેશન): ₹30-40 લાખ",
                projection_3: "અંદાજિત નેટ પ્રોફિટ: ₹1.1 કરોડ",
                benefits_title: "રોકાણકાર ફાયદા",
                benefit_1: "દર મહિને પ્રગતિ અહેવાલ",
                benefit_2: "પારદર્શક, પરફોર્મન્સ આધારિત મોડેલ",
                benefit_3: "નફા સાથે ફરીથી રોકાણ કે બહાર નીકળવાનો વિકલ્પ"
            },
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    // For elements with only a value to translate, like the info cards
                    if (el.classList.contains('value') || el.tagName === 'SPAN') {
                        el.textContent = translations[lang][key];
                    } else {
                        el.textContent = translations[lang][key];
                    }
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });

        function nextSlide() {
            window.location.href = 'slide11.html';
        }

        function previousSlide() {
            window.location.href = 'slide9.html';
        }
    </script>
</body>
</html>
