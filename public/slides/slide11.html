<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title data-translate-key="slide10_title">Whamart - Slide 10: Investment Summary</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img { height: 40px; width: auto; object-fit: contain; margin-bottom: 1rem; }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }
        .reasons-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            flex-grow: 1;
        }
        .reason-item {
            display: flex;
            align-items: flex-start;
        }
        .reason-item i {
            font-size: 1.2rem;
            color: var(--primary);
            margin-right: 1rem;
            margin-top: 0.25rem;
        }
        .reason-item p { font-size: 1rem; line-height: 1.6; }
        .tagline {
            margin-top: 3rem;
            padding: 2rem;
            text-align: center;
            background-color: var(--light-gray);
            border-radius: 12px;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--accent);
        }
        .tagline i { margin: 0 0.5rem; opacity: 0.7; }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">11</div>
            </div>
        </div>
        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                                <p class="subtitle" data-translate-key="subtitle">Investment Summary</p>
                                <h1 class="title" data-translate-key="title">Why Whamart is the Right Bet</h1>
            </div>
            <div class="reasons-grid colorful-reasons" style="animation-delay: 0.4s;">
    <div class="reason-card" style="background:linear-gradient(120deg,#e0ffe7,#d6f5ff);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#25D366,#128C7E);"><i class="fas fa-users"></i></div>
        <div class="reason-text"><p data-translate-key="reason1">Massive, underserved market of over 8 crore sellers.</p></div>
    </div>
    <div class="reason-card" style="background:linear-gradient(120deg,#fffbe7,#ffe6e6);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#fdc500,#ff6f61);"><i class="fas fa-bullseye"></i></div>
        <div class="reason-text"><p data-translate-key="reason2">Zero dependency on external tools or apps.</p></div>
    </div>
    <div class="reason-card" style="background:linear-gradient(120deg,#e6e6ff,#ffe6fa);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#6F42C1,#34B7F1);"><i class="fas fa-globe-asia"></i></div>
        <div class="reason-text"><p data-translate-key="reason3">Scalable PAN India with regional language support.</p></div>
    </div>
    <div class="reason-card" style="background:linear-gradient(120deg,#ffe6fa,#e6e6ff);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#fdc500,#25D366);"><i class="fas fa-hand-holding-heart"></i></div>
        <div class="reason-text"><p data-translate-key="reason4">Social impact: empowering India's smallest entrepreneurs.</p></div>
    </div>
    <div class="reason-card" style="background:linear-gradient(120deg,#d6f5ff,#e0ffe7);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#34B7F1,#128C7E);"><i class="fas fa-rocket"></i></div>
        <div class="reason-text"><p data-translate-key="reason5">Ready to launch with team and assets in place.</p></div>
    </div>
    <div class="reason-card" style="background:linear-gradient(120deg,#ffe6e6,#fffbe7);">
        <div class="reason-icon" style="background:linear-gradient(135deg,#fdc500,#6F42C1);"><i class="fas fa-chart-line"></i></div>
        <div class="reason-text"><p data-translate-key="reason6">Clear exit strategy with monthly investor updates.</p></div>
    </div>
</div>
<style>
.colorful-reasons {
    display: flex !important;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: flex-start;
}
.reason-card {
    min-width: 270px;
    max-width: 340px;
    flex: 1 1 270px;
    display: flex;
    align-items: flex-start;
    border-radius: 1.3rem;
    box-shadow: 0 2px 12px rgba(37,211,102,0.09);
    padding: 1.7rem 1.3rem 1.2rem 1.3rem;
    margin-bottom: 0.3rem;
    position: relative;
    transition: transform 0.18s, box-shadow 0.18s;
    background-clip: padding-box;
}
.reason-card:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 0 8px 28px rgba(37,211,102,0.14);
}
.reason-icon {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.65rem;
    margin-right: 1.1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    flex-shrink: 0;
}
.reason-text p {
    font-size: 1.13rem;
    font-weight: 600;
    color: #212529;
    margin: 0;
    line-height: 1.7;
}
@media (max-width: 900px) {
    .colorful-reasons { gap: 1rem; }
    .reason-card { min-width: 180px; padding: 1.1rem 0.7rem; }
    .reason-icon { width: 38px; height: 38px; font-size: 1.1rem; margin-right: 0.7rem; }
    .reason-text p { font-size: 0.97rem; }
}
</style>
            <div class="tagline" style="animation-delay: 0.6s;">
                <i class="fas fa-quote-left"></i>
                                <strong data-translate-key="tagline">Back a made-in-India product built for India's growth</strong>
                <i class="fas fa-quote-right"></i>
            </div>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
                const translations = {
            en: {
                slide10_title: "Whamart - Slide 10: Investment Summary",
                investor_deck: "Investor Deck",
                subtitle: "Investment Summary",
                title: "Why Whamart is the Right Bet",
                reason1: "Massive, underserved market of over 8 crore sellers.",
                reason2: "Zero dependency on external tools or apps.",
                reason3: "Scalable PAN India with regional language support.",
                reason4: "Social impact: empowering India's smallest entrepreneurs.",
                reason5: "Ready to launch with team and assets in place.",
                reason6: "Clear exit strategy with monthly investor updates.",
                tagline: "Back a made-in-India product built for India's growth",
                lang_en: "English",
                lang_hi: "Hindi",
                lang_gu: "Gujarati"
            },
            hi: {
                slide10_title: "व्हामार्ट - स्लाइड 10: इन्वेस्टमेंट सारांश",
                investor_deck: "निवेशक डेक",
                subtitle: "इन्वेस्टमेंट सारांश",
                title: "व्हामार्ट क्यों बेस्ट है",
                reason1: "8 करोड़+ सेलर्स का बड़ा मार्केट, जो अब तक नहीं जुड़ा है।",
                reason2: "कोई बाहरी टूल या ऐप की जरूरत नहीं।",
                reason3: "हर राज्य में, अपनी भाषा में स्केलेबल।",
                reason4: "सोशल इम्पैक्ट: छोटे बिजनेस वालों को ताकत देना।",
                reason5: "टीम और जरूरी चीजों के साथ लॉन्च को तैयार।",
                reason6: "हर महीने इन्वेस्टर अपडेट और क्लियर एग्जिट प्लान।",
                tagline: "भारत की ग्रोथ के लिए बने मेड-इन-इंडिया प्रोडक्ट को सपोर्ट करें",
                lang_en: "अंग्रेजी",
                lang_hi: "हिंदी",
                lang_gu: "गुजराती"
            },
            gu: {
                slide10_title: "વ્હામાર્ટ - સ્લાઇડ 10: ઇન્વેસ્ટમેન્ટ સારાંશ",
                investor_deck: "રોકાણકાર ડેક",
                subtitle: "ઇન્વેસ્ટમેન્ટ સારાંશ",
                title: "વ્હામાર્ટ કેમ શ્રેષ્ઠ છે",
                reason1: "8 કરોડથી વધુ સેલર્સનું મોટું બજાર, જે જોડાયું નથી.",
                reason2: "કોઈ બહારના ટૂલ કે એપની જરૂર નથી.",
                reason3: "દરેક રાજ્યમાં, પોતાની ભાષામાં સ્કેલ કરી શકાય તેવું.",
                reason4: "સોશિયલ ઇમ્પેક્ટ: નાના બિઝનેસને શક્તિ આપવી.",
                reason5: "ટીમ અને જરૂરી વસ્તુઓ સાથે લોન્ચ માટે તૈયાર.",
                reason6: "દર મહિને રોકાણકાર અપડેટ અને ક્લિયર એક્ઝિટ પ્લાન.",
                tagline: "ભારતની ગ્રોથ માટે બનેલા મેડ-ઇન-ઇન્ડિયા પ્રોડક્ટને સપોર્ટ કરો",
                lang_en: "અંગ્રેજી",
                lang_hi: "હિન્દી",
                lang_gu: "ગુજરાતી"
            }
        };

        function getLanguageFromURL() {
            const params = new URLSearchParams(window.location.search);
            return params.get('lang') || localStorage.getItem('preferredLanguage') || 'en';
        }

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            updateText(lang);
        }

        function updateText(lang) {
            document.querySelectorAll('[data-translate-key]').forEach(element => {
                const key = element.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    element.innerHTML = translations[lang][key];
                }
            });
        }

        function nextSlide() {
            const lang = getLanguageFromURL();
            window.location.href = `slide12.html?lang=${lang}`;
        }

        function previousSlide() {
            const lang = getLanguageFromURL();
            window.location.href = `slide10.html?lang=${lang}`;
        }

        document.addEventListener('DOMContentLoaded', () => {
            const initialLang = getLanguageFromURL();
            setLanguage(initialLang);
        });
    </script>
</body>
</html>
