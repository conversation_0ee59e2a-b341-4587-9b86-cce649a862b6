<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 4: Market & TAM</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            /* Remove max-height to allow full viewport usage */
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }

        .sidebar-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .sidebar-footer .slide-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.5;
        }

        .main-content {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            padding: 2.2rem 2.5rem;
            height: 100%;
            min-height: 0;
            overflow-y: auto;
            box-sizing: border-box;
        }

        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }

        .slide4-flex-wrap {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            align-items: stretch;
            gap: 2.5rem;
            flex: 1;
            min-height: 0;
            height: 100%;
        }
        .segments-col, .stats-col {
            display: flex;
            flex-direction: column;
            justify-content: center;
            min-width: 0;
            flex: 1 1 0;
            gap: 1.2rem;
        }
        .segments-list {
            display: flex;
            flex-direction: column;
            gap: 1.2rem;
        }
        .segment-card {
            display: flex;
            align-items: flex-start;
            gap: 1.1rem;
            background: var(--light-gray);
            border-radius: 14px;
            padding: 1.1rem 1.5rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        .segment-icon {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            color: #fff;
            margin-right: 0.7rem;
        }
        .segment-card.c1 .segment-icon { background: var(--primary); }
        .segment-card.c2 .segment-icon { background: var(--accent); }
        .segment-card.c3 .segment-icon { background: #6F42C1; }
        .segment-card h4 {
            font-size: 1.08rem;
            font-weight: 700;
            margin-bottom: 0.2rem;
        }
        .segment-card p {
            font-size: 0.97rem;
            color: var(--gray);
        }
        .stats-pills {
            display: flex;
            flex-direction: column;
            gap: 1.3rem;
            justify-content: center;
        }
        .stat-pill {
            display: flex;
            align-items: center;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            color: #fff;
            border-radius: 2rem;
            padding: 1rem 2rem;
            gap: 1.5rem;
            box-shadow: 0 4px 16px rgba(7,94,84,0.09);
            min-width: 0;
        }
        .stat-pill.c1 { background: linear-gradient(90deg, var(--primary), #13b36a); }
        .stat-pill.c2 { background: linear-gradient(90deg, var(--accent), #1a8a7b); }
        .stat-pill.c3 { background: linear-gradient(90deg, #6F42C1, #25D366); }
        .stat-pill-num {
            font-size: 2rem;
            font-weight: 800;
            margin-right: 1.1rem;
            white-space: nowrap;
        }
        .stat-pill-desc {
            font-size: 1.04rem;
            color: #fff;
            opacity: 0.96;
        }
        /* Sticky tagline bar at bottom */
        .slide4-tagline-footer {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100%;
            background: linear-gradient(90deg, var(--primary), var(--accent));
            color: #fff;
            font-size: 1.08rem;
            font-weight: 600;
            padding: 1.1rem 0;
            text-align: center;
            border-bottom-left-radius: 24px;
            border-bottom-right-radius: 24px;
            z-index: 10;
            letter-spacing: 0.5px;
        }
        /* Timeline for segments */
        
        
        
        
        
        
        /* Stat bubbles */
        
        
        
        /* Tagline ribbon */
        

        .grid-item h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }

        .feature-list .feature-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .feature-item .icon {
            font-size: 1.5rem;
            color: var(--primary);
            margin-right: 1rem;
            width: 30px;
            text-align: center;
        }

        .feature-item-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .feature-item-content p {
            font-size: 0.95rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .market-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            text-align: center;
        }

        .stat-item .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--accent);
        }

        .stat-item .stat-description {
            font-size: 1rem;
            color: var(--gray);
        }

        .tagline {
            background-color: var(--light-gray);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--dark);
            margin-top: 2rem;
        }

        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }

        /* Animations */
        .main-content > * { 
            animation: slideUp 0.6s ease-out forwards; opacity: 0; 
        }
        @keyframes slideUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">04</div>
            </div>
        </div>

        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                <p class="subtitle" data-translate-key="slide_title">Market Opportunity</p>
                <h1 class="title" data-translate-key="main_heading">Tapping into a Vast Digital Frontier</h1>
            </div>

            <div class="slide4-flex-wrap" style="animation-delay: 0.4s;">
                <!-- Left: Target Segments as colorful cards -->
                <div class="segments-col">
                    <h3 data-translate-key="target_segments_title">Target Segments</h3>
                    <div class="segments-list">
                        <div class="segment-card c1">
                            <div class="segment-icon"><i class="fas fa-store"></i></div>
                            <div>
                                <h4 data-translate-key="segment1_title">Retail & Services</h4>
                                <p data-translate-key="segment1_text">Kirana stores, boutiques, tailors, repair shops, and parlours.</p>
                            </div>
                        </div>
                        <div class="segment-card c2">
                            <div class="segment-icon"><i class="fas fa-user-tie"></i></div>
                            <div>
                                <h4 data-translate-key="segment2_title">Freelancers</h4>
                                <p data-translate-key="segment2_text">Mehendi artists, tutors, astrologers, and event planners.</p>
                            </div>
                        </div>
                        <div class="segment-card c3">
                            <div class="segment-icon"><i class="fas fa-download"></i></div>
                            <div>
                                <h4 data-translate-key="segment3_title">Digital Sellers</h4>
                                <p data-translate-key="segment3_text">Creators selling ebooks, courses, PDFs, and digital art.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Right: Market Stats as horizontal pills -->
                <div class="stats-col">
                    <h3 data-translate-key="market_potential_title">Market Potential</h3>
                    <div class="stats-pills">
                        <div class="stat-pill c1">
                            <div class="stat-pill-num">8 Cr+</div>
                            <div class="stat-pill-desc" data-translate-key="stat1_desc">Micro & Small Businesses in India</div>
                        </div>
                        <div class="stat-pill c2">
                            <div class="stat-pill-num">40 Cr+</div>
                            <div class="stat-pill-desc" data-translate-key="stat2_desc">Daily Business Messages on WhatsApp</div>
                        </div>
                        <div class="stat-pill c3">
                            <div class="stat-pill-num">95%</div>
                            <div class="stat-pill-desc" data-translate-key="stat3_desc">Untapped Market for Digital Storefronts</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide5.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide3.html';
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Market Opportunity",
                main_heading: "Tapping into a Vast Digital Frontier",
                target_segments_title: "Target Segments",
                segment1_title: "Retail & Services",
                segment1_text: "Kirana stores, boutiques, tailors, repair shops, and parlours.",
                segment2_title: "Freelancers",
                segment2_text: "Mehendi artists, tutors, astrologers, and event planners.",
                segment3_title: "Digital Sellers",
                segment3_text: "Creators selling ebooks, courses, PDFs, and digital art.",
                market_potential_title: "Market Potential",
                stat1_desc: "Micro & Small Businesses in India",
                stat2_desc: "Daily Business Messages on WhatsApp",
                stat3_desc: "Untapped Market for Digital Storefronts",
                tagline: "A ₹1500/year solution for a ₹30,000+ earning opportunity per vendor."
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                slide_title: "मार्केट ऑपर्च्युनिटी",
                main_heading: "एक बड़ा डिजिटल फ्रंटियर",
                target_segments_title: "टारगेट सेगमेंट्स",
                segment1_title: "रिटेल और सर्विसेज",
                segment1_text: "किराना, बुटीक, टेलर, रिपेयर शॉप और पार्लर।",
                segment2_title: "फ्रीलांसर",
                segment2_text: "मेहंदी आर्टिस्ट, ट्यूटर, ज्योतिषी और इवेंट प्लानर।",
                segment3_title: "डिजिटल सेलर्स",
                segment3_text: "ईबुक, कोर्स, PDF और डिजिटल आर्ट बेचने वाले क्रिएटर्स।",
                market_potential_title: "मार्केट पोटेंशियल",
                stat1_desc: "भारत में माइक्रो और स्मॉल बिज़नेस",
                stat2_desc: "व्हाट्सएप पर रोज़ाना बिज़नेस मैसेज",
                stat3_desc: "डिजिटल स्टोरफ्रंट्स के लिए अनटैप्ड मार्केट",
                tagline: "हर विक्रेता के लिए ₹30,000+ कमाई के मौके के लिए ₹1500/साल का सॉल्यूशन।"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                slide_title: "માર્કેટ ઓપર્ચ્યુનિટી",
                main_heading: "વિશાળ ડિજિટલ ફ્રન્ટિયર",
                target_segments_title: "ટારગેટ સેગમેન્ટ્સ",
                segment1_title: "રિટેલ અને સર્વિસીસ",
                segment1_text: "કિરાણા, બુટિક, ટેલર, રિપેર શોપ અને પાર્લર.",
                segment2_title: "ફ્રીલાન્સર",
                segment2_text: "મેહંદી આર્ટિસ્ટ, ટ્યુટર, જ્યોતિષી અને ઇવેન્ટ પ્લાનર.",
                segment3_title: "ડિજિટલ સેલર્સ",
                segment3_text: "ઇબુક, કોર્સ, PDF અને ડિજિટલ આર્ટ વેચતા ક્રિએટર્સ.",
                market_potential_title: "માર્કેટ પોટેન્શિયલ",
                stat1_desc: "ભારતમાં માઇક્રો અને સ્મોલ બિઝનેસ",
                stat2_desc: "વોટ્સએપ પર રોજના બિઝનેસ મેસેજ",
                stat3_desc: "ડિજિટલ સ્ટોરફ્રન્ટ્સ માટે અનટેપ્ડ માર્કેટ",
                tagline: "દરેક વેચનાર માટે ₹30,000+ કમાણીની તક માટે ₹1500/વર્ષનું સોલ્યુશન."
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>
