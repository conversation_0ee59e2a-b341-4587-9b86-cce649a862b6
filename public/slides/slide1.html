<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Investor Presentation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }

        .sidebar-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .sidebar-footer .slide-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.5;
        }

        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .logo {
            width: 150px;
            margin-bottom: 1.5rem;
        }

        h1 {
            font-size: 4.5rem;
            font-weight: 800;
            color: var(--dark);
            line-height: 1.1;
            margin-bottom: 1rem;
        }

        .subtitle {
            font-size: 1.75rem;
            color: var(--gray);
            max-width: 700px;
            margin-bottom: 2rem;
        }

        .presenter {
            font-size: 1.25rem;
            color: var(--accent);
            font-weight: 600;
            margin-bottom: 2rem; /* Added margin */
        }

        .lang-selector {
            position: absolute;
            top: 2rem;
            right: 2rem;
        }

        .lang-selector select {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            border: 1px solid var(--gray);
            background-color: var(--white);
            font-family: var(--font-family);
            font-size: 1rem;
            cursor: pointer;
        }

        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }

        .nav-btn:disabled {
            background-color: var(--gray);
            cursor: not-allowed;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .slide-container {
                flex-direction: column;
                height: 100vh;
                width: 100vw;
                margin: 0;
                border-radius: 0;
                max-height: none;
            }
            .sidebar {
                width: 100%;
                height: auto;
                flex-direction: row;
                align-items: center;
                padding: 1rem 2rem;
            }
            .sidebar-footer { display: none; }
            .main-content { padding: 2rem; justify-content: center; }
            h1 { font-size: 3.5rem; }
            .subtitle { font-size: 1.5rem; }
            .navigation {
                bottom: 1.5rem;
                right: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .main-content { padding: 1.5rem; }
            h1 { font-size: 2.8rem; }
            .subtitle { font-size: 1.3rem; }
            .logo { width: 100px; height: 100px; }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        body { animation: fadeIn 0.5s ease-in-out; }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">01</div>
            </div>
        </div>

        <div class="main-content">
            <div class="lang-selector">
                <select id="language-select" onchange="setLanguage(this.value)">
                    <option value="en">English</option>
                    <option value="hi">हिन्दी</option>
                    <option value="gu">ગુજરાતી</option>
                </select>
            </div>
            <img src="../WhaMart_Logo.png" alt="Whamart Logo" class="logo" style="animation-delay: 0.2s;">
            <p class="subtitle" style="animation-delay: 0.6s;" data-translate-key="subtitle">Empowering Bharat's Local Businesses Digitally</p>
            <p class="presenter" style="animation-delay: 0.8s;" data-translate-key="presenter">Presented by Karan Solanki – Founder, Whamart</p>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide" disabled>
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide2.html';
        }
        
        function previousSlide() {
            // This is the first slide, no action needed.
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                subtitle: "Empowering Bharat's Local Businesses Digitally",
                presenter: "Presented by Karan Solanki – Founder, Whamart"
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                subtitle: "भारत के लोकल बिज़नेस को डिजिटल तरीके से मजबूत बनाना",
                presenter: "प्रस्तुतकर्ता: करण सोलंकी – फाउंडर, Whamart"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                subtitle: "ભારતના લોકલ બિઝનેસને ડિજિટલ રીતે મજબૂત બનાવવું",
                presenter: "પ્રસ્તુતકર્તા: કરણ સોલંકી – ફાઉન્ડર, Whamart"
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
            document.getElementById('language-select').value = lang;
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>
