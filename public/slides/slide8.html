<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 7: Investment & Use of Funds</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
            --chart-colors: ['#25D366', '#128C7E', '#075E54', '#34B7F1', '#DCF8C6', '#ECE5DD'];
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
        }
        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1.2fr;
            gap: 3rem;
            align-items: center;
        }
        .chart-container { 
            width: 100%;
            max-width: 400px;
            margin: 0 auto;
        }
        .table-container h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        .budget-table {
            width: 100%;
            border-collapse: collapse;
        }
        .budget-table th, .budget-table td {
            padding: 0.8rem 1rem;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }
        .budget-table th {
            font-weight: 600;
            color: var(--gray);
        }
        .budget-table td.category-name { font-weight: 500; }
        .budget-table td.budget-amount { font-weight: 600; text-align: right; }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">08</div>
            </div>
        </div>
        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                <p class="subtitle" data-translate-key="slide_title">Investment & Use of Funds</p>
                <h1 class="title" data-translate-key="main_heading">Strategic Capital Allocation</h1>
            </div>
            <div class="funds-content-grid" style="animation-delay: 0.3s; display: flex; flex-wrap: wrap; gap: 2rem; justify-content: center; align-items: flex-start;">
                <div class="funds-chart-card">
                    <div class="chart-title"><i class="fas fa-chart-pie" style="color:#25D366;"></i> <span data-translate-key="chart_label">Use of Funds</span></div>
                    <div class="chart-container" style="background: #f0fdf6; border-radius: 18px; padding: 1.2rem 1.2rem 0.7rem 1.2rem; box-shadow: 0 2px 10px rgba(7,94,84,0.07);">
                        <canvas id="useOfFundsChart"></canvas>
                    </div>
                </div>
                <div class="budget-cards-grid">
                    <h3 class="budget-cards-title" data-translate-key="budget_breakdown">Budget Breakdown (Total: ₹10 Lakh)</h3>
                    <div class="budget-cards">
                        <div class="budget-card" style="background: linear-gradient(120deg,#e0ffe7 70%,#d6f5ff 100%);">
                            <div class="icon-circle" style="background:#25D366;"><i class="fas fa-bullhorn"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_influencer">Influencer Marketing</span>
                                <span class="budget-card-amount">₹2,50,000</span>
                            </div>
                        </div>
                        <div class="budget-card" style="background: linear-gradient(120deg,#fffbe7 70%,#ffe6e6 100%);">
                            <div class="icon-circle" style="background:#fdc500;"><i class="fab fa-youtube"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_ads">Meta & YouTube Ads</span>
                                <span class="budget-card-amount">₹2,00,000</span>
                            </div>
                        </div>
                        <div class="budget-card" style="background: linear-gradient(120deg,#e6e6ff 70%,#ffe6fa 100%);">
                            <div class="icon-circle" style="background:#6F42C1;"><i class="fas fa-image"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_banner">City Banner Ads</span>
                                <span class="budget-card-amount">₹1,00,000</span>
                            </div>
                        </div>
                        <div class="budget-card" style="background: linear-gradient(120deg,#f0fdf6 70%,#c5f7fa 100%);">
                            <div class="icon-circle" style="background:#34B7F1;"><i class="fas fa-server"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_servers">High-Speed Servers</span>
                                <span class="budget-card-amount">₹1,00,000</span>
                            </div>
                        </div>
                        <div class="budget-card" style="background: linear-gradient(120deg,#fffbe7 70%,#e0ffe7 100%);">
                            <div class="icon-circle" style="background:#128C7E;"><i class="fas fa-users"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_salaries">Team Salaries (10)</span>
                                <span class="budget-card-amount">₹3,00,000</span>
                            </div>
                        </div>
                        <div class="budget-card" style="background: linear-gradient(120deg,#ffe6fa 70%,#e6e6ff 100%);">
                            <div class="icon-circle" style="background:#C2185B;"><i class="fas fa-balance-scale"></i></div>
                            <div class="budget-card-info">
                                <span class="budget-card-category" data-translate-key="cat_legal">Legal & Admin</span>
                                <span class="budget-card-amount">₹50,000</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <style>
                .funds-content-grid {
                    width: 100%;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 2rem;
                    justify-content: center;
                    align-items: flex-start;
                }
                .funds-chart-card {
                    min-width: 320px;
                    max-width: 400px;
                    flex: 1 1 340px;
                    display: flex;
                    flex-direction: column;
                    gap: 1rem;
                    align-items: center;
                }
                .chart-title {
                    font-size: 1.1rem;
                    font-weight: 700;
                    color: #128C7E;
                    margin-bottom: 0.5rem;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }
                .budget-cards-grid {
                    flex: 2 1 400px;
                    display: flex;
                    flex-direction: column;
                    gap: 1.1rem;
                }
                .budget-cards-title {
                    font-size: 1.1rem;
                    font-weight: 700;
                    color: #075E54;
                    margin-bottom: 0.5rem;
                }
                .budget-cards {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(210px, 1fr));
                    gap: 1.1rem;
                }
                .budget-card {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    background: var(--light-gray);
                    border-radius: 14px;
                    box-shadow: 0 2px 10px rgba(7,94,84,0.08);
                    padding: 0.8rem 1rem;
                    min-width: 0;
                    animation: slideUp 0.6s ease-out forwards;
                    opacity: 0;
                    font-size: 1rem;
                }
                .budget-card .icon-circle {
                    width: 40px;
                    height: 40px;
                    border-radius: 12px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.25rem;
                    color: #fff;
                    flex-shrink: 0;
                    box-shadow: 0 1px 6px rgba(7,94,84,0.09);
                }
                .budget-card-info {
                    display: flex;
                    flex-direction: column;
                    gap: 0.2rem;
                }
                .budget-card-category {
                    font-weight: 600;
                    color: #075E54;
                    font-size: 0.98rem;
                }
                .budget-card-amount {
                    font-weight: 700;
                    color: #128C7E;
                    font-size: 1.02rem;
                }
                @media (max-width: 900px) {
                    .main-content {
                        padding: 1.2rem 0.5rem;
                    }
                    .funds-content-grid {
                        flex-direction: column;
                        gap: 1.2rem;
                    }
                    .budget-cards {
                        grid-template-columns: 1fr;
                    }
                }
            </style>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
        let useOfFundsChart; // Declare chart variable in a broader scope

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Investment & Use of Funds",
                main_heading: "Strategic Capital Allocation",
                budget_breakdown: "Budget Breakdown (Total: ₹10 Lakh)",
                th_category: "Category",
                th_allocation: "Allocation",
                cat_influencer: "Influencer Marketing",
                cat_ads: "Meta & YouTube Ads",
                cat_banner: "City Banner Ads",
                cat_servers: "High-Speed Servers",
                cat_salaries: "Team Salaries (10)",
                cat_legal: "Legal & Admin",
                chart_label: "Use of Funds"
            },
            hi: {
                investor_deck: "निवेशक डेक",
                slide_title: "इन्वेस्टमेंट और फंड का यूज़",
                main_heading: "स्मार्ट तरीके से पैसा बाँटना",
                budget_breakdown: "बजट डिटेल्स (कुल: ₹10 लाख)",
                th_category: "कैटेगरी",
                th_allocation: "फंड बाँटना",
                cat_influencer: "इन्फ्लुएंसर मार्केटिंग",
                cat_ads: "मेटा और यूट्यूब ऐड्स",
                cat_banner: "सिटी बैनर ऐड्स",
                cat_servers: "हाई-स्पीड सर्वर",
                cat_salaries: "टीम सैलरी (10)",
                cat_legal: "कानूनी और एडमिन",
                chart_label: "फंड का यूज़"
            },
            gu: {
                investor_deck: "રોકાણકાર ડેક",
                slide_title: "ઇન્વેસ્ટમેન્ટ અને ફંડનો ઉપયોગ",
                main_heading: "સ્માર્ટ રીતે ફંડ ફાળવવું",
                budget_breakdown: "બજેટ વિગતો (કુલ: ₹10 લાખ)",
                th_category: "કેટેગરી",
                th_allocation: "ફંડ ફાળવણી",
                cat_influencer: "ઇન્ફ્લુએન્સર માર્કેટિંગ",
                cat_ads: "મેટા અને યુટ્યુબ એડ્સ",
                cat_banner: "શહેર બેનર એડ્સ",
                cat_servers: "હાઇ-સ્પીડ સર્વર્સ",
                cat_salaries: "ટીમ સેલેરી (10)",
                cat_legal: "કાનૂની અને એડમિન",
                chart_label: "ફંડનો ઉપયોગ"
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });

            // Update chart labels
            if (useOfFundsChart) {
                const chartTranslations = translations[lang];
                useOfFundsChart.data.labels = [
                    chartTranslations.cat_influencer,
                    chartTranslations.cat_ads,
                    chartTranslations.cat_banner,
                    chartTranslations.cat_servers,
                    chartTranslations.cat_salaries,
                    chartTranslations.cat_legal
                ];
                useOfFundsChart.data.datasets[0].label = chartTranslations.chart_label;
                useOfFundsChart.update();
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            const ctx = document.getElementById('useOfFundsChart').getContext('2d');
            useOfFundsChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [], // Will be populated by setLanguage
                    datasets: [{
                        label: '', // Will be populated by setLanguage
                        data: [250000, 200000, 100000, 100000, 300000, 50000],
                        backgroundColor: ['#25D366', '#128C7E', '#075E54', '#34B7F1', '#DCF8C6', '#6C757D'],
                        borderColor: '#FFFFFF',
                        borderWidth: 4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: {
                                    family: 'Poppins'
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });

        function nextSlide() {
            window.location.href = 'slide9.html';
        }
        function previousSlide() {
            window.location.href = 'slide7.html';
        }
    </script>
</body>
</html>
