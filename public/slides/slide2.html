<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 2: The Problem We're Solving</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;

            /* New accent colors for cards */
            --color-1: #E9F7EF;
            --color-2: #E6F3FF;
            --color-3: #FFF4E6;
            --color-4: #FEEEEE;
            --color-5: #F3E8FF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }

        .sidebar-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .sidebar-footer .slide-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.5;
        }

        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header .slide-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--primary);
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 0.5rem;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--dark);
            line-height: 1.2;
        }

        .problems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            flex-grow: 1;
        }

        .problem-card {
            background-color: var(--light-gray);
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .problem-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.07);
        }

        .card-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
        }
        .card-icon.c1 { background-color: var(--color-1); color: var(--primary); }
        .card-icon.c2 { background-color: var(--color-2); color: #007BFF; }
        .card-icon.c3 { background-color: var(--color-3); color: #FD7E14; }
        .card-icon.c4 { background-color: var(--color-4); color: #DC3545; }
        .card-icon.c5 { background-color: var(--color-5); color: #6F42C1; }


        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .card-text {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .slide-container {
                flex-direction: column;
                height: 100vh;
                width: 100vw;
                margin: 0;
                border-radius: 0;
                max-height: none;
            }
            .sidebar {
                width: 100%;
                height: auto;
                flex-direction: row;
                align-items: center;
                padding: 1rem 2rem;
            }
            .sidebar-footer { display: none; }
            .main-content { padding: 2rem; overflow-y: auto; }
            .header h1 { font-size: 2rem; }
            .navigation {
                bottom: 1.5rem;
                right: 1.5rem;
            }
        }

        @media (max-width: 768px) {
            .main-content { padding: 1.5rem; }
            .header h1 { font-size: 1.8rem; }
            .problems-grid { grid-template-columns: 1fr; }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        body { animation: fadeIn 0.5s ease-in-out; }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .problem-card, .header {
            animation: slideUp 0.5s ease-out forwards;
            opacity: 0;
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">02</div>
            </div>
        </div>

        <div class="main-content">
            <header class="header" style="animation-delay: 0s;">
                <p class="slide-title" data-translate-key="slide_title">Problem Statement</p>
                <h1 data-translate-key="main_heading">The Challenges for India's Small Businesses</h1>
            </header>

            <div class="problems-grid">
                <div class="problem-card" style="animation-delay: 0.1s;">
                    <div class="card-icon c1"><i class="fas fa-shop-slash"></i></div>
                    <h3 class="card-title" data-translate-key="problem1_title">Digital Divide</h3>
                    <p class="card-text" data-translate-key="problem1_text">Over 80 million small businesses in India struggle to establish a digital presence.</p>
                </div>
                <div class="problem-card" style="animation-delay: 0.2s;">
                    <div class="card-icon c2"><i class="fas fa-puzzle-piece"></i></div>
                    <h3 class="card-title" data-translate-key="problem2_title">Complex Platforms</h3>
                    <p class="card-text" data-translate-key="problem2_text">Current e-commerce solutions are often too costly, technical, and difficult to manage.</p>
                </div>
                <div class="problem-card" style="animation-delay: 0.3s;">
                    <div class="card-icon c3"><i class="fab fa-whatsapp"></i></div>
                    <h3 class="card-title" data-translate-key="problem3_title">Expensive APIs</h3>
                    <p class="card-text" data-translate-key="problem3_text">The official WhatsApp Business API is prohibitively expensive for most micro-vendors.</p>
                </div>
                <div class="problem-card" style="animation-delay: 0.4s;">
                    <div class="card-icon c4"><i class="fas fa-credit-card"></i></div>
                    <h3 class="card-title" data-translate-key="problem4_title">Payment Hurdles</h3>
                    <p class="card-text" data-translate-key="problem4_text">Payment gateways involve lengthy KYC processes, setup times, and high commissions.</p>
                </div>
                <div class="problem-card" style="animation-delay: 0.5s;">
                    <div class="card-icon c5"><i class="fas fa-shield-alt"></i></div>
                    <h3 class="card-title" data-translate-key="problem5_title">Trust Deficit</h3>
                    <p class="card-text" data-translate-key="small_vendors_lack">Small vendors lack the necessary tools (like verified identity, invoicing) to build customer trust.</p>
                </div>
                 <div class="problem-card" style="animation-delay: 0.6s;">
                    <div class="card-icon c1"><i class="fas fa-lightbulb"></i></div>
                    <h3 class="card-title" data-translate-key="opportunity_title">The Opportunity</h3>
                    <p class="card-text" data-translate-key="opportunity_text">A simple, affordable, and trustworthy way to go digital and reach customers through WhatsApp.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide3.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide1.html';
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Problem Statement",
                main_heading: "The Challenges for India's Small Businesses",
                problem1_title: "Digital Divide",
                problem1_text: "Over 80 million small businesses in India struggle to establish a digital presence.",
                problem2_title: "Complex Platforms",
                problem2_text: "Current e-commerce solutions are often too costly, technical, and difficult to manage.",
                problem3_title: "Expensive APIs",
                problem3_text: "The official WhatsApp Business API is prohibitively expensive for most micro-vendors.",
                problem4_title: "Payment Hurdles",
                problem4_text: "Payment gateways involve lengthy KYC processes, setup times, and high commissions.",
                problem5_title: "Trust Deficit",
                small_vendors_lack: "Small vendors lack the necessary tools (like verified identity, invoicing) to build customer trust.",
                opportunity_title: "The Opportunity",
                opportunity_text: "A simple, affordable, and trustworthy way to go digital and reach customers through WhatsApp."
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                slide_title: "समस्या स्टेटमेंट",
                main_heading: "भारत के छोटे बिज़नेस के लिए चुनौतियाँ",
                problem1_title: "डिजिटल गैप",
                problem1_text: "भारत में 80 मिलियन से ज्यादा छोटे बिज़नेस डिजिटल पहचान बनाने के लिए जूझ रहे हैं।",
                problem2_title: "मुश्किल प्लेटफॉर्म",
                problem2_text: "अभी के ई-कॉमर्स सॉल्यूशन अक्सर बहुत महंगे, टेक्निकल और चलाने में मुश्किल हैं।",
                problem3_title: "महंगे एपीआई",
                problem3_text: "ऑफिशियल व्हाट्सएप बिज़नेस API ज्यादातर छोटे दुकानदारों के लिए बहुत महंगा है।",
                problem4_title: "पेमेंट की दिक्कतें",
                problem4_text: "पेमेंट गेटवे में लंबी KYC, सेटअप टाइम और ज्यादा कमीशन लगते हैं।",
                problem5_title: "भरोसे की कमी",
                small_vendors_lack: "छोटे दुकानदारों के पास कस्टमर का भरोसा बनाने के लिए जरूरी टूल्स (जैसे वेरिफाइड आईडी, इनवॉइसिंग) नहीं हैं।",
                opportunity_title: "मौका",
                opportunity_text: "WhatsApp के जरिए डिजिटल होने और कस्टमर तक पहुँचने का आसान, सस्ता और भरोसेमंद तरीका।"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                slide_title: "સમસ્યા સ્ટેટમેન્ટ",
                main_heading: "ભારતના નાના બિઝનેસ માટે પડકારો",
                problem1_title: "ડિજિટલ ગેપ",
                problem1_text: "ભારતમાં 80 મિલિયનથી વધુ નાના બિઝનેસ ડિજિટલ ઓળખ બનાવવા માટે સંઘર્ષ કરે છે.",
                problem2_title: "મુશ્કેલ પ્લેટફોર્મ",
                problem2_text: "હાલના ઇ-કોમર્સ સોલ્યુશન્સ ઘણીવાર ખર્ચાળ, ટેકનિકલ અને ચલાવવા મુશ્કેલ છે.",
                problem3_title: "મોંઘા API",
                problem3_text: "ઓફિશિયલ WhatsApp Business API મોટાભાગના નાના વેપારીઓ માટે ખૂબ જ મોંઘું છે.",
                problem4_title: "પેમેન્ટની સમસ્યા",
                problem4_text: "પેમેન્ટ ગેટવેમાં લાંબી KYC, સેટઅપ ટાઈમ અને વધુ કમિશન હોય છે.",
                problem5_title: "ભરોસાની ઉણપ",
                small_vendors_lack: "નાના વેપારીઓ પાસે ગ્રાહકનો ભરોસો મેળવવા જરૂરી ટૂલ્સ (જેમ કે વેરિફાઇડ ઓળખ, ઇન્વોઇસિંગ) નથી.",
                opportunity_title: "મોકો",
                opportunity_text: "WhatsApp દ્વારા ડિજિટલ થવા અને ગ્રાહકો સુધી પહોંચવા માટે સરળ, સસ્તું અને ભરોસાપાત્ર ઉપાય."
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>
