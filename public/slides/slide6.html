<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 6: Business Model</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 1.6rem 1.6rem;
            overflow-y: visible;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .header .subtitle {
            font-size: 1.02rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.35rem;
        }
        .header .title {
            font-size: 1.75rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 1.1rem;
        }
        .pricing-plans {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 2rem;
            margin-bottom: 2rem;
        }
        .plan-card {
            background-color: var(--light-gray);
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .plan-card .plan-title { font-size: 1.01rem; font-weight: 700; margin-bottom: 0.25rem; }
        .plan-card .plan-price { font-size: 1.25rem; font-weight: 800; color: var(--accent); margin-bottom: 0.3rem; }
        .plan-card .plan-price span { font-size: 0.9rem; font-weight: 500; color: var(--gray); }
        .guarantee {
            text-align: center;
            font-weight: 600;
            color: var(--gray);
            margin-bottom: 1.1rem;
        }
        .guarantee-bar {
            animation-delay: 0.4s; 
            background: linear-gradient(90deg, #25D366 60%, #075E54 100%); 
            color: #fff; 
            border-radius: 10px; 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            padding: 0.45rem 0.7rem; 
            font-size: 0.91rem; 
            font-weight: 600; 
            margin-bottom: 1.1rem; 
            gap: 0.5rem; 
            box-shadow: 0 2px 12px rgba(7,94,84,0.08);
        }
        .details-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2.5rem;
        }
        .details-column h3 {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.8rem;
        }
        .details-list {
            list-style: none;
        }
        .details-list li {
            display: flex;
            align-items: center;
            margin-bottom: 0.6rem;
            font-size: 0.95rem;
        }
        .details-list i {
            color: var(--primary);
            margin-right: 0.6rem;
        }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">06</div>
            </div>
        </div>
        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                <p class="subtitle" data-translate-key="slide_title">Business Model</p>
                <h1 class="title" data-translate-key="main_heading">Simple, Transparent & Scalable</h1>
            </div>
            <div class="pricing-grid" style="animation-delay: 0.3s; display: flex; justify-content: center; gap: 1.5rem; margin-bottom: 1.2rem; flex-wrap: wrap;">
                <div class="plan-card fancy-card" style="background: linear-gradient(135deg, #e0ffe7 60%, #d6f5ff 100%);">
                    <div class="plan-icon" style="background: #25D366; color: #fff;"><i class="fas fa-leaf"></i></div>
                    <h3 class="plan-title" data-translate-key="basic_plan">Basic Plan</h3>
                    <div class="plan-price">₹1500<span data-translate-key="per_year">/year</span></div>
                </div>
                <div class="plan-card fancy-card" style="background: linear-gradient(135deg, #fffbe7 60%, #ffe6e6 100%);">
                    <div class="plan-icon" style="background: #fdc500; color: #fff;"><i class="fas fa-star"></i></div>
                    <h3 class="plan-title" data-translate-key="standard_plan">Standard Plan</h3>
                    <div class="plan-price">₹2000<span data-translate-key="per_year">/year</span></div>
                </div>
                <div class="plan-card fancy-card" style="background: linear-gradient(135deg, #e6e6ff 60%, #ffe6fa 100%);">
                    <div class="plan-icon" style="background: #6F42C1; color: #fff;"><i class="fas fa-crown"></i></div>
                    <h3 class="plan-title" data-translate-key="premium_plan">Premium Plan</h3>
                    <div class="plan-price">₹3000<span data-translate-key="per_year">/year</span></div>
                </div>
            </div>
            <div class="guarantee-bar" style="animation-delay: 0.4s; background: linear-gradient(90deg, #25D366 60%, #075E54 100%); color: #fff; border-radius: 10px; display: flex; align-items: center; justify-content: center; padding: 0.45rem 0.7rem; font-size: 0.91rem; font-weight: 600; margin-bottom: 1.1rem; gap: 0.5rem; box-shadow: 0 2px 12px rgba(7,94,84,0.08);">
                <span><i class="fas fa-shield-alt"></i></span>
                <span data-translate-key="guarantee_text">No Setup Fee | No Hidden Charges | No Commission on Sales</span>
            </div>
            <div class="model-cards-grid" style="animation-delay: 0.5s; display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1.8rem; align-items: start;">
                <div class="model-card">
                    <h3 class="model-title" style="color: #075E54; margin-bottom: 1rem; display: flex; align-items: center; gap: 0.6rem;"><i class="fas fa-wallet" style="color: #25D366;"></i> <span data-translate-key="whamart_earns_title">How Whamart Earns</span></h3>
                    <div class="model-points">
                        <div class="model-point" style="background: #e0ffe7; color: #075E54;"><i class="fas fa-check-circle"></i> <span data-translate-key="earns_1">One-time yearly subscription</span></div>
                        <div class="model-point" style="background: #e6f5ff; color: #075E54;"><i class="fas fa-coins"></i> <span data-translate-key="earns_2">100% revenue from vendor plans</span></div>
                        <div class="model-point" style="background: #fffbe7; color: #075E54;"><i class="fas fa-ban"></i> <span data-translate-key="earns_3">No transaction fees or deductions</span></div>
                        <div class="model-point" style="background: #ffe6fa; color: #075E54;"><i class="fas fa-sync-alt"></i> <span data-translate-key="earns_4">Predictable, recurring income model</span></div>
                    </div>
                </div>
                <div class="model-card">
                    <h3 class="model-title" style="color: #25D366; margin-bottom: 1.2rem; display: flex; align-items: center; gap: 0.6rem;"><i class="fas fa-rocket"></i> <span data-translate-key="vendor_potential_title">Vendor Earning Potential</span></h3>
                    <div class="model-points">
                        <div class="model-point" style="background: #e6e6ff; color: #6F42C1;"><i class="fas fa-robot"></i> <span data-translate-key="potential_1">24x7 automated selling via chat</span></div>
                        <div class="model-point" style="background: #e0ffe7; color: #25D366;"><i class="fas fa-chart-line"></i> <span data-translate-key="potential_2">Daily earning potential: ₹500 – ₹3500</span></div>
                        <div class="model-point" style="background: #fffbe7; color: #fdc500;"><i class="fas fa-calendar-alt"></i> <span data-translate-key="potential_3">Monthly potential: ₹15,000 – ₹1,00,000</span></div>
                        <div class="model-point" style="background: #ffe6fa; color: #C2185B;"><i class="fas fa-undo-alt"></i> <span data-translate-key="potential_4">Investment pays back in just 1-2 days</span></div>
                    </div>
                </div>
            </div>
        </div>

        <style>
        /* Extra styles for redesign */
        .fancy-card {
            box-shadow: 0 4px 18px rgba(7,94,84,0.07), 0 1.5px 4px rgba(7,94,84,0.07);
            border: none;
            position: relative;
            min-width: 170px;
            flex: 1 1 170px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.4rem;
            padding: 1.1rem 0.7rem 0.9rem 0.7rem;
        }
        .plan-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.15rem;
            margin-bottom: 0.3rem;
            box-shadow: 0 2px 8px rgba(7,94,84,0.07);
        }
        .model-cards-grid {
            width: 100%;
        }
        .model-card {
            background: var(--light-gray);
            border-radius: 12px;
            padding: 1rem 0.7rem 0.7rem 0.7rem;
            box-shadow: 0 2px 8px rgba(7,94,84,0.05);
            min-width: 0;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.3rem;
        }
        .model-title {
            font-size: 1.02rem;
            font-weight: 700;
        }
        .model-points {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            width: 100%;
        }
        .model-point {
            display: flex;
            align-items: center;
            gap: 0.45rem;
            font-size: 0.92rem;
            font-weight: 500;
            padding: 0.38rem 0.65rem;
            border-radius: 7px;
            box-shadow: 0 1px 4px rgba(7,94,84,0.07);
        }
        @media (max-width: 900px) {
            .pricing-grid, .model-cards-grid {
                flex-direction: column !important;
                grid-template-columns: 1fr !important;
                gap: 0.7rem !important;
            }
            .main-content {
                padding: 0.7rem 0.2rem;
            }
        }
        </style>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
        function nextSlide() {
            window.location.href = 'slide7.html';
        }
        function previousSlide() {
            window.location.href = 'slide5.html';
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Business Model",
                main_heading: "Simple, Transparent & Scalable",
                basic_plan: "Basic Plan",
                standard_plan: "Standard Plan",
                premium_plan: "Premium Plan",
                per_year: "/year",
                guarantee_text: "No Setup Fee | No Hidden Charges | No Commission on Sales",
                whamart_earns_title: "How Whamart Earns",
                earns_1: "One-time yearly subscription",
                earns_2: "100% revenue from vendor plans",
                earns_3: "No transaction fees or deductions",
                earns_4: "Predictable, recurring income model",
                vendor_potential_title: "Vendor Earning Potential",
                potential_1: "24x7 automated selling via chat",
                potential_2: "Daily earning potential: ₹500 – ₹3500",
                potential_3: "Monthly potential: ₹15,000 – ₹1,00,000",
                potential_4: "Investment pays back in just 1-2 days"
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                slide_title: "बिज़नेस मॉडल",
                main_heading: "आसान, पारदर्शी और बढ़ने योग्य",
                basic_plan: "बेसिक प्लान",
                standard_plan: "स्टैंडर्ड प्लान",
                premium_plan: "प्रीमियम प्लान",
                per_year: "/साल",
                guarantee_text: "कोई सेटअप फीस नहीं | कोई छुपा चार्ज नहीं | सेल्स पर कोई कमीशन नहीं",
                whamart_earns_title: "व्हामार्ट कैसे कमाता है",
                earns_1: "सालाना एक बार की सब्सक्रिप्शन",
                earns_2: "वेंडर प्लान्स से 100% कमाई",
                earns_3: "कोई ट्रांजैक्शन फीस या कटौती नहीं",
                earns_4: "पक्का, बार-बार आने वाली इनकम",
                vendor_potential_title: "वेंडर की कमाई का मौका",
                potential_1: "चैट से 24x7 ऑटोमैटिक सेलिंग",
                potential_2: "रोज़ की कमाई: ₹500 – ₹3500",
                potential_3: "महीने की कमाई: ₹15,000 – ₹1,00,000",
                potential_4: "इन्वेस्टमेंट सिर्फ 1-2 दिन में वापस"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                slide_title: "બિઝનેસ મોડેલ",
                main_heading: "સરળ, પારદર્શક અને વધતી",
                basic_plan: "બેસિક પ્લાન",
                standard_plan: "સ્ટાન્ડર્ડ પ્લાન",
                premium_plan: "પ્રીમિયમ પ્લાન",
                per_year: "/વર્ષ",
                guarantee_text: "કોઈ સેટઅપ ફી નહીં | કોઈ છુપાયેલ ચાર્જ નહીં | વેચાણ પર કોઈ કમિશન નહીં",
                whamart_earns_title: "વ્હામાર્ટ કેવી રીતે કમાય છે",
                earns_1: "વર્ષે એકવાર સબ્સ્ક્રિપ્શન",
                earns_2: "વેન્ડર પ્લાન્સમાંથી 100% કમાણી",
                earns_3: "કોઈ ટ્રાન્ઝેક્શન ફી કે કપાત નહીં",
                earns_4: "પક્કી, વારંવાર આવતી આવક",
                vendor_potential_title: "વેન્ડર કમાણીની તક",
                potential_1: "ચેટથી 24x7 ઓટોમેટિક વેચાણ",
                potential_2: "રોજની કમાણી: ₹500 – ₹3500",
                potential_3: "માસિક કમાણી: ₹15,000 – ₹1,00,000",
                potential_4: "ઇન્વેસ્ટમેન્ટ માત્ર 1-2 દિવસમાં પાછું"
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>