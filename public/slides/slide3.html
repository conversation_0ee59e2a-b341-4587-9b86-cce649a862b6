<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Our Solution</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            font-size: 16px;
        }

        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }

        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }

        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }

        .sidebar-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            opacity: 0.9;
        }

        .sidebar-footer .slide-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.5;
        }

        .main-content {
            flex-grow: 1;
            padding: 2rem 3rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .header {
            margin-bottom: 2rem;
        }

        .header .subtitle {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--primary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .header h1 {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            line-height: 1.2;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            flex-grow: 1;
        }

        .feature-column h2 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--dark);
        }

        .feature-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .feature-icon {
            flex-shrink: 0;
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background-color: var(--primary);
            color: var(--white);
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.2rem;
        }

        .feature-details h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .feature-details p {
            font-size: 0.95rem;
            color: var(--gray);
            line-height: 1.5;
        }

        /* Solution cards (slide 3 modern look) */
        .solution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            flex-grow: 1;
        }
        .solution-card {
            background-color: var(--light-gray);
            border-radius: 16px;
            padding: 1.5rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .solution-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.07);
        }
        .solution-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 1.5rem;
        }
        .solution-icon.c1 { background-color: #E9F7EF; color: var(--primary); }
        .solution-icon.c2 { background-color: #E6F3FF; color: #007BFF; }
        .solution-icon.c3 { background-color: #FFF4E6; color: #FD7E14; }
        .solution-icon.c4 { background-color: #FEEEEE; color: #DC3545; }
        .solution-icon.c5 { background-color: #F3E8FF; color: #6F42C1; }
        .solution-title {
            font-size: 1.1rem;
            font-weight: 600;
        }
        .solution-text {
            font-size: 0.9rem;
            color: var(--gray);
            line-height: 1.5;
        }

        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
            z-index: 1000;
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }

        .nav-btn:disabled {
            background-color: var(--gray);
            cursor: not-allowed;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .slide-container { flex-direction: column; height: 100vh; width: 100vw; margin: 0; border-radius: 0; max-height: none; }
            .sidebar { width: 100%; height: auto; flex-direction: row; align-items: center; padding: 1rem 2rem; }
            .sidebar-footer { display: none; }
            .main-content { padding: 2rem; }
            .header h1 { font-size: 2.5rem; }
            .navigation { bottom: 1.5rem; right: 1.5rem; }
        }

        @media (max-width: 768px) {
            .features-grid { grid-template-columns: 1fr; gap: 1rem; }
            .main-content { padding: 1.5rem; }
            .header h1 { font-size: 2.2rem; }
        }

        /* Animations */
        @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
        body { animation: fadeIn 0.5s ease-in-out; }

        @keyframes slideInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .main-content > * { animation: slideInUp 0.6s ease-out forwards; opacity: 0; }
        .feature-item { animation: slideInUp 0.6s ease-out forwards; opacity: 0; }

    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">03</div>
            </div>
        </div>

        <div class="main-content">
            <div class="header" style="animation-delay: 0.1s;">
                <p class="subtitle" data-translate-key="slide_title">Our Solution</p>
                <h1 data-translate-key="main_heading">A Comprehensive Digital Toolkit</h1>
            </div>

            <div class="solution-grid">
                <div class="solution-card" style="animation-delay: 0.1s;">
                    <div class="solution-icon c1"><i class="fas fa-store"></i></div>
                    <h3 class="solution-title" data-translate-key="feature1_title">Digital Storefront</h3>
                    <p class="solution-text" data-translate-key="feature1_text">Create a professional digital store on a unique Whamart link in under 30 seconds.</p>
                </div>
                <div class="solution-card" style="animation-delay: 0.2s;">
                    <div class="solution-icon c2"><i class="fas fa-book-open"></i></div>
                    <h3 class="solution-title" data-translate-key="feature2_title">Product Catalog</h3>
                    <p class="solution-text" data-translate-key="feature2_text">Easily manage products with pricing, images, and detailed descriptions.</p>
                </div>
                <div class="solution-card" style="animation-delay: 0.3s;">
                    <div class="solution-icon c3"><i class="fas fa-file-pdf"></i></div>
                    <h3 class="solution-title" data-translate-key="feature3_title">PDF Bill Generation</h3>
                    <p class="solution-text" data-translate-key="feature3_text">Automatically generate and send professional PDF bills to every customer.</p>
                </div>
                <div class="solution-card" style="animation-delay: 0.4s;">
                    <div class="solution-icon c4"><i class="fas fa-check-circle"></i></div>
                    <h3 class="solution-title" data-translate-key="benefit1_title">Verified Blue Tick</h3>
                    <p class="solution-text" data-translate-key="benefit1_text">Gain credibility and trust with a verified blue tick for your store.</p>
                </div>
                <div class="solution-card" style="animation-delay: 0.5s;">
                    <div class="solution-icon c5"><i class="fas fa-robot"></i></div>
                    <h3 class="solution-title" data-translate-key="benefit2_title">24x7 Automation</h3>
                    <p class="solution-text" data-translate-key="benefit2_text">Your store runs on autopilot, even when you're offline.</p>
                </div>
                <div class="solution-card" style="animation-delay: 0.6s;">
                    <div class="solution-icon c2"><i class="fas fa-rupee-sign"></i></div>
                    <h3 class="solution-title" data-translate-key="benefit3_title">Direct UPI Payments</h3>
                    <p class="solution-text" data-translate-key="benefit3_text">Accept payments from GPay, PhonePe, and Paytm with zero gateway fees.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>

    <script>
        function nextSlide() {
            window.location.href = 'slide4.html';
        }
        
        function previousSlide() {
            window.location.href = 'slide2.html';
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Our Solution",
                main_heading: "A Comprehensive Digital Toolkit",
                core_features_title: "Core Features",
                feature1_title: "Digital Storefront",
                feature1_text: "Create a professional digital store on a unique Whamart link in under 30 seconds.",
                feature2_title: "Product Catalog",
                feature2_text: "Easily manage products with pricing, images, and detailed descriptions.",
                feature3_title: "PDF Bill Generation",
                feature3_text: "Automatically generate and send professional PDF bills to every customer.",
                advanced_benefits_title: "Advanced Benefits",
                benefit1_title: "Verified Blue Tick",
                benefit1_text: "Gain credibility and trust with a verified blue tick for your store.",
                benefit2_title: "24x7 Automation",
                benefit2_text: "Your store runs on autopilot, even when you're offline.",
                benefit3_title: "Direct UPI Payments",
                benefit3_text: "Accept payments from GPay, PhonePe, and Paytm with zero gateway fees."
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                slide_title: "हमारा सॉल्यूशन",
                main_heading: "एक कम्पलीट डिजिटल टूलकिट",
                core_features_title: "मुख्य फीचर्स",
                feature1_title: "डिजिटल स्टोरफ्रंट",
                feature1_text: "30 सेकंड से कम में एक यूनिक Whamart लिंक पर प्रोफेशनल डिजिटल स्टोर बनाएं।",
                feature2_title: "प्रोडक्ट कैटलॉग",
                feature2_text: "प्राइस, फोटो और डिटेल्स के साथ प्रोडक्ट्स को आसानी से मैनेज करें।",
                feature3_title: "PDF बिल जनरेशन",
                feature3_text: "हर कस्टमर को ऑटोमेटिकली प्रोफेशनल PDF बिल बनाएं और भेजें।",
                advanced_benefits_title: "एडवांस्ड फायदे",
                benefit1_title: "वेरिफाइड ब्लू टिक",
                benefit1_text: "अपने स्टोर के लिए वेरिफाइड ब्लू टिक से भरोसा और क्रेडिबिलिटी पाएं।",
                benefit2_title: "24x7 ऑटोमेशन",
                benefit2_text: "जब आप ऑफलाइन हों तब भी आपका स्टोर ऑटोपायलट पर चलता है।",
                benefit3_title: "डायरेक्ट UPI पेमेंट",
                benefit3_text: "जीरो गेटवे फीस में GPay, PhonePe और Paytm से पेमेंट लें।"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                slide_title: "અમારું સોલ્યુશન",
                main_heading: "એક સંપૂર્ણ ડિજિટલ ટૂલકિટ",
                core_features_title: "મુખ્ય ફીચર્સ",
                feature1_title: "ડિજિટલ સ્ટોરફ્રન્ટ",
                feature1_text: "30 સેકન્ડથી ઓછા સમયમાં યુનિક Whamart લિંક પર પ્રોફેશનલ ડિજિટલ સ્ટોર બનાવો.",
                feature2_title: "પ્રોડક્ટ કેટલોગ",
                feature2_text: "પ્રાઈસ, ફોટો અને વિગત સાથે પ્રોડક્ટ્સ સરળતાથી મેનેજ કરો.",
                feature3_title: "PDF બિલ જનરેશન",
                feature3_text: "દરેક ગ્રાહક માટે આપમેળે પ્રોફેશનલ PDF બિલ બનાવો અને મોકલો.",
                advanced_benefits_title: "એડવાન્સ્ડ લાભો",
                benefit1_title: "વેરિફાઇડ બ્લુ ટિક",
                benefit1_text: "તમારા સ્ટોર માટે વેરિફાઇડ બ્લુ ટિકથી વિશ્વાસ અને ક્રેડિબિલિટી મેળવો.",
                benefit2_title: "24x7 ઓટોમેશન",
                benefit2_text: "જ્યારે તમે ઑફલાઇન હોવ ત્યારે પણ તમારો સ્ટોર ઓટોપાયલટ પર ચાલે છે.",
                benefit3_title: "ડાયરેક્ટ UPI પેમેન્ટ",
                benefit3_text: "જીરો ગેટવે ફી સાથે GPay, PhonePe અને Paytm થી પેમેન્ટ લો."
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>
