<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title data-translate-key="slide11_title">Whamart - Slide 11: Thank You & Contact</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            text-align: center;
            flex-shrink: 0;
        }
                .sidebar-header img { height: 40px; width: auto; object-fit: contain; margin-bottom: 1rem; }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .thank-you-icon { font-size: 3rem; opacity: 0.8; }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .header .title {
            font-size: 3rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 1rem;
        }
        .header .subtitle {
            font-size: 1.25rem;
            color: var(--gray);
            margin-bottom: 2.5rem;
        }
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: flex-start;
        }
        .section h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--accent);
        }
        .summary-list li, .contact-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 1.25rem;
            font-size: 1rem;
        }
        .summary-list i, .contact-list i {
            color: var(--primary);
            margin-right: 1rem;
            font-size: 1.2rem;
            margin-top: 0.2rem;
        }
        .contact-list a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 600;
            transition: opacity 0.3s ease;
        }
        .contact-list a:hover { opacity: 0.8; }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover:not(:disabled) {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .nav-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="thank-you-icon"><i class="fas fa-hands-helping"></i></div>
            </div>
        </div>
        <div class="main-content final-slide-bg">
    <div class="final-header" style="animation-delay: 0.2s;">
        <i class="fas fa-hands-clapping final-thank-icon"></i>
        <h1 class="final-title" data-translate-key="thank_you">Thank You</h1>
        <p class="final-subtitle" data-translate-key="subtitle">Let's build the future of local commerce, together.</p>
    </div>
    <div class="final-cards-row" style="animation-delay: 0.4s;">
        <div class="final-card glass summary-glass">
            <h3 data-translate-key="final_summary">Final Summary</h3>
            <ul class="summary-list">
                <li><span class="final-icon" style="background:linear-gradient(135deg,#25D366,#128C7E);"><i class="fas fa-bullseye"></i></span><p data-translate-key="summary_ask"><strong>Ask:</strong> ₹10L for customer acquisition, infra, and marketing.</p></li>
                <li><span class="final-icon" style="background:linear-gradient(135deg,#fdc500,#ff6f61);"><i class="fas fa-door-open"></i></span><p data-translate-key="summary_entry"><strong>Entry:</strong> Join with ₹5L or more.</p></li>
                <li><span class="final-icon" style="background:linear-gradient(135deg,#6F42C1,#34B7F1);"><i class="fas fa-chart-line"></i></span><p data-translate-key="summary_return"><strong>Return:</strong> Up to 40% ROI in 6 months with transparent reporting.</p></li>
            </ul>
        </div>
        <div class="final-card glass contact-glass">
            <h3 data-translate-key="get_in_touch">Get in Touch</h3>
            <ul class="contact-list">
                <li><span class="final-icon" style="background:linear-gradient(135deg,#34B7F1,#128C7E);"><i class="fas fa-envelope"></i></span><p><a href="mailto:<EMAIL>"><EMAIL></a></p></li>
                <li><span class="final-icon" style="background:linear-gradient(135deg,#fdc500,#25D366);"><i class="fas fa-phone"></i></span><p><a href="tel:+919023276063">+91 9023276063</a></p></li>
                <li><span class="final-icon" style="background:linear-gradient(135deg,#6F42C1,#fdc500);"><i class="fas fa-globe"></i></span><p><a href="#" target="_blank">whamart.shop/demo</a></p></li>
            </ul>
        </div>
    </div>
</div>
<style>
.final-slide-bg {
    background: linear-gradient(120deg,#f7f9fa 60%,#e0ffe7 100%);
    min-height: 100%;
    border-radius: 0 0 24px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
    padding-bottom: 1.5rem;
}
.final-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 2.2rem;
}
.final-thank-icon {
    font-size: 3.3rem;
    color: #25D366;
    margin-bottom: 0.7rem;
    animation: popIn 0.7s cubic-bezier(.42,1.6,.58,1) 0.1s both;
}
@keyframes popIn {
    0% { transform: scale(0.7); opacity: 0; }
    80% { transform: scale(1.15); opacity: 1; }
    100% { transform: scale(1); }
}
.final-title {
    font-size: 2.8rem;
    font-weight: 900;
    color: #212529;
    letter-spacing: 1px;
    margin-bottom: 0.3rem;
}
.final-subtitle {
    font-size: 1.23rem;
    color: #128C7E;
    margin-bottom: 1.2rem;
    font-weight: 600;
    text-align: center;
}
.final-cards-row {
    display: flex;
    gap: 2.6rem;
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
}
.final-card.glass {
    border-radius: 1.3rem;
    box-shadow: 0 4px 24px rgba(37,211,102,0.09);
    padding: 2.1rem 2.2rem 1.7rem 2.2rem;
    min-width: 320px;
    max-width: 410px;
    background: rgba(255,255,255,0.93);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    margin-bottom: 1.2rem;
    transition: box-shadow 0.18s;
}
.final-card.glass:hover {
    box-shadow: 0 8px 32px rgba(37,211,102,0.13);
}
.summary-glass {
    background: linear-gradient(120deg,#e0ffe7 60%,#d6f5ff 100%);
}
.contact-glass {
    background: linear-gradient(120deg,#fffbe7 60%,#ffe6e6 100%);
}
.final-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 1.2rem;
    margin-right: 1rem;
    margin-top: 0.15rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.07);
    flex-shrink: 0;
}
.summary-list li, .contact-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.1rem;
    font-size: 1.05rem;
}
.summary-list p, .contact-list p {
    margin: 0;
    font-size: 1.05rem;
    color: #212529;
    font-weight: 500;
}
@media (max-width: 900px) {
    .final-cards-row { flex-direction: column; gap: 1.2rem; align-items: center; }
    .final-card.glass { min-width: 90vw; max-width: 98vw; padding: 1.2rem 0.7rem; }
}
</style>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide" disabled>
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
                const translations = {
            en: {
                slide11_title: "Whamart - Slide 11: Thank You & Contact",
                investor_deck: "Investor Deck",
                thank_you: "Thank You",
                subtitle: "Let's build the future of local commerce, together.",
                final_summary: "Final Summary",
                summary_ask: "<strong>Ask:</strong> ₹10L for customer acquisition, infra, and marketing.",
                summary_entry: "<strong>Entry:</strong> Join with ₹5L or more.",
                summary_return: "<strong>Return:</strong> Up to 40% ROI in 6 months with transparent reporting.",
                get_in_touch: "Get in Touch",
                lang_en: "English",
                lang_hi: "Hindi",
                lang_gu: "Gujarati"
            },
            hi: {
                slide11_title: "व्हामार्ट - स्लाइड 11: थैंक यू और संपर्क",
                investor_deck: "निवेशक डेक",
                thank_you: "थैंक यू",
                subtitle: "चलो, साथ मिलकर लोकल बिजनेस का फ्यूचर बनाएं।",
                final_summary: "फाइनल सारांश",
                summary_ask: "<strong>मांग:</strong> कस्टमर लाने, इन्फ्रा और मार्केटिंग के लिए ₹10 लाख।",
                summary_entry: "<strong>एंट्री:</strong> ₹5 लाख या ज्यादा के साथ जुड़ें।",
                summary_return: "<strong>रिटर्न:</strong> 6 महीने में 40% तक ROI, सबकुछ ट्रांसपेरेंट।",
                get_in_touch: "संपर्क करें",
                lang_en: "अंग्रेजी",
                lang_hi: "हिंदी",
                lang_gu: "गुजराती"
            },
            gu: {
                slide11_title: "વ્હામાર્ટ - સ્લાઇડ 11: થેંક યુ અને સંપર્ક",
                investor_deck: "રોકાણકાર ડેક",
                thank_you: "થેંક યુ",
                subtitle: "ચાલો, સાથે મળીને લોકલ બિઝનેસનું ભવિષ્ય બનાવીએ.",
                final_summary: "ફાઈનલ સારાંશ",
                summary_ask: "<strong>માંગ:</strong> ગ્રાહકો લાવવા, ઇન્ફ્રા અને માર્કેટિંગ માટે ₹10 લાખ.",
                summary_entry: "<strong>એન્ટ્રી:</strong> ₹5 લાખ કે વધુ સાથે જોડાઓ.",
                summary_return: "<strong>રિટર્ન:</strong> 6 મહિનામાં 40% સુધી ROI, બધું ટ્રાન્સપેરન્ટ.",
                get_in_touch: "સંપર્ક કરો",
                lang_en: "અંગ્રેજી",
                lang_hi: "હિન્દી",
                lang_gu: "ગુજરાતી"
            }
        };

        function getLanguageFromURL() {
            const params = new URLSearchParams(window.location.search);
            return params.get('lang') || localStorage.getItem('preferredLanguage') || 'en';
        }

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            updateText(lang);
        }

        function updateText(lang) {
            document.querySelectorAll('[data-translate-key]').forEach(element => {
                const key = element.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    element.innerHTML = translations[lang][key];
                }
            });
        }

        function nextSlide() {
            // Last slide, no action needed.
        }

        function previousSlide() {
            const lang = getLanguageFromURL();
            window.location.href = `slide11.html?lang=${lang}`;
        }

        document.addEventListener('DOMContentLoaded', () => {
            const initialLang = getLanguageFromURL();
            setLanguage(initialLang);
        });
    </script>
</body>
</html>
