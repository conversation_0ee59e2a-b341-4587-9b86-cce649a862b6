<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Whamart - Slide 5: Competition</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html {
            font-size: 16px;
        }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img {
            height: 40px;
            width: auto;
            object-fit: contain;
            margin-bottom: 1rem;
        }
        .sidebar-header h3 {
            font-size: 1.2rem;
            font-weight: 600;
            opacity: 0.9;
        }
        .sidebar-footer .slide-number {
            font-size: 3rem;
            font-weight: 800;
            opacity: 0.5;
        }
        .main-content {
            flex-grow: 1;
            padding: 3rem 4rem;
            overflow-y: auto;
        }
        .header .subtitle {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }
        .header .title {
            font-size: 2.8rem;
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 2rem;
        }
        .comparison-table-modern {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    margin: 0 auto 0 auto;
    padding: 0 2rem;
    animation: fadeIn 0.7s cubic-bezier(.4,2,.6,1) both;
}
.comparison-table-modern table {
    width: 100%;
    max-width: 1100px;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--white);
    border-radius: 18px;
    overflow: hidden;
    box-shadow: 0 6px 24px rgba(7,94,84,0.06);
    table-layout: fixed;
}
.comparison-table-modern th, .comparison-table-modern td {
    padding: 1.05rem 1.1rem;
    font-size: 1.04rem;
    text-align: left;
    vertical-align: middle;
}
.comparison-table-modern th:first-child, .comparison-table-modern td:first-child {
    min-width: 180px;
    max-width: 230px;
    width: 200px;
    white-space: nowrap;
}
.comparison-table-modern th {
    background: linear-gradient(90deg, var(--primary), var(--accent));
    color: #fff;
    font-size: 1.05rem;
    font-weight: 700;
    border: none;
}
.comparison-table-modern td {
    background: var(--light-gray);
    color: var(--dark);
    border-bottom: 1.5px solid #e9ecef;
    overflow-wrap: break-word;
}
.comparison-table-modern tr:last-child td {
    border-bottom: none;
}
.comparison-table-modern .comp-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 0.7rem;
    vertical-align: middle;
    box-shadow: 0 2px 8px rgba(7,94,84,0.09);
}
.comparison-table-modern .c1 .comp-icon { background: linear-gradient(135deg, #96f7c4, var(--primary)); color: #075E54; }
.comparison-table-modern .c2 .comp-icon { background: linear-gradient(135deg, #fff3cd, #fd7e14); color: #fd7e14; }
.comparison-table-modern .c3 .comp-icon { background: linear-gradient(135deg, #e0c3fc, #6F42C1); color: #6F42C1; }
.comparison-table-modern .c4 .comp-icon { background: linear-gradient(135deg, #d1e7dd, #0d6efd); color: #0d6efd; }
.comparison-table-modern .c5 .comp-icon { background: linear-gradient(135deg, #d4f8e8, #25D366); color: #25D366; }
.comparison-table-modern .competitor-name {
    font-weight: 600;
    font-size: 1.08rem;
    color: var(--dark);
}
.comparison-table-modern .whamart-edge {
    color: var(--primary);
    font-weight: 700;
    display: flex;
    align-items: center;
}
.comparison-table-modern .whamart-edge i {
    margin-right: 0.4rem;
    color: var(--accent);
}
@media (max-width: 900px) {
    .header .title { font-size: 2rem; }
    .comparison-table-modern th, .comparison-table-modern td { font-size: 0.98rem; padding: 0.8rem 0.5rem; }
}
@media (max-width: 600px) {
    .slide-container { padding: 0.5rem; }
    .header { padding: 1rem 0.5rem 0.5rem 0.5rem; }
    .comparison-table-modern { padding: 0; }
    .comparison-table-modern table { font-size: 0.92rem; }
}

        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.97); }
            to { opacity: 1; transform: scale(1); }
        }
        .competitor-name {
            font-weight: 600;
        }
        .whamart-edge {
            font-weight: 600;
            color: var(--accent);
        }
        .whamart-edge i {
            margin-right: 0.5rem;
            color: var(--primary);
        }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover {
            background-color: var(--primary);
            transform: translateY(-3px);
        }
        .main-content > * {
            animation: slideUp 0.6s ease-out forwards;
            opacity: 0;
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">05</div>
            </div>
        </div>
        <div class="main-content">
            <div class="header" style="animation-delay: 0.2s;">
                <p class="subtitle" data-translate-key="slide_title">Competitive Landscape</p>
                <h1 class="title" data-translate-key="main_heading">How We Stand Apart</h1>
            </div>
            <div class="comparison-table-modern" style="animation-delay: 0.4s;">
    <table>
        <thead>
            <tr>
                <th data-translate-key="th_platform">Platform</th>
                <th data-translate-key="th_strengths">Strengths</th>
                <th data-translate-key="th_weaknesses">Weaknesses</th>
                <th data-translate-key="th_edge">Whamart's Edge</th>
            </tr>
        </thead>
        <tbody>
            <tr class="c1">
                <td><span class="comp-icon"><i class="fab fa-shopify"></i></span> <span class="competitor-name">Shopify</span></td>
                <td data-translate-key="shopify_strengths">Feature-rich, scalable</td>
                <td data-translate-key="shopify_weaknesses">Expensive, complex setup</td>
                <td class="whamart-edge"><i class="fas fa-check-circle"></i> <span data-translate-key="shopify_edge">Affordable & Simple</span></td>
            </tr>
            <tr class="c2">
                <td><span class="comp-icon"><i class="fas fa-utensils"></i></span> <span class="competitor-name">Swiggy Mini</span></td>
                <td data-translate-key="swiggy_strengths">Food-specific, discovery</td>
                <td data-translate-key="swiggy_weaknesses">Limited to food, high commission</td>
                <td class="whamart-edge"><i class="fas fa-check-circle"></i> <span data-translate-key="swiggy_edge">Multi-category Platform</span></td>
            </tr>
            <tr class="c3">
                <td><span class="comp-icon"><i class="fas fa-bolt"></i></span> <span class="competitor-name">Dukaan</span></td>
                <td data-translate-key="dukaan_strengths">Quick setup</td>
                <td data-translate-key="dukaan_weaknesses">Requires separate payment gateway</td>
                <td class="whamart-edge"><i class="fas fa-check-circle"></i> <span data-translate-key="dukaan_edge">Direct UPI, 0% Fee</span></td>
            </tr>
            <tr class="c4">
                <td><span class="comp-icon"><i class="fas fa-store"></i></span> <span class="competitor-name">Showroom</span></td>
                <td data-translate-key="showroom_strengths">Easy catalogs</td>
                <td data-translate-key="showroom_weaknesses">Lacks robust automation</td>
                <td class="whamart-edge"><i class="fas fa-check-circle"></i> <span data-translate-key="showroom_edge">24x7 Chat-based Selling</span></td>
            </tr>
            <tr class="c5">
                <td><span class="comp-icon"><i class="fab fa-whatsapp"></i></span> <span class="competitor-name">WhatsApp API</span></td>
                <td data-translate-key="whatsapp_strengths">Official, reliable</td>
                <td data-translate-key="whatsapp_weaknesses">Costly, lengthy approval</td>
                <td class="whamart-edge"><i class="fas fa-check-circle"></i> <span data-translate-key="whatsapp_edge">Instant Setup, No Approval</span></td>
            </tr>
        </tbody>
    </table>
</div>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide">
            <i class="fas fa-arrow-left"></i>
        </button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide">
            <i class="fas fa-arrow-right"></i>
        </button>
    </div>
    <script>
        function nextSlide() {
            window.location.href = 'slide6.html';
        }
        function previousSlide() {
            window.location.href = 'slide4.html';
        }

        const translations = {
            en: {
                investor_deck: "Investor Deck",
                slide_title: "Competitive Landscape",
                main_heading: "How We Stand Apart",
                th_platform: "Platform",
                th_strengths: "Strengths",
                th_weaknesses: "Weaknesses",
                th_edge: "Whamart's Edge",
                shopify_strengths: "Feature-rich, scalable",
                shopify_weaknesses: "Expensive, complex setup",
                shopify_edge: "Affordable & Simple",
                swiggy_strengths: "Food-specific, discovery",
                swiggy_weaknesses: "Limited to food, high commission",
                swiggy_edge: "Multi-category Platform",
                dukaan_strengths: "Quick setup",
                dukaan_weaknesses: "Requires separate payment gateway",
                dukaan_edge: "Direct UPI, 0% Fee",
                showroom_strengths: "Easy catalogs",
                showroom_weaknesses: "Lacks robust automation",
                showroom_edge: "24x7 Chat-based Selling",
                whatsapp_strengths: "Official, reliable",
                whatsapp_weaknesses: "Costly, lengthy approval",
                whatsapp_edge: "Instant Setup, No Approval"
            },
            hi: {
                investor_deck: "इन्वेस्टर डेक",
                slide_title: "कंपटीटिव लैंडस्केप",
                main_heading: "हम कैसे अलग हैं",
                th_platform: "प्लेटफॉर्म",
                th_strengths: "खासियतें",
                th_weaknesses: "कमज़ोरियाँ",
                th_edge: "Whamart की बढ़त",
                shopify_strengths: "फीचर-रिच, स्केलेबल",
                shopify_weaknesses: "महंगा, सेटअप मुश्किल",
                shopify_edge: "सस्ता और आसान",
                swiggy_strengths: "फूड-स्पेशल, डिस्कवरी",
                swiggy_weaknesses: "सिर्फ फूड, ज्यादा कमीशन",
                swiggy_edge: "मल्टी-कैटेगरी प्लेटफॉर्म",
                dukaan_strengths: "फास्ट सेटअप",
                dukaan_weaknesses: "अलग पेमेंट गेटवे चाहिए",
                dukaan_edge: "डायरेक्ट UPI, 0% फीस",
                showroom_strengths: "आसान कैटलॉग",
                showroom_weaknesses: "ऑटोमेशन की कमी",
                showroom_edge: "24x7 चैट बेस्ड सेलिंग",
                whatsapp_strengths: "ऑफिशियल, भरोसेमंद",
                whatsapp_weaknesses: "महंगा, लंबा अप्रूवल",
                whatsapp_edge: "इंस्टेंट सेटअप, कोई अप्रूवल नहीं"
            },
            gu: {
                investor_deck: "ઇન્વેસ્ટર ડેક",
                slide_title: "કમ્પિટીટીવ લેન્ડસ્કેપ",
                main_heading: "અમે કેમ અલગ છીએ",
                th_platform: "પ્લેટફોર્મ",
                th_strengths: "ખાસિયતો",
                th_weaknesses: "કમજોરીઓ",
                th_edge: "Whamartની એડવાન્ટેજ",
                shopify_strengths: "ફીચર-રિચ, સ્કેલેબલ",
                shopify_weaknesses: "મોંઘું, સેટઅપ મુશ્કેલ",
                shopify_edge: "સસ્તું અને સરળ",
                swiggy_strengths: "ફૂડ-સ્પેશિયલ, શોધ",
                swiggy_weaknesses: "માત્ર ફૂડ, વધુ કમિશન",
                swiggy_edge: "મલ્ટી-કેટેગરી પ્લેટફોર્મ",
                dukaan_strengths: "ઝડપી સેટઅપ",
                dukaan_weaknesses: "અલગ પેમેન્ટ ગેટવે જોઈએ",
                dukaan_edge: "ડાયરેક્ટ UPI, 0% ફી",
                showroom_strengths: "સરળ કેટલોગ",
                showroom_weaknesses: "ઓટોમેશનની અછત",
                showroom_edge: "24x7 ચેટ આધારિત વેચાણ",
                whatsapp_strengths: "ઓફિશિયલ, વિશ્વસનીય",
                whatsapp_weaknesses: "મોંઘું, લાંબો મંજૂરી સમય",
                whatsapp_edge: "ઇન્સ્ટન્ટ સેટઅપ, મંજૂરી નહીં"
            }
        };

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            document.documentElement.lang = lang;
            document.querySelectorAll('[data-translate-key]').forEach(el => {
                const key = el.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    el.textContent = translations[lang][key];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const preferredLanguage = localStorage.getItem('preferredLanguage') || 'en';
            setLanguage(preferredLanguage);
        });
    </script>
</body>
</html>