<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-translate-key="slide7_title">Whamart - Slide 7: Vendor Retention</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <style>
        :root {
            --primary: #25D366;
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F7F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --font-family: 'Poppins', sans-serif;
        }
        * { margin: 0; padding: 0; box-sizing: border-box; }
        html { font-size: 16px; }
        body {
            font-family: var(--font-family);
            background-color: var(--light-gray);
            color: var(--dark);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            overflow: hidden;
        }
        .slide-container {
            width: 95vw;
            height: 95vh;
            max-width: 1400px;
            max-height: 800px;
            background-color: var(--white);
            border-radius: 24px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }
        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, var(--accent), #054a41);
            color: var(--white);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            flex-shrink: 0;
        }
        .sidebar-header img { height: 40px; width: auto; object-fit: contain; margin-bottom: 1rem; }
        .sidebar-header h3 { font-size: 1.2rem; font-weight: 600; opacity: 0.9; }
        .sidebar-footer .slide-number { font-size: 3rem; font-weight: 800; opacity: 0.5; }
        .main-content {
            flex-grow: 1;
            padding: 2rem 3rem;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .header .title {
            font-size: 2.2rem; /* Adjusted for longer title */
            font-weight: 800;
            color: var(--dark);
            margin-bottom: 1.5rem;
            text-align: center;
        }
        .content-area {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }
        .content-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            flex-grow: 1;
            padding: 1rem 0;
        }
        .content-section {
            background-color: #F7F9FA;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e0e0e0;
        }
        .content-section h3 {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--accent);
            margin-bottom: 1rem;
        }
        .content-section ul {
            list-style: none;
            padding: 0;
        }
        .content-section li {
            font-size: 0.95rem;
            line-height: 1.7;
            margin-bottom: 0.75rem;
        }
        .quote-tagline {
            text-align: center;
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--dark);
            margin-top: auto;
            padding-top: 1.5rem;
            font-style: italic;
        }
        .navigation {
            position: fixed;
            bottom: 3rem;
            right: 4rem;
            display: flex;
            gap: 1rem;
        }
        .nav-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background-color: var(--dark);
            color: var(--white);
            font-size: 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease, transform 0.3s ease;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .nav-btn:hover { background-color: var(--primary); transform: translateY(-3px); }
    </style>
</head>
<body>
    <div class="slide-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <img src="../WhaMart_Logo.png" alt="Whamart Logo">
                <h3 data-translate-key="investor_deck">Investor Deck</h3>
            </div>
            <div class="sidebar-footer">
                <div class="slide-number">07</div>
            </div>
        </div>
        <div class="main-content">
            <div class="content-area">
                <div class="header">
                    <h1 class="title" data-translate-key="title">Why Vendors Stick With Us – 24x7 Usage = 100% Retention</h1>
                </div>
                <div class="content-sections">
                    <div class="content-section">
                        <h3 data-translate-key="top_section_title">Daily Use Behaviors</h3>
                        <ul>
                            <li data-translate-key="top_sec_item1">🔁 Customers chat daily for prices, orders, queries</li>
                            <li data-translate-key="top_sec_item2">📤 Vendor shares store link on WhatsApp, Instagram, SMS</li>
                            <li data-translate-key="top_sec_item3">📄 PDF bills are generated for every customer</li>
                            <li data-translate-key="top_sec_item4">📊 Dashboard shows order history, responses, revenue</li>
                        </ul>
                    </div>
                    <div class="content-section">
                        <h3 data-translate-key="middle_section_title">Retention Boosters</h3>
                        <ul>
                            <li data-translate-key="mid_sec_item1">✅ No app needed – works instantly in WhatsApp</li>
                            <li data-translate-key="mid_sec_item2">✅ UPI payments = real cash received, instantly</li>
                            <li data-translate-key="mid_sec_item3">✅ Verified badge builds customer trust</li>
                            <li data-translate-key="mid_sec_item4">✅ Sellers start earning from Day 1</li>
                        </ul>
                    </div>
                    <div class="content-section">
                        <h3 data-translate-key="bottom_section_title">Adoption Funnel</h3>
                        <ul>
                            <li data-translate-key="bot_sec_item1">1️⃣ Free Trial (7 Days)</li>
                            <li data-translate-key="bot_sec_item2">2️⃣ Onboarding with auto-setup</li>
                            <li data-translate-key="bot_sec_item3">3️⃣ Real sales & engagement in 2–3 days</li>
                            <li data-translate-key="bot_sec_item4">4️⃣ Paid conversion with no push needed</li>
                        </ul>
                    </div>
                </div>
                <p class="quote-tagline" data-translate-key="quote">“Whamart becomes a habit, not a tool.”</p>
            </div>
        </div>
    </div>
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()" aria-label="Previous Slide"><i class="fas fa-arrow-left"></i></button>
        <button class="nav-btn" onclick="nextSlide()" aria-label="Next Slide"><i class="fas fa-arrow-right"></i></button>
    </div>
    <script>
        const translations = {
            en: {
                slide7_title: "Whamart - Slide 7: Vendor Retention",
                investor_deck: "Investor Deck",
                title: "Why Vendors Stick With Us – 24x7 Usage = 100% Retention",
                top_section_title: "Daily Use Behaviors",
                top_sec_item1: "🔁 Customers chat daily for prices, orders, queries",
                top_sec_item2: "📤 Vendor shares store link on WhatsApp, Instagram, SMS",
                top_sec_item3: "📄 PDF bills are generated for every customer",
                top_sec_item4: "📊 Dashboard shows order history, responses, revenue",
                middle_section_title: "Retention Boosters",
                mid_sec_item1: "✅ No app needed – works instantly in WhatsApp",
                mid_sec_item2: "✅ UPI payments = real cash received, instantly",
                mid_sec_item3: "✅ Verified badge builds customer trust",
                mid_sec_item4: "✅ Sellers start earning from Day 1",
                bottom_section_title: "Adoption Funnel",
                bot_sec_item1: "1️⃣ Free Trial (7 Days)",
                bot_sec_item2: "2️⃣ Onboarding with auto-setup",
                bot_sec_item3: "3️⃣ Real sales & engagement in 2–3 days",
                bot_sec_item4: "4️⃣ Paid conversion with no push needed",
                quote: "“Whamart becomes a habit, not a tool.”"
            },
            hi: {
                slide7_title: "व्हामार्ट - स्लाइड 7: विक्रेता रिटेंशन",
                investor_deck: "इन्वेस्टर डेक",
                title: "विक्रेता हमारे साथ क्यों टिके रहते हैं – 24x7 इस्तेमाल = 100% रिटेंशन",
                top_section_title: "रोज़मर्रा के यूज़",
                top_sec_item1: "🔁 ग्राहक रोज़ प्राइस, ऑर्डर, सवाल के लिए चैट करते हैं",
                top_sec_item2: "📤 विक्रेता व्हाट्सएप, इंस्टा, SMS पर स्टोर लिंक शेयर करता है",
                top_sec_item3: "📄 हर ग्राहक को PDF बिल मिलता है",
                top_sec_item4: "📊 डैशबोर्ड में ऑर्डर हिस्ट्री, फीडबैक, इनकम दिखती है",
                middle_section_title: "रिटेंशन बूस्टर्स",
                mid_sec_item1: "✅ कोई ऐप नहीं चाहिए – व्हाट्सएप में तुरंत चलता है",
                mid_sec_item2: "✅ UPI पेमेंट = तुरंत असली पैसे मिलते हैं",
                mid_sec_item3: "✅ वेरिफाइड बैज से ग्राहक भरोसा करते हैं",
                mid_sec_item4: "✅ सेलर पहले दिन से कमाई शुरू करता है",
                bottom_section_title: "अपनाने की स्टेप्स",
                bot_sec_item1: "1️⃣ फ्री ट्रायल (7 दिन)",
                bot_sec_item2: "2️⃣ ऑटो-सेटअप के साथ ऑनबोर्डिंग",
                bot_sec_item3: "3️⃣ 2-3 दिन में असली सेल्स और एंगेजमेंट",
                bot_sec_item4: "4️⃣ बिना ज़ोर दिए पेड कन्वर्जन",
                quote: "“व्हामार्ट आदत बन जाता है, टूल नहीं।”"
            },
            gu: {
                slide7_title: "વ્હામાર્ટ - સ્લાઇડ 7: વિક્રેતા રિટેન્શન",
                investor_deck: "ઇન્વેસ્ટર ડેક",
                title: "વિક્રેતાઓ અમારી સાથે કેમ જોડાય રહે છે – 24x7 ઉપયોગ = 100% રિટેન્શન",
                top_section_title: "રોજિંદા ઉપયોગ",
                top_sec_item1: "🔁 ગ્રાહકો દરરોજ ભાવ, ઓર્ડર, પ્રશ્ન માટે ચેટ કરે છે",
                top_sec_item2: "📤 વિક્રેતા વોટ્સએપ, ઇન્સ્ટા, SMS પર સ્ટોર લિંક શેર કરે છે",
                top_sec_item3: "📄 દરેક ગ્રાહકને PDF બિલ મળે છે",
                top_sec_item4: "📊 ડેશબોર્ડમાં ઓર્ડર ઇતિહાસ, ફીડબેક, આવક દેખાય છે",
                middle_section_title: "રિટેન્શન બૂસ્ટર્સ",
                mid_sec_item1: "✅ કોઈ એપ્લિકેશન નહીં જોઈએ – વોટ્સએપમાં તરત ચાલે છે",
                mid_sec_item2: "✅ UPI પેમેન્ટ = તરત જ અસલી પૈસા મળે છે",
                mid_sec_item3: "✅ વેરિફાઇડ બેજથી ગ્રાહકો વિશ્વાસ કરે છે",
                mid_sec_item4: "✅ વેચનાર પહેલાથી જ કમાણી શરૂ કરે છે",
                bottom_section_title: "અપનાવાની સ્ટેપ્સ",
                bot_sec_item1: "1️⃣ ફ્રી ટ્રાયલ (7 દિવસ)",
                bot_sec_item2: "2️⃣ ઓટો-સેટઅપ સાથે ઓનબોર્ડિંગ",
                bot_sec_item3: "3️⃣ 2-3 દિવસમાં અસલી વેચાણ અને એંગેજમેન્ટ",
                bot_sec_item4: "4️⃣ દબાણ વિના પેઇડ રૂપાંતરણ",
                quote: "“વ્હામાર્ટ આદત બની જાય છે, ટૂલ નહીં.”"
            }
        };

        function getLanguageFromURL() {
            const params = new URLSearchParams(window.location.search);
            return params.get('lang') || localStorage.getItem('preferredLanguage') || 'en';
        }

        function setLanguage(lang) {
            localStorage.setItem('preferredLanguage', lang);
            updateText(lang);
        }

        function updateText(lang) {
            document.querySelectorAll('[data-translate-key]').forEach(element => {
                const key = element.getAttribute('data-translate-key');
                if (translations[lang] && translations[lang][key]) {
                    element.innerHTML = translations[lang][key];
                }
            });
        }

        function nextSlide() {
            const lang = getLanguageFromURL();
            window.location.href = `slide8.html?lang=${lang}`;
        }

        function previousSlide() {
            const lang = getLanguageFromURL();
            window.location.href = `slide6.html?lang=${lang}`;
        }

        document.addEventListener('DOMContentLoaded', () => {
            const initialLang = getLanguageFromURL();
            setLanguage(initialLang);
        });
    </script>
</body>
</html