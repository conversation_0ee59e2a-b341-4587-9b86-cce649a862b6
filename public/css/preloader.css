/* ===== GLOBAL PRELOADER - DUKKAN ===== */
/* Modern & Clean Design with Green Theme */

/* Preloader Container */
.dukkan-preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #ffffff;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: opacity 0.5s ease, visibility 0.5s ease;
    visibility: visible;
    opacity: 1;
}

/* Hidden State */
.dukkan-preloader.hidden {
    opacity: 0;
    visibility: hidden;
}

/* Preloader Content */
.preloader-content {
    text-align: center;
    max-width: 300px;
    padding: 2rem;
}

/* Animated Icon Container */
.preloader-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 2rem;
    position: relative;
    background: linear-gradient(135deg, #10b981, #34d399);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
    animation: preloader-bounce 2s ease-in-out infinite;
}

/* Store Icon */
.preloader-icon i {
    font-size: 2.5rem;
    color: #ffffff;
    animation: preloader-pulse 1.5s ease-in-out infinite;
}

/* Alternative: CSS Store Icon (if Font Awesome not available) */
.preloader-icon::before {
    content: '🏪';
    font-size: 2.5rem;
    animation: preloader-pulse 1.5s ease-in-out infinite;
}

/* Hide emoji if Font Awesome icon is present */
.preloader-icon i + .preloader-icon::before {
    display: none;
}

/* Tagline */
.preloader-tagline {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #064e3b;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
    animation: preloader-fadeIn 1s ease-in-out 0.5s both;
}

/* Loading Text */
.preloader-loading {
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    animation: preloader-fadeIn 1s ease-in-out 1s both;
}

/* Loading Dots Animation */
.loading-dots {
    display: inline-block;
    animation: preloader-dots 1.5s linear infinite;
}

/* Animations */
@keyframes preloader-bounce {
    0%, 100% {
        transform: translateY(0px) scale(1);
    }
    50% {
        transform: translateY(-10px) scale(1.05);
    }
}

@keyframes preloader-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes preloader-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes preloader-dots {
    0%, 20% {
        content: '';
    }
    40% {
        content: '.';
    }
    60% {
        content: '..';
    }
    80%, 100% {
        content: '...';
    }
}

/* Spinner Alternative */
.preloader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #10b981;
    border-radius: 50%;
    animation: preloader-spin 1s linear infinite;
    margin: 0 auto 2rem;
    display: none; /* Hidden by default, can be activated via JS */
}

@keyframes preloader-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Bar */
.preloader-progress {
    width: 100%;
    max-width: 200px;
    height: 4px;
    background: #e5e7eb;
    border-radius: 2px;
    margin: 1.5rem auto 0;
    overflow: hidden;
    position: relative;
}

.preloader-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #34d399);
    border-radius: 2px;
    width: 0%;
    animation: preloader-progress 2s ease-in-out infinite;
}

@keyframes preloader-progress {
    0% {
        width: 0%;
        transform: translateX(-100%);
    }
    50% {
        width: 100%;
        transform: translateX(0%);
    }
    100% {
        width: 100%;
        transform: translateX(100%);
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .preloader-content {
        padding: 1.5rem;
        max-width: 280px;
    }
    
    .preloader-icon {
        width: 70px;
        height: 70px;
        margin-bottom: 1.5rem;
    }
    
    .preloader-icon i,
    .preloader-icon::before {
        font-size: 2rem;
    }
    
    .preloader-tagline {
        font-size: 1.125rem;
        margin-bottom: 0.75rem;
    }
    
    .preloader-loading {
        font-size: 0.8rem;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .preloader-icon {
        box-shadow: 0 8px 32px rgba(16, 185, 129, 0.4);
    }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
    .dukkan-preloader {
        background: #1f2937;
    }
    
    .preloader-tagline {
        color: #ecfdf5;
    }
    
    .preloader-loading {
        color: #9ca3af;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .preloader-icon,
    .preloader-icon i,
    .preloader-icon::before,
    .preloader-tagline,
    .preloader-loading,
    .preloader-progress-bar {
        animation: none;
    }
    
    .dukkan-preloader {
        transition: none;
    }
}

/* Print Styles */
@media print {
    .dukkan-preloader {
        display: none !important;
    }
}
