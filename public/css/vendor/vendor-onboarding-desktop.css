/* ===== VENDOR ONBOARDING CSS - DESKTOP VERSION ===== */

/* ===== VARIABLES ===== */
:root {
    /* Colors */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    
    /* Gray Scale */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* UI Colors */
    --body-bg: #f8fafc;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --text: #1f2937;
    --text-muted: #6b7280;
    --text-light: #9ca3af;
    --success: #10b981;
    --danger: #ef4444;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Typography */
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;
    --font-4xl: 2.25rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    
    /* Borders */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition: all 0.2s ease;
}

/* ===== BASE STYLES ===== */
body {
    font-family: var(--font-sans);
    background-color: var(--body-bg);
    color: var(--text);
    margin: 0;
    padding: 0;
}

/* ===== CONTAINER ===== */
.onboarding-container {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-6);
}

/* ===== PROGRESS BAR ===== */
.progress-container {
    margin-bottom: var(--spacing-8);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-6);
}

.progress-fill {
    height: 100%;
    background: var(--primary);
    transition: width 0.5s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2);
    flex: 1;
    z-index: 1;
}

.step-number {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-full);
    background: var(--gray-200);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-base);
    transition: var(--transition);
}

.step.active .step-number {
    background: var(--primary);
    color: var(--white);
    box-shadow: 0 0 0 4px var(--primary-bg);
}

.step.completed .step-number {
    background: var(--success);
    color: var(--white);
}

.step-label {
    font-size: var(--font-sm);
    color: var(--text-muted);
    font-weight: 500;
    text-align: center;
    transition: var(--transition);
}

.step.active .step-label {
    color: var(--text);
    font-weight: 600;
}

/* ===== STEP CONTENT ===== */
.step-content {
    background-color: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    padding: var(--spacing-8);
    box-shadow: var(--shadow);
    transition: var(--transition);
    margin-bottom: var(--spacing-6);
}

.step-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.step-header h2 {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-2) 0;
}

.step-header p {
    font-size: var(--font-lg);
    color: var(--text-muted);
    margin: 0;
}

/* ===== FORM STYLES ===== */
.onboarding-form {
    max-width: 700px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: var(--spacing-6);
}

.form-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-6);
}

.form-label {
    display: block;
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text);
    margin-bottom: var(--spacing-2);
}

.form-label.required::after {
    content: '*';
    color: var(--danger);
    margin-left: var(--spacing-1);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: var(--font-base);
    color: var(--text);
    background-color: var(--card-bg);
    transition: var(--transition);
    box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(22, 185, 16, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* ===== FORM ACTIONS ===== */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-8);
    gap: var(--spacing-4);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--radius);
    font-size: var(--font-base);
    font-weight: 600;
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
    transition: var(--transition);
    background: none;
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
    border-color: var(--primary);
    margin-left: auto;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--card-bg);
    color: var(--text-muted);
    border-color: var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
    color: var(--text);
}

/* ===== FINAL STEP ===== */
.share-section {
    padding: var(--spacing-8);
    background-color: var(--card-bg);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.congrats-message {
    margin-bottom: var(--spacing-8);
}

.congrats-message i {
    font-size: var(--font-4xl);
    color: var(--success);
    margin-bottom: var(--spacing-4);
}

.congrats-message h3 {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-2) 0;
}

.congrats-message p {
    font-size: var(--font-lg);
    color: var(--text-muted);
    margin: 0;
}

.store-preview {
    background-color: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.store-info {
    margin-bottom: var(--spacing-4);
    padding: var(--spacing-4);
    background-color: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
}

.store-info h3 {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text);
    margin: 0 0 var(--spacing-1) 0;
}

.store-info p {
    font-size: var(--font-sm);
    color: var(--text-muted);
    margin: 0;
}

.preview-product {
    padding: var(--spacing-4);
    background-color: var(--white);
    border-radius: var(--radius);
    box-shadow: var(--shadow-sm);
    display: flex;
    gap: var(--spacing-4);
    align-items: center;
    text-align: left;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: var(--radius);
    background-color: var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.product-details h4 {
    font-size: var(--font-base);
    font-weight: 600;
    color: var(--text);
    margin: 0 0 var(--spacing-1) 0;
}

.product-details p {
    font-size: var(--font-sm);
    color: var(--text-muted);
    margin: 0 0 var(--spacing-2) 0;
}

.product-price {
    font-weight: 700;
    color: var(--text);
}

/* ===== SHARE LINK ===== */
.share-link-container {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-6);
    gap: var(--spacing-2);
}

.store-link {
    flex: 1;
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--gray-50);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: var(--font-base);
    color: var(--text);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: left;
}

.copy-btn {
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.copy-btn:hover {
    background-color: var(--primary-dark);
}

.share-buttons {
    display: flex;
    gap: var(--spacing-4);
}

.share-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
    padding: var(--spacing-4);
    border: none;
    border-radius: var(--radius);
    font-size: var(--font-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.share-btn.whatsapp {
    background-color: #25D366;
    color: var(--white);
}

.share-btn.whatsapp:hover {
    background-color: #128C7E;
}

.share-btn.facebook {
    background-color: #1877F2;
    color: var(--white);
}

.share-btn.facebook:hover {
    background-color: #166FE5;
}

.completion-section {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.success-message {
    margin-bottom: var(--spacing-8);
}

.success-message i {
    font-size: var(--font-4xl);
    color: var(--success);
    margin-bottom: var(--spacing-4);
}

.success-message h3 {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-2) 0;
}

.success-message p {
    font-size: var(--font-base);
    color: var(--text-muted);
    margin: 0;
}

/* ===== RADIO GROUPS ===== */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.radio-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    cursor: pointer;
    transition: var(--transition);
}

.radio-option:hover {
    background-color: var(--gray-50);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-full);
    position: relative;
    transition: var(--transition);
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary);
    background-color: var(--primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: var(--white);
    border-radius: var(--radius-full);
}

/* Alpine.js - Hide elements until Alpine initializes them */
[x-cloak] {
    display: none !important;
}
