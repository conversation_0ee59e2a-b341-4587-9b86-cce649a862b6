/* ===== NATIVE MOBILE APP CUSTOMER MANAGEMENT ===== */
/* iOS/Android Style Design with Green Theme */

/* Hide mobile version on larger screens */
@media (min-width: 769px) {
    .mobile-customers-app {
        display: none !important;
    }
}

/* Hide desktop version on mobile screens */
@media (max-width: 768px) {
    .customers-container {
        display: none !important;
    }
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;
    
    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    
    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
    
    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    
    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }
    
    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--background);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* ===== NATIVE APP LAYOUT ===== */
    .mobile-customers-app {
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }
    
    /* Using vendor layout header */
    
    /* ===== NATIVE STATS CARDS ===== */
    .mobile-stats-section {
        padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-stats-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }
    
    .mobile-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-stat-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
        height: 90px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .mobile-stat-card:active {
        transform: scale(0.98);
    }
    
    .mobile-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-stat-number {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
    }
    
    .mobile-stat-card.new {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-stat-card.active {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-stat-card.returning {
        background: linear-gradient(135deg, #8b5cf6, #a78bfa);
    }
    
    /* ===== NATIVE FILTERS ===== */
    .mobile-filters-section {
        padding: var(--spacing-md) 0;
        background: var(--white);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-filter-scroll {
        padding: 0 var(--spacing-lg);
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        display: flex;
        gap: var(--spacing-md);
    }

    .mobile-filter-scroll::-webkit-scrollbar {
        display: none;
    }

    .mobile-filter-chip {
        display: inline-block;
        padding: var(--spacing-sm) var(--spacing-lg);
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        font-size: var(--text-sm);
        font-weight: 500;
        white-space: nowrap;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
    }

    .mobile-filter-chip.active {
        background: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-sm);
    }

    .mobile-filter-chip:active {
        transform: scale(0.96);
    }

    /* ===== MOBILE LIST HEADER ===== */
    .mobile-list-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--white);
        border-bottom: 1px solid var(--border-light);
    }
    
    .mobile-list-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-sort-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-xs) var(--spacing-sm);
        border: 1px solid var(--border);
        border-radius: var(--radius-md);
        background: var(--white);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        font-weight: 500;
    }
    
    /* ===== NATIVE CUSTOMERS LIST ===== */
    .mobile-customers-list {
        padding: 0 var(--spacing-sm) var(--spacing-3xl);
    }

    .mobile-customer-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-card);
        transition: all 0.2s ease;
        border: 1px solid var(--border-light);
        position: relative;
        overflow: hidden;
    }

    .mobile-customer-card:active {
        transform: translateY(1px);
        box-shadow: var(--shadow-sm);
    }

    .mobile-customer-card:hover {
        border-color: var(--primary-light);
    }

    .mobile-customer-top {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);
    }
    
    .mobile-customer-id {
        font-weight: 600;
        color: var(--primary);
        font-size: var(--text-base);
    }
    
    .mobile-customer-amount {
        font-weight: 700;
        color: var(--text-primary);
        font-size: var(--text-base);
    }
    
    .mobile-customer-date {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-customer-status-chip {
        display: inline-block;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: 600;
        text-transform: uppercase;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-customer-status-chip.active {
        background: #dcfce7;
        color: #166534;
    }
    
    .mobile-customer-status-chip.inactive {
        background: #fee2e2;
        color: #b91c1c;
    }
    
    .mobile-customer-status-chip.pending {
        background: #fef3c7;
        color: #92400e;
    }

    .mobile-customer-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .mobile-customer-info {
        display: flex;
        gap: var(--spacing-md);
        align-items: center;
        margin-bottom: var(--spacing-md);
    }

    .mobile-customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: var(--radius-full);
        background: var(--primary-bg);
        color: var(--primary-dark);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: var(--text-base);
        flex-shrink: 0;
    }

    .mobile-customer-details {
        flex: 1;
        min-width: 0;
    }

    .mobile-customer-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }

    .mobile-customer-email {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .mobile-customer-status {
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: var(--spacing-xs);
    }

    .mobile-customer-status.active {
        background: #dcfce7;
        color: #166534;
    }

    .mobile-customer-status.inactive {
        background: #fee2e2;
        color: #b91c1c;
    }

    .mobile-customer-status.pending {
        background: #fef3c7;
        color: #92400e;
    }

    .mobile-customer-body {
        padding: var(--spacing-md) var(--spacing-lg);
    }

    .mobile-customer-meta {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-lg);
    }

    .mobile-meta-item {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .mobile-meta-label {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .mobile-meta-value {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-customer-body {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .mobile-customer-actions {
        display: flex;
        gap: var(--spacing-md);
        justify-content: space-between;
        margin-top: var(--spacing-lg);
    }

    .mobile-action-btn {
        flex: 1;
        height: 40px;
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        max-width: 48%;
    }
    
    .mobile-action-btn.view-btn, .mobile-action-btn.primary {
        background: var(--primary);
        color: var(--white);
        border: none;
    }
    
    .mobile-action-btn.update-btn, .mobile-action-btn.secondary {
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: none;
    }

    .mobile-action-btn:active {
        background: var(--surface-secondary);
        transform: scale(0.98);
    }

    .mobile-action-btn.primary {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }

    .mobile-action-btn.view-btn:active, .mobile-action-btn.primary:active {
        background: var(--primary-dark);
        transform: scale(0.98);
    }

    .mobile-action-btn.update-btn:active, .mobile-action-btn.secondary:active {
        background: var(--border);
        transform: scale(0.98);
    }
    
    .mobile-action-btn.danger {
        color: var(--error);
        border-color: var(--error);
    }

    .mobile-action-btn.danger:active {
        background: #fee2e2;
        transform: scale(0.98);
    }

    /* ===== NATIVE LOADING STATE ===== */
    .mobile-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-3xl);
    }

    .mobile-loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--border);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: mobile-spin 1s linear infinite;
    }

    @keyframes mobile-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .mobile-loading-text {
        margin-top: var(--spacing-lg);
        font-size: var(--text-base);
        color: var(--text-secondary);
    }

    /* ===== NATIVE EMPTY STATE ===== */
    .mobile-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-3xl);
        text-align: center;
    }

    .mobile-empty-icon {
        width: 64px;
        height: 64px;
        border-radius: var(--radius-full);
        background: var(--surface-secondary);
        color: var(--text-tertiary);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-2xl);
        margin-bottom: var(--spacing-xl);
    }

    .mobile-empty-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }

    .mobile-empty-description {
        font-size: var(--text-base);
        color: var(--text-tertiary);
        line-height: 1.6;
        max-width: 280px;
        margin: 0 auto;
    }

    /* ===== NATIVE PAGINATION ===== */
    .mobile-pagination {
        padding: var(--spacing-xl) var(--spacing-lg);
        background: var(--white);
        border-radius: var(--radius-lg);
        margin: var(--spacing-lg) var(--spacing-sm) 0;
        box-shadow: var(--shadow-card);
    }

    .mobile-pagination-info {
        text-align: center;
        margin-bottom: var(--spacing-lg);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }

    .mobile-pagination-controls {
        display: flex;
        justify-content: space-between;
        gap: var(--spacing-md);
    }

    .mobile-pagination-btn {
        flex: 1;
        height: 48px;
        border: 2px solid var(--primary);
        background: var(--white);
        color: var(--primary);
        border-radius: var(--radius-md);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }

    .mobile-pagination-btn.primary {
        background: var(--primary);
        color: var(--white);
    }
    
    .mobile-pagination-btn.primary:active:not(.disabled) {
        background: var(--primary-dark);
        transform: scale(0.98);
    }
    
    .mobile-pagination-btn:active:not(.disabled) {
        background: var(--primary-bg);
        transform: scale(0.98);
    }
    
    .mobile-pagination-btn.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        border-color: var(--border);
        color: var(--text-tertiary);
        background: var(--surface-secondary);
    }
    
    .mobile-pagination-btn.disabled.primary {
        background: var(--text-tertiary);
        color: var(--white);
    }
    
    /* ===== NATIVE SEARCH BAR ===== */
    .mobile-search-container {
        padding: var(--spacing-md) var(--spacing-lg);
        background: var(--white);
        position: sticky;
        top: var(--spacing-3xl);
        z-index: 9;
        border-bottom: 1px solid var(--border);
    }
    
    .mobile-search-bar {
        display: flex;
        align-items: center;
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        padding: 0 var(--spacing-md);
        height: 44px;
        width: 100%;
    }
    
    .mobile-search-icon {
        color: var(--text-tertiary);
        font-size: var(--text-lg);
        margin-right: var(--spacing-sm);
    }
    
    .mobile-search-input {
        background: transparent;
        border: none;
        outline: none;
        font-size: var(--text-base);
        color: var(--text-primary);
        width: 100%;
        height: 100%;
        font-family: inherit;
    }
    
    .mobile-search-input::placeholder {
        color: var(--text-tertiary);
    }
    
    /* Alpine.js Transition Styles */
    [x-cloak] { 
        display: none !important; 
    }
}

/* End of mobile styles */
/* === SMALL MOBILE STYLES (max-width: 480px) === */
@media (max-width: 480px) {
    /* Keep stats grid in 2x2 layout even on small screens */
    .mobile-stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-customer-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }
    
    /* Keep the avatar and customer details layout as shown in screenshot */
    .mobile-customer-info {
        width: 100%;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    /* Keep meta items in two columns */
    .mobile-customer-meta {
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    /* Keep action buttons side by side like in the screenshot */
    .mobile-customer-actions {
        display: flex;
        flex-direction: row;
        gap: var(--spacing-md);
        justify-content: space-between;
    }
    
    .mobile-action-btn {
        max-width: 48%;
        flex: 1;
    }
    
    .mobile-header {
        padding: var(--spacing-md);
    }
    
    .mobile-pagination {
        padding: var(--spacing-lg);
    }
    
    .mobile-pagination-controls {
        display: flex;
        flex-direction: row;
        gap: var(--spacing-md);
    }
}
