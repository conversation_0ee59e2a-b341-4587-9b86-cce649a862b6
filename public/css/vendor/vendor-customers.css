/* ===== MODERN CUSTOMER MANAGEMENT 2025 ===== */
/* Ultra Clean & Professional Design - DESKTOP ONLY */

/* Hide desktop view on mobile */
@media (max-width: 768px) {
    .customers-container {
        display: none !important;
    }
}

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* ===== DESKTOP ONLY STYLES ===== */
@media (min-width: 769px) {
    :root {
        /* Modern Color Palette */
        --primary: #16b910;
        --primary-light: #34d349;
        --primary-dark: #059669;
        --primary-bg: #d1fae5;
        --accent: #06b6d4;
        
        /* Status Colors */
        --success: #10b981;
        --warning: #f59e0b;
        --error: #ef4444;
        --info: #3b82f6;
        
        /* Neutral Palette */
        --white: #ffffff;
        --gray-50: #f8fafc;
        --gray-100: #f1f5f9;
        --gray-200: #e2e8f0;
        --gray-300: #cbd5e1;
        --gray-400: #94a3b8;
        --gray-500: #64748b;
        --gray-600: #475569;
        --gray-700: #334155;
        --gray-800: #1e293b;
        --gray-900: #0f172a;
        
        /* Typography */
        --font-family: 'Inter', system-ui, -apple-system, sans-serif;
        --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
        
        /* Spacing */
        --space-1: 0.25rem; --space-2: 0.5rem; --space-3: 0.75rem; --space-4: 1rem; --space-5: 1.25rem; --space-6: 1.5rem; --space-8: 2rem; --space-10: 2.5rem; --space-12: 3rem;
        
        /* Border Radius */
        --radius-sm: 0.25rem; --radius: 0.5rem; --radius-md: 0.75rem; --radius-lg: 1rem; --radius-xl: 1.5rem;
        
        /* Shadows */
        --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
        --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

        /* Transitions */
        --transition: 250ms ease;
    }

    /* === BASE & LAYOUT === */
    .customers-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: var(--space-8) var(--space-6);
        font-family: var(--font-family);
    }

    /* === HEADER === */
    .customers-header {
        margin-bottom: var(--space-8);
    }
    .customers-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-900);
        margin-bottom: var(--space-2);
    }
    .customers-subtitle {
        font-size: 1rem;
        color: var(--gray-500);
    }

    /* === STATS CARDS === */
    .stats-container {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: var(--space-6);
        margin-bottom: var(--space-8);
    }
    .stats-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        padding: var(--space-6);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        transition: all var(--transition);
        position: relative;
        overflow: hidden;
    }
    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0; left: 0; right: 0; height: 4px;
        background: var(--primary);
    }
    .stats-icon {
        font-size: 1.5rem;
        margin-bottom: var(--space-4);
        color: var(--primary);
    }
    .stats-number {
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--gray-900);
    }
    .stats-label {
        font-size: 0.875rem;
        color: var(--gray-500);
        font-weight: 500;
    }

    /* === FILTERS === */
    .filters-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-4);
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        margin-bottom: var(--space-8);
        border: 1px solid var(--gray-200);
    }
    .search-container {
        position: relative;
        display: flex;
        align-items: center;
    }
    .search-icon {
        position: absolute;
        left: var(--space-4);
        color: var(--gray-400);
    }
    .search-input {
        height: 44px;
        padding-left: var(--space-10);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        background: var(--white);
        transition: all var(--transition);
        min-width: 300px;
    }
    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px var(--primary-bg);
    }
    .filter-buttons {
        display: flex;
        gap: var(--space-2);
    }
    .filter-btn {
        padding: var(--space-2) var(--space-4);
        border: 1px solid var(--gray-300);
        background: var(--white);
        color: var(--gray-700);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        font-weight: 500;
        cursor: pointer;
        transition: all var(--transition);
    }
    .filter-btn:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
    }
    .filter-btn.active {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }

    /* === CUSTOMERS TABLE === */
    .customers-table-container {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--gray-200);
        overflow: hidden;
    }
    .customers-table {
        width: 100%;
        border-collapse: collapse;
    }
    .customers-table th, .customers-table td {
        padding: var(--space-4) var(--space-6);
        text-align: left;
        vertical-align: middle;
    }
    .customers-table th {
        font-size: 0.75rem;
        font-weight: 600;
        color: var(--gray-500);
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
    }
    .customers-table td {
        border-bottom: 1px solid var(--gray-100);
        font-size: 0.875rem;
        color: var(--gray-900);
    }
    .customers-table tr:last-child td {
        border-bottom: none;
    }
    .customers-table tr:hover {
        background: var(--gray-50);
    }
    .customer-info {
        display: flex;
        align-items: center;
        gap: var(--space-3);
    }
    .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        object-fit: cover;
    }
    .customer-name {
        font-weight: 500;
    }
    .status-badge {
        display: inline-block;
        padding: var(--space-1) var(--space-3);
        border-radius: var(--radius-lg);
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: capitalize;
    }
    .status-badge.active {
        background: var(--primary-bg);
        color: var(--primary-dark);
    }
    .status-badge.inactive {
        background: var(--gray-200);
        color: var(--gray-600);
    }
    .action-buttons {
        display: flex;
        gap: var(--space-2);
    }
    .action-buttons .btn {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-2) var(--space-3);
        font-size: 0.875rem;
        font-weight: 500;
        border-radius: var(--radius-md);
        border: 1px solid transparent;
        cursor: pointer;
        transition: all var(--transition);
    }
    .action-buttons .btn i {
        font-size: 0.8rem;
    }
    .action-buttons .btn.btn-primary {
        background-color: var(--primary);
        color: var(--white);
    }
    .action-buttons .btn.btn-primary:hover {
        background-color: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }
    .action-buttons .btn.btn-outline {
        background-color: var(--white);
        color: var(--gray-700);
        border-color: var(--gray-300);
    }
    .action-buttons .btn.btn-outline:hover {
        background-color: var(--gray-100);
        border-color: var(--gray-400);
        color: var(--gray-900);
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    /* === PAGINATION === */
    .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-4) var(--space-6);
        border-top: 1px solid var(--gray-200);
    }
    .pagination-info {
        font-size: 0.875rem;
        color: var(--gray-600);
    }
    .pagination-buttons {
        display: flex;
        gap: var(--space-2);
    }
    .pagination-btn {
        padding: var(--space-2) var(--space-4);
        border: 1px solid var(--gray-300);
        background: var(--white);
        color: var(--gray-700);
        border-radius: var(--radius-md);
        font-size: 0.875rem;
        cursor: pointer;
        transition: all var(--transition);
    }
    .pagination-btn:hover:not(:disabled) {
        background: var(--gray-100);
    }
    .pagination-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .pagination-btn.active {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }

    /* === LOADING & EMPTY STATES === */
    .loading-container, .empty-container {
        text-align: center;
        padding: var(--space-16) var(--space-6);
        background: var(--white);
        border-radius: var(--radius-xl);
        border: 1px solid var(--gray-200);
    }
    .loading-spinner {
        width: 48px;
        height: 48px;
        border: 5px solid var(--primary-bg);
        border-top-color: var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
    }
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
    .empty-icon {
        font-size: 3rem;
        color: var(--gray-300);
        margin-bottom: var(--space-4);
    }
    .empty-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--gray-900);
        margin-bottom: var(--space-2);
    }
    .empty-text {
        font-size: 0.875rem;
        color: var(--gray-500);
    }
}
