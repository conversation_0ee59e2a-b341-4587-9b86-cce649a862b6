/* ===== NATIVE MOBILE APP ORDER MANAGEMENT ===== */
/* iOS/Android Style Design with Green Theme */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    .mobile-orders-app {
        display: none !important;
    }
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;
    
    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    
    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
    
    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    
    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }
    
    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--background);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* ===== NATIVE APP LAYOUT ===== */
    .mobile-orders-app {
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }
    
    /* ===== NATIVE STATS CARDS ===== */
    .mobile-stats-section {
        padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-lg);
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-stats-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }
    
    .mobile-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-stat-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
    }
    
    .mobile-stat-card:active {
        transform: scale(0.98);
    }
    
    .mobile-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-stat-number {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
    }
    
    /* Status-specific gradients */
    .mobile-stat-card.pending {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    .mobile-stat-card.processing {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-stat-card.completed {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    /* ===== NATIVE FILTER BAR ===== */
    .mobile-filters {
        background: var(--white);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-filters-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-filter-chips {
        display: flex;
        gap: var(--spacing-sm);
        overflow-x: auto;
        padding-bottom: var(--spacing-xs);
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .mobile-filter-chips::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-filter-chip {
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: none;
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--text-sm);
        font-weight: 500;
        white-space: nowrap;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
    }
    
    .mobile-filter-chip.active {
        background: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-filter-chip:active {
        transform: scale(0.96);
    }
    
    /* ===== NATIVE ORDERS LIST ===== */
    .mobile-orders-list {
        padding: 0 var(--spacing-sm) var(--spacing-3xl);
    }
    
    .mobile-orders-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
        padding: 0 var(--spacing-xs);
    }
    
    .mobile-orders-count {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-sort-btn {
        background: var(--surface-secondary);
        border: none;
        border-radius: var(--radius-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }
    
    /* ===== NATIVE ORDER CARDS ===== */
    .mobile-order-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-md);
        box-shadow: var(--shadow-card);
        overflow: hidden;
        transition: all 0.2s ease;
        border: 1px solid var(--border-light);
    }
    
    .mobile-order-card:active {
        transform: translateY(1px);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-order-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .mobile-order-id {
        font-size: var(--text-base);
        font-weight: 700;
        color: var(--primary);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    .mobile-order-date {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        margin-top: var(--spacing-xs);
    }
    
    .mobile-order-amount {
        text-align: right;
    }
    
    .mobile-order-price {
        font-size: var(--text-lg);
        font-weight: 700;
        color: var(--text-primary);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    .mobile-order-status {
        display: inline-flex;
        align-items: center;
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--radius-full);
        font-size: var(--text-xs);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-top: var(--spacing-xs);
    }
    
    .mobile-order-status.pending {
        background: #fef3c7;
        color: #92400e;
    }
    
    .mobile-order-status.processing {
        background: #dbeafe;
        color: #1e40af;
    }
    
    .mobile-order-status.shipped {
        background: #e0e7ff;
        color: #3730a3;
    }
    
    .mobile-order-status.delivered {
        background: var(--primary-bg);
        color: var(--primary-dark);
    }
    
    .mobile-order-status.cancelled {
        background: #fee2e2;
        color: #b91c1c;
    }
    
    .mobile-order-body {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .mobile-customer-info {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-weight: 600;
        font-size: var(--text-base);
        flex-shrink: 0;
    }
    
    .mobile-customer-details {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-customer-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-customer-email {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .mobile-order-actions {
        display: flex;
        gap: var(--spacing-sm);
    }
    
    .mobile-action-btn {
        flex: 1;
        height: 40px;
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--text-sm);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
    }
    
    .mobile-action-btn.primary {
        background: var(--primary);
        color: var(--white);
    }
    
    .mobile-action-btn.primary:active {
        background: var(--primary-dark);
        transform: scale(0.98);
    }
    
    .mobile-action-btn.secondary {
        background: var(--surface-secondary);
        color: var(--text-secondary);
    }
    
    .mobile-action-btn.secondary:active {
        background: var(--border);
        transform: scale(0.98);
    }
    
    /* ===== NATIVE LOADING STATE ===== */
    .mobile-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-3xl);
    }
    
    .mobile-loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--border);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: mobile-spin 1s linear infinite;
    }
    
    .mobile-loading-text {
        margin-top: var(--spacing-lg);
        font-size: var(--text-sm);
        color: var(--text-tertiary);
    }
    
    @keyframes mobile-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* ===== NATIVE EMPTY STATE ===== */
    .mobile-empty-state {
        text-align: center;
        padding: var(--spacing-3xl) var(--spacing-lg);
    }
    
    .mobile-empty-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: var(--surface-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-xl);
        font-size: var(--text-3xl);
        color: var(--text-tertiary);
    }
    
    .mobile-empty-title {
        font-size: var(--text-xl);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-empty-description {
        font-size: var(--text-base);
        color: var(--text-tertiary);
        line-height: 1.6;
        max-width: 280px;
        margin: 0 auto;
    }
    
    /* ===== NATIVE PAGINATION ===== */
    .mobile-pagination {
        padding: var(--spacing-xl) var(--spacing-lg);
        background: var(--white);
        border-radius: var(--radius-lg);
        margin: var(--spacing-lg) var(--spacing-sm) 0;
        box-shadow: var(--shadow-card);
    }
    
    .mobile-pagination-info {
        text-align: center;
        margin-bottom: var(--spacing-lg);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .mobile-pagination-controls {
        display: flex;
        gap: var(--spacing-md);
    }
    
    .mobile-pagination-btn {
        flex: 1;
        height: 48px;
        border: 2px solid var(--primary);
        background: var(--white);
        color: var(--primary);
        border-radius: var(--radius-md);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }
    
    .mobile-pagination-btn.primary {
        background: var(--primary);
        color: var(--white);
    }
    
    .mobile-pagination-btn.primary:active:not(.disabled) {
        background: var(--primary-dark);
        transform: scale(0.98);
    }
    
    .mobile-pagination-btn:active:not(.disabled) {
        background: var(--primary-bg);
        transform: scale(0.98);
    }
    
    .mobile-pagination-btn.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        border-color: var(--border);
        color: var(--text-tertiary);
        background: var(--surface-secondary);
    }
    
    .mobile-pagination-btn.disabled.primary {
        background: var(--text-tertiary);
        color: var(--white);
    }
    
    .mobile-load-more {
        background: var(--white);
        border: 2px solid var(--primary);
        color: var(--primary);
        border-radius: var(--radius-md);
        padding: var(--spacing-md) var(--spacing-2xl);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        width: 100%;
        max-width: 200px;
    }
    
    .mobile-load-more:active {
        background: var(--primary);
        color: var(--white);
        transform: scale(0.98);
    }
    
    /* ===== NATIVE PULL TO REFRESH ===== */
    .mobile-pull-refresh {
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-tertiary);
        font-size: var(--text-sm);
        transform: translateY(-60px);
        transition: transform 0.3s ease;
    }
    
    .mobile-pull-refresh.active {
        transform: translateY(0);
    }
    
    /* ===== NATIVE SAFE AREAS ===== */
    .mobile-safe-bottom {
        height: var(--safe-area-bottom);
        background: var(--white);
    }
    
    /* ===== NATIVE HAPTIC FEEDBACK ===== */
    .mobile-haptic {
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }
    
    /* ===== NATIVE ACCESSIBILITY ===== */
    .mobile-sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }
    
    /* ===== NATIVE ANIMATIONS ===== */
    @keyframes mobile-fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .mobile-animate-in {
        animation: mobile-fadeIn 0.4s ease-out;
    }
    
    /* ===== NATIVE SCROLLBAR ===== */
    .mobile-scroll::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-scroll {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    
    /* ===== NATIVE ORDER DETAILS MODAL ===== */
    .mobile-modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        transition: all 0.3s ease;
    }
    
    .mobile-modal-overlay[x-show][style*="display: none"] {
        display: none !important;
    }
    
    .mobile-modal-overlay[x-show]:not([style*="display: none"]) {
        display: flex !important;
    }
    
    .mobile-modal {
        background: var(--white);
        border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        width: 100%;
        max-height: 90vh;
        transition: transform 0.3s ease;
        overflow: hidden;
        position: relative;
        transform: translateY(0);
    }
    
    .mobile-modal-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky;
        top: 0;
        background: var(--white);
        z-index: 10;
    }
    
    .mobile-modal-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-modal-close {
        width: 32px;
        height: 32px;
        border: none;
        background: var(--surface-secondary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: var(--text-secondary);
        font-size: var(--text-lg);
        transition: all 0.2s ease;
    }
    
    .mobile-modal-close:active {
        background: var(--border);
        transform: scale(0.95);
    }
    
    .mobile-modal-content {
        padding: var(--spacing-lg);
        max-height: calc(90vh - 80px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Order Details Modal Specific Styles */
    .mobile-order-detail-section {
        margin-bottom: var(--spacing-xl);
    }
    
    .mobile-detail-section-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }
    
    .mobile-detail-section-icon {
        width: 20px;
        height: 20px;
        background: var(--primary-bg);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: var(--text-sm);
    }
    
    .mobile-detail-card {
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-detail-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-detail-row:last-child {
        margin-bottom: 0;
    }
    
    .mobile-detail-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
        flex-shrink: 0;
        min-width: 90px;
    }
    
    .mobile-detail-value {
        font-size: var(--text-base);
        color: var(--text-primary);
        font-weight: 600;
        text-align: right;
        flex: 1;
    }
    
    .mobile-detail-value.mono {
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    /* Product Items List */
    .mobile-product-item {
        background: var(--white);
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        margin-bottom: var(--spacing-sm);
        border: 1px solid var(--border-light);
        display: flex;
        gap: var(--spacing-md);
    }
    
    .mobile-product-image {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-sm);
        background: var(--surface-secondary);
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--text-tertiary);
        font-size: var(--text-xs);
    }
    
    .mobile-product-details {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-product-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .mobile-product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-product-qty {
        font-size: var(--text-sm);
        color: var(--text-secondary);
    }
    
    .mobile-product-price {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--primary);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    /* Order Summary */
    .mobile-order-summary {
        background: var(--primary-bg);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        border: 1px solid rgba(16, 185, 129, 0.2);
    }
    
    .mobile-summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: var(--spacing-sm);
    }
    
    .mobile-summary-row:last-child {
        margin-bottom: 0;
        padding-top: var(--spacing-sm);
        border-top: 1px solid rgba(16, 185, 129, 0.2);
        font-weight: 700;
    }
    
    .mobile-summary-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
    }
    
    .mobile-summary-value {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    .mobile-summary-row:last-child .mobile-summary-label,
    .mobile-summary-row:last-child .mobile-summary-value {
        font-size: var(--text-base);
        color: var(--primary-dark);
    }
    
    /* ===== ORDER STATUS UPDATE MODAL ===== */
    .mobile-status-options {
        display: grid;
        gap: var(--spacing-md);
    }
    
    .mobile-status-option {
        background: var(--white);
        border: 2px solid var(--border-light);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .mobile-status-option.selected {
        border-color: var(--primary);
        background: var(--primary-bg);
    }
    
    .mobile-status-option:active {
        transform: scale(0.98);
    }
    
    .mobile-status-radio {
        width: 20px;
        height: 20px;
        border: 2px solid var(--border);
        border-radius: 50%;
        position: relative;
        flex-shrink: 0;
        transition: all 0.2s ease;
    }
    
    .mobile-status-option.selected .mobile-status-radio {
        border-color: var(--primary);
        background: var(--primary);
    }
    
    .mobile-status-option.selected .mobile-status-radio::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6px;
        height: 6px;
        background: var(--white);
        border-radius: 50%;
    }
    
    .mobile-status-info {
        flex: 1;
    }
    
    .mobile-status-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-status-description {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        line-height: 1.4;
    }
    
    .mobile-status-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-lg);
        flex-shrink: 0;
    }
    
    .mobile-status-option.pending .mobile-status-icon {
        background: #fef3c7;
        color: #92400e;
    }
    
    .mobile-status-option.processing .mobile-status-icon {
        background: #dbeafe;
        color: #1e40af;
    }
    
    .mobile-status-option.shipped .mobile-status-icon {
        background: #e0e7ff;
        color: #3730a3;
    }
    
    .mobile-status-option.delivered .mobile-status-icon {
        background: var(--primary-bg);
        color: var(--primary-dark);
    }
    
    .mobile-status-option.cancelled .mobile-status-icon {
        background: #fee2e2;
        color: #b91c1c;
    }
    
    .mobile-modal-actions {
        padding: var(--spacing-lg);
        border-top: 1px solid var(--border-light);
        display: flex;
        gap: var(--spacing-md);
        background: var(--white);
        position: sticky;
        bottom: 0;
    }
    
    .mobile-modal-btn {
        flex: 1;
        height: 48px;
        border: none;
        border-radius: var(--radius-md);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }
    
    .mobile-modal-btn.primary {
        background: var(--primary);
        color: var(--white);
    }
    
    .mobile-modal-btn.primary:active {
        background: var(--primary-dark);
        transform: scale(0.98);
    }
    
    .mobile-modal-btn.secondary {
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: 1px solid var(--border);
    }
    
    .mobile-modal-btn.secondary:active {
        background: var(--border);
        transform: scale(0.98);
    }
    
    .mobile-modal-btn:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    
    /* Current Order Status Indicator */
    .mobile-current-status {
        background: var(--primary-bg);
        border: 1px solid var(--primary);
        border-radius: var(--radius-md);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
    }
    
    .mobile-current-status-icon {
        width: 40px;
        height: 40px;
        background: var(--primary);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--white);
        font-size: var(--text-lg);
    }
    
    .mobile-current-status-info {
        flex: 1;
    }
    
    .mobile-current-status-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-current-status-name {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--primary-dark);
    }
    
    /* Alpine.js Transition Styles */
    [x-cloak] { 
        display: none !important; 
    }
    
    /* Modal Enter/Leave Transitions */
    .mobile-modal-overlay {
        transition: opacity 300ms ease;
    }
    
    /* Hidden State */
    .mobile-modal-overlay[style*="display: none"] {
        opacity: 0;
        pointer-events: none;
    }
    
    /* Visible State */  
    .mobile-modal-overlay:not([style*="display: none"]) {
        opacity: 1;
        pointer-events: auto;
    }
    
    /* Modal Slide Animation */
    .mobile-modal {
        animation: slideUp 0.3s ease-out;
    }
    
    @keyframes slideUp {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
}
