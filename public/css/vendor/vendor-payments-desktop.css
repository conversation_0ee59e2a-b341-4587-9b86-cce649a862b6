/* ===== MODERN SETTINGS 2025 ===== */
/* DESKTOP ONLY */

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

:root {
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-500: #64748b;
    --gray-700: #334155;
    --gray-900: #0f172a;
    --font-family: 'Inter', system-ui, sans-serif;
    --space-2: 0.5rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1.5rem;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --transition: 250ms ease;
}

.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

.settings-header {
    margin-bottom: var(--space-8);
}

.settings-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.settings-subtitle {
    font-size: 1rem;
    color: var(--gray-500);
}

.settings-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-6);
}

.settings-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all var(--transition);
}

.settings-card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    padding: var(--space-5) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    background-color: var(--gray-50);
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
}

.card-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: var(--space-2);
}

.card-body {
    padding: var(--space-6);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--gray-100);
    padding: var(--space-5) 0;
}

.setting-item:last-of-type {
    border-bottom: none;
}

.setting-item:first-of-type {
    padding-top: 0;
}

.setting-item:last-of-type {
    padding-bottom: 0;
}

.setting-info .setting-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-700);
}

.setting-info .setting-description {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin-top: var(--space-2);
}

.setting-item-details {
    padding: var(--space-4);
    margin-top: var(--space-4);
    background-color: var(--gray-50);
    border-radius: var(--radius-lg);
}

.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: var(--space-2);
    display: block;
}

.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-lg);
    background: var(--white);
}

.gateway-notice {
    text-align: center;
    padding: var(--space-8) 0;
    color: var(--gray-500);
}

.gateway-notice i {
    font-size: 2rem;
    margin-bottom: var(--space-4);
    display: block;
}

.settings-actions {
    margin-top: var(--space-8);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-4);
}

.btn {
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
}

/* Switch Toggle Control */
.switch {
  position: relative;
  display: inline-block;
  width: 52px;
  height: 30px;
}

.switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  box-shadow: var(--shadow-sm);
  transition: .4s;
}

input:checked + .slider {
  background-color: var(--primary);
}

input:checked + .slider:before {
  transform: translateX(22px);
}

.slider.round {
  border-radius: 30px;
}

.slider.round:before {
  border-radius: 50%;
}
