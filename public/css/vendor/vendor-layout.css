/* ===== WHAMART VENDOR DASHBOARD LAYOUT - DESKTOP ONLY ===== 
   Mobile styles are in vendor-layout-mobile.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
  --primary: #66bb6a;
  --primary-dark: #4caf50;
  --primary-light: #e8ffe1;
  --sidebar-bg: linear-gradient(180deg, #7ed957 0%, #66bb6a 100%);
  --sidebar-collapsed: 72px;
  --sidebar-expanded: 240px;
  --header-height: 64px;
  --surface: #fff;
  --border: #e0e0e0;
  --text: #222;
  --text-light: #fff;
  --text-muted: #666;
  --radius: 12px;
  --shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  --transition: all 0.25s cubic-bezier(.4,0,.2,1);
  --z-sidebar: 100;
  --z-header: 101;
}

* { box-sizing: border-box; margin: 0; padding: 0; }
html { font-size: 16px; }
body {
  font-family: 'Poppins', Arial, sans-serif;
  background: #f7f8fa;
  color: var(--text);
  min-height: 100vh;
  overflow-x: hidden;
}

.vendor-layout {
  display: flex;
  min-height: 100vh;
  background: #f7f8fa;
}

/* SIDEBAR - Modern White Design */
.vendor-sidebar {
  width: var(--sidebar-expanded);
  background: #ffffff;
  color: var(--text);
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0; left: 0; bottom: 0;
  z-index: var(--z-header);
  transition: var(--transition);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-right: 1px solid #f0f0f0;
  overflow: hidden;
}

/* Collapsed Sidebar State */
.sidebar-minimized .vendor-sidebar,
.vendor-sidebar.collapsed { 
  width: var(--sidebar-collapsed); 
}

/* Brand Section - With Hamburger Menu */
.sidebar-brand {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--header-height);
  padding: 0 1.8rem;
  font-weight: 700;
  font-size: 1.3rem;
  letter-spacing: 0.3px;
  border-bottom: 1px solid #f5f5f5;
  background: #ffffff;
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

/* Show brand icon when collapsed */
.sidebar-brand::before {
  content: 'W';
  position: absolute;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(102, 187, 106, 0.3);
}

/* Show brand icon when collapsed */
.sidebar-minimized .sidebar-brand::before,
.vendor-sidebar.collapsed .sidebar-brand::before {
  opacity: 1;
  transform: scale(1);
}

/* Hide brand text when collapsed */
.sidebar-minimized .brand-text,
.vendor-sidebar.collapsed .brand-text {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.2s ease;
}

/* Show only hamburger when collapsed */
.sidebar-minimized .sidebar-brand,
.vendor-sidebar.collapsed .sidebar-brand {
  justify-content: center;
  padding: 0 1rem;
}

.sidebar-minimized .brand-text-container,
.vendor-sidebar.collapsed .brand-text-container {
  display: none;
}

.sidebar-brand::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1.8rem;
  right: 1.8rem;
  height: 1px;
  background: linear-gradient(90deg, var(--primary) 0%, transparent 100%);
  transition: var(--transition);
}

/* Adjust brand border when collapsed */
.sidebar-minimized .sidebar-brand::after,
.vendor-sidebar.collapsed .sidebar-brand::after {
  left: 1rem;
  right: 1rem;
}

.brand-text { 
  color: var(--primary-dark); 
  font-size: 1.2rem; 
  font-weight: 700; 
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: var(--transition);
}

/* Navigation Section */
.sidebar-nav {
  flex: 1;
  padding: 2rem 1rem;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary-light) transparent;
  transition: var(--transition);
}

/* Collapsed sidebar navigation */
.sidebar-minimized .sidebar-nav,
.vendor-sidebar.collapsed .sidebar-nav {
  padding: 2rem 0.5rem;
}

.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}
.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}
.sidebar-nav::-webkit-scrollbar-thumb {
  background: var(--primary-light);
  border-radius: 2px;
}
.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

.nav-list { list-style: none; }
.nav-item { margin-bottom: 0.3rem; }
.nav-link {
  display: flex; 
  align-items: center;
  padding: 0.85rem 1.5rem;
  color: #555;
  border-radius: 14px;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  gap: 1.2rem;
  position: relative;
  margin: 0 0.2rem;
  overflow: hidden;
  white-space: nowrap;
}

/* Collapsed nav links - center icons and hide text */
.sidebar-minimized .nav-link,
.vendor-sidebar.collapsed .nav-link {
  justify-content: center;
  padding: 0.85rem 0.5rem;
  gap: 0;
  margin: 0 0.1rem;
}

/* Hide nav text when collapsed */
.sidebar-minimized .nav-item-text,
.vendor-sidebar.collapsed .nav-item-text {
  opacity: 0;
  transform: translateX(-20px);
  transition: all 0.2s ease;
  position: absolute;
  pointer-events: none;
}

.nav-link::before {
  content: '';
  position: absolute;
  left: 0; top: 0; bottom: 0; right: 0;
  border-radius: 14px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}
.nav-link:hover {
  color: var(--primary-dark);
  transform: translateX(4px);
}

/* Settings Dropdown Menu */
.nav-link.dropdown-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 0.8em;
}

.rotate-180 {
    transform: rotate(180deg);
}

.settings-submenu {
    padding-left: 20px;
    margin-left: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
    max-height: 500px; /* Adjust as needed */
    transition: max-height 0.5s ease-in-out, padding 0.3s ease;
}

.sidebar-minimized .settings-submenu {
    display: none; /* Hide submenu when sidebar is minimized */
}

.settings-submenu .nav-link {
    padding-left: 3.2rem !important; /* Align with main menu text */
    font-size: 0.9rem;
    font-weight: 500;
}

.settings-submenu .nav-link.active {
    color: var(--primary-dark);
    font-weight: 600;
}


/* Collapsed hover effect - no transform */
.sidebar-minimized .nav-link:hover,
.vendor-sidebar.collapsed .nav-link:hover {
  transform: none;
}

.nav-link:hover::before {
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(102, 187, 106, 0.1) 100%);
  box-shadow: 0 4px 12px rgba(102, 187, 106, 0.15);
}
.nav-link.active {
  color: var(--primary-dark);
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary-light) 0%, rgba(102, 187, 106, 0.15) 100%);
  box-shadow: 0 4px 16px rgba(102, 187, 106, 0.2);
  transform: translateX(6px);
}

/* Collapsed active state - no transform */
.sidebar-minimized .nav-link.active,
.vendor-sidebar.collapsed .nav-link.active {
  transform: none;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  left: -1rem; top: 50%; 
  transform: translateY(-50%);
  width: 4px; height: 24px;
  border-radius: 2px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  box-shadow: 0 2px 8px rgba(102, 187, 106, 0.4);
  transition: var(--transition);
}

/* Hide active indicator when collapsed */
.sidebar-minimized .nav-link.active::after,
.vendor-sidebar.collapsed .nav-link.active::after {
  opacity: 0;
}

.nav-icon {
  font-size: 1.3rem;
  width: 24px; 
  text-align: center;
  transition: var(--transition);
  flex-shrink: 0;
}
.nav-link:hover .nav-icon,
.nav-link.active .nav-icon {
  transform: scale(1.1);
}

/* Tooltip for collapsed sidebar */
.sidebar-minimized .nav-link,
.vendor-sidebar.collapsed .nav-link {
  position: relative;
}

.sidebar-minimized .nav-link:hover::after,
.vendor-sidebar.collapsed .nav-link:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: calc(100% + 15px);
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* MAIN CONTENT AREA */
.vendor-main {
  flex: 1;
  margin-left: var(--sidebar-expanded);
  min-height: 100vh;
  background: #f7f8fa;
  transition: var(--transition);
  position: relative;
  overflow-x: hidden;
}

/* Adjust main content when sidebar is collapsed */
.sidebar-minimized .vendor-main {
  margin-left: var(--sidebar-collapsed);
}

/* DESKTOP HEADER */
.vendor-header {
  position: sticky;
  top: 0;
  height: var(--header-height);
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 2rem;
  padding-left: calc(var(--sidebar-expanded) + 2rem);
  z-index: var(--z-sidebar);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
  transition: var(--transition);
}

/* Adjust header padding when sidebar is collapsed */
.sidebar-minimized .vendor-header {
  padding-left: calc(var(--sidebar-collapsed) + 2rem);
}

/* Header Left - Remove hamburger menu */
.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/* Header Hamburger Menu Button */
.header-hamburger {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.3rem;
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 8px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-hamburger:hover {
  background: var(--primary-light);
  color: var(--primary-dark);
  transform: scale(1.05);
}

.header-hamburger:active {
  transform: scale(0.95);
}

.header-hamburger:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Header Right */
.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-action {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.1rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  position: relative;
  transition: var(--transition);
}

.header-action:hover {
  background: var(--primary-light);
  color: var(--primary-dark);
}

.header-profile {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 1rem;
  border-radius: 12px;
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid transparent;
}

.header-profile:hover {
  background: var(--primary-light);
  border-color: var(--primary);
}

.profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.profile-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.profile-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text);
  line-height: 1.2;
}

.profile-role {
  font-size: 0.8rem;
  color: var(--text-muted);
  line-height: 1.2;
}

/* PAGE CONTENT */
.page-content {
  padding: 2rem;
  max-width: 100%;
  overflow-x: auto;
}

.page-header {
  margin-bottom: 2rem;
}

.page-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text);
  margin-bottom: 0.5rem;
}

.page-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
}

/* CARDS */
.card {
  background: #fff;
  border-radius: var(--radius);
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border);
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text);
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--border);
  background: #f8f9fa;
  border-radius: 0 0 var(--radius) var(--radius);
}

/* FORMS */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text);
}

.form-control {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: var(--transition);
  background: #fff;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(102, 187, 106, 0.1);
}

/* BUTTONS */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  text-decoration: none;
  gap: 0.5rem;
}

.btn-primary {
  background: var(--primary);
  color: #fff;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 187, 106, 0.3);
}

.btn-outline {
  background: transparent;
  color: var(--primary);
  border: 1px solid var(--primary);
}

.btn-outline:hover {
  background: var(--primary);
  color: #fff;
}

/* TABLES */
.table-responsive {
  overflow-x: auto;
  border-radius: var(--radius);
  border: 1px solid var(--border);
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
}

.table th,
.table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border);
}

.table th {
  background: #f8f9fa;
  font-weight: 600;
  color: var(--text);
}

.table tbody tr:hover {
  background: #f8f9fa;
}

/* NOTIFICATIONS */
.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 18px;
  text-align: center;
}

/* Navigation Link Styling */
.nav-link.logout-btn {
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
  color: #555;
  padding: 0.85rem 1.5rem;
  border-radius: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
}

.nav-link.logout-btn:hover {
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

/* Hide mobile elements on desktop */
.mobile-header,
.mobile-profile, 
.mobile-divider, 
.mobile-billing-item, 
.mobile-app-footer,
.mobile-menu,
.bottom-nav,
.fab {
  display: none !important;
}

/* Show desktop elements */
.desktop-menu {
  display: block !important;
}

.desktop-header {
  display: flex !important;
}

/* === SIDEBAR FOOTER (App Version & Made in India) === */
.sidebar-footer {
  padding: 1.5rem 1.8rem 2rem 1.8rem;
  border-top: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  margin-top: auto;
  transition: var(--transition);
  position: relative;
}

.sidebar-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 1.8rem;
  right: 1.8rem;
  height: 1px;
  background: linear-gradient(90deg, var(--primary) 0%, transparent 50%, var(--primary) 100%);
  opacity: 0.3;
}

.sidebar-footer-content {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  transition: var(--transition);
}

.version-info {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  margin-bottom: 0.2rem;
  opacity: 0.8;
  transition: var(--transition);
}

.version-text {
  font-size: 0.85rem;
  color: var(--text-muted);
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  opacity: 0.8;
}

.made-in-india {
  display: flex;
  align-items: center;
  gap: 0.6rem;
  font-size: 0.9rem;
  color: #333;
  font-weight: 500;
  letter-spacing: 0.2px;
  transition: var(--transition);
  cursor: default;
  padding: 0.3rem 0;
  border-radius: 6px;
}

.made-in-india:hover {
  color: var(--primary-dark);
  transform: translateY(-1px);
}

.made-in-india i {
  color: #e74c3c;
  font-size: 1rem;
  animation: heartbeat 2s ease-in-out infinite;
}

.made-in-india .india-flag {
  width: 16px;
  height: 12px;
  border-radius: 2px;
  margin-left: 0.2rem;
}

/* Heartbeat animation for the heart icon */
@keyframes heartbeat {
  0%, 50%, 100% { transform: scale(1); }
  25%, 75% { transform: scale(1.1); }
}

/* Collapsed sidebar footer styling */
.sidebar-minimized .sidebar-footer,
.vendor-sidebar.collapsed .sidebar-footer {
  padding: 1rem 0.5rem;
  text-align: center;
}

.sidebar-minimized .sidebar-footer-content,
.vendor-sidebar.collapsed .sidebar-footer-content {
  align-items: center;
  gap: 0.5rem;
}

.sidebar-minimized .version-info,
.vendor-sidebar.collapsed .version-info {
  display: none;
}

.sidebar-minimized .made-in-india,
.vendor-sidebar.collapsed .made-in-india {
  font-size: 0;
  gap: 0;
  justify-content: center;
}

.sidebar-minimized .made-in-india span,
.vendor-sidebar.collapsed .made-in-india span {
  display: none;
}

.sidebar-minimized .made-in-india i,
.vendor-sidebar.collapsed .made-in-india i {
  font-size: 1.1rem;
  margin: 0;
}

.sidebar-minimized .sidebar-footer::before,
.vendor-sidebar.collapsed .sidebar-footer::before {
  left: 0.5rem;
  right: 0.5rem;
}

/* Responsive: Hide sidebar-footer on mobile */
@media (max-width: 768px) {
  .sidebar-footer {
    display: none !important;
  }
}

/* UTILITIES */
.hidden { display: none !important; }
.text-primary { color: var(--primary-dark) !important; }
.text-success { color: var(--primary) !important; }
.text-danger { color: #f44336 !important; }
.text-warning { color: #ff9800 !important; }
.bg-primary { background: var(--primary) !important; color: #fff !important; }
.rounded { border-radius: var(--radius) !important; }
.shadow { box-shadow: var(--shadow) !important; }

/* ACCESSIBILITY */
.nav-link:focus, .sidebar-hamburger:focus, .header-action:focus, .header-profile:focus {
  outline: 2px solid var(--primary-dark);
  outline-offset: 2px;
}

/* PRINT */
@media print {
  .vendor-sidebar, .vendor-header { display: none !important; }
  .vendor-main { margin: 0 !important; padding: 0 !important; }
  .page-content { box-shadow: none !important; border-radius: 0 !important; }
}

/* Hamburger Menu Button in Sidebar */
.sidebar-hamburger {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-hamburger:hover {
  background: var(--primary-light);
  color: var(--primary-dark);
}

/* Brand Text Container */
.brand-text-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}
