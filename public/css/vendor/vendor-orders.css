/* ===== MODERN ORDER MANAGEMENT 2025 ===== */
/* Ultra Clean & Professional Design - DESKTOP ONLY */

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* ===== DESKTOP ONLY STYLES ===== */
@media (min-width: 769px) {
:root {
    /* Modern Color Palette */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --accent: #06b6d4;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Neutral Palette */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 250ms ease;
    --transition-slow: 350ms ease;
}

/* === BASE STYLES === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === LAYOUT === */
.orders-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

/* === HEADER === */
.orders-header {
    margin-bottom: var(--space-8);
}

.orders-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.orders-subtitle {
    font-size: 1rem;
    color: var(--gray-500);
    font-weight: 400;
}

/* === STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary);
}

.stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.stat-info p {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.stat-icon.total { background: #d1fae5; color: #10b981; }
.stat-icon.pending { background: #FEF3C7; color: #D97706; }
.stat-icon.processing { background: #DBEAFE; color: #2563EB; }
.stat-icon.completed { background: #d1fae5; color: #10b981; }

/* === FILTERS === */
.filters-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.filters-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: var(--space-4);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-700);
}

.filter-input,
.filter-select {
    height: 44px;
    padding: 0 var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    background: var(--white);
    transition: all 0.2s ease;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px #d1fae5;
}

.search-input-wrapper {
    position: relative;
}

.search-input {
    padding-left: var(--space-10);
}

.search-icon {
    position: absolute;
    left: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    font-size: 0.875rem;
}

.clear-filters-btn {
    height: 44px;
    width: 44px;
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-filters-btn:hover {
    background: var(--gray-50);
    color: var(--gray-700);
}

/* === TABLE === */
.table-section {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
}

.table-actions {
    display: flex;
    gap: var(--space-3);
}

.table-btn {
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.table-btn:hover {
    background: var(--gray-50);
}

.table-btn.primary {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

.table-btn.primary:hover {
    background: var(--primary-dark);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th {
    background: var(--gray-50);
    padding: var(--space-4) var(--space-6);
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-500);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--gray-200);
}

.orders-table td {
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid var(--gray-100);
    font-size: 0.875rem;
    color: var(--gray-900);
    vertical-align: middle;
}

.orders-table tr:last-child td {
    border-bottom: none;
}

.orders-table tr:hover {
    background: var(--gray-50);
}

/* === TABLE CELLS === */
.order-id {
    font-weight: 600;
    color: var(--primary);
    font-family: var(--font-mono);
}

.customer-info {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
}

.customer-name {
    font-weight: 500;
    color: var(--gray-900);
}

.customer-email {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.order-amount {
    font-weight: 600;
    color: var(--gray-900);
    font-family: var(--font-mono);
}

.order-status {
    display: inline-flex;
    align-items: center;
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.order-status.pending {
    background: #FEF3C7;
    color: #92400E;
}

.order-status.processing {
    background: #DBEAFE;
    color: #1E40AF;
}

.order-status.shipped {
    background: #E0E7FF;
    color: #3730A3;
}

.order-status.delivered {
    background: #D1FAE5;
    color: #059669;
}

.order-status.cancelled {
    background: #FEE2E2;
    color: #B91C1C;
}

.order-date {
    color: var(--gray-500);
    font-size: 0.875rem;
    font-family: var(--font-mono);
}

.order-actions {
    display: flex;
    gap: var(--space-2);
}

.action-btn {
    padding: var(--space-1) var(--space-2);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.view {
    background: #d1fae5;
    color: #10b981;
}

.action-btn.view:hover {
    background: var(--primary);
    color: var(--white);
}

.action-btn.edit {
    background: var(--gray-100);
    color: var(--gray-600);
}

.action-btn.edit:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* === PAGINATION === */
.pagination {
    padding: var(--space-4) var(--space-6);
    border-top: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: between;
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-500);
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin-left: auto;
}

.pagination-btn {
    padding: var(--space-1) var(--space-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--radius-md);
    background: var(--white);
    color: var(--gray-700);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--gray-50);
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--primary);
    color: var(--white);
    border-color: var(--primary);
}

/* === LOADING STATE === */
.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-16);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === EMPTY STATE === */
.empty-state {
    text-align: center;
    padding: var(--space-16) var(--space-8);
}

.empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-6);
    font-size: 2rem;
    color: var(--gray-400);
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.empty-description {
    font-size: 0.875rem;
    color: var(--gray-500);
    max-width: 400px;
    margin: 0 auto;
}

/* === RESPONSIVE === */
@media (min-width: 769px) and (max-width: 1024px) {
    .orders-container {
        padding: var(--space-4);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-4);
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .table-section {
        overflow-x: auto;
    }
    
    .orders-table {
        min-width: 600px;
    }
    
    .pagination {
        flex-direction: column;
        gap: var(--space-4);
        align-items: stretch;
    }
    
    .pagination-controls {
        margin-left: 0;
        justify-content: center;
    }
}

/* === DESKTOP MODALS === */
.desktop-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-6);
    transition: all 0.3s ease;
}

.desktop-modal {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 900px;
    max-height: 90vh;
    overflow: hidden;
    transition: all 0.3s ease;
}

.desktop-modal.smaller {
    max-width: 600px;
}

/* Modal Header */
.desktop-modal-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--gray-50);
}

.desktop-modal-title-section {
    flex: 1;
}

.desktop-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.desktop-modal-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin: 0;
}

.desktop-modal-close {
    width: 36px;
    height: 36px;
    border: none;
    background: var(--white);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--gray-500);
    font-size: 0.875rem;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-sm);
}

.desktop-modal-close:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

/* Modal Content */
.desktop-modal-content {
    padding: var(--space-6);
    max-height: calc(90vh - 160px);
    overflow-y: auto;
}

.desktop-modal-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-6);
}

.desktop-modal-grid:last-child {
    margin-bottom: 0;
}

/* Detail Cards */
.desktop-detail-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.desktop-detail-card.full-width {
    grid-column: 1 / -1;
}

.desktop-detail-card-header {
    background: var(--gray-50);
    padding: var(--space-4) var(--space-5);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.desktop-detail-card-header i {
    color: var(--primary);
    font-size: 1rem;
}

.desktop-detail-card-header h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.desktop-detail-card-body {
    padding: var(--space-5);
}

/* Detail Rows */
.desktop-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-4);
    padding-bottom: var(--space-3);
    border-bottom: 1px solid var(--gray-100);
}

.desktop-detail-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.desktop-detail-row.total {
    margin-top: var(--space-4);
    padding-top: var(--space-4);
    border-top: 2px solid var(--gray-200);
    border-bottom: none;
    font-weight: 600;
}

.desktop-detail-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    flex-shrink: 0;
    min-width: 120px;
}

.desktop-detail-value {
    font-size: 0.875rem;
    color: var(--gray-900);
    font-weight: 500;
    text-align: right;
}

.desktop-detail-value.mono {
    font-family: var(--font-mono);
    background: var(--gray-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.8rem;
}

.desktop-detail-value.amount {
    font-family: var(--font-mono);
    font-weight: 600;
    color: var(--primary);
    font-size: 1rem;
}

.desktop-detail-value.success {
    color: var(--success);
    font-weight: 600;
}

/* Items Table */
.desktop-items-table {
    background: var(--gray-50);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.desktop-items-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--space-4);
    padding: var(--space-3) var(--space-4);
    background: var(--gray-100);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--gray-700);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.desktop-items-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--space-4);
    padding: var(--space-4);
    border-bottom: 1px solid var(--gray-200);
    align-items: center;
}

.desktop-items-row:last-child {
    border-bottom: none;
}

.desktop-item-product {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.desktop-item-icon {
    width: 32px;
    height: 32px;
    background: var(--white);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.desktop-item-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
}

.desktop-item-qty,
.desktop-item-price,
.desktop-item-total {
    font-size: 0.875rem;
    color: var(--gray-700);
    text-align: center;
}

.desktop-item-total {
    font-weight: 600;
    color: var(--gray-900);
}

/* Current Status Display */
.desktop-current-status {
    background: var(--primary-bg);
    border: 1px solid var(--primary);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.desktop-current-status-badge {
    flex-shrink: 0;
}

.desktop-current-status-info h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-1) 0;
}

.desktop-current-status-info p {
    font-size: 0.8rem;
    color: var(--gray-600);
    margin: 0;
}

/* Status Options */
.desktop-status-options {
    display: grid;
    gap: var(--space-3);
}

.desktop-status-option {
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.desktop-status-option:hover {
    border-color: var(--primary);
    background: var(--gray-50);
}

.desktop-status-option.selected {
    border-color: var(--primary);
    background: var(--primary-bg);
    box-shadow: var(--shadow-sm);
}

.desktop-status-option.current {
    opacity: 0.6;
    cursor: not-allowed;
}

.desktop-status-radio {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.desktop-status-option.selected .desktop-status-radio {
    border-color: var(--primary);
}

.desktop-status-radio-inner {
    width: 8px;
    height: 8px;
    background: var(--primary);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.desktop-status-info {
    flex: 1;
}

.desktop-status-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.desktop-status-description {
    font-size: 0.8rem;
    color: var(--gray-600);
}

.desktop-status-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

/* Modal Footer */
.desktop-modal-footer {
    padding: var(--space-5) var(--space-6);
    border-top: 1px solid var(--gray-200);
    background: var(--gray-50);
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
}

.desktop-modal-btn {
    padding: var(--space-2) var(--space-5);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    border: none;
}

.desktop-modal-btn.secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.desktop-modal-btn.secondary:hover {
    background: var(--gray-50);
}

.desktop-modal-btn.primary {
    background: var(--primary);
    color: var(--white);
}

.desktop-modal-btn.primary:hover:not(:disabled) {
    background: var(--primary-dark);
}

.desktop-modal-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Modal Responsive */
@media (min-width: 769px) and (max-width: 1024px) {
    .desktop-modal {
        max-width: 700px;
        margin: var(--space-4);
    }
    
    .desktop-modal-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }
    
    .desktop-items-header,
    .desktop-items-row {
        grid-template-columns: 2fr 80px 100px 100px;
        gap: var(--space-2);
    }
}

/* Hide Desktop Modals on Mobile */
@media (max-width: 768px) {
    .desktop-modal-overlay {
        display: none !important;
    }
}

/* === UTILITIES === */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

} /* End of @media (min-width: 769px) */

/* ===== HIDE DESKTOP ON MOBILE ===== */
@media (max-width: 768px) {
    .orders-container,
    .orders-header,
    .stats-grid,
    .filters-section,
    .table-section,
    .loading-container,
    .empty-state {
        display: none !important;
    }
}
