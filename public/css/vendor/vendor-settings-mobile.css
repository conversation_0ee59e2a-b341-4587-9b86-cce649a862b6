/* ===== NATIVE MOBILE APP SETTINGS ===== */
/* iOS/Android Style Design with Green Theme */

:root {
    /* Green Theme Colors */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --primary-text: #064e3b;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* Native Spacing */
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;

    /* Native Radius */
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-full: 50px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* Native Typography */
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
}

/* ===== BASE & LAYOUT ===== */
body {
    background-color: var(--background);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

.settings-container {
    padding: 0;
}

/* ===== HEADER ===== */
.settings-header {
    background: var(--surface);
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top, 0px));
    border-bottom: 1px solid var(--border);
}

.settings-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.settings-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

/* ===== SETTINGS GROUP (CARD) ===== */
.settings-grid {
    padding: var(--spacing-2xl) var(--spacing-lg);
    display: grid;
    gap: var(--spacing-2xl);
}

.settings-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
}

.card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.card-body {
    padding: 0;
}

/* ===== SETTING ITEM ===== */
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
    padding-right: var(--spacing-md);
}

.setting-name {
    font-size: var(--text-base);
    font-weight: 500;
    color: var(--text-primary);
}

.setting-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.setting-control {
    flex-shrink: 0;
}

/* ===== FORM STYLES ===== */
.form-grid-2,
.form-grid-3,
.form-group {
    padding: 0 var(--spacing-lg);
}

.form-grid-2,
.form-grid-3 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
}

.form-group {
    padding-top: var(--spacing-lg);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.settings-section-body {
    padding: 0;
}

.form-grid-2 .form-group {
    padding: 0;
    border-bottom: none;
    margin-bottom: var(--spacing-xl);
}

.form-group:last-child {
    border-bottom: none;
}

.form-label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-label.required::after {
    content: ' *';
    color: var(--danger);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    background: var(--background);
    color: var(--text-primary);
    -webkit-appearance: none;
    appearance: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

.form-textarea {
    min-height: 100px;
}

.form-help {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-sm);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--text-base);
    font-weight: 600;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:active {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-secondary);
    border-color: var(--border);
}

.btn-secondary:active {
    background: var(--border);
}

/* Header buttons should be icon-only */
.settings-header-right .btn {
    padding: var(--spacing-md);
    border-radius: var(--radius-full);
}

.settings-header-right .btn span {
    display: none;
}

/* ===== ACTIONS FOOTER ===== */
.settings-actions {
    background: var(--surface);
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.settings-actions .btn {
    width: 100%;
}

/* ===== COMPONENTS: FILE UPLOAD ===== */
.file-upload-wrapper {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.profile-avatar-preview {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--border);
    background-color: var(--surface-secondary);
}

.file-upload-label {
    display: flex;
    flex-direction: column;
}

.file-upload-label span:first-child {
    color: var(--primary);
    font-weight: 500;
    cursor: pointer;
}

.file-upload-hint {
    font-size: var(--text-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
}

/* ===== COMPONENTS: NATIVE TOGGLE SWITCH ===== */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* ===== LOGO UPLOAD STYLES ===== */
.logo-upload-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg) 0;
}

.logo-upload {
    width: 120px;
    height: 120px;
    border: 2px dashed var(--border);
    border-radius: var(--radius-lg);
}

.logo-preview {
    width: 120px;
    height: 120px;
    border-radius: var(--radius-lg);
}

.logo-image {
    border-radius: var(--radius-lg);
}

.logo-remove-btn {
    width: 28px;
    height: 28px;
}

/* ===== BUSINESS TYPE SELECTOR ===== */
.business-type-selector {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    padding: 0 var(--spacing-lg);
}

.business-type-option {
    width: 100%;
}

.business-type-content {
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

/* ===== BUSINESS TIMING ===== */
.business-timing-container {
    border-radius: var(--radius-lg);
    margin: 0 var(--spacing-lg);
}

.business-timing-header {
    display: grid;
    grid-template-columns: 1fr 80px 1fr;
    padding: var(--spacing-md);
    font-size: var(--text-xs);
}

.business-timing-row {
    display: grid;
    grid-template-columns: 1fr 80px 1fr;
    padding: var(--spacing-md);
    font-size: var(--text-sm);
}

.hours-inputs {
    flex-direction: column;
    gap: var(--spacing-xs);
}

.time-input {
    width: 100%;
    padding: var(--spacing-xs);
    font-size: var(--text-xs);
}

.time-separator {
    align-self: center;
}

/* ===== THEME SELECTOR ===== */
.theme-selector {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.theme-option {
    border-radius: var(--radius-md);
}

.theme-preview {
    height: 100px;
    border-radius: var(--radius-md) var(--radius-md) 0 0;
}

.theme-name {
    font-size: var(--text-xs);
    padding: 8px;
    text-align: center;
}

.theme-buttons {
    display: flex;
    padding: 0 8px 8px;
    gap: 4px;
}

.theme-btn {
    flex: 1;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: var(--text-xs);
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.theme-btn.apply {
    background-color: var(--primary);
    color: white;
}

.theme-btn.preview {
    background-color: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
}

.theme-btn.preview:hover {
    background-color: #e9f5f0;
    color: var(--primary);
}

/* Step Indicator for Mobile */
.step-indicator {
    display: flex;
    overflow-x: auto;
    padding: 1rem;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    position: relative;
}

.step-indicator::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
}

.step-indicator::before {
    content: '';
    height: 2px;
    width: calc(100% - 2rem);
    background-color: var(--border);
    position: absolute;
    top: calc(1rem + 16px);
    left: 1rem;
    z-index: 1;
}

.step {
    min-width: 80px;
    margin-right: 10px;
    flex: 0 0 auto;
}

.step-number {
    width: 28px;
    height: 28px;
    font-size: 12px;
}

.step-title {
    font-size: 10px;
    white-space: nowrap;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Step Navigation for Mobile */
.step-navigation {
    padding: 1rem;
    flex-direction: column;
    gap: 10px;
}

.step-counter {
    text-align: center;
    margin: 5px 0;
    font-size: 14px;
    color: var(--text-muted);
}

.step-content {
    padding: 1rem;
}

.step-form {
    display: none;
}

.step-form.active {
    display: block;
}

/* ===== LOCATION FIELDS ===== */
.location-fields {
    padding: 0;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary);
    color: var(--primary);
    margin: 0 var(--spacing-lg);
    width: calc(100% - var(--spacing-lg) * 2);
    justify-content: center;
    font-size: var(--text-sm);
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* ===== NEW MOBILE SETTINGS STYLES ===== */

.mobile-settings-app {
    min-height: 100vh;
    background: var(--background);
    padding-top: var(--safe-area-top);
    padding-bottom: calc(var(--safe-area-bottom) + var(--spacing-xl));
    position: relative;
}

.mobile-settings-header {
    background: var(--surface);
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + var(--safe-area-top));
    border-bottom: 1px solid var(--border);
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.mobile-settings-title {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
}

.mobile-settings-content {
    padding: var(--spacing-lg);
}

.mobile-settings-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-card);
    border: 1px solid var(--border-light);
    overflow: hidden;
}

.mobile-settings-card-header {
    display: flex;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--surface-secondary);
}

.mobile-settings-card-icon {
    font-size: var(--text-lg);
    color: var(--primary);
    margin-right: var(--spacing-md);
    width: 24px;
    text-align: center;
}

.mobile-settings-card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.mobile-settings-card-body {
    padding: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.mobile-form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.mobile-form-label {
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.mobile-form-input {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    background: var(--background);
    color: var(--text-primary);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.mobile-form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.mobile-switch-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
}

.mobile-switch-label {
    font-size: var(--text-base);
    color: var(--text-primary);
}

.mobile-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
    flex-shrink: 0;
}

.mobile-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.mobile-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e9e9eb;
    transition: .2s;
    border-radius: 34px;
}

.mobile-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .2s;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.mobile-switch input:checked + .mobile-slider {
    background-color: var(--primary);
}

.mobile-switch input:checked + .mobile-slider:before {
    transform: translateX(20px);
}

/* ===== ACTION BUTTONS ===== */
.settings-actions {
    padding: var(--spacing-lg);
    background: var(--surface);
    border-top: 1px solid var(--border);
    position: sticky;
    bottom: 0;
    padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom, 0px));
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:active {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
}

.btn-secondary:active {
    background: var(--border);
}

