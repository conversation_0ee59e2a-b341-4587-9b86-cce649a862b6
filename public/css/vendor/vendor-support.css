/* ===== MODERN VENDOR SUPPORT CSS ===== */

/* === SUPPORT HEADER === */
.support-header {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    margin-bottom: var(--vendor-space-8);
    text-align: center;
    box-shadow: var(--vendor-shadow-sm);
}

.support-title {
    font-size: var(--vendor-font-size-3xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-3) 0;
}

.support-subtitle {
    font-size: var(--vendor-font-size-lg);
    color: var(--vendor-text-muted);
    margin: 0;
}

/* === CONTACT METHODS === */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--vendor-space-6);
    margin-bottom: var(--vendor-space-8);
}

.contact-card {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    text-align: center;
    transition: var(--vendor-transition);
    box-shadow: var(--vendor-shadow-sm);
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--vendor-shadow-lg);
}

.contact-icon {
    width: 64px;
    height: 64px;
    background: var(--vendor-primary);
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--vendor-space-4);
    font-size: var(--vendor-font-size-2xl);
    color: white;
    box-shadow: var(--vendor-shadow-md);
}

.contact-title {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
}

.contact-info {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
}

/* === MAIN CONTENT === */
.support-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--vendor-space-8);
}

/* === FAQ SECTION === */
.faq-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
}

.faq-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-6) 0;
}

.faq-item {
    border-bottom: 1px solid var(--vendor-border);
    padding: var(--vendor-space-4) 0;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: none;
    border: none;
    padding: var(--vendor-space-3);
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    text-align: left;
    cursor: pointer;
    border-radius: var(--vendor-radius-lg);
    transition: var(--vendor-transition);
}

.faq-question:hover {
    background: var(--vendor-gray-50);
}

.faq-answer {
    padding: var(--vendor-space-4) var(--vendor-space-3);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-secondary);
    line-height: 1.6;
}

/* === SUPPORT FORM === */
.support-form-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
}

.form-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-6) 0;
}

.form-group {
    margin-bottom: var(--vendor-space-5);
}

.form-label {
    display: block;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface);
    transition: var(--vendor-transition);
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(102, 187, 106, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    width: 100%;
    padding: var(--vendor-space-4);
    background: var(--vendor-primary);
    color: white;
    border: none;
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--vendor-transition);
}

.submit-btn:hover {
    background: var(--vendor-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .support-content {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-6);
    }
}

@media (max-width: 768px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
    
    .support-header {
        padding: var(--vendor-space-6);
    }
    
    .faq-section,
    .support-form-section {
        padding: var(--vendor-space-6);
    }
    
    .support-title {
        font-size: var(--vendor-font-size-2xl);
    }
}

@media (max-width: 480px) {
    .support-header {
        padding: var(--vendor-space-5);
    }
    
    .contact-card {
        padding: var(--vendor-space-6);
    }
    
    .contact-icon {
        width: 56px;
        height: 56px;
        font-size: var(--vendor-font-size-xl);
    }
}