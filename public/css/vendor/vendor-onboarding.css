/* ===== VENDOR ONBOARDING CSS ===== */

/* Container */
.onboarding-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--vendor-space-6);
}

/* Progress Bar */
.progress-container {
    margin-bottom: var(--vendor-space-8);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--vendor-border);
    border-radius: var(--vendor-radius-full);
    overflow: hidden;
    margin-bottom: var(--vendor-space-6);
}

.progress-fill {
    height: 100%;
    background: var(--vendor-primary);
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--vendor-space-2);
    flex: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: var(--vendor-radius-full);
    background: var(--vendor-gray-200);
    color: var(--vendor-text-muted);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--vendor-font-size-sm);
    transition: var(--vendor-transition);
}

.step.active .step-number {
    background: var(--vendor-primary);
    color: white;
}

.step.completed .step-number {
    background: var(--vendor-success);
    color: white;
}

.step-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-align: center;
}

.step.active .step-label {
    color: var(--vendor-text-primary);
    font-weight: 600;
}

/* Step Content */
.step-content {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
}

.step-header {
    text-align: center;
    margin-bottom: var(--vendor-space-8);
}

.step-header h2 {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
}

.step-header p {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0;
}

/* Form Styles */
.onboarding-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: var(--vendor-space-6);
}

.form-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--vendor-space-6);
}

.form-label {
    display: block;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.form-label.required::after {
    content: '*';
    color: var(--vendor-danger);
    margin-left: var(--vendor-space-1);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface);
    transition: var(--vendor-transition);
    box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(102, 187, 106, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--vendor-space-8);
    gap: var(--vendor-space-4);
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-4) var(--vendor-space-6);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
    transition: var(--vendor-transition);
    background: none;
}

.btn-primary {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
    margin-left: auto;
}

.btn-primary:hover {
    background: var(--vendor-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-secondary {
    background: var(--vendor-surface);
    color: var(--vendor-text-secondary);
    border-color: var(--vendor-border);
}

.btn-secondary:hover {
    background: var(--vendor-gray-50);
    color: var(--vendor-text-primary);
}

.btn-large {
    padding: var(--vendor-space-5) var(--vendor-space-8);
    font-size: var(--vendor-font-size-lg);
}

/* Store Preview */
.store-preview {
    margin-bottom: var(--vendor-space-8);
}

.preview-card {
    background: var(--vendor-gray-50);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6);
    border: 1px solid var(--vendor-border);
}

.preview-header {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-4);
}

.store-avatar {
    width: 48px;
    height: 48px;
    background: var(--vendor-primary);
    border-radius: var(--vendor-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: var(--vendor-font-size-lg);
}

.store-info h3 {
    font-size: var(--vendor-font-size-lg);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1) 0;
}

.store-info p {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
}

.preview-product {
    padding: var(--vendor-space-4);
    background: white;
    border-radius: var(--vendor-radius-lg);
    border: 1px solid var(--vendor-border);
}

.product-name {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.product-price {
    font-size: var(--vendor-font-size-lg);
    font-weight: 700;
    color: var(--vendor-primary);
}

/* Share Section */
.share-section {
    margin-bottom: var(--vendor-space-8);
}

.store-link {
    margin-bottom: var(--vendor-space-6);
}

.link-container {
    display: flex;
    gap: var(--vendor-space-2);
}

.link-container .form-input {
    flex: 1;
    background: var(--vendor-gray-50);
}

.copy-btn {
    padding: var(--vendor-space-4);
    background: var(--vendor-primary);
    color: white;
    border: none;
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition);
}

.copy-btn:hover {
    background: var(--vendor-primary-dark);
}

.share-buttons {
    display: flex;
    gap: var(--vendor-space-4);
}

.share-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-4);
    border: none;
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--vendor-transition);
}

.share-btn.whatsapp {
    background: #25d366;
    color: white;
}

.share-btn.whatsapp:hover {
    background: #20b954;
}

.share-btn.facebook {
    background: #1877f2;
    color: white;
}

.share-btn.facebook:hover {
    background: #166fe5;
}

/* Completion Section */
.completion-section {
    text-align: center;
}

.success-message {
    margin-bottom: var(--vendor-space-8);
}

.success-message i {
    font-size: var(--vendor-font-size-4xl);
    color: var(--vendor-success);
    margin-bottom: var(--vendor-space-4);
}

.success-message h3 {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
}

.success-message p {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0;
}

/* Radio Groups */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-3);
}

.radio-option {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    cursor: pointer;
    transition: var(--vendor-transition);
}

.radio-option:hover {
    background: var(--vendor-gray-50);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--vendor-border);
    border-radius: var(--vendor-radius-full);
    position: relative;
    transition: var(--vendor-transition);
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--vendor-primary);
    background: var(--vendor-primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: var(--vendor-radius-full);
}

/* Responsive Design */
@media (max-width: 768px) {
  .onboarding-container {
    padding: 0;
    border-radius: 0 0 24px 24px;
    min-height: 100vh;
    background: rgba(255,255,255,0.95);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    backdrop-filter: blur(8px);
  }
  .step-content {
    border-radius: 24px 24px 0 0;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.08);
    min-height: 70vh;
    margin-bottom: 64px;
    padding-bottom: 32px;
  }
  .form-input, .form-select, .form-textarea {
    border: none;
    border-radius: 16px;
    background: #f7f8fa;
    font-size: 1.1rem;
    padding: 18px 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  }
  .form-actions {
    position: fixed;
    left: 0; right: 0; bottom: 0;
    background: #fff;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.10);
    border-radius: 24px 24px 0 0;
    padding: 16px 24px;
    z-index: 100;
    flex-direction: row;
    gap: 12px;
  }
  .progress-container {
    position: sticky;
    top: 0;
    z-index: 101;
    background: #fff;
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding-top: 8px;
  }
  .share-section, .completion-section {
    border-radius: 24px 24px 0 0;
    box-shadow: 0 -2px 16px rgba(0,0,0,0.10);
    background: #fff;
    margin-bottom: 0;
    padding-bottom: 80px;
  }
  .step-header {
    padding-top: 24px;
    padding-bottom: 12px;
  }
}
@media (max-width: 480px) {
  .onboarding-container, .step-content, .share-section, .completion-section {
    border-radius: 0;
    box-shadow: none;
    padding-left: 0;
    padding-right: 0;
  }
  .form-input, .form-select, .form-textarea {
    font-size: 1rem;
    padding: 14px 10px;
  }
  .form-actions {
    padding: 12px 8px;
  }
  .progress-container {
    border-radius: 0;
    box-shadow: none;
  }
}
/* App-bar style header for mobile */
@media (max-width: 768px) {
  .onboarding-container::before {
    content: '';
    display: block;
    position: fixed;
    top: 0; left: 0; right: 0;
    height: 56px;
    background: linear-gradient(90deg, #43e97b 0%, #38f9d7 100%);
    z-index: 200;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
  }
  .step-header {
    margin-top: 56px;
  }
}