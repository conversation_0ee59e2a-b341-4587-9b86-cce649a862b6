/* ===== MODERN VENDOR SUPPORT CSS - DESKTOP VERSION ===== */

/* ===== VARIABLES ===== */
:root {
    /* Colors */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    
    /* Gray Scale */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* UI Colors */
    --body-bg: #f8fafc;
    --card-bg: #ffffff;
    --border-color: #e5e7eb;
    --text: #1f2937;
    --text-muted: #6b7280;
    --text-light: #9ca3af;
    
    /* Spacing */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    
    /* Typography */
    --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-sm: 0.875rem;
    --font-base: 1rem;
    --font-lg: 1.125rem;
    --font-xl: 1.25rem;
    --font-2xl: 1.5rem;
    --font-3xl: 1.875rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    
    /* Borders */
    --radius-sm: 0.25rem;
    --radius: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition: all 0.2s ease;
}

/* ===== BASE STYLES ===== */
body {
    font-family: var(--font-sans);
    background-color: var(--body-bg);
    color: var(--text);
}

/* ===== LAYOUT ===== */
.settings-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-6);
}

/* ===== SUPPORT HEADER ===== */
.support-header {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-8);
    margin-bottom: var(--spacing-8);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.support-header:hover {
    box-shadow: var(--shadow-md);
}

.support-title {
    font-size: var(--font-3xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-2) 0;
}

.support-subtitle {
    font-size: var(--font-lg);
    color: var(--text-muted);
    margin: 0;
}

/* ===== CONTACT METHODS ===== */
.contact-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

.contact-card {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-6);
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--shadow);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.contact-icon {
    width: 64px;
    height: 64px;
    background-color: var(--primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-4);
    font-size: var(--font-2xl);
    color: var(--white);
    box-shadow: var(--shadow);
}

.contact-title {
    font-size: var(--font-lg);
    font-weight: 600;
    color: var(--text);
    margin: var(--spacing-3) 0 var(--spacing-2) 0;
}

.contact-info {
    font-size: var(--font-base);
    color: var(--text-muted);
    margin: 0;
}

/* ===== MAIN CONTENT ===== */
.support-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-8);
}

/* ===== FAQ SECTION ===== */
.faq-section {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-8);
    box-shadow: var(--shadow);
}

.faq-title {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-6) 0;
}

.faq-item {
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-4) 0;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: none;
    border: none;
    padding: var(--spacing-3);
    font-size: var(--font-base);
    font-weight: 600;
    color: var(--text);
    text-align: left;
    cursor: pointer;
    border-radius: var(--radius);
    transition: var(--transition);
}

.faq-question:hover {
    background-color: var(--gray-50);
}

.faq-answer {
    padding: var(--spacing-4) var(--spacing-3);
    font-size: var(--font-base);
    color: var(--text-muted);
    line-height: 1.6;
}

/* ===== SUPPORT FORM ===== */
.support-form-section {
    background-color: var(--card-bg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    padding: var(--spacing-8);
    box-shadow: var(--shadow);
}

.form-title {
    font-size: var(--font-2xl);
    font-weight: 700;
    color: var(--text);
    margin: 0 0 var(--spacing-6) 0;
}

.form-group {
    margin-bottom: var(--spacing-5);
}

.form-label {
    display: block;
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text);
    margin-bottom: var(--spacing-2);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-3) var(--spacing-4);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: var(--font-base);
    color: var(--text);
    background-color: var(--card-bg);
    transition: var(--transition);
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(22, 185, 16, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    width: 100%;
    padding: var(--spacing-4);
    background-color: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius);
    font-size: var(--font-base);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-2);
}

.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Alpine.js - Hide elements until Alpine initializes them */
[x-cloak] {
    display: none !important;
}
