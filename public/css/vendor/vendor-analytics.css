/* ===== MODERN ANALYTICS DASHBOARD 2025 ===== */
/* Ultra Clean & Professional Design - DESKTOP ONLY */

/* Hide desktop view on mobile */
@media (max-width: 768px) {
    .analytics-header {
        display: none !important;
    }
    .stats-grid {
        display: none !important;
    }
    .chart-grid {
        display: none !important;
    }
    .table-section {
        display: none !important;
    }
    .export-section {
        display: none !important;
    }
    
    /* Force show mobile elements */
    .mobile-date-range {
        display: block !important;
        visibility: visible !important;
    }
    
    .mobile-stats-section {
        display: block !important;
        visibility: visible !important;
    }
    
    .mobile-chart-container {
        display: block !important;
        visibility: visible !important;
    }
    
    .mobile-analytics-sections {
        display: block !important;
        visibility: visible !important;
    }
    
    /* Ensure analytics container has proper styling for mobile */
    .analytics-container {
        padding: 0 !important;
        max-width: 100% !important;
        margin: 0 !important;
        min-height: 100vh !important;
        background: #f8fafc !important;
    }
    
    /* Override any conflicting styles */
    * {
        box-sizing: border-box !important;
    }
}

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* ===== DESKTOP ONLY STYLES ===== */
@media (min-width: 769px) {
    /* Hide mobile elements on desktop */
    .mobile-date-range,
    .mobile-stats-container,
    .mobile-chart-container,
    .mobile-stats-section,
    .mobile-analytics-sections {
        display: none !important;
    }

:root {
    /* Modern Color Palette */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --accent: #06b6d4;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Neutral Palette */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 250ms ease;
    --transition-slow: 350ms ease;
}

/* === BASE STYLES === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === LAYOUT === */
.analytics-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

/* === HEADER === */
.analytics-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.analytics-title {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
    letter-spacing: -0.025em;
}

.analytics-subtitle {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin-bottom: var(--space-6);
    font-weight: 400;
}

.date-range-selector {
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.date-range-btn {
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.date-range-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.date-range-btn:hover::before {
    left: 100%;
}

.date-range-btn:hover {
    background: var(--gray-50);
    color: var(--gray-900);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.date-range-btn.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    box-shadow: var(--shadow-md);
}

/* === LOADING === */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.stat-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--primary-light));
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.stat-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.stat-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.stat-info p {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

/* Multicolor stat icons for analytics */
.stat-icon.revenue {
    background: #d1fae5;
    color: #10b981;
}

.stat-icon.orders {
    background: #DBEAFE;
    color: #2563EB;
}

.stat-icon.customers {
    background: #EDE9FE;
    color: #7C3AED;
}

.stat-icon.conversion {
    background: #FEF3C7;
    color: #D97706;
}

.stat-icon.growth {
    background: #CCFBF1;
    color: #0D9488;
}

.stat-icon.sales {
    background: #FED7AA;
    color: #EA580C;
}

.stat-icon.products {
    background: #E0E7FF;
    color: #4338CA;
}

.stat-icon.visitors {
    background: #FEF3C7;
    color: #D97706;
}

.stat-icon.sessions {
    background: #FCE7F3;
    color: #BE185D;
}

.stat-icon.profit {
    background: #D1FAE5;
    color: #059669;
}

.stat-icon.expenses {
    background: #FEF3C7;
    color: #D97706;
}

.stat-icon.pageviews {
    background: #CFFAFE;
    color: #0891B2;
}

.stat-icon.bounce-rate {
    background: #FEE2E2;
    color: #DC2626;
}

.stat-icon.avg-session {
    background: #ECFCCB;
    color: #65A30D;
}

.stat-card.revenue::before {
    background: linear-gradient(90deg, #10b981, #34d399);
}

.stat-card.orders::before {
    background: linear-gradient(90deg, #2563EB, #60a5fa);
}

.stat-card.customers::before {
    background: linear-gradient(90deg, #7C3AED, #a855f7);
}

.stat-card.conversion::before {
    background: linear-gradient(90deg, #D97706, #fbbf24);
}

.stat-card.growth::before {
    background: linear-gradient(90deg, #0D9488, #2dd4bf);
}

.stat-card.sales::before {
    background: linear-gradient(90deg, #EA580C, #fb923c);
}

.stat-card.products::before {
    background: linear-gradient(90deg, #4338CA, #8b5cf6);
}

.stat-card.visitors::before {
    background: linear-gradient(90deg, #D97706, #fbbf24);
}

.stat-card.sessions::before {
    background: linear-gradient(90deg, #BE185D, #f472b6);
}

.stat-card.profit::before {
    background: linear-gradient(90deg, #059669, #10b981);
}

.stat-card.expenses::before {
    background: linear-gradient(90deg, #D97706, #f59e0b);
}

.stat-card.pageviews::before {
    background: linear-gradient(90deg, #0891B2, #06b6d4);
}

.stat-card.bounce-rate::before {
    background: linear-gradient(90deg, #DC2626, #ef4444);
}

.stat-card.avg-session::before {
    background: linear-gradient(90deg, #65A30D, #84cc16);
}

.stat-trend {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-1);
    font-size: 0.75rem;
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius);
    z-index: 3;
}

.stat-trend.up {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.stat-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

/* Enhanced hover effects for multicolor stat cards */
.stat-card.revenue:hover {
    box-shadow: 0 10px 25px -5px rgba(16, 185, 129, 0.25), 0 4px 6px -2px rgba(16, 185, 129, 0.05);
}

.stat-card.orders:hover {
    box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.25), 0 4px 6px -2px rgba(37, 99, 235, 0.05);
}

.stat-card.customers:hover {
    box-shadow: 0 10px 25px -5px rgba(124, 58, 237, 0.25), 0 4px 6px -2px rgba(124, 58, 237, 0.05);
}

.stat-card.conversion:hover {
    box-shadow: 0 10px 25px -5px rgba(217, 119, 6, 0.25), 0 4px 6px -2px rgba(217, 119, 6, 0.05);
}

.stat-card.growth:hover {
    box-shadow: 0 10px 25px -5px rgba(13, 148, 136, 0.25), 0 4px 6px -2px rgba(13, 148, 136, 0.05);
}

.stat-card.sales:hover {
    box-shadow: 0 10px 25px -5px rgba(234, 88, 12, 0.25), 0 4px 6px -2px rgba(234, 88, 12, 0.05);
}

.stat-card.products:hover {
    box-shadow: 0 10px 25px -5px rgba(67, 56, 202, 0.25), 0 4px 6px -2px rgba(67, 56, 202, 0.05);
}

.stat-card.visitors:hover {
    box-shadow: 0 10px 25px -5px rgba(217, 119, 6, 0.25), 0 4px 6px -2px rgba(217, 119, 6, 0.05);
}

.stat-card.sessions:hover {
    box-shadow: 0 10px 25px -5px rgba(190, 24, 93, 0.25), 0 4px 6px -2px rgba(190, 24, 93, 0.05);
}

.stat-card.profit:hover {
    box-shadow: 0 10px 25px -5px rgba(5, 150, 105, 0.25), 0 4px 6px -2px rgba(5, 150, 105, 0.05);
}

.stat-card.expenses:hover {
    box-shadow: 0 10px 25px -5px rgba(217, 119, 6, 0.25), 0 4px 6px -2px rgba(217, 119, 6, 0.05);
}

.stat-card.pageviews:hover {
    box-shadow: 0 10px 25px -5px rgba(8, 145, 178, 0.25), 0 4px 6px -2px rgba(8, 145, 178, 0.05);
}

.stat-card.bounce-rate:hover {
    box-shadow: 0 10px 25px -5px rgba(220, 38, 38, 0.25), 0 4px 6px -2px rgba(220, 38, 38, 0.05);
}

.stat-card.avg-session:hover {
    box-shadow: 0 10px 25px -5px rgba(101, 163, 13, 0.25), 0 4px 6px -2px rgba(101, 163, 13, 0.05);
}

/* === CHART GRID === */
.chart-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-6);
    margin-bottom: var(--space-8);
}

.chart-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.chart-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: var(--space-2);
}

.chart-control {
    padding: var(--space-2) var(--space-3);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.chart-control:hover {
    background: var(--gray-50);
    color: var(--gray-900);
}

.chart-control.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.chart-body {
    padding: var(--space-6);
}

.chart-container {
    height: 300px;
    position: relative;
}

.chart-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.chart-link:hover {
    color: var(--primary-dark);
}

.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    color: var(--gray-500);
    font-size: 0.875rem;
}

/* === TABLE SECTION === */
.table-section {
    margin-bottom: var(--space-8);
}

.table-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-6);
}

.table-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.table-header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.table-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.table-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
}

.table-link:hover {
    color: var(--primary-dark);
}

.table-body {
    padding: var(--space-6);
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th {
    text-align: left;
    padding: var(--space-3);
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-900);
    border-bottom: 1px solid var(--gray-200);
}

.analytics-table td {
    padding: var(--space-3);
    font-size: 0.875rem;
    color: var(--gray-600);
    border-bottom: 1px solid var(--gray-100);
}

.analytics-table tr:hover {
    background: var(--gray-50);
}

/* === EXPORT SECTION === */
.export-section {
    margin-bottom: var(--space-8);
}

.export-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.export-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.export-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-1) 0;
}

.export-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin: 0;
}

.export-actions {
    display: flex;
    gap: var(--space-3);
}

.export-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--gray-700);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.export-btn:hover {
    background: var(--gray-50);
    color: var(--gray-900);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.export-btn.primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.export-btn.primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
}

/* === EMPTY STATES === */
.empty-cell {
    text-align: center;
    color: var(--gray-400);
    font-style: italic;
    padding: var(--space-8);
}

.empty-state {
    text-align: center;
    padding: var(--space-12);
    color: var(--gray-500);
}

.empty-state .empty-icon {
    font-size: 3rem;
    color: var(--gray-300);
    margin-bottom: var(--space-4);
}

.empty-state h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-2) 0;
}

.empty-state p {
    font-size: 0.875rem;
    color: var(--gray-500);
    margin: 0;
}

/* === RESPONSIVE === */
@media (max-width: 1024px) {
    .chart-grid {
        grid-template-columns: 1fr;
    }
    
    .table-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 900px) {
    .analytics-container {
        padding: var(--space-6) var(--space-4);
    }
    
    .export-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
    }
    
    .export-actions {
        flex-wrap: wrap;
    }
    
    .date-range-selector {
        justify-content: center;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

} /* End of @media (min-width: 769px) */
