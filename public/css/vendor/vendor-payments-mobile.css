/* ===== NATIVE MOBILE APP SETTINGS ===== */
/* iOS/Android Style Design with Green Theme */

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* Native Spacing */
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;

    /* Native Radius */
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-full: 50px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* Native Typography */
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
}

/* ===== BASE & LAYOUT ===== */
body {
    background-color: var(--background);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    color: var(--text-primary);
}

.settings-container {
    padding: 0;
}

/* ===== HEADER ===== */
.settings-header {
    background: var(--surface);
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top, 0px));
    border-bottom: 1px solid var(--border);
}

.settings-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.settings-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

/* ===== SETTINGS GROUP (CARD) ===== */
.settings-grid {
    padding: var(--spacing-2xl) var(--spacing-lg);
    display: grid;
    gap: var(--spacing-2xl);
}

.settings-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
}

.card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.card-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.card-body {
    padding: 0;
}

/* ===== SETTING ITEM ===== */
.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    flex: 1;
    padding-right: var(--spacing-md);
}

.setting-name {
    font-size: var(--text-base);
    font-weight: 500;
    color: var(--text-primary);
}

.setting-description {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-top: var(--spacing-sm);
}

.setting-control {
    flex-shrink: 0;
}

/* ===== NATIVE TOGGLE SWITCH ===== */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* ===== ACTION BUTTONS ===== */
.settings-actions {
    padding: var(--spacing-lg);
    background: var(--surface);
    border-top: 1px solid var(--border);
    position: sticky;
    bottom: 0;
    padding-bottom: calc(var(--spacing-lg) + env(safe-area-inset-bottom, 0px));
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
}

.btn-primary:active {
    background: var(--primary-dark);
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
}

.btn-secondary:active {
    background: var(--border);
}
