/* ===== VENDOR ONBOARDING CSS - MOBILE VERSION ===== */
/* iOS/Android Style Design with Green Theme */

:root {
    /* Green Theme Colors */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --primary-text: #064e3b;
    --success: #10b981;
    --danger: #ef4444;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* Native Spacing */
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;

    /* Native Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-2xl: 24px;
    --radius-full: 9999px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 6px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 12px rgba(0, 0, 0, 0.12);

    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
}

/* ===== BASE & LAYOUT ===== */
body {
    background-color: var(--background);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

.onboarding-container {
    padding: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ===== APP BAR HEADER ===== */
.onboarding-container::before {
    content: '';
    display: block;
    position: fixed;
    top: 0; 
    left: 0; 
    right: 0;
    height: 56px;
    background: var(--primary);
    z-index: 200;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    box-shadow: var(--shadow-md);
}

/* ===== PROGRESS BAR ===== */
.progress-container {
    position: sticky;
    top: 0;
    z-index: 100;
    padding: var(--spacing-sm);
    padding-top: calc(56px + var(--spacing-md));
    background: var(--surface);
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--border-light);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
}

.progress-fill {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 var(--spacing-sm);
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
    flex: 1;
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--surface-secondary);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--text-sm);
}

.step.active .step-number {
    background: var(--primary);
    color: var(--white);
    box-shadow: 0 0 0 4px rgba(22, 185, 16, 0.15);
}

.step.completed .step-number {
    background: var(--success);
    color: var(--white);
}

.step-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    font-weight: 500;
}

.step.active .step-label {
    color: var(--text-primary);
    font-weight: 600;
}

/* ===== STEP CONTENT ===== */
.step-content {
    flex: 1;
    background: var(--surface);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    margin-top: var(--spacing-lg);
    padding: var(--spacing-xl) var(--spacing-lg);
    box-shadow: var(--shadow-md);
    overflow-x: hidden;
    padding-bottom: 100px; /* Space for fixed bottom actions */
}

.step-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.step-header h2 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.step-header p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* ===== FORM STYLES ===== */
.onboarding-form {
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-xl);
}

.form-grid-2 {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-label.required::after {
    content: '*';
    color: var(--danger);
    margin-left: var(--spacing-sm);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    color: var(--text-primary);
    background: var(--surface-secondary);
    box-shadow: var(--shadow-sm);
    box-sizing: border-box;
    -webkit-appearance: none;
    appearance: none;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(22, 185, 16, 0.25);
}

.form-textarea {
    min-height: 120px;
    resize: none;
}

.form-select {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23475569' viewBox='0 0 16 16'%3E%3Cpath d='M8 12L2 6h12L8 12z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 40px;
}

/* ===== FORM ACTIONS ===== */
.form-actions {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    padding: var(--spacing-lg);
    background: var(--surface);
    border-top: 1px solid var(--border);
    box-shadow: var(--shadow-md);
    z-index: 100;
    gap: var(--spacing-md);
    align-items: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    height: 48px;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--primary);
    color: var(--white);
    flex: 1;
}

.btn-primary:active {
    background: var(--primary-dark);
    transform: scale(0.98);
}

.btn-secondary {
    background: var(--surface-secondary);
    color: var(--text-secondary);
    padding: 0 var(--spacing-lg);
}

.btn-secondary:active {
    background: var(--border);
    transform: scale(0.98);
}

/* ===== FINAL STEP ===== */
.share-section {
    padding: var(--spacing-xl) var(--spacing-lg);
    background: var(--surface);
    border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
    text-align: center;
}

.congrats-message {
    margin-bottom: var(--spacing-xl);
}

.congrats-message i {
    font-size: var(--text-2xl);
    color: var(--success);
    background: var(--primary-bg);
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
}

.congrats-message h3 {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.congrats-message p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.store-preview {
    background: var(--surface-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.store-info {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: var(--surface);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    text-align: left;
}

.store-info h3 {
    font-size: var(--text-base);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.store-info p {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

.preview-product {
    padding: var(--spacing-lg);
    background: var(--surface);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
    text-align: left;
}

.product-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    background: var(--surface-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-tertiary);
    flex-shrink: 0;
}

.product-details {
    flex: 1;
}

.product-details h4 {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.product-details p {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-sm) 0;
}

.product-price {
    font-size: var(--text-sm);
    font-weight: 700;
    color: var(--primary);
}

/* ===== SHARE LINK ===== */
.share-link-container {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xl);
    padding: 0 var(--spacing-sm);
    gap: var(--spacing-sm);
}

.store-link {
    flex: 1;
    padding: var(--spacing-md);
    background: var(--surface-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    font-size: var(--text-sm);
    color: var(--text-primary);
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.copy-btn {
    padding: var(--spacing-md);
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.copy-btn:active {
    background: var(--primary-dark);
    transform: scale(0.95);
}

.share-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    padding: 0 var(--spacing-sm);
}

.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    height: 52px;
}

.share-btn.whatsapp {
    background: #25D366;
    color: var(--white);
}

.share-btn.whatsapp:active {
    background: #128C7E;
    transform: scale(0.98);
}

.share-btn.facebook {
    background: #1877F2;
    color: var(--white);
}

.share-btn.facebook:active {
    background: #166FE5;
    transform: scale(0.98);
}

.completion-section {
    text-align: center;
}

.dashboard-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    width: 100%;
    height: 54px;
    background: var(--primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: var(--shadow-md);
    margin-top: var(--spacing-2xl);
}

.dashboard-btn:active {
    background: var(--primary-dark);
    transform: scale(0.98);
    box-shadow: var(--shadow-sm);
}

/* ===== RADIO GROUPS ===== */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.radio-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 22px;
    height: 22px;
    border: 2px solid var(--border);
    border-radius: var(--radius-full);
    position: relative;
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary);
    background: var(--primary);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 10px;
    height: 10px;
    background: var(--white);
    border-radius: var(--radius-full);
}
