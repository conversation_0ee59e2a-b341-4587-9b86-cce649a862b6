/* ===== ULTRA MODERN VENDOR DASHBOARD CSS ===== */
/* Completely redesigned dashboard with modern aesthetics */

/* === COMPACT WELCOME SECTION === */
.welcome-section {
    background: rgba(102, 187, 106, 0.1);
    border-radius: var(--vendor-radius-xl);
    padding: var(--vendor-space-6) var(--vendor-space-8);
    margin-bottom: var(--vendor-space-6);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.8);
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    min-height: 80px;
}

.welcome-section::before {
    content: '';
    position: absolute;
    top: -30%;
    right: -10%;
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--vendor-primary), var(--vendor-primary-dark));
    border-radius: 50%;
    opacity: 0.06;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.welcome-content {
    position: relative;
    z-index: 2;
    width: 100%;
}

.welcome-title {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    line-height: 1.3;
    color: var(--vendor-text-primary);
}

/* Welcome button styles removed - using simple one-liner welcome message */

/* === MODERN STATS CARDS === */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-4);
    margin-bottom: var(--vendor-space-8);
    width: 100%;
    box-sizing: border-box;
}

.stats-card {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    padding: var(--vendor-space-5);
    border: 1px solid var(--vendor-border);
    transition: var(--vendor-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
}





.stats-card.sales { --stats-color: var(--vendor-primary); }
.stats-card.orders { --stats-color: var(--vendor-accent); }
.stats-card.products { --stats-color: var(--vendor-success); }
.stats-card.customers { --stats-color: var(--vendor-warning); }

.stats-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-4);
}

.stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    background: var(--stats-color);
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    overflow: hidden;
}



.stats-trend {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    backdrop-filter: blur(10px);
}

.stats-trend.up {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-success);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.stats-trend.down {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.stats-value {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 800;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
    line-height: 1;
    font-family: var(--vendor-font-mono);
    color: var(--vendor-text-primary);
}

.stats-label {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* === DASHBOARD LAYOUT === */
.dashboard-section {
    margin-bottom: var(--vendor-space-8);
}

.dashboard-section:last-child {
    margin-bottom: 0;
}

/* === MODERN RECENT ORDERS SECTION === */
.orders-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    overflow: hidden;
    margin-bottom: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    width: 100%;
    box-sizing: border-box;
}

.section-header {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    border-bottom: 1px solid var(--vendor-border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--vendor-gray-50);
}

.section-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0;
    color: var(--vendor-text-primary);
}

.section-action {
    color: var(--vendor-primary);
    text-decoration: none;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    transition: var(--vendor-transition);
    padding: var(--vendor-space-2) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    border: 1px solid transparent;
}

.section-action:hover {
    background: var(--vendor-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.orders-table {
    width: 100%;
    border-collapse: collapse;
}

.orders-table th,
.orders-table td {
    padding: var(--vendor-space-5) var(--vendor-space-8);
    text-align: left;
    border-bottom: 1px solid var(--vendor-border);
}

.orders-table th {
    background: var(--vendor-gray-50);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-muted);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.orders-table td {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    font-weight: 500;
}

.orders-table tbody tr {
    transition: var(--vendor-transition);
}

.orders-table tbody tr:hover {
    background: var(--vendor-gray-50);
}

.order-id {
    font-weight: 600;
    color: var(--vendor-primary);
    font-family: var(--vendor-font-mono);
}

.order-status {
    display: inline-flex;
    align-items: center;
    padding: var(--vendor-space-2) var(--vendor-space-3);
    border-radius: var(--vendor-radius-full);
    font-size: var(--vendor-font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid;
}

.order-status.pending {
    background: rgba(217, 119, 6, 0.1);
    color: var(--vendor-warning);
    border-color: rgba(217, 119, 6, 0.2);
}

.order-status.processing {
    background: rgba(126, 217, 87, 0.1);
    color: var(--vendor-primary);
    border-color: rgba(126, 217, 87, 0.2);
}

.order-status.completed {
    background: rgba(76, 175, 80, 0.1);
    color: var(--vendor-success);
    border-color: rgba(76, 175, 80, 0.2);
}

.order-status.cancelled {
    background: rgba(239, 68, 68, 0.1);
    color: var(--vendor-danger);
    border-color: rgba(239, 68, 68, 0.2);
}

/* === MODERN PAGINATION === */
.pagination {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-5) var(--vendor-space-8);
    background: var(--vendor-gray-50);
    border-radius: 0 0 var(--vendor-radius-2xl) var(--vendor-radius-2xl);
}

.pagination-info {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-2);
}

.pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid var(--vendor-border);
    background: var(--vendor-surface);
    color: var(--vendor-text-muted);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    cursor: pointer;
    transition: var(--vendor-transition);
    font-weight: 500;
}

.pagination-btn:hover:not(:disabled) {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.pagination-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
    box-shadow: var(--vendor-shadow-md);
}

/* === MODERN QUICK ACTIONS === */
.quick-actions-section {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-8);
    box-shadow: var(--vendor-shadow-sm);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.9);
    width: 100%;
    box-sizing: border-box;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--vendor-space-4);
}

.quick-action-card {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-5);
    border-radius: var(--vendor-radius-xl);
    background: var(--vendor-gray-50);
    text-decoration: none;
    color: var(--vendor-text-primary);
    transition: var(--vendor-transition);
    border: 1px solid var(--vendor-border);
    position: relative;
    overflow: hidden;
}



.quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--vendor-radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--vendor-font-size-lg);
    color: white;
    flex-shrink: 0;
    box-shadow: var(--vendor-shadow-md);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.quick-action-card:nth-child(1) .quick-action-icon {
    background: var(--vendor-primary);
}
.quick-action-card:nth-child(2) .quick-action-icon {
    background: var(--vendor-primary);
}
.quick-action-card:nth-child(3) .quick-action-icon {
    background: var(--vendor-primary);
}
.quick-action-card:nth-child(4) .quick-action-icon {
    background: var(--vendor-primary);
}



.quick-action-content {
    flex: 1;
    position: relative;
    z-index: 1;
}

.quick-action-title {
    font-size: var(--vendor-font-size-base);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
    line-height: 1.3;
}

.quick-action-desc {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    line-height: 1.4;
    font-weight: 400;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--vendor-space-3);
    }

    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: var(--vendor-space-3);
    }
}

@media (max-width: 768px) {
  /* Mobile App-style Dashboard */
  .welcome-section {
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(102, 187, 106, 0.05) 100%);
    border-radius: 20px;
    margin: 0 0 24px 0;
    padding: 24px 20px;
    border: none;
    box-shadow: 0 4px 20px rgba(126, 217, 87, 0.15);
    backdrop-filter: blur(20px);
  }
  
  .welcome-title {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-bottom: 8px;
    color: #2d3748;
    font-weight: 700;
  }
  
  /* Mobile Stats Grid */
  .stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 24px;
  }
  
  .stats-card {
    background: #fff;
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }
  
  .stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #7ED957, #66BB6A);
    border-radius: 20px 20px 0 0;
  }
  
  .stats-card:hover,
  .stats-card:active {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
  }
  
  .stats-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
  }
  
  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: #fff;
    background: linear-gradient(135deg, #7ED957 0%, #66BB6A 100%);
    box-shadow: 0 4px 16px rgba(126, 217, 87, 0.3);
  }
  
  .stats-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(34, 197, 94, 0.1);
    color: #22c55e;
  }
  
  .stats-value {
    font-size: 2rem;
    font-weight: 800;
    color: #1a202c;
    margin-bottom: 4px;
    line-height: 1.1;
  }
  
  /* Mobile Dashboard Sections */
  .dashboard-section {
    margin-bottom: 24px;
  }
  
  .orders-section, .quick-actions-section {
    background: #fff;
    border-radius: 20px;
    padding: 24px 20px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
  }
  
  .section-header {
    margin-bottom: 20px;
  }
  
  .section-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
  }
  
  .section-action {
    color: #7ED957;
    font-weight: 600;
    text-decoration: none;
    font-size: 0.9rem;
    padding: 8px 16px;
    border-radius: 12px;
    background: rgba(126, 217, 87, 0.1);
    transition: all 0.2s ease;
  }
  
  .section-action:hover {
    background: rgba(126, 217, 87, 0.2);
    transform: translateY(-1px);
  }
  
  /* Mobile Quick Actions */
  .quick-actions-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
  }
  
  .quick-action-card {
    background: #fff;
    border-radius: 18px;
    padding: 20px 16px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    overflow: hidden;
  }
  
  .quick-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #7ED957, #66BB6A);
    border-radius: 18px 18px 0 0;
  }
  
  .quick-action-card:hover,
  .quick-action-card:active {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
  }
  
  .quick-action-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    background: linear-gradient(135deg, #7ED957 0%, #66BB6A 100%);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-bottom: 12px;
    box-shadow: 0 4px 16px rgba(126, 217, 87, 0.3);
  }
  
  .quick-action-title {
    font-size: 0.95rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 4px;
    line-height: 1.2;
  }
  
  .quick-action-desc {
    font-size: 0.8rem;
    color: #718096;
    line-height: 1.3;
  }
  
  /* Mobile Empty State */
  .empty-state {
    padding: 40px 20px;
    text-align: center;
  }
  
  .empty-icon {
    font-size: 3rem;
    color: #e2e8f0;
    margin-bottom: 16px;
  }
  
  .empty-state h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #4a5568;
    margin-bottom: 8px;
  }
  
  .empty-state p {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 20px;
    line-height: 1.4;
  }
  
  .empty-state .btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #7ED957 0%, #66BB6A 100%);
    color: #fff;
    text-decoration: none;
    border-radius: 16px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 16px rgba(126, 217, 87, 0.3);
    transition: all 0.3s ease;
  }
  
  .empty-state .btn:hover,
  .empty-state .btn:active {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(126, 217, 87, 0.4);
  }
}
}
@media (max-width: 480px) {
  /* Extra small screens optimization */
  .welcome-section {
    border-radius: 16px;
    margin: 0 0 20px 0;
    padding: 20px 16px;
  }
  
  .welcome-title {
    font-size: 1.3rem;
  }
  
  .stats-grid {
    gap: 12px;
    margin-bottom: 20px;
  }
  
  .stats-card {
    border-radius: 16px;
    padding: 16px;
    min-height: 80px;
  }
  
  .stats-value {
    font-size: 1.6rem;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .quick-action-card {
    border-radius: 16px;
    padding: 16px;
    min-height: 80px;
    flex-direction: row;
    text-align: left;
    align-items: center;
    justify-content: flex-start;
  }
  
  .quick-action-icon {
    width: 40px;
    height: 40px;
    margin-bottom: 0;
    margin-right: 16px;
    font-size: 1.1rem;
  }
  
  .quick-action-content {
    flex: 1;
  }
  
  .quick-action-title {
    font-size: 0.9rem;
    margin-bottom: 2px;
  }
  
  .quick-action-desc {
    font-size: 0.75rem;
  }
  
  .orders-section, .quick-actions-section {
    border-radius: 16px;
    padding: 20px 16px;
  }
  
  .section-title {
    font-size: 1.2rem;
  }
  
  .dashboard-section {
    margin-bottom: 20px;
  }
}
/* === EMPTY STATE === */
.empty-state {
    text-align: center;
    padding: var(--vendor-space-12) var(--vendor-space-8);
    color: var(--vendor-text-muted);
}

.empty-icon {
    font-size: var(--vendor-font-size-4xl);
    color: var(--vendor-gray-400);
    margin-bottom: var(--vendor-space-4);
}

.empty-state h3 {
    font-size: var(--vendor-font-size-xl);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
}

.empty-state p {
    font-size: var(--vendor-font-size-base);
    color: var(--vendor-text-muted);
    margin: 0 0 var(--vendor-space-6) 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state .btn {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-6);
    background: var(--vendor-primary);
    color: white;
    text-decoration: none;
    border-radius: var(--vendor-radius-lg);
    font-weight: 600;
    transition: var(--vendor-transition);
}

.empty-state .btn:hover {
    background: var(--vendor-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}
