/* ===== MODERN VENDOR SETTINGS CSS ===== */
/* Consistent with vendor dashboard design patterns */

/* === SETTINGS HEADER === */
.settings-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--vendor-space-8);
    padding: var(--vendor-space-6) var(--vendor-space-8);
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    box-shadow: var(--vendor-shadow-sm);
}

.settings-header-left h1 {
    font-size: var(--vendor-font-size-2xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-1) 0;
}

.settings-header-left p {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
}

.settings-header-right {
    display: flex;
    gap: var(--vendor-space-3);
}

/* === SETTINGS LAYOUT === */
.settings-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: var(--vendor-space-6);
    align-items: start;
}

/* === SETTINGS SIDEBAR === */
.settings-sidebar {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    padding: var(--vendor-space-6);
    box-shadow: var(--vendor-shadow-sm);
    position: sticky;
    top: var(--vendor-space-6);
}

.settings-nav {
    display: flex;
    flex-direction: column;
    gap: var(--vendor-space-2);
}

.settings-nav-item {
    display: flex;
    align-items: center;
    gap: var(--vendor-space-3);
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border-radius: var(--vendor-radius-lg);
    color: var(--vendor-text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--vendor-transition);
    cursor: pointer;
}

.settings-nav-item:hover {
    background: var(--vendor-gray-50);
    color: var(--vendor-text-primary);
}

.settings-nav-item.active {
    background: var(--vendor-primary);
    color: white;
    box-shadow: var(--vendor-shadow-md);
}

.settings-nav-icon {
    font-size: var(--vendor-font-size-lg);
    width: 20px;
    text-align: center;
}

/* === SETTINGS CONTENT === */
.settings-content {
    background: var(--vendor-surface);
    border-radius: var(--vendor-radius-2xl);
    border: 1px solid var(--vendor-border);
    box-shadow: var(--vendor-shadow-sm);
    overflow: hidden;
}

.settings-section {
    padding: var(--vendor-space-8);
    display: none;
}

.settings-section.active,
.settings-section[x-show] {
    display: block;
}

.settings-section-header {
    margin-bottom: var(--vendor-space-6);
    padding-bottom: var(--vendor-space-4);
    border-bottom: 1px solid var(--vendor-border);
}

.settings-section-title {
    font-size: var(--vendor-font-size-xl);
    font-weight: 700;
    color: var(--vendor-text-primary);
    margin: 0 0 var(--vendor-space-2) 0;
}

.settings-section-description {
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-muted);
    margin: 0;
}

/* === FORM STYLES === */
.form-group {
    margin-bottom: var(--vendor-space-6);
}

.form-grid-2 {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--vendor-space-6);
}

.form-label {
    display: block;
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-2);
}

.form-label.required::after {
    content: '*';
    color: var(--vendor-danger);
    margin-left: var(--vendor-space-1);
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: var(--vendor-space-3) var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    color: var(--vendor-text-primary);
    background: var(--vendor-surface);
    transition: var(--vendor-transition);
    box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--vendor-primary);
    box-shadow: 0 0 0 3px rgba(102, 187, 106, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-help {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
    margin-top: var(--vendor-space-2);
}

/* === FILE UPLOAD === */
.file-upload {
    position: relative;
    border: 2px dashed var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    padding: var(--vendor-space-8);
    text-align: center;
    transition: var(--vendor-transition);
}

.file-upload:hover {
    border-color: var(--vendor-primary);
    background: rgba(102, 187, 106, 0.02);
}

.file-upload-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--vendor-space-2);
    cursor: pointer;
}

.file-upload-icon {
    font-size: var(--vendor-font-size-2xl);
    color: var(--vendor-primary);
}

.file-upload-text {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
}

.file-upload-hint {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
}

/* === TOGGLE SWITCHES === */
.toggle-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--vendor-space-4);
    border: 1px solid var(--vendor-border);
    border-radius: var(--vendor-radius-lg);
    background: var(--vendor-gray-50);
}

.toggle-info {
    flex: 1;
}

.toggle-title {
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    color: var(--vendor-text-primary);
    margin-bottom: var(--vendor-space-1);
}

.toggle-description {
    font-size: var(--vendor-font-size-xs);
    color: var(--vendor-text-muted);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 48px;
    height: 24px;
    flex-shrink: 0;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--vendor-gray-300);
    transition: var(--vendor-transition);
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background: white;
    transition: var(--vendor-transition);
    border-radius: 50%;
    box-shadow: var(--vendor-shadow-sm);
}

input:checked + .toggle-slider {
    background: var(--vendor-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(24px);
}

/* === BUTTONS === */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--vendor-space-2);
    padding: var(--vendor-space-3) var(--vendor-space-5);
    border-radius: var(--vendor-radius-lg);
    font-size: var(--vendor-font-size-sm);
    font-weight: 600;
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
    transition: var(--vendor-transition);
    background: none;
}

.btn-primary {
    background: var(--vendor-primary);
    color: white;
    border-color: var(--vendor-primary);
}

.btn-primary:hover {
    background: var(--vendor-primary-dark);
    border-color: var(--vendor-primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--vendor-shadow-md);
}

.btn-secondary {
    background: var(--vendor-surface);
    color: var(--vendor-text-secondary);
    border-color: var(--vendor-border);
}

.btn-secondary:hover {
    background: var(--vendor-gray-50);
    color: var(--vendor-text-primary);
    border-color: var(--vendor-gray-300);
}

/* === SETTINGS ACTIONS === */
.settings-actions {
    padding: var(--vendor-space-6) var(--vendor-space-8);
    border-top: 1px solid var(--vendor-border);
    background: var(--vendor-gray-50);
    display: flex;
    gap: var(--vendor-space-3);
    justify-content: flex-end;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 1024px) {
    .settings-layout {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
    
    .settings-sidebar {
        position: static;
        order: -1;
    }
    
    .settings-nav {
        flex-direction: row;
        overflow-x: auto;
        gap: var(--vendor-space-1);
    }
    
    .settings-nav-item {
        white-space: nowrap;
        flex-shrink: 0;
    }
}

@media (max-width: 768px) {
    .settings-header {
        flex-direction: column;
        gap: var(--vendor-space-4);
        align-items: stretch;
        padding: var(--vendor-space-5) var(--vendor-space-6);
    }
    
    .settings-header-right {
        justify-content: center;
    }
    
    .form-grid-2 {
        grid-template-columns: 1fr;
        gap: var(--vendor-space-4);
    }
    
    .settings-section {
        padding: var(--vendor-space-6);
    }
    
    .settings-actions {
        padding: var(--vendor-space-5) var(--vendor-space-6);
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .settings-header {
        padding: var(--vendor-space-4) var(--vendor-space-5);
    }
    
    .settings-section {
        padding: var(--vendor-space-5);
    }
    
    .settings-actions {
        padding: var(--vendor-space-4) var(--vendor-space-5);
    }
    
    .btn {
        justify-content: center;
    }
}