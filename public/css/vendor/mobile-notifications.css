/* ===== MOBILE NOTIFICATIONS PAGE - <PERSON><PERSON>ERN APP-LIKE DESIGN ===== */
/* Based on the successful mobile orders page design patterns */

/* Default: Hide mobile layout, show desktop */
.mobile-notifications-container {
  display: none;
}

.desktop-notifications-container {
  display: block;
}

/* Mobile-First CSS Variables */
:root {
  /* Mobile Notification Variables */
  --mobile-notification-bg: #f8fafc;
  --mobile-notification-card-bg: #ffffff;
  --mobile-notification-card-shadow: 0 4px 16px rgba(0,0,0,0.08);
  --mobile-notification-card-radius: 20px;
  --mobile-notification-spacing: 16px;
  --mobile-filter-height: 60px;
  
  /* Notification Type Colors */
  --notification-order: #7ED957;
  --notification-order-bg: rgba(126, 217, 87, 0.1);
  --notification-review: #FFB800;
  --notification-review-bg: rgba(255, 184, 0, 0.1);
  --notification-payment: #4CAF50;
  --notification-payment-bg: rgba(76, 175, 80, 0.1);
  --notification-alert: #FF5722;
  --notification-alert-bg: rgba(255, 87, 34, 0.1);
  
  /* Touch Targets */
  --mobile-touch-target: 48px;
  --mobile-button-height: 44px;
  --mobile-icon-size: 20px;
  
  /* Animations */
  --mobile-animation-fast: 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  --mobile-animation-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --mobile-animation-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
  
  /* Show mobile layout, hide desktop */
  .mobile-notifications-container {
    display: block !important;
  }
  
  .desktop-notifications-container {
    display: none !important;
  }
  
  /* Page Container - Remove all padding */
  .vendor-content {
    background: var(--mobile-notification-bg);
    min-height: 100vh;
    padding: 0 !important;
    margin: 0 !important;
    overflow-x: hidden;
    position: relative; /* Ensure proper stacking */
  }
  
  /* Remove page content padding on mobile */
  .page-content {
    padding: 0 !important;
    margin: 0 !important;
    background: var(--mobile-notification-bg);
  }
  
  /* Target vendor main wrapper */
  .vendor-main {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  /* Ensure mobile container takes full space */
  .mobile-notifications-container {
    width: 100%;
    min-height: 100vh;
    background: var(--mobile-notification-bg);
    position: relative;
    /* Ensure content starts below fixed header */
    padding-top: 0; /* No additional padding since filter section handles header spacing */
  }
  
  /* ===== MOBILE FILTER SECTION ===== */
  .mobile-filter-section {
    padding: 84px 16px 40px 16px; /* 84px top padding = 64px header + 20px spacing, 40px bottom for spacing */
    background: white;
    border-radius: 0; /* Remove top border radius since it's at the top */
    margin-top: 0;
    position: relative;
    z-index: 10;
    box-shadow: 0 -4px 16px rgba(0,0,0,0.05);
  }
  
  .mobile-filter-chips {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    padding: 4px 0;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  
  .mobile-filter-chips::-webkit-scrollbar {
    display: none;
  }
  
  .mobile-filter-chip {
    background: #f1f5f9;
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 600;
    color: #64748b;
    white-space: nowrap;
    transition: var(--mobile-animation-fast);
    min-width: max-content;
  }
  
  .mobile-filter-chip.active {
    background: #7ED957;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(126, 217, 87, 0.3);
  }
  
  .mobile-filter-chip:active {
    transform: scale(0.95);
  }
  
  /* ===== MOBILE NOTIFICATIONS LIST ===== */
  .mobile-notifications-list {
    padding: 32px 16px 100px 16px; /* Increased top padding for better spacing between filter and cards */
    background: var(--mobile-notification-bg);
    margin-top: 0; /* Remove any default margin */
    min-height: calc(100vh - 120px); /* Reduced since no header */
  }
  
  /* Native scroll hide */
  .native-scroll-hide {
    overflow-y: scroll;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .native-scroll-hide::-webkit-scrollbar {
    display: none;
  }
  
  .mobile-notification-card {
    background: var(--mobile-notification-card-bg);
    border-radius: var(--mobile-notification-card-radius);
    box-shadow: var(--mobile-notification-card-shadow);
    margin-bottom: 16px;
    overflow: hidden;
    transition: var(--mobile-animation-normal);
    position: relative;
    border-left: 4px solid transparent;
  }
  
  .mobile-notification-card.unread {
    border-left-color: #7ED957;
    background: linear-gradient(90deg, rgba(126, 217, 87, 0.05) 0%, #fff 100%);
  }
  
  .mobile-notification-card.urgent {
    border-left-color: #FF5722;
    background: linear-gradient(90deg, rgba(255, 87, 34, 0.05) 0%, #fff 100%);
    box-shadow: 0 4px 20px rgba(255, 87, 34, 0.15);
  }
  
  .mobile-notification-card:active {
    transform: scale(0.98);
  }
  
  /* Notification Card Content */
  .mobile-card-content {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }
  
  .mobile-notification-icon {
    width: 48px;
    height: 48px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: white;
    flex-shrink: 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  .mobile-notification-icon.order-icon {
    background: linear-gradient(135deg, var(--notification-order) 0%, #66BB6A 100%);
  }
  
  .mobile-notification-icon.review-icon {
    background: linear-gradient(135deg, var(--notification-review) 0%, #FFA000 100%);
  }
  
  .mobile-notification-icon.payment-icon {
    background: linear-gradient(135deg, var(--notification-payment) 0%, #43A047 100%);
  }
  
  .mobile-notification-icon.alert-icon {
    background: linear-gradient(135deg, var(--notification-alert) 0%, #E64A19 100%);
  }
  
  .mobile-notification-body {
    flex: 1;
    min-width: 0;
  }
  
  .mobile-notification-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;
  }
  
  .mobile-notification-title {
    font-size: 1.1rem;
    font-weight: 700;
    color: #1e293b;
    line-height: 1.3;
    margin: 0;
  }
  
  .mobile-notification-time {
    font-size: 0.8rem;
    color: #64748b;
    font-weight: 500;
    white-space: nowrap;
    margin-left: 12px;
  }
  
  .mobile-notification-message {
    font-size: 0.95rem;
    color: #475569;
    line-height: 1.4;
    margin-bottom: 12px;
  }
  
  .mobile-notification-meta {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .mobile-notification-type {
    background: #f1f5f9;
    color: #64748b;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .mobile-unread-dot {
    width: 8px;
    height: 8px;
    background: #7ED957;
    border-radius: 50%;
    flex-shrink: 0;
  }
  
  .mobile-urgent-badge {
    background: linear-gradient(135deg, #FF5722 0%, #E64A19 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(255, 87, 34, 0.3);
  }
  
  .mobile-notification-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .mobile-quick-action {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    border: none;
    background: #f8fafc;
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: var(--mobile-animation-fast);
  }
  
  .mobile-quick-action:active {
    transform: scale(0.9);
  }
  
  .mobile-quick-action:nth-child(1) {
    background: var(--notification-order-bg);
    color: var(--notification-order);
  }
  
  .mobile-quick-action.delete {
    background: rgba(255, 87, 34, 0.1);
    color: #FF5722;
  }
  
  /* ===== PULL TO REFRESH ===== */
  .mobile-pull-refresh {
    text-align: center;
    padding: 20px;
    color: #64748b;
    font-size: 0.875rem;
    opacity: 0;
    transform: translateY(-20px);
    transition: var(--mobile-animation-normal);
  }
  
  .mobile-pull-refresh.active {
    opacity: 1;
    transform: translateY(0);
  }
  
  .mobile-refresh-icon {
    display: inline-block;
    margin-right: 8px;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  /* ===== EMPTY STATE ===== */
  .mobile-notifications-empty {
    text-align: center;
    padding: 60px 20px;
    color: #64748b;
  }
  
  .mobile-empty-icon {
    font-size: 4rem;
    color: #cbd5e1;
    margin-bottom: 24px;
  }
  
  .mobile-empty-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #475569;
    margin-bottom: 8px;
  }
  
  .mobile-empty-description {
    font-size: 0.875rem;
    line-height: 1.6;
    max-width: 280px;
    margin: 0 auto;
  }
  
  /* ===== LOADING STATES ===== */
  .mobile-loading-card {
    background: white;
    border-radius: var(--mobile-notification-card-radius);
    box-shadow: var(--mobile-notification-card-shadow);
    margin-bottom: 16px;
    padding: 20px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    animation: pulse 1.5s ease-in-out infinite alternate;
  }
  
  .mobile-loading-icon {
    width: 48px;
    height: 48px;
    background: #f1f5f9;
    border-radius: 16px;
    flex-shrink: 0;
  }
  
  .mobile-loading-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .mobile-loading-bar {
    height: 12px;
    background: #f1f5f9;
    border-radius: 6px;
  }
  
  .mobile-loading-bar.short {
    width: 60%;
  }
  
  .mobile-loading-bar.medium {
    width: 80%;
  }
  
  .mobile-loading-bar.long {
    width: 100%;
  }
  
  @keyframes pulse {
    0% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  /* ===== RESPONSIVE ADJUSTMENTS ===== */
  @media (max-width: 480px) {
    .mobile-notifications-header {
      padding: 16px 12px 12px 12px;
    }
    
    .mobile-notifications-title {
      font-size: 1.5rem;
    }
    
    .mobile-notifications-list {
      padding: 0 12px 100px 12px;
    }
    
    .mobile-notification-card {
      margin-bottom: 12px;
    }
    
    .mobile-card-content {
      padding: 16px;
    }
  }
}

/* ===== DESKTOP STYLES ===== */
.desktop-notifications-container {
  padding: 24px;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(102, 187, 106, 0.05) 100%);
  border-radius: 24px;
  border: 1px solid rgba(126, 217, 87, 0.2);
}

.notifications-header-left h1 {
  font-size: 2rem;
  font-weight: 800;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.notifications-header-left p {
  color: #64748b;
  margin: 0;
  font-size: 1.1rem;
}

.notifications-header-right {
  display: flex;
  gap: 16px;
}

.btn {
  padding: 12px 24px;
  border-radius: 12px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: var(--mobile-animation-fast);
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn.btn-secondary {
  background: white;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.btn.btn-primary {
  background: linear-gradient(135deg, #7ED957 0%, #66BB6A 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(126, 217, 87, 0.3);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.desktop-notifications-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.desktop-notification-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f1f5f9;
  display: flex;
  align-items: flex-start;
  gap: 20px;
  transition: var(--mobile-animation-fast);
  cursor: pointer;
}

.desktop-notification-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.desktop-notification-card.unread {
  border-left: 4px solid #7ED957;
  background: linear-gradient(90deg, rgba(126, 217, 87, 0.02) 0%, #fff 100%);
}

.desktop-notification-card.urgent {
  border-left: 4px solid #FF5722;
  background: linear-gradient(90deg, rgba(255, 87, 34, 0.02) 0%, #fff 100%);
}

/* Hide Desktop on Mobile */
@media (max-width: 768px) {
  .desktop-notifications-container {
    display: none !important;
  }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.mobile-notification-card:focus,
.desktop-notification-card:focus {
  outline: 2px solid #7ED957;
  outline-offset: 2px;
}
