/* Vendor Profile Mobile Styles */
:root {
    --primary-color: #10b981;
    --background-color: #f9fafb;
    --card-background: #ffffff;
    --text-color: #1f2937;
    --text-muted: #6b7280;
    --border-color: #e5e7eb;
    --spacing-unit: 16px;
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.profile-container {
    padding: var(--spacing-unit);
}

.profile-header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: var(--spacing-unit);
}

.profile-card {
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: var(--spacing-unit);
    padding: var(--spacing-unit);
}

.form-section h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: var(--spacing-unit);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.form-group {
    margin-bottom: var(--spacing-unit);
}

.form-group label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="tel"] {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 16px;
}

.form-group input[readonly] {
    background-color: #f3f4f6;
    cursor: not-allowed;
}

.form-group small {
    font-size: 13px;
    color: var(--text-muted);
    margin-top: 4px;
    display: block;
}

.btn {
    display: inline-block;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    width: 100%;
}

.btn-secondary {
    background-color: var(--border-color);
    color: var(--text-color);
    width: 100%;
}

.profile-actions {
    margin-top: var(--spacing-unit);
}
