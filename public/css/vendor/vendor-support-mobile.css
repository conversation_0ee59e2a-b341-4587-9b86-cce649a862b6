/* ===== NATIVE MOBILE APP SUPPORT ===== */
/* iOS/Android Style Design with Green Theme */

:root {
    /* Green Theme Colors */
    --primary: #16b910;
    --primary-light: #34d349;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --primary-text: #064e3b;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* Native Spacing */
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;

    /* Native Radius */
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-full: 50px;

    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);

    /* Native Typography */
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
}

/* ===== BASE & LAYOUT ===== */
body {
    background-color: var(--background);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
    color: var(--text-primary);
    margin: 0;
    padding: 0;
}

.settings-container {
    padding: 0;
}

/* ===== HEADER ===== */
.support-header {
    background: var(--surface);
    padding: var(--spacing-lg);
    padding-top: calc(var(--spacing-lg) + env(safe-area-inset-top, 0px));
    border-bottom: 1px solid var(--border);
    text-align: center;
}

.support-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.support-subtitle {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* ===== CONTACT CARDS ===== */
.contact-grid {
    padding: var(--spacing-lg);
    display: grid;
    gap: var(--spacing-lg);
    grid-template-columns: 1fr;
}

.contact-card {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
}

.contact-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    margin-right: var(--spacing-lg);
    flex-shrink: 0;
}

.contact-info-container {
    flex: 1;
}

.contact-title {
    font-size: var(--text-base);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 var(--spacing-sm) 0;
}

.contact-info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin: 0;
}

/* ===== SUPPORT CONTENT ===== */
.support-content {
    padding: var(--spacing-lg);
    display: grid;
    gap: var(--spacing-2xl);
    grid-template-columns: 1fr;
}

/* ===== FAQ SECTION ===== */
.faq-section {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.faq-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    margin: 0;
}

.faq-item {
    border-bottom: 1px solid var(--border-light);
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: none;
    border: none;
    text-align: left;
    font-size: var(--text-base);
    font-weight: 500;
    color: var(--text-primary);
}

.faq-answer {
    padding: 0 var(--spacing-lg) var(--spacing-lg);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    line-height: 1.5;
}

/* ===== SUPPORT FORM ===== */
.support-form-section {
    background: var(--surface);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-card);
    overflow: hidden;
}

.form-title {
    font-size: var(--text-lg);
    font-weight: 600;
    color: var(--text-primary);
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    margin: 0;
}

form {
    padding: var(--spacing-lg);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    color: var(--text-primary);
    background: var(--background);
    box-sizing: border-box;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(22, 185, 16, 0.1);
}

.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* ===== ACTION BUTTONS ===== */
.submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--text-base);
    font-weight: 600;
    background: var(--primary);
    color: var(--white);
    cursor: pointer;
    gap: var(--spacing-sm);
    transition: all 0.2s ease;
}

.submit-btn:active {
    background: var(--primary-dark);
    transform: scale(0.98);
}

/* ===== NATIVE TOGGLE SWITCH ===== */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

input:checked + .toggle-slider {
    background-color: var(--primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}
