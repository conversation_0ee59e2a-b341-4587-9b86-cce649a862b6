/* ===== NATIVE MOBILE APP ANALYTICS ===== */
/* iOS/Android Style Design with Green Theme - Mobile Only */

/* Hide mobile view on desktop */
@media (min-width: 769px) {
    /* Hide all mobile specific elements on desktop */
    .mobile-date-range,
    .mobile-stats-section,
    .mobile-chart-container,
    .mobile-analytics-sections {
        display: none !important;
    }
}

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;
    
    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    
    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
    
    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    
    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }
    
    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif !important;
        background: var(--background) !important;
        color: var(--text-primary) !important;
        font-size: var(--text-base) !important;
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    
    /* ===== NATIVE APP LAYOUT ===== */
    .analytics-container {
        min-height: 100vh !important;
        background: var(--background) !important;
        padding-top: calc(var(--safe-area-top) + 10px) !important; /* Reduced space for header */
        padding-bottom: var(--safe-area-bottom) !important; /* Remove extra bottom padding */
        position: relative !important;
        max-width: none !important;
        margin: 0 !important;
        padding-left: 4px !important; /* Reduced left padding */
        padding-right: 4px !important; /* Reduced right padding */
        z-index: 1 !important; /* Ensure it's below header/footer */
    }
    
    /* Hide desktop elements on mobile */
    .analytics-header,
    .stats-grid,
    .chart-grid,
    .table-section,
    .export-section {
        display: none !important;
    }
    
    /* Ensure mobile elements are visible */
    .mobile-date-range,
    .mobile-stats-section,
    .mobile-chart-container,
    .mobile-analytics-sections {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 10 !important; /* Below header/footer but above content */
    }
    
    /* ===== MOBILE DATE RANGE SELECTOR ===== */
    .mobile-date-range {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin: var(--spacing-md) 4px var(--spacing-lg) 4px; /* Reduced left/right margin */
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        margin-top: var(--spacing-md); /* Add top margin for header spacing */
    }
    
    .mobile-date-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        text-align: center;
    }
    
    .mobile-date-buttons {
        display: flex;
        gap: var(--spacing-xs);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding: var(--spacing-xs) 0;
    }
    
    .mobile-date-buttons::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-date-btn {
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--text-sm);
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 80px;
        text-align: center;
    }
    
    .mobile-date-btn:active {
        transform: scale(0.95);
    }
    
    .mobile-date-btn.active {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: var(--white);
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }
    
    /* ===== MOBILE CHART CONTAINER ===== */
    .mobile-chart-container {
        padding: 0 var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .mobile-chart-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        overflow: hidden;
    }
    
    .mobile-chart-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        background: linear-gradient(135deg, var(--primary-bg), var(--surface));
    }
    
    .mobile-chart-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
        text-align: center;
    }
    
    .mobile-chart-controls {
        display: flex;
        gap: var(--spacing-xs);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding: var(--spacing-xs) 0;
    }
    
    .mobile-chart-controls::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-chart-control {
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--text-xs);
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 70px;
        text-align: center;
    }
    
    .mobile-chart-control:active {
        transform: scale(0.95);
    }
    
    .mobile-chart-control.active {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: var(--white);
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }
    
    .mobile-chart-body {
        padding: var(--spacing-lg);
        background: var(--surface);
    }
    
    .mobile-chart-canvas {
        position: relative;
        height: 250px;
        width: 100%;
    }
    
    .mobile-chart-canvas canvas {
        width: 100% !important;
        height: 100% !important;
    }
    
    /* ===== MOBILE LOADING STATES ===== */
    .mobile-loading {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-3xl);
        gap: var(--spacing-md);
    }
    
    .mobile-loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--border);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: mobile-spin 1s linear infinite;
    }
    
    .mobile-loading-text {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
        text-align: center;
    }
    
    @keyframes mobile-spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* ===== MOBILE TIMESTAMP STYLES ===== */
    .mobile-timestamp {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        margin-top: var(--spacing-xs);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
        text-align: center;
    }
    
    .mobile-date-selected {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        text-align: center;
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--primary-bg);
        border-radius: var(--radius-md);
        margin-bottom: var(--spacing-md);
        font-weight: 500;
    }
    
    .mobile-time-range-display {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        padding: var(--spacing-xs) var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-full);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-time-range-icon {
        font-size: var(--text-xs);
        color: var(--primary);
    }
    
    .mobile-time-range-text {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    /* ===== MOBILE REVENUE TREND STYLES ===== */
    .mobile-revenue-trend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
        margin-top: var(--spacing-md);
        padding: var(--spacing-md);
        background: linear-gradient(135deg, var(--primary-bg), var(--surface));
        border-radius: var(--radius-lg);
        border: 1px solid var(--border-light);
    }
    
    .mobile-revenue-trend-icon {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: var(--primary);
        color: var(--white);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-sm);
    }
    
    .mobile-revenue-trend-content {
        flex: 1;
        text-align: center;
    }
    
    .mobile-revenue-trend-title {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-revenue-trend-value {
        font-size: var(--text-lg);
        font-weight: 700;
        color: var(--primary);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    .mobile-revenue-trend-change {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        margin-top: var(--spacing-xs);
        font-size: var(--text-xs);
        font-weight: 600;
    }
    
    .mobile-revenue-trend-change.positive {
        color: var(--success);
    }
    
    .mobile-revenue-trend-change.negative {
        color: var(--error);
    }
    
    .mobile-revenue-trend-change i {
        font-size: 10px;
    }
    
    /* ===== MOBILE CHART LEGENDS ===== */
    .mobile-chart-legend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-lg);
        margin-top: var(--spacing-md);
        padding: var(--spacing-sm) var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        font-size: var(--text-xs);
    }
    
    .mobile-chart-legend-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }
    
    .mobile-chart-legend-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--primary);
    }
    
    .mobile-chart-legend-text {
        font-weight: 500;
        color: var(--text-secondary);
    }
    
    /* ===== MOBILE ANALYTICS INSIGHTS ===== */
    .mobile-analytics-insights {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
    }
    
    .mobile-insights-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        text-align: center;
    }
    
    .mobile-insights-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-insight-card {
        background: linear-gradient(135deg, var(--primary-bg), var(--surface));
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        text-align: center;
        border: 1px solid var(--border-light);
    }
    
    .mobile-insight-value {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--primary);
        margin-bottom: var(--spacing-xs);
        font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
    }
    
    .mobile-insight-label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
        font-weight: 500;
    }

    /* ===== NATIVE STATS CARDS ===== */
    .mobile-stats-section {
        padding: var(--spacing-sm) 4px var(--spacing-lg) 4px; /* Reduced left/right padding */
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-stats-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }
    
    .mobile-stats-container {
        padding: var(--spacing-sm) 4px var(--spacing-lg) 4px; /* Reduced left/right padding */
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-stat-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
    }
    
    .mobile-stat-card:active {
        transform: scale(0.98);
    }
    
    /* Multicolor gradients for analytics stats */
    .mobile-stat-card.revenue {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-stat-card.orders {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-stat-card.customers {
        background: linear-gradient(135deg, #8b5cf6, #a855f7);
    }
    
    .mobile-stat-card.visitors {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    .mobile-stat-card.conversion {
        background: linear-gradient(135deg, #ec4899, #f472b6);
    }
    
    .mobile-stat-card.growth {
        background: linear-gradient(135deg, #14b8a6, #2dd4bf);
    }
    
    .mobile-stat-card.sales {
        background: linear-gradient(135deg, #f97316, #fb923c);
    }
    
    .mobile-stat-card.products {
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
    }
    
    .mobile-stat-card.expenses {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }
    
    .mobile-stat-card.profit {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }
    
    .mobile-stat-card.sessions {
        background: linear-gradient(135deg, #ec4899, #f472b6) !important;
    }
    
    .mobile-stat-card.pageviews {
        background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
    }
    
    .mobile-stat-card.bounce-rate {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }
    
    .mobile-stat-card.avg-session {
        background: linear-gradient(135deg, #84cc16, #65a30d) !important;
    }
    
    .mobile-stat-card.pending {
        background: linear-gradient(135deg, var(--warning), #fbbf24) !important;
    }
    
    .mobile-stat-card.completed {
        background: linear-gradient(135deg, var(--success), var(--primary-light)) !important;
    }
    
    .mobile-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-stat-value {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
        color: var(--white);
    }
    
    .mobile-stat-trend {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        font-size: var(--text-xs);
        font-weight: 600;
        color: var(--white);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-trend i {
        font-size: 10px;
    }
    
    /* ===== MOBILE ANALYTICS SECTIONS ===== */
    .mobile-analytics-sections {
        padding: 0 4px; /* Reduced left/right padding */
        margin-bottom: var(--spacing-2xl);
    }
    
    .mobile-analytics-section {
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        margin-bottom: var(--spacing-lg);
        overflow: hidden;
    }
    
    .mobile-analytics-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-analytics-section-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .mobile-analytics-link {
        color: var(--primary);
        text-decoration: none;
        font-size: var(--text-xs);
        font-weight: 500;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        transition: all 0.2s ease;
    }
    
    .mobile-analytics-link:active {
        background: var(--primary-bg);
        transform: scale(0.95);
    }
    
    .mobile-analytics-body {
        padding: var(--spacing-lg);
    }
    
    /* ===== MOBILE DATE RANGE SELECTOR ===== */
    .mobile-date-range {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        margin: var(--spacing-md) 4px var(--spacing-lg) 4px; /* Reduced left/right margin */
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
    }
    
    .mobile-date-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
        text-align: center;
    }
    
    .mobile-date-buttons {
        display: flex;
        gap: var(--spacing-xs);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding: var(--spacing-xs) 0;
    }
    
    .mobile-date-buttons::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-date-btn {
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--text-sm);
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 80px;
        text-align: center;
    }
    
    .mobile-date-btn:active {
        transform: scale(0.95);
    }
    
    .mobile-date-btn.active {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: var(--white);
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }
    
    /* ===== MOBILE CHART CONTAINER ===== */
    .mobile-chart-container {
        padding: 0 var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
    }
    
    .mobile-chart-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        overflow: hidden;
    }
    
    .mobile-chart-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        background: linear-gradient(135deg, var(--primary-bg), var(--surface));
    }
    
    .mobile-chart-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-md) 0;
        text-align: center;
    }
    
    .mobile-chart-controls {
        display: flex;
        gap: var(--spacing-xs);
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        padding: var(--spacing-xs) 0;
    }
    
    .mobile-chart-controls::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-chart-control {
        background: var(--surface-secondary);
        border: 1px solid var(--border);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--text-xs);
        font-weight: 500;
        color: var(--text-secondary);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;
        flex-shrink: 0;
        min-width: 70px;
        text-align: center;
    }
    
    .mobile-chart-control:active {
        transform: scale(0.95);
    }
    
    .mobile-chart-control.active {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        color: var(--white);
        border-color: var(--primary);
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.3);
    }
    
    .mobile-chart-body {
        padding: var(--spacing-lg);
        background: var(--surface);
    }
    
    .mobile-chart-canvas {
        position: relative;
        height: 250px;
        width: 100%;
    }
    
    .mobile-chart-canvas canvas {
        width: 100% !important;
        height: 100% !important;
    }
    
    /* ===== MOBILE TRAFFIC SOURCES ===== */
    .mobile-traffic-sources {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .mobile-traffic-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
    }
    
    .mobile-traffic-item:active {
        transform: scale(0.98);
        background: var(--border-light);
    }
    
    .mobile-traffic-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: var(--text-sm);
        flex-shrink: 0;
    }
    
    .mobile-traffic-info {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-traffic-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-traffic-stats {
        display: flex;
        gap: var(--spacing-md);
        font-size: var(--text-xs);
        color: var(--text-secondary);
    }
    
    .mobile-traffic-stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
    }
    
    .mobile-traffic-stat-value {
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-traffic-stat-label {
        font-size: 10px;
        color: var(--text-tertiary);
    }
    
    /* ===== MOBILE DEMOGRAPHICS ===== */
    .mobile-demographics-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-demographic-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        color: var(--white);
        position: relative;
        overflow: hidden;
        text-align: center;
        transition: all 0.2s ease;
    }
    
    .mobile-demographic-card:active {
        transform: scale(0.98);
    }
    
    .mobile-demographic-card::before {
        content: '';
        position: absolute;
        top: -30%;
        right: -15px;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-demographic-age {
        font-size: var(--text-base);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-demographic-stats {
        display: flex;
        justify-content: space-between;
        font-size: var(--text-xs);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-demographic-stats span {
        font-weight: 600;
    }
    
    /* Age group specific gradients */
    .mobile-demographic-card:nth-child(1) {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-demographic-card:nth-child(2) {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-demographic-card:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }
    
    .mobile-demographic-card:nth-child(4) {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    /* ===== MOBILE BEST PRODUCTS ===== */
    .mobile-best-products {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .mobile-product-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
    }
    
    .mobile-product-item:active {
        transform: scale(0.98);
        background: var(--border-light);
    }
    
    .mobile-product-image {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-md);
        background: var(--primary-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: var(--text-lg);
        flex-shrink: 0;
        overflow: hidden;
    }
    
    .mobile-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .mobile-product-info {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-product-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .mobile-product-stats {
        display: flex;
        gap: var(--spacing-md);
        font-size: var(--text-xs);
        color: var(--text-secondary);
    }
    
    .mobile-product-stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
    }
    
    .mobile-product-stat-value {
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-product-stat-label {
        font-size: 10px;
        color: var(--text-tertiary);
    }
    
    .mobile-product-rank {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: var(--primary);
        color: var(--white);
        border-radius: 50%;
        font-size: var(--text-xs);
        font-weight: 700;
        flex-shrink: 0;
    }
    
    /* ===== MOBILE EMPTY STATES ===== */
    .mobile-empty-state {
        text-align: center;
        padding: var(--spacing-2xl) var(--spacing-md);
    }
    
    .mobile-empty-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--surface-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--text-xl);
        color: var(--text-tertiary);
    }
    
    .mobile-empty-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-empty-description {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        line-height: 1.4;
    }
}

/* ===== DESKTOP ANALYTICS STYLES (OUTSIDE MOBILE QUERY) ===== */
@media (min-width: 769px) {
    /* ===== CHART GRID ===== */
    .chart-grid {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);
    }
    
    .chart-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border);
    }
    
    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .chart-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .chart-controls {
        display: flex;
        gap: var(--spacing-xs);
        flex-wrap: wrap;
    }
    
    .chart-control {
        padding: var(--spacing-xs) var(--spacing-sm);
        border: 1px solid var(--border);
        border-radius: var(--radius-sm);
        background: var(--surface);
        color: var(--text-secondary);
        font-size: var(--text-xs);
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .chart-control:active {
        transform: scale(0.95);
    }
    
    .chart-control.active {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }
    
    .chart-container {
        height: 200px;
        position: relative;
    }
    
    .chart-link {
        color: var(--primary);
        text-decoration: none;
        font-size: var(--text-xs);
        font-weight: 500;
    }
    
    .chart-loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
    
    /* ===== TABLE SECTION ===== */
    .table-section {
        margin-bottom: var(--spacing-lg);
    }
    
    .table-grid {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }
    
    .table-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border);
    }
    
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-md);
    }
    
    .table-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .table-link {
        color: var(--primary);
        text-decoration: none;
        font-size: var(--text-xs);
        font-weight: 500;
    }
    
    .analytics-table {
        width: 100%;
        border-collapse: collapse;
        font-size: var(--text-sm);
    }
    
    .analytics-table th {
        text-align: left;
        padding: var(--spacing-sm);
        font-weight: 600;
        color: var(--text-primary);
        border-bottom: 1px solid var(--border);
        font-size: var(--text-xs);
    }
    
    .analytics-table td {
        padding: var(--spacing-sm);
        color: var(--text-secondary);
        border-bottom: 1px solid var(--border-light);
        font-size: var(--text-xs);
    }
    
    /* ===== EXPORT SECTION ===== */
    .export-section {
        margin-bottom: var(--spacing-lg);
    }
    
    .export-card {
        background: var(--surface);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border);
    }
    
    .export-header {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .export-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .export-subtitle {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.4;
    }
    
    .export-actions {
        display: flex;
        gap: var(--spacing-sm);
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .export-btn {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);
        border: 1px solid var(--border);
        border-radius: var(--radius-md);
        background: var(--surface);
        color: var(--text-secondary);
        font-size: var(--text-sm);
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 70px;
        justify-content: center;
    }
    
    .export-btn:active {
        transform: scale(0.95);
    }
    
    .export-btn.primary {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
        box-shadow: var(--shadow-sm);
    }
    
    /* ===== EMPTY STATES ===== */
    .empty-cell {
        text-align: center;
        color: var(--text-tertiary);
        font-style: italic;
        padding: var(--spacing-xl);
        font-size: var(--text-sm);
    }
    
    .empty-state {
        text-align: center;
        padding: var(--spacing-2xl);
        color: var(--text-secondary);
    }
    
    .empty-state .empty-icon {
        font-size: var(--text-2xl);
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-md);
    }
    
    .empty-state h3 {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0 0 var(--spacing-sm) 0;
    }
    
    .empty-state p {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
        line-height: 1.4;
    }
    
    /* ===== ACCESSIBILITY ===== */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }
    
    /* Status-specific gradients for stats */
    .mobile-stat-card.revenue {
        background: linear-gradient(135deg, var(--success), var(--primary-light)) !important;
    }
    
    .mobile-stat-card.orders {
        background: linear-gradient(135deg, var(--info), #60a5fa) !important;
    }
    
    .mobile-stat-card.customers {
        background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
    }
    
    .mobile-stat-card.conversion {
        background: linear-gradient(135deg, var(--warning), #fbbf24) !important;
    }
    
    /* Default gradient for total-revenue */
    .mobile-stat-card.total-revenue {
        background: linear-gradient(135deg, var(--success), var(--primary-light)) !important;
    }
    
    /* Default gradient for total-orders */
    .mobile-stat-card.total-orders {
        background: linear-gradient(135deg, var(--info), #60a5fa) !important;
    }
    
    /* Default gradient for new-customers */
    .mobile-stat-card.new-customers {
        background: linear-gradient(135deg, #8b5cf6, #a855f7) !important;
    }
    
    /* Default gradient for conversion-rate */
    .mobile-stat-card.conversion-rate {
        background: linear-gradient(135deg, var(--warning), #fbbf24) !important;
    }
    
    /* Additional stat card variations */
    .mobile-stat-card.sales {
        background: linear-gradient(135deg, #f97316, #fb923c) !important;
    }
    
    .mobile-stat-card.products {
        background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;
    }
    
    .mobile-stat-card.visitors {
        background: linear-gradient(135deg, var(--warning), #fbbf24) !important;
    }
    
    .mobile-stat-card.growth {
        background: linear-gradient(135deg, #14b8a6, #2dd4bf) !important;
    }
    
    .mobile-stat-card.profit {
        background: linear-gradient(135deg, #10b981, #059669) !important;
    }
    
    .mobile-stat-card.expenses {
        background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    }
    
    .mobile-stat-card.sessions {
        background: linear-gradient(135deg, #ec4899, #f472b6) !important;
    }
    
    .mobile-stat-card.pageviews {
        background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
    }
    
    .mobile-stat-card.bounce-rate {
        background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    }
    
    .mobile-stat-card.avg-session {
        background: linear-gradient(135deg, #84cc16, #65a30d) !important;
    }
    
    .mobile-stat-card.pending {
        background: linear-gradient(135deg, var(--warning), #fbbf24) !important;
    }
    
    .mobile-stat-card.completed {
        background: linear-gradient(135deg, var(--success), var(--primary-light)) !important;
    }
    
    /* ===== MOBILE ANALYTICS SECTIONS ===== */
    .mobile-analytics-sections {
        padding: 0 4px; /* Reduced left/right padding */
        margin-bottom: var(--spacing-2xl);
    }
    
    .mobile-analytics-section {
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
        margin-bottom: var(--spacing-lg);
        overflow: hidden;
    }
    
    .mobile-analytics-header {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
        border-bottom: 1px solid var(--border-light);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .mobile-analytics-section-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin: 0;
    }
    
    .mobile-analytics-link {
        color: var(--primary);
        text-decoration: none;
        font-size: var(--text-xs);
        font-weight: 500;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        transition: all 0.2s ease;
    }
    
    .mobile-analytics-link:active {
        background: var(--primary-bg);
        transform: scale(0.95);
    }
    
    .mobile-analytics-body {
        padding: var(--spacing-lg);
    }
    
    /* ===== MOBILE TRAFFIC SOURCES ===== */
    .mobile-traffic-sources {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .mobile-traffic-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
    }
    
    .mobile-traffic-item:active {
        transform: scale(0.98);
        background: var(--border-light);
    }
    
    .mobile-traffic-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: var(--text-sm);
        flex-shrink: 0;
    }
    
    .mobile-traffic-info {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-traffic-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-traffic-stats {
        display: flex;
        gap: var(--spacing-md);
        font-size: var(--text-xs);
        color: var(--text-secondary);
    }
    
    .mobile-traffic-stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
    }
    
    .mobile-traffic-stat-value {
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-traffic-stat-label {
        font-size: 10px;
        color: var(--text-tertiary);
    }
    
    /* ===== MOBILE DEMOGRAPHICS ===== */
    .mobile-demographics-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-demographic-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-md);
        padding: var(--spacing-md);
        color: var(--white);
        position: relative;
        overflow: hidden;
        text-align: center;
        transition: all 0.2s ease;
    }
    
    .mobile-demographic-card:active {
        transform: scale(0.98);
    }
    
    .mobile-demographic-card::before {
        content: '';
        position: absolute;
        top: -30%;
        right: -15px;
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-demographic-age {
        font-size: var(--text-base);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-demographic-stats {
        display: flex;
        justify-content: space-between;
        font-size: var(--text-xs);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-demographic-stats span {
        font-weight: 600;
    }
    
    /* Age group specific gradients */
    .mobile-demographic-card:nth-child(1) {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-demographic-card:nth-child(2) {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-demographic-card:nth-child(3) {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }
    
    .mobile-demographic-card:nth-child(4) {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    /* ===== MOBILE BEST PRODUCTS ===== */
    .mobile-best-products {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .mobile-product-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding: var(--spacing-md);
        background: var(--surface-secondary);
        border-radius: var(--radius-md);
        transition: all 0.2s ease;
    }
    
    .mobile-product-item:active {
        transform: scale(0.98);
        background: var(--border-light);
    }
    
    .mobile-product-image {
        width: 50px;
        height: 50px;
        border-radius: var(--radius-md);
        background: var(--primary-bg);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: var(--text-lg);
        flex-shrink: 0;
        overflow: hidden;
    }
    
    .mobile-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .mobile-product-info {
        flex: 1;
        min-width: 0;
    }
    
    .mobile-product-name {
        font-size: var(--text-sm);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .mobile-product-stats {
        display: flex;
        gap: var(--spacing-md);
        font-size: var(--text-xs);
        color: var(--text-secondary);
    }
    
    .mobile-product-stat {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
    }
    
    .mobile-product-stat-value {
        font-weight: 600;
        color: var(--text-primary);
    }
    
    .mobile-product-stat-label {
        font-size: 10px;
        color: var(--text-tertiary);
    }
    
    .mobile-product-rank {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background: var(--primary);
        color: var(--white);
        border-radius: 50%;
        font-size: var(--text-xs);
        font-weight: 700;
        flex-shrink: 0;
    }
    
    /* ===== MOBILE EMPTY STATES ===== */
    .mobile-empty-state {
        text-align: center;
        padding: var(--spacing-2xl) var(--spacing-md);
    }
    
    .mobile-empty-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: var(--surface-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto var(--spacing-md);
        font-size: var(--text-xl);
        color: var(--text-tertiary);
    }
    
    .mobile-empty-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-xs);
    }
    
    .mobile-empty-description {
        font-size: var(--text-sm);
        color: var(--text-tertiary);
        line-height: 1.4;
    }
}

/* Close desktop media query */