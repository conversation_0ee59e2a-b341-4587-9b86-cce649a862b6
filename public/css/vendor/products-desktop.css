/* ===== MODERN PRODUCT MANAGEMENT 2025 ===== */
/* Ultra Clean & Professional Design - DESKTOP ONLY */

/* Alpine.js cloaking */
[x-cloak] { display: none !important; }

/* ===== DESKTOP ONLY STYLES ===== */
@media (min-width: 769px) {
:root {
    /* Modern Color Palette - Consistent with Mobile */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #d1fae5;
    --accent: #06b6d4;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Neutral Palette */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-md: 0.75rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions */
    --transition-fast: 150ms ease;
    --transition: 250ms ease;
    --transition-slow: 350ms ease;
}

/* === BASE STYLES === */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-family);
    background-color: var(--gray-50);
    color: var(--gray-900);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === LAYOUT === */
.products-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-8) var(--space-6);
}

/* === HEADER === */
.products-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--space-8);
    flex-wrap: wrap;
    gap: var(--space-6);
    padding: var(--space-6) 0;
    border-bottom: 1px solid var(--gray-200);
}

.products-header-left h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--space-2) 0;
    line-height: 1.1;
}

.products-header-left p {
    color: var(--gray-500);
    margin: 0;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
}

.products-actions {
    display: flex;
    gap: var(--space-3);
    flex-wrap: wrap;
    align-items: center;
}

.products-actions .btn {
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    text-decoration: none;
    cursor: pointer;
}

.products-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary);
    color: var(--primary);
}

.products-actions .btn-primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.products-actions .btn-primary:hover {
    background: var(--primary-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* === FILTERS SECTION === */
.products-filters {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.products-filters::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary);
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-6);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-2);
}

.filter-label {
    font-size: 0.75rem;
    font-weight: 700;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin: 0;
}

.filter-input,
.filter-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--gray-900);
    background-color: var(--white);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(22, 185, 16, 0.1), var(--shadow-md);
    transform: translateY(-2px);
}

.filter-input:hover,
.filter-select:hover {
    border-color: var(--primary);
    box-shadow: var(--shadow);
}

.search-input-container {
    position: relative;
}

.search-input-container .search-icon {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    pointer-events: none;
    font-size: 1rem;
    z-index: 1;
}

.search-input-container .search-input {
    padding-left: calc(var(--space-4) + var(--space-6));
}

/* === PRODUCTS TABLE === */
.products-table-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: var(--space-8);
}

.table-wrapper {
    overflow-x: auto;
}

.products-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;
}

.products-table thead {
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
    border-bottom: 2px solid var(--gray-200);
}

.products-table th {
    padding: var(--space-4) var(--space-4);
    text-align: left;
    font-weight: 700;
    color: var(--gray-600);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    white-space: nowrap;
    position: relative;
}

.products-table th:not(:last-child)::after {
    content: '';
    position: absolute;
    right: 0;
    top: 25%;
    height: 50%;
    width: 1px;
    background: var(--gray-200);
}

.products-table tbody tr {
    border-bottom: 1px solid var(--gray-100);
    transition: var(--transition);
}

.products-table tbody tr:hover {
    background: var(--gray-50);
}

.products-table tbody tr:last-child {
    border-bottom: none;
}

.products-table td {
    padding: var(--space-4);
    vertical-align: middle;
    border-right: 1px solid var(--gray-100);
}

.products-table td:last-child {
    border-right: none;
}

/* Table Column Widths */
.table-checkbox {
    width: 50px;
    text-align: center;
}

.table-product {
    min-width: 300px;
}

.table-category {
    width: 150px;
}

.table-price {
    width: 120px;
}

.table-stock {
    width: 120px;
}

.table-status {
    width: 100px;
}

.table-actions {
    width: 150px;
}

/* Product Info Cell */
.product-info-cell {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.product-image-thumb {
    flex-shrink: 0;
}

.product-thumb {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    object-fit: cover;
    border: 2px solid var(--gray-200);
    transition: var(--transition);
}

.product-thumb:hover {
    transform: scale(1.1);
    border-color: var(--primary);
}

.product-thumb-placeholder {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: var(--gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    font-size: 1.25rem;
    border: 2px dashed var(--gray-200);
}

.product-details {
    flex: 1;
    min-width: 0;
}

.product-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--space-1) 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Category Badge */
.category-badge {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    background: var(--gray-100);
    color: var(--gray-600);
    padding: var(--space-2) var(--space-3);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: capitalize;
    border: 1px solid var(--gray-200);
}

.category-badge i {
    font-size: 10px;
    color: var(--primary);
}

/* Price Cell */
.table-price {
    font-weight: 500;
    color: var(--gray-900);
}

/* Stock Info */
.table-stock {
    font-weight: 500;
    color: var(--gray-900);
}

/* Status Badge */
.status-badge {
    padding: var(--space-2) var(--space-4);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    border: 1px solid transparent;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
    border-color: rgba(16, 185, 129, 0.2);
}

.status-badge.inactive {
    background: rgba(156, 163, 175, 0.1);
    color: var(--gray-400);
    border-color: rgba(156, 163, 175, 0.2);
}

.status-badge.draft {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info);
    border-color: rgba(59, 130, 246, 0.2);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: var(--space-2);
    justify-content: center;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-400);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.view-btn:hover {
    background: var(--info);
    color: white;
    border-color: var(--info);
}

.edit-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.duplicate-btn:hover {
    background: #8B5CF6;
    color: white;
    border-color: #8B5CF6;
}

.delete-btn:hover {
    background: var(--error);
    color: white;
    border-color: var(--error);
}

/* === EMPTY STATE === */
.empty-state-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: var(--space-8);
}

.empty-state {
    text-align: center;
    max-width: 500px;
}

.empty-state .empty-icon {
    font-size: 4rem;
    color: var(--gray-300);
    margin-bottom: var(--space-6);
    opacity: 0.6;
}

.empty-state h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--space-4) 0;
}

.empty-state p {
    font-size: 1rem;
    color: var(--gray-500);
    margin: 0 0 var(--space-6) 0;
    line-height: 1.6;
}

.empty-actions {
    display: flex;
    gap: var(--space-4);
    justify-content: center;
    flex-wrap: wrap;
}

/* === PAGINATION === */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--space-4);
    padding: var(--space-6) 0;
    border-top: 1px solid var(--gray-100);
    margin-top: var(--space-8);
}

.pagination-info {
    font-size: 0.875rem;
    color: var(--gray-500);
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border: 1px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
}

.pagination-btn:hover:not(.disabled) {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-numbers {
    display: flex;
    gap: var(--space-1);
}

.pagination-number {
    width: 40px;
    height: 40px;
    border: 1px solid var(--gray-200);
    background: var(--white);
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination-number:hover {
    background: var(--gray-100);
    border-color: var(--primary);
}

.pagination-number.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.items-per-page {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    color: var(--gray-500);
}

.items-select {
    padding: var(--space-1) var(--space-2);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    font-size: 0.875rem;
    background: var(--white);
    color: var(--gray-900);
}

/* === MODAL STYLES === */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4);
}

.modal-container {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-6);
    border-bottom: 1px solid var(--gray-200);
    background: var(--gray-50);
}

.modal-header h2 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0;
}

.modal-close {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    border: none;
    background: var(--white);
    color: var(--gray-400);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--error);
    color: white;
}

.modal-content {
    padding: var(--space-6);
    overflow-y: auto;
    flex: 1;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: var(--space-3);
    margin-top: var(--space-6);
    padding-top: var(--space-4);
    border-top: 1px solid var(--gray-100);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600);
    margin-bottom: var(--space-2);
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: var(--space-3) var(--space-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius);
    font-size: 0.875rem;
    color: var(--gray-900);
    background-color: var(--white);
    transition: var(--transition);
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(22, 185, 16, 0.2);
}

.form-textarea {
    resize: vertical;
    line-height: 1.5;
}

.form-group {
    margin-bottom: var(--space-4);
}

/* === IMAGE UPLOAD === */
.image-upload-section {
    border: 2px dashed var(--gray-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    background: var(--gray-50);
}

.image-gallery-modal {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: var(--space-3);
    max-width: 100%;
}

.image-item-modal {
    position: relative;
    aspect-ratio: 1;
    border-radius: var(--radius);
    overflow: hidden;
    border: 2px solid var(--gray-200);
}

.image-preview-modal {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-remove-btn {
    position: absolute;
    top: 4px;
    right: 4px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--error);
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    transition: var(--transition);
}

.image-remove-btn:hover {
    transform: scale(1.1);
}

.image-upload-btn {
    aspect-ratio: 1;
    border: 2px dashed var(--primary);
    border-radius: var(--radius);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: var(--space-1);
    cursor: pointer;
    transition: var(--transition);
    color: var(--primary);
    background: var(--white);
}

.image-upload-btn:hover {
    background: var(--primary-bg);
    transform: scale(1.02);
}

.image-upload-btn i {
    font-size: 1.25rem;
}

.image-upload-btn span {
    font-size: 0.75rem;
    font-weight: 600;
}

/* === CHECKBOX STYLES === */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    cursor: pointer;
    font-weight: 500;
    color: var(--gray-900);
}

.checkbox {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius);
    position: relative;
    transition: var(--transition);
    flex-shrink: 0;
}

.checkbox:checked + .checkbox-custom {
    background: var(--primary);
    border-color: var(--primary);
}

.checkbox:checked + .checkbox-custom::after {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
}

} /* End of media query */
