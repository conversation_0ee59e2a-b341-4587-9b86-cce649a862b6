/* ===== NATIVE MOBILE APP PRODUCTS ===== */
/* iOS/Android Style Design with Green Theme - Mobile Only */

:root {
    /* Green Theme Colors */
    --primary: #10b981;
    --primary-light: #34d399;
    --primary-dark: #059669;
    --primary-bg: #ecfdf5;
    --primary-text: #064e3b;
    
    /* Status Colors */
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #3b82f6;
    
    /* Native Colors */
    --white: #ffffff;
    --background: #f8fafc;
    --surface: #ffffff;
    --surface-secondary: #f1f5f9;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-tertiary: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --divider: #e2e8f0;
    
    /* Native Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
    --spacing-2xl: 24px;
    --spacing-3xl: 32px;
    
    /* Native Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
    
    /* Native Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 2px 8px rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-card: 0 1px 3px rgba(0, 0, 0, 0.1);
    
    /* Native Typography */
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    
    /* Safe Area */
    --safe-area-top: env(safe-area-inset-top, 0px);
    --safe-area-bottom: env(safe-area-inset-bottom, 0px);
}

/* ===== MOBILE-ONLY STYLES ===== */
@media (max-width: 768px) {
    * {
        box-sizing: border-box;
        -webkit-tap-highlight-color: transparent;
        margin: 0;
        padding: 0;
    }
    html, body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
        background: var(--background);
        color: var(--text-primary);
        font-size: var(--text-base);
        line-height: 1.5;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    /* ===== NATIVE APP LAYOUT ===== */
    .mobile-products-app {
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }
    
    /* Main content wrapper */
    .vendor-content {
        min-height: 100vh;
        background: var(--background);
        padding-top: var(--safe-area-top);
        padding-bottom: var(--safe-area-bottom);
        position: relative;
    }
    /* Header */
    .products-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
        padding: 0 var(--spacing-xs);
    }
    .products-header-left h1 {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--primary-text);
        margin-bottom: var(--spacing-xs);
    }
    .products-header-left p {
        font-size: var(--text-base);
        color: var(--text-secondary);
    }
    /* Actions */
    .products-actions {
        display: flex;
        gap: var(--spacing-sm);
    }
    .products-actions .btn {
        font-size: var(--text-sm);
        padding: var(--spacing-sm) var(--spacing-md);
        border-radius: var(--radius-sm);
    }
    /* Filters */
    .products-filters {
        background: var(--white);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--spacing-md);
    }
    .filters-row {
        display: flex;
        flex-wrap: wrap;
        gap: var(--spacing-md);
    }
    .filter-group {
        flex: 1 1 140px;
        min-width: 120px;
    }
    .filter-label {
        font-size: var(--text-xs);
        color: var(--text-tertiary);
        margin-bottom: var(--spacing-xs);
        display: block;
    }
    .filter-input, .filter-select {
        width: 100%;
        padding: var(--spacing-sm);
        border-radius: var(--radius-sm);
        border: 1px solid var(--border);
        font-size: var(--text-base);
        background: var(--surface-secondary);
        color: var(--text-primary);
    }
    .search-input-container {
        position: relative;
    }
    .search-icon {
        position: absolute;
        left: 8px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-tertiary);
        font-size: var(--text-base);
    }
    .search-input {
        padding-left: 2em;
    }
    /* Table */
    .products-table-container {
        overflow-x: auto;
        background: var(--white);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        margin-bottom: var(--spacing-lg);
    }
    .products-table {
        width: 100%;
        border-collapse: collapse;
        font-size: var(--text-sm);
    }
    .products-table th, .products-table td {
        padding: var(--spacing-sm);
        text-align: left;
        border-bottom: 1px solid var(--border-light);
    }
    .products-table th {
        color: var(--text-tertiary);
        font-weight: 600;
        background: var(--surface-secondary);
    }
    .products-table tr:last-child td {
        border-bottom: none;
    }
    /* Empty State */
    .empty-state-container {
        text-align: center;
        padding: var(--spacing-3xl) var(--spacing-lg);
        color: var(--text-tertiary);
    }
    .empty-icon {
        font-size: 3rem;
        color: var(--border);
        margin-bottom: var(--spacing-lg);
        opacity: 0.7;
    }
    .empty-state h3 {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }
    .empty-state p {
        font-size: var(--text-base);
        color: var(--text-secondary);
        margin-bottom: var(--spacing-lg);
    }
    .empty-actions .btn {
        margin: 0 var(--spacing-xs);
    }
    /* Button Styles */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 0.5em;
        padding: var(--spacing-sm) var(--spacing-lg);
        border-radius: var(--radius-full);
        border: 1px solid var(--border);
        background: var(--white);
        color: var(--primary-text);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: background 0.2s, color 0.2s, border 0.2s;
        box-shadow: none;
    }
    .btn-primary {
        background: var(--primary);
        color: var(--white);
        border: 1px solid var(--primary);
    }
    .btn-outline-secondary {
        background: var(--white);
        color: var(--primary-text);
        border: 1px solid var(--border);
    }
    .btn:active, .btn-primary:active {
        background: var(--primary-dark);
        color: var(--white);
        border-color: var(--primary-dark);
    }
    /* Products Actions Buttons */
    .products-actions .btn {
        min-width: 40px;
        padding: var(--spacing-xs) var(--spacing-md);
        font-size: var(--text-sm);
        border-radius: var(--radius-md);
    }
    /* Advanced/Reset Buttons */
    .products-filters .btn {
        font-size: var(--text-xs);
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--radius-full);
    }
    /* Select and Input */
    .filter-select, .filter-input {
        border: 1px solid var(--border);
        background: var(--surface-secondary);
        color: var(--text-primary);
        border-radius: var(--radius-sm);
        font-size: var(--text-base);
        padding: var(--spacing-sm);
        margin-bottom: var(--spacing-xs);
    }
    /* Empty State Icon Centering */
    .empty-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
    /* Responsive Fixes for Filters */
    .filters-row {
        flex-direction: column;
        gap: var(--spacing-sm);
    }
    .filter-group {
        margin-bottom: var(--spacing-sm);
    }
    /* Responsive Table Fixes */
    .products-table-container {
        margin-top: var(--spacing-md);
    }
    .products-table th, .products-table td {
        font-size: var(--text-xs);
        padding: var(--spacing-xs);
    }
    /* Hide table on mobile if you want card view (optional) */
    /* .products-table-container { display: none; } */
    /* Add more as needed for your UI */
    /* App Card Sections */
    .products-header,
    .products-filters,
    .empty-state-container {
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        margin: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
        padding: var(--spacing-lg);
    }
    .products-header {
        margin-top: var(--spacing-lg);
        background: linear-gradient(90deg, var(--primary-bg), var(--white));
        box-shadow: var(--shadow-md);
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
    }
    /* Gradient Title */
    .products-header-left h1 {
        background: linear-gradient(90deg, var(--primary), var(--primary-light));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        font-size: var(--text-2xl);
        font-weight: 800;
        margin-bottom: var(--spacing-xs);
    }
    /* Filter Chips */
    .products-filters .btn, .products-filters .filter-select, .products-filters .filter-input {
        background: var(--surface-secondary);
        color: var(--primary-text);
        border-radius: var(--radius-full);
        border: none;
        font-weight: 600;
        font-size: var(--text-sm);
        margin-right: var(--spacing-xs);
        margin-bottom: var(--spacing-xs);
        box-shadow: var(--shadow-sm);
        padding: var(--spacing-xs) var(--spacing-lg);
        transition: background 0.2s, color 0.2s;
    }
    .products-filters .btn.active, .products-filters .filter-select:focus, .products-filters .filter-input:focus {
        background: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-md);
    }
    /* Search Input Modern Look */
    .search-input-container {
        background: var(--surface-secondary);
        border-radius: var(--radius-full);
        box-shadow: var(--shadow-sm);
        padding: 0 var(--spacing-sm);
        margin-bottom: var(--spacing-sm);
    }
    .search-input {
        background: transparent;
        border: none;
        outline: none;
        font-size: var(--text-base);
        color: var(--text-primary);
        width: 100%;
        padding: var(--spacing-sm) 0 var(--spacing-sm) 2em;
    }
    .search-icon {
        left: var(--spacing-sm);
        color: var(--primary);
        font-size: var(--text-lg);
    }
    /* Product Table as Card List */
    .products-table-container {
        background: none;
        box-shadow: none;
        padding: 0;
    }
    .products-table {
        display: block;
        width: 100%;
        border-collapse: separate;
        border-spacing: 0 12px;
        background: none;
    }
    .products-table thead { display: none; }
    .products-table tr {
        display: block;
        background: var(--surface);
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-card);
        margin-bottom: var(--spacing-md);
        padding: var(--spacing-md);
    }
    .products-table td {
        display: flex;
        align-items: center;
        font-size: var(--text-base);
        border: none;
        padding: var(--spacing-xs) 0;
        background: none;
        box-shadow: none;
    }
    .products-table td:before {
        content: attr(data-label);
        flex: 0 0 120px;
        color: var(--text-tertiary);
        font-size: var(--text-xs);
        font-weight: 600;
        margin-right: var(--spacing-xs);
    }
    /* Floating Action Button */
    .fab-add-product {
        position: fixed;
        right: var(--spacing-lg);
        bottom: var(--spacing-3xl);
        z-index: 100;
        background: var(--primary);
        color: var(--white);
        border-radius: 50%;
        width: 56px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.75rem;
        box-shadow: var(--shadow-lg);
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .fab-add-product:active {
        background: var(--primary-dark);
        transform: translateY(1px);
        box-shadow: var(--shadow-card);
    }
    /* Mobile Loading Overlay */
    .mobile-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        min-height: 100px;
        background: rgba(255,255,255,0.85);
        z-index: 50;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-lg);
        box-shadow: var(--shadow-md);
        padding: var(--spacing-xl) 0;
    }
    
    .mobile-loading-spinner {
        width: 32px;
        height: 32px;
        border: 4px solid var(--primary-bg);
        border-top: 4px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: var(--spacing-md);
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .mobile-loading-text {
        color: var(--primary-text);
        font-size: var(--text-base);
        font-weight: 600;
        letter-spacing: 0.2px;
    }

    /* ===== NATIVE PAGINATION ===== */
    .mobile-pagination {
        padding: var(--spacing-lg);
        background: var(--white);
        border-radius: var(--radius-lg);
        margin: var(--spacing-md) var(--spacing-sm) 0;
        box-shadow: var(--shadow-card);
        border: 1px solid var(--border-light);
    }
    
    .mobile-pagination-info {
        text-align: center;
        margin-bottom: var(--spacing-lg);
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
    }
    
    .mobile-pagination-controls {
        display: flex;
        gap: var(--spacing-md);
    }
    
    .mobile-pagination-btn {
        flex: 1;
        height: 44px;
        border: 1px solid var(--border);
        background: var(--white);
        color: var(--text-primary);
        border-radius: var(--radius-md);
        font-size: var(--text-base);
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-sm);
    }
    
    .mobile-pagination-btn.primary {
        background: var(--primary);
        color: var(--white);
        border-color: var(--primary);
    }
    
    .mobile-pagination-btn.primary:active:not(.disabled) {
        background: var(--primary-dark);
        transform: translateY(1px);
    }
    
    .mobile-pagination-btn:active:not(.disabled) {
        background: var(--surface-secondary);
        transform: translateY(1px);
    }
    
    .mobile-pagination-btn.disabled {
        opacity: 0.4;
        cursor: not-allowed;
        border-color: var(--border);
        color: var(--text-tertiary);
        background: var(--surface-secondary);
    }

    /* Remove debug border and ensure clean background */
    .mobile-products-app {
        border: none !important;
        background: var(--background) !important;
    }
    
    /* Clean section spacing */
    .mobile-products-app > * {
        margin-bottom: var(--spacing-md);
    }
    
    /* Products container alignment */
    .mobile-products-container {
        padding: 0 var(--spacing-sm) var(--spacing-3xl);
    }

    /* ===== NATIVE FILTER BAR ===== */
    .mobile-filters {
        background: var(--white);
        padding: var(--spacing-lg);
        border-bottom: 1px solid var(--border-light);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-filters-title {
        font-size: var(--text-base);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-filter-chips {
        display: flex;
        gap: var(--spacing-sm);
        overflow-x: auto;
        padding-bottom: var(--spacing-xs);
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .mobile-filter-chips::-webkit-scrollbar {
        display: none;
    }
    
    .mobile-filter-chip {
        background: var(--surface-secondary);
        color: var(--text-secondary);
        border: none;
        border-radius: var(--radius-full);
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--text-sm);
        font-weight: 500;
        white-space: nowrap;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
    }
    
    .mobile-filter-chip.active {
        background: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-filter-chip:active {
        transform: scale(0.96);
    }

    /* ===== NATIVE STATS CARDS (EXACT MATCH TO ORDER PAGE) ===== */
    .mobile-stats-section {
        padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
        background: var(--white);
        margin-top: 0;
        margin-bottom: var(--spacing-md);
    }
    
    .mobile-stats-title {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-lg);
    }
    
    .mobile-stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-md);
    }
    
    .mobile-stat-card {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
        border-radius: var(--radius-lg);
        padding: var(--spacing-lg);
        color: var(--white);
        position: relative;
        overflow: hidden;
        box-shadow: var(--shadow-card);
        transition: transform 0.2s ease;
    }
    
    .mobile-stat-card:active {
        transform: scale(0.98);
    }
    
    .mobile-stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20px;
        width: 100px;
        height: 100px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .mobile-stat-number {
        font-size: var(--text-2xl);
        font-weight: 700;
        margin-bottom: var(--spacing-xs);
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-label {
        font-size: var(--text-sm);
        opacity: 0.9;
        position: relative;
        z-index: 2;
    }
    
    .mobile-stat-icon {
        position: absolute;
        top: var(--spacing-md);
        right: var(--spacing-md);
        font-size: var(--text-xl);
        opacity: 0.8;
        z-index: 2;
    }
    
    /* Status-specific gradients for product stats */
    .mobile-stat-card.pending {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    .mobile-stat-card.processing {
        background: linear-gradient(135deg, var(--info), #60a5fa);
    }
    
    .mobile-stat-card.completed {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-stat-card.instock {
        background: linear-gradient(135deg, var(--success), var(--primary-light));
    }
    
    .mobile-stat-card.lowstock {
        background: linear-gradient(135deg, var(--warning), #fbbf24);
    }
    
    .mobile-stat-card.outofstock {
        background: linear-gradient(135deg, #ef4444, #f87171);
    }
    
    .mobile-stat-card.total {
        background: linear-gradient(135deg, var(--primary), var(--primary-light));
    }
    
    /* ===== NATIVE PRODUCTS LIST ===== */
    .mobile-products-list {
        padding: 0 var(--spacing-sm) var(--spacing-3xl);
    }
    
    .mobile-products-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: var(--spacing-lg);
        padding: 0 var(--spacing-xs);
    }
    
    .mobile-products-count {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
    }
    
    /* ===== SIMPLE MOBILE PRODUCT CARDS (3-ROW LAYOUT) ===== */
    .mobile-product-card {
        background: var(--white);
        border-radius: var(--radius-lg);
        margin-bottom: var(--spacing-lg);
        box-shadow: var(--shadow-card);
        transition: all 0.2s ease;
        border: 1px solid var(--border-light);
        padding: var(--spacing-lg);
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
        position: relative;
    }
    
    .mobile-product-card:active {
        transform: translateY(1px);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-product-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }
    
    /* Simple Product Image */
    .mobile-product-card .mobile-product-image {
        width: 48px;
        height: 48px;
        border-radius: var(--radius-md);
        background: var(--surface-secondary);
        object-fit: cover;
        flex-shrink: 0;
        margin-bottom: var(--spacing-xs);
        border: 1px solid var(--border-light);
        transition: all 0.2s ease;
    }
    
    .mobile-product-card .mobile-product-image:hover {
        transform: scale(1.05);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-product-details {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    /* Create 3 rows layout */
    .mobile-product-details > * {
        margin: 0;
    }
    
    /* Simple Product Name */
    .mobile-product-name {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--text-primary);
        order: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.4;
    }
    
    /* Simple Category & Price */
    .mobile-product-category {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: 500;
        order: 2;
        text-transform: capitalize;
        background: var(--surface-secondary);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        width: fit-content;
        font-size: var(--text-xs);
    }
    
    .mobile-product-price {
        font-size: var(--text-xl);
        font-weight: 700;
        color: var(--primary);
        order: 3;
    }
    
    /* Hide stock information - we don't want it */
    .mobile-product-stock {
        display: none !important;
    }
    
    /* Simple Product Status - Same style as category */
    .mobile-product-status {
        display: inline-flex;
        align-items: center;
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--text-xs);
        font-weight: 500;
        text-transform: capitalize;
        order: 2;
        width: fit-content;
        background: var(--surface-secondary);
        color: var(--text-secondary);
    }
    
    .mobile-product-status.published {
        background: var(--primary-bg);
        color: var(--primary);
    }
    
    .mobile-product-status.draft {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }
    
    .mobile-product-status.inactive {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }
    
    /* Simple Action Buttons */
    .mobile-product-actions {
        display: flex;
        gap: var(--spacing-sm);
        order: 4;
    }
    
    .mobile-product-action-btn {
        width: 32px;
        height: 32px;
        border-radius: var(--radius-sm);
        border: 1px solid var(--border);
        background: var(--white);
        color: var(--text-secondary);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        font-size: var(--text-sm);
        box-shadow: none;
    }
    
    .mobile-product-action-btn:active {
        transform: scale(0.95);
        box-shadow: var(--shadow-sm);
    }
    
    .mobile-product-action-btn:hover {
        background: var(--surface-secondary);
        border-color: var(--border);
    }
    
    .mobile-product-action-btn:nth-child(1):active {
        background: var(--text-tertiary);
        color: var(--white);
    }
    
    .mobile-product-action-btn:nth-child(2):active {
        background: var(--info);
        color: var(--white);
    }
    
    .mobile-product-action-btn:nth-child(3):active {
        background: var(--error);
        color: var(--white);
    }
    
    /* Simple Layout Structure */
    .mobile-product-card {
        display: block !important;
        position: relative;
    }
    
    .mobile-product-details {
        display: grid !important;
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
        grid-template-areas: 
            "image-name"
            "category-status-price" 
            "actions";
        position: relative;
    }
    
    /* Row 1: Image + Name */
    .mobile-product-image {
        grid-area: image-name;
        float: left;
        margin-right: var(--spacing-md);
        margin-bottom: 0;
    }
    
    .mobile-product-name {
        grid-area: image-name;
        padding-left: 64px; /* 48px image + 16px gap */
        display: flex;
        align-items: center;
        min-height: 48px;
        line-height: 1.3;
        white-space: normal;
        overflow: visible;
        text-overflow: unset;
    }
    
    /* Row 2: Category + Status + Price with equal spacing */
    .mobile-product-category {
        grid-area: category-status-price;
        float: left;
        margin-right: var(--spacing-md);
    }
    
    .mobile-product-status {
        grid-area: category-status-price;
        float: left;
        margin-right: var(--spacing-md);
    }
    
    .mobile-product-price {
        grid-area: category-status-price;
        float: right;
    }
    
    /* Row 3: Actions */
    .mobile-product-actions {
        grid-area: actions;
        float: right;
    }
    
    /* Clear floats for each grid area */
    .mobile-product-details::after {
        content: "";
        display: table;
        clear: both;
    }
    
    /* Simple alignment */
    .mobile-product-category,
    .mobile-product-status,
    .mobile-product-price {
        display: flex;
        align-items: center;
        min-height: 28px;
    }
    
    .mobile-product-actions {
        display: flex;
        align-items: center;
        min-height: 32px;
    }
    
    /* Responsive improvements */
    @media (max-width: 375px) {
        .mobile-product-card {
            padding: var(--spacing-md);
        }
        
        .mobile-product-details {
            gap: var(--spacing-sm);
        }
        
        .mobile-product-name {
            padding-left: 56px;
            font-size: var(--text-base);
        }
        
        .mobile-product-image {
            width: 40px;
            height: 40px;
            margin-right: var(--spacing-sm);
        }
        
        .mobile-product-price {
            font-size: var(--text-lg);
        }
        
        .mobile-product-action-btn {
            width: 28px;
            height: 28px;
            font-size: var(--text-xs);
        }
    }
    
    /* Simple touch animation */
    @media (hover: none) and (pointer: coarse) {
        .mobile-product-card:active {
            animation: simplePulse 0.2s ease;
        }
        
        @keyframes simplePulse {
            0% { transform: scale(1); }
            50% { transform: scale(0.98); }
            100% { transform: scale(1); }
        }
    }
}
