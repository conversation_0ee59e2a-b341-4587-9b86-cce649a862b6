/* ===== WHAMART VENDOR DASHBOARD MOBILE LAYOUT ===== 
   Desktop styles are in vendor-layout.css */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
  --primary: #66bb6a;
  --primary-dark: #4caf50;
  --primary-light: #e8ffe1;
  --sidebar-bg: linear-gradient(180deg, #7ed957 0%, #66bb6a 100%);
  --sidebar-collapsed: 72px;
  --sidebar-expanded: 240px;
  --header-height: 64px;
  --surface: #fff;
  --border: #e0e0e0;
  --text: #222;
  --text-light: #fff;
  --text-muted: #666;
  --radius: 12px;
  --shadow: 0 2px 8px 0 rgba(0,0,0,0.06);
  --transition: all 0.25s cubic-bezier(.4,0,.2,1);
  --z-sidebar: 100;
  --z-header: 101;
}

* { box-sizing: border-box; margin: 0; padding: 0; }
html { font-size: 16px; }
body {
  font-family: 'Poppins', Arial, sans-serif;
  background: #f7f8fa;
  color: var(--text);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Hide desktop elements on mobile */
.vendor-sidebar,
.vendor-header,
.desktop-menu,
.desktop-header {
  display: none !important;
}

/* ===== MOBILE HEADER DESIGN - PERFECT ALIGNMENT ===== */
.mobile-header {
  display: none !important;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-header);
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(10px);
}

.mobile-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 1rem;
  background: #fff;
  position: relative;
  max-width: 100%;
}

.mobile-header-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 60px; /* Fixed width for consistency */
}

.mobile-header-center {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-width: 0; /* Allow shrinking */
}

.mobile-header-right {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 60px; /* Fixed width to match left side */
}

/* Brand text styling */
.mobile-brand-text {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-dark);
  text-align: center;
}

/* Mobile header buttons - perfectly aligned */
.mobile-header-menu-btn {
  width: 44px;
  height: 44px;
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
  box-shadow: 0 2px 8px rgba(102, 187, 106, 0.3);
  position: relative;
  margin: 0;
  flex-shrink: 0;
}

.mobile-header-menu-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 187, 106, 0.4);
}

.mobile-header-action-btn {
  width: 44px;
  height: 44px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-dark);
  font-size: 1.2rem;
  cursor: pointer;
  position: relative;
  margin: 0;
  flex-shrink: 0;
  text-decoration: none; /* For link styling */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-header-action-btn:hover,
.mobile-header-action-btn:focus {
  background: var(--primary-light);
  color: var(--primary);
  border-color: var(--primary);
  text-decoration: none;
  outline: none;
}

/* FontAwesome icon fallback */
.mobile-header-menu-btn i {
  font-family: 'Font Awesome 5 Free', sans-serif;
  font-weight: 900;
}

.mobile-header-action-btn i {
  font-family: 'Font Awesome 5 Free', sans-serif;
  font-weight: 900;
}

/* If FontAwesome fails, use text fallback */
.mobile-header-menu-btn i:before {
  content: "☰";
  font-family: Arial, sans-serif;
}

.mobile-header-action-btn i:before {
  content: "🔔";
  font-family: Arial, sans-serif;
}

/* Notification badge */
.mobile-notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  background: #ff4757;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

@media (max-width: 480px) {
  .mobile-header-content {
    height: 56px;
    padding: 0 0.75rem;
  }
  
  .mobile-header-left,
  .mobile-header-right {
    width: 50px; /* Slightly smaller on very small screens */
  }
  
  .mobile-header-menu-btn,
  .mobile-header-action-btn {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .mobile-brand-text {
    font-size: 1.1rem;
  }
}

@media (max-width: 360px) {
  .mobile-header-content {
    padding: 0.5rem 0.5rem;
    height: 52px;
  }
  
  .mobile-header-left,
  .mobile-header-right {
    width: 44px;
  }
  
  .mobile-brand-text {
    font-size: 1rem;
  }
  
  .mobile-header-menu-btn,
  .mobile-header-action-btn {
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
}

/* Mobile Header Transitions - No hiding behavior */
.mobile-header {
  transition: box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile Header Scroll States */
.mobile-header.scrolled {
  box-shadow: 0 6px 30px rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(15px);
}

/* Keep header always visible - override any hiding classes */
.mobile-header.visible,
.mobile-header {
  transform: translateY(0) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Mobile Header Responsive Adjustments - Consolidated */

/* MOBILE RESPONSIVE LAYOUT */
@media (max-width: 700px) {
  /* Hide desktop sidebar completely on mobile */
  .vendor-sidebar { 
    display: none; 
  }
  .vendor-header { 
    left: 0; 
  }
  .vendor-main { 
    margin-left: 0; 
    padding-top: 60px; /* Mobile header height */
  }
  .page-content { 
    padding: 0 0.5rem 1rem; /* Remove top padding to align with header */
  }
  
  /* Mobile sidebar is managed separately through Alpine.js */
  .mobile-sidebar-open .vendor-sidebar {
    display: flex;
    width: var(--sidebar-expanded);
    transform: translateX(0);
  }
}

/* MODERN NATIVE MOBILE BOTTOM NAVIGATION */
@media (max-width: 700px) {
  .bottom-nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    height: 80px;
    background: #ffffff;
    border-top: 2px solid var(--primary-light);
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 200;
    box-shadow: 0 -6px 30px rgba(126, 217, 87, 0.15);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    padding: 12px 8px max(8px, env(safe-area-inset-bottom));
  }
  
  .bottom-nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8A8A8A;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 8px 4px;
    position: relative;
    min-height: 56px;
    border-radius: 16px;
    margin: 0 4px;
  }
  
  .bottom-nav-item:hover {
    color: var(--primary);
    transform: translateY(-1px);
    background: rgba(126, 217, 87, 0.05);
  }
  
  .bottom-nav-item.active {
    color: #ffffff;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(126, 217, 87, 0.4);
  }
  
  .bottom-nav-icon {
    font-size: 20px;
    font-weight: normal;
    font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", "Segoe UI Symbol", "Android Emoji", "EmojiSymbols", "EmojiOne Mozilla", "Twemoji Mozilla", "Segoe UI", system-ui, sans-serif;
    margin-bottom: 4px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    line-height: 1;
    display: inline-block;
    min-width: 20px;
    text-align: center;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .bottom-nav-item.active .bottom-nav-icon {
    transform: scale(1.15);
    font-weight: normal;
    filter: drop-shadow(0 2px 6px rgba(255, 255, 255, 0.6));
  }
  
  .bottom-nav-item:hover .bottom-nav-icon {
    transform: scale(1.05);
    font-weight: normal;
    filter: drop-shadow(0 1px 3px rgba(126, 217, 87, 0.4));
  }
  
  .bottom-nav-label {
    font-size: 11px;
    font-weight: 500;
    line-height: 1.2;
    text-align: center;
    opacity: 0.8;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .bottom-nav-item.active .bottom-nav-label {
    opacity: 1;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
  
  .bottom-nav-item:hover .bottom-nav-label {
    opacity: 1;
    font-weight: 600;
  }
  
  /* Badge for notifications */
  .bottom-nav-badge {
    position: absolute;
    top: 4px;
    right: 50%;
    transform: translateX(12px);
    background: linear-gradient(135deg, #FF4757 0%, #FF3742 100%);
    color: white;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 4px;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
    border: 2px solid #ffffff;
    animation: badgePulse 2s infinite;
  }
  
  @keyframes badgePulse {
    0%, 100% { 
      transform: translateX(12px) scale(1); 
      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.4);
    }
    50% { 
      transform: translateX(12px) scale(1.1); 
      box-shadow: 0 4px 12px rgba(255, 71, 87, 0.6);
    }
  }
  
  /* Enhanced FAB positioning removed - no longer needed */
  
  /* Add bottom padding to main content to prevent overlap */
  .vendor-main {
    padding-bottom: 100px;
  }
}

/* iOS Safari Safe Area Support for Bottom Navigation */
@supports (padding: max(0px)) {
  .bottom-nav {
    padding-bottom: max(12px, env(safe-area-inset-bottom));
    border-top-left-radius: 24px;
    border-top-right-radius: 24px;
  }
}

/* Enhanced Mobile Experience */
@media (max-width: 700px) {
  /* Ensure content doesn't get hidden behind bottom nav */
  .page-content {
    padding-bottom: 100px;
  }
  
  /* Prevent text selection on navigation items */
  .bottom-nav-item {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
  
  /* Smoother animations for low-end devices */
  @media (prefers-reduced-motion: reduce) {
    .bottom-nav-item,
    .bottom-nav-icon,
    .bottom-nav-label {
      transition: none;
    }
    
    .bottom-nav-badge {
      animation: none;
    }
  }
  
  /* High contrast mode support */
  @media (prefers-contrast: high) {
    .bottom-nav {
      border-top: 2px solid #000;
    }
    
    .bottom-nav-item.active {
      background: #000;
      color: #fff;
    }
  }
  
  /* Dark mode considerations (if needed in future) */
  @media (prefers-color-scheme: dark) {
    .bottom-nav {
      background: #ffffff;
      border-top-color: var(--primary);
      border-top-width: 2px;
      box-shadow: 0 -6px 30px rgba(126, 217, 87, 0.2);
    }
    
    .bottom-nav-item {
      color: #8A8A8A;
    }
    
    .bottom-nav-item:hover {
      color: var(--primary);
      background: rgba(126, 217, 87, 0.1);
    }
    
    .bottom-nav-item.active {
      color: #ffffff;
      background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    }
    
    .bottom-nav-icon {
      font-weight: 400;
    }
    
    .bottom-nav-item.active .bottom-nav-icon,
    .bottom-nav-item:hover .bottom-nav-icon {
      font-weight: 500;
    }
  }
}

/* Performance optimizations */
.bottom-nav {
  will-change: transform;
  contain: layout style paint;
}

.bottom-nav-item {
  will-change: transform, color;
}

/* Mobile Sidebar Content - Hide on Desktop */
.mobile-profile, 
.mobile-divider, 
.mobile-billing-item, 
.mobile-app-footer,
.mobile-menu {
  display: none !important;
}

/* Mobile Specific Styles */
@media (max-width: 768px) {
  /* Show mobile content on mobile */
  .mobile-profile, 
  .mobile-divider, 
  .mobile-billing-item, 
  .mobile-app-footer,
  .mobile-menu {
    display: block !important;
  }
  
  /* Show mobile header on mobile */
  .mobile-header {
    display: flex !important;
  }
}

/* Force hide desktop header on mobile */
@media (max-width: 768px) {
  .vendor-header.desktop-header {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
  }
  
  .mobile-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Mobile Layout Adjustments */
@media (max-width: 768px) {
  .vendor-main-wrapper {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }
  
  .vendor-main {
    flex: 1;
    padding-bottom: 80px; /* Space for bottom nav */
  }
  
  /* Mobile header shadow on scroll */
  .mobile-header.scrolled {
    box-shadow: 0 6px 30px rgba(0, 0, 0, 0.12);
    backdrop-filter: blur(15px);
  }
  
  /* Mobile sidebar overlay */
  .vendor-mobile-overlay.active {
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(2px);
  }
}

/* Hamburger icon thickness and sizing fix */
.mobile-header-menu-btn i,
.mobile-header-menu-btn svg {
  font-size: 1.5rem;
  /* If using SVG or custom icon, increase stroke-width */
  stroke-width: 3.5px !important;
}

/* For font-based icons (like FontAwesome), use text-shadow for thickness */
.mobile-header-menu-btn i {
  text-shadow: 0 0 1px #fff, 0 0 1px #fff;
}

/* --- Restore and enhance hamburger and notification button styles --- */
.mobile-header-menu-btn {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  color: #fff;
  border: none;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 1.5rem;
  box-shadow: 0 4px 12px rgba(102, 187, 106, 0.25);
  position: relative;
}

.mobile-header-menu-btn:hover,
.mobile-header-menu-btn:active {
  background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 187, 106, 0.35);
}

.mobile-header-menu-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.mobile-header-action-btn {
  width: 44px;
  height: 44px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-dark);
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.mobile-header-action-btn:hover,
.mobile-header-action-btn:active {
  background: var(--primary-light);
  color: var(--primary);
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 187, 106, 0.15);
}

.mobile-header-action-btn:active {
  transform: translateY(0);
  transition: transform 0.1s ease;
}

.mobile-notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid #fff;
}

@media (max-width: 480px) {
  .mobile-header-menu-btn,
  .mobile-header-action-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

/* --- Ensure mobile sidebar overlays header and content on mobile --- */
@media (max-width: 900px) {
  .vendor-sidebar {
    z-index: 99999 !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
  }
  .mobile-sidebar-open .vendor-sidebar {
    display: flex !important;
    position: fixed !important;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: var(--sidebar-expanded);
    height: 100vh;
    background: #fff;
    z-index: 99999 !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    transition: transform 0.3s cubic-bezier(0.4,0,0.2,1);
  }
}

/* Optional: dark overlay for sidebar open state */
.vendor-mobile-overlay.active {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: 99998;
  background: rgba(0,0,0,0.4);
  backdrop-filter: blur(2px);
}

@media (max-width: 900px) {
  .vendor-sidebar {
    border-radius: 0 18px 18px 0 !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    overflow: hidden;
    /* Already has z-index and position: fixed from previous fix */
  }
  .mobile-sidebar-open .vendor-sidebar {
    border-radius: 0 18px 18px 0 !important;
    box-shadow: 0 8px 32px rgba(0,0,0,0.25);
    overflow: hidden;
  }
  /* --- FIX: Always show nav-item-text in mobile sidebar --- */
  .vendor-sidebar .nav-item-text {
    opacity: 1 !important;
    transform: none !important;
    position: static !important;
    pointer-events: auto !important;
    transition: none !important;
  }
}

/* ===== NUCLEAR MOBILE HEADER RESET ===== */
/* Completely reset and override all mobile header styling */

/* Force clean rendering for all mobile header elements */
.mobile-header,
.mobile-header *,
.mobile-header *:before,
.mobile-header *:after {
  all: unset !important;
  box-sizing: border-box !important;
  font-family: 'Poppins', Arial, sans-serif !important;
}

/* Rebuild mobile header from scratch */
.mobile-header {
  display: none !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 999 !important;
  background: #ffffff !important;
  border-bottom: 1px solid #f0f0f0 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
  height: 60px !important;
  width: 100% !important;
}

.mobile-header-content {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  height: 60px !important;
  padding: 0 1rem !important;
  background: #fff !important;
  width: 100% !important;
  position: relative !important;
}

.mobile-header-left {
  display: flex !important;
  align-items: center !important;
  flex: none !important;
}

.mobile-header-center {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex: 1 !important;
}

.mobile-header-right {
  display: flex !important;
  align-items: center !important;
  flex: none !important;
}

.mobile-brand-text {
  font-size: 1.2rem !important;
  font-weight: 700 !important;
  color: #4caf50 !important;
  text-align: center !important;
  margin: 0 !important;
  padding: 0 !important;
  display: block !important;
}

/* Completely rebuild buttons */
.mobile-header-menu-btn {
  width: 44px !important;
  height: 44px !important;
  background: #66bb6a !important;
  color: white !important;
  border: none !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  font-size: 1.2rem !important;
  position: relative !important;
  box-shadow: 0 2px 8px rgba(102, 187, 106, 0.3) !important;
  margin: 0 !important;
  padding: 0 !important;
}

.mobile-header-action-btn {
  width: 44px !important;
  height: 44px !important;
  background: #f8f9fa !important;
  border: 1px solid #e9ecef !important;
  border-radius: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: #4caf50 !important;
  font-size: 1.2rem !important;
  cursor: pointer !important;
  position: relative !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Icon content override */
.mobile-header-menu-btn:before {
  content: "☰" !important;
  font-family: Arial, sans-serif !important;
  font-size: 1.2rem !important;
  color: white !important;
  display: block !important;
  text-align: center !important;
  line-height: 1 !important;
}

.mobile-header-action-btn:before {
  content: "🔔" !important;
  font-family: Arial, sans-serif !important;
  font-size: 1rem !important;
  color: #4caf50 !important;
  display: block !important;
  text-align: center !important;
  line-height: 1 !important;
}

/* Hide any child elements that might cause issues */
.mobile-header-menu-btn *,
.mobile-header-action-btn * {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Notification badge */
.mobile-notification-badge {
  position: absolute !important;
  top: -4px !important;
  right: -4px !important;
  width: 18px !important;
  height: 18px !important;
  background: #ff4757 !important;
  color: white !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2px solid #fff !important;
  z-index: 1 !important;
}

/* Mobile header show on mobile */
@media (max-width: 768px) {
  .mobile-header {
    display: flex !important;
  }
}

/* ===== Native Mobile App Style Sidebar ===== */
@media (max-width: 768px) {
  .vendor-sidebar {
    display: flex !important;
    flex-direction: column;
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    width: 85vw;
    max-width: 340px;
    min-width: 280px;
    background: linear-gradient(180deg, #ffffff 0%, #fafbfc 100%);
    z-index: 10001;
    box-shadow: 4px 0 32px rgba(0,0,0,0.15), 0 0 0 1px rgba(0,0,0,0.05);
    border-top-right-radius: 28px;
    border-bottom-right-radius: 28px;
    transform: translateX(-100%);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    overflow-x: hidden;
  }
  
  .mobile-sidebar-open .vendor-sidebar {
    transform: translateX(0);
  }
  
  /* Native App Profile Header */
  .mobile-profile-card {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 2rem 1.75rem 1.5rem 1.75rem;
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    border-top-right-radius: 28px;
    position: relative;
    overflow: hidden;
  }
  
  .mobile-profile-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100px;
    height: 100px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    opacity: 0.6;
  }
  
  .mobile-user-info {
    flex: 1;
    z-index: 2;
  }
  
  .mobile-user-name {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.3;
    margin-bottom: 4px;
  }
  
  .mobile-user-role {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.85);
    font-weight: 500;
    margin-bottom: 2px;
  }
  
  .mobile-user-email {
    font-size: 0.8rem;
    color: rgba(255,255,255,0.7);
    font-weight: 400;
  }
  
  .mobile-profile-badge {
    width: 48px;
    height: 48px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255,255,255,0.3);
    z-index: 2;
  }
  
  /* Native Navigation List */
  .nav-list.mobile-menu {
    margin: 0;
    padding: 1.5rem 0 0 0;
    list-style: none;
    flex: 1;
  }
  
  .nav-list.mobile-menu .nav-item {
    margin: 0 1rem 0.5rem 1rem;
  }
  
  .nav-list.mobile-menu .nav-link {
    display: flex;
    align-items: center;
    gap: 1.2rem;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    color: #2c3e50;
    background: transparent;
    border: none;
    border-radius: 16px;
    font-weight: 500;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    position: relative;
    min-height: 52px;
  }
  
  .nav-list.mobile-menu .nav-link:hover {
    background: rgba(102, 187, 106, 0.08);
    color: var(--primary-dark);
    transform: translateX(4px);
  }
  
  .nav-list.mobile-menu .nav-link.active {
    background: linear-gradient(135deg, var(--primary-light) 0%, rgba(102, 187, 106, 0.15) 100%);
    color: var(--primary-dark);
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(102, 187, 106, 0.2);
    transform: translateX(4px);
  }
  
  .nav-list.mobile-menu .nav-link i {
    font-size: 1.3rem;
    color: var(--primary);
    min-width: 24px;
    text-align: center;
    transition: all 0.3s ease;
  }
  
  .nav-list.mobile-menu .nav-link.active i {
    color: var(--primary-dark);
    transform: scale(1.1);
  }
  
  /* Modern Dividers */
  .nav-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #e0e6ed 50%, transparent 100%);
    margin: 1rem 2rem;
    border-radius: 2px;
  }
  
  /* Disabled Items */
  .mobile-billing-item {
    opacity: 0.5;
    pointer-events: none;
  }
  
  .mobile-billing-item .nav-link {
    color: #95a5a6;
  }
  
  /* Logout Button */
  .logout-btn {
    color: #e74c3c !important;
    background: none;
    border: none;
    font-weight: 600;
    font-size: 1rem;
    padding: 1rem 1.5rem;
    border-radius: 16px;
    width: 100%;
    text-align: left;
    display: flex;
    align-items: center;
    gap: 1.2rem;
    transition: all 0.3s ease;
    min-height: 52px;
    margin: 0 1rem;
  }
  
  .logout-btn i {
    color: #e74c3c;
    font-size: 1.3rem;
    min-width: 24px;
    text-align: center;
  }
  
  .logout-btn:hover,
  .logout-btn:active {
    background: rgba(231, 76, 60, 0.1);
    transform: translateX(4px);
  }
  
  /* Modern Footer */
  .mobile-app-footer {
    margin-top: auto;
    padding: 1.5rem 1.75rem 2rem 1.75rem;
    border-top: 1px solid rgba(224, 230, 237, 0.6);
    background: linear-gradient(180deg, rgba(250,251,252,0.8) 0%, #f8f9fa 100%);
    border-bottom-right-radius: 28px;
    backdrop-filter: blur(10px);
  }
  
  .mobile-app-footer .app-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-dark);
    margin-bottom: 4px;
  }
  
  .mobile-app-footer .version-info {
    font-size: 0.85rem;
    color: #7f8c8d;
    margin-bottom: 0.8rem;
    font-weight: 500;
  }
  
  .mobile-app-footer .made-in-india {
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 0.9rem;
    color: #27ae60;
    font-weight: 500;
  }
  
  .mobile-app-footer .made-in-india i {
    color: #e74c3c;
    animation: heartbeat 2s ease-in-out infinite;
  }
  
  @keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
  
  .mobile-app-footer .india-flag {
    width: 24px;
    height: 16px;
    border-radius: 3px;
    margin-left: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.15);
    border: 1px solid rgba(255,255,255,0.8);
  }
  
  /* Smooth scrollbar for mobile sidebar */
  .vendor-sidebar::-webkit-scrollbar {
    width: 4px;
  }
  
  .vendor-sidebar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .vendor-sidebar::-webkit-scrollbar-thumb {
    background: rgba(102, 187, 106, 0.3);
    border-radius: 2px;
  }
  
  .vendor-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 187, 106, 0.5);
  }
}

/* Hide sidebar-footer in mobile view */
@media (max-width: 768px) {
  .sidebar-footer {
    display: none !important;
  }
  
  /* Hide sidebar header (WhaMart Vendor Panel) in mobile sidebar */
  .vendor-sidebar .sidebar-header {
    display: none !important;
  }
}
