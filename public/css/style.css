/* --- Whamart Modern Redesign (Light Green) --- */

/* --- General & Fonts --- */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    --whatsapp-green: #7ED957; /* Light green */
    --whatsapp-green-dark: #5DC264; /* Medium green */
    --whatsapp-teal: #4CAF50; /* Light teal */
    --whatsapp-teal-dark: #3EA045; /* Medium teal */
    --light-gray: #f0f2f5;
    --light-bg: #f7f8fa;
    --dark-text: #333;
    --light-text: #666;
    --section-bg: #F8FFF5; /* Very light green background */
    --hover-bg: #E8FFE1; /* Hover light green */
}

body, .whamart-global-bg {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    background-color: #fff;
    margin: 0;
}

.global-bg-wrapper {
    min-height: 100vh;
    background: linear-gradient(120deg, #f7f8fa 0%, #e9ffe8 100%);
    position: relative;
    z-index: 0;
}

/* Add subtle background SVG pattern if desired */
.global-bg-wrapper::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background-image: url('/img/hero-pattern.svg');
    background-repeat: repeat;
    opacity: 0.04;
    z-index: 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* --- Hero Section --- */
.hero-section.global-hero {
    background: linear-gradient(120deg, #f7f8fa 60%, #e9ffe8 100%);
    padding: 80px 0 40px 0;
    position: relative;
    z-index: 1;
}
.hero-flex {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    justify-content: center;
    gap: 40px;
}
@media (min-width: 900px) {
    .hero-flex {
        flex-direction: row;
        justify-content: space-between;
        gap: 80px;
    }
}
.hero-main {
    flex: 1;
    min-width: 300px;
}
.hero-title {
    font-size: 2.7rem;
    font-weight: 800;
    color: var(--whatsapp-green-dark);
    margin-bottom: 18px;
    line-height: 1.13;
}
.hero-subtitle {
    font-size: 1.25rem;
    color: var(--dark-text);
    margin-bottom: 28px;
    opacity: 0.85;
}
.hero-cta-row {
    display: flex;
    gap: 18px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}
.hero-social-proof {
    margin-top: 18px;
    display: flex;
    align-items: center;
    gap: 18px;
    font-size: 1rem;
    color: var(--light-text);
}
.hero-social-proof .badges-img {
    height: 28px;
}
.hero-visual {
    flex: 1;
    min-width: 260px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.hero-visual img {
    max-width: 400px;
    width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 30px rgba(126,217,87,0.13));
}

/* --- Social Proof Section --- */
.social-proof-section {
    background: #f8fff5;
    padding: 32px 0 12px 0;
    text-align: center;
}
.social-proof-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 32px;
    margin-bottom: 10px;
}
.social-proof-logos img {
    height: 32px;
    opacity: 0.8;
    filter: grayscale(1);
    transition: opacity 0.2s;
}
.social-proof-logos img:hover {
    opacity: 1;
    filter: none;
}
.ratings-row {
    font-size: 1rem;
    color: var(--light-text);
    display: flex;
    justify-content: center;
    gap: 24px;
}

/* --- Benefits Section --- */
.benefits-section {
    background: #fff;
    padding: 80px 0 40px 0;
}
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 32px;
    margin-top: 48px;
}
.benefit-card {
    background: #f8fff5;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.05);
    padding: 36px 28px;
    text-align: center;
    transition: box-shadow 0.2s, transform 0.2s;
    position: relative;
}
.benefit-card:hover {
    box-shadow: 0 8px 32px rgba(126,217,87,0.13);
    transform: translateY(-5px) scale(1.03);
}
.benefit-card .icon {
    font-size: 2.3rem;
    color: var(--whatsapp-green-dark);
    margin-bottom: 18px;
}
.benefit-card h3 {
    font-size: 1.17rem;
    font-weight: 700;
    margin-bottom: 10px;
}
.benefit-card p {
    color: var(--light-text);
    font-size: 1rem;
}

/* --- How It Works Section --- */
.howitworks-section {
    background: #f7f8fa;
    padding: 80px 0 40px 0;
}
.howitworks-steps {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin-top: 38px;
}
@media (min-width: 900px) {
    .howitworks-steps {
        flex-direction: row;
        justify-content: center;
    }
}
.step {
    background: #fff;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.08);
    padding: 32px 26px;
    text-align: center;
    flex: 1;
    min-width: 180px;
    position: relative;
}
.step-number {
    background: var(--whatsapp-green);
    color: #fff;
    font-weight: 700;
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(126,217,87,0.13);
}
.step h4 {
    font-size: 1.18rem;
    margin-bottom: 8px;
}
.step p {
    color: var(--light-text);
    font-size: 1rem;
}

/* --- Solutions Section --- */
.solutions-section {
    background: #fff;
    padding: 80px 0 40px 0;
}
.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 32px;
    margin-top: 48px;
}
.solution-card {
    background: #f7f8fa;
    border-radius: 14px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.06);
    padding: 32px 20px;
    text-align: center;
    transition: box-shadow 0.2s, transform 0.2s;
    position: relative;
}
.solution-card:hover {
    box-shadow: 0 8px 32px rgba(126,217,87,0.13);
    transform: translateY(-4px) scale(1.02);
}
.solution-card .icon {
    font-size: 2rem;
    color: var(--whatsapp-teal-dark);
    margin-bottom: 14px;
}
.solution-card h4 {
    font-size: 1.07rem;
    font-weight: 700;
    margin-bottom: 8px;
}
.solution-card p {
    color: var(--light-text);
    font-size: 0.98rem;
}

/* --- Testimonials Section --- */
.testimonials-section {
    background-color: var(--light-bg);
    padding: 80px 0;
}
.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}
.testimonial-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}
.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}
.testimonial-content {
    position: relative;
    margin-bottom: 20px;
}
.testimonial-content p {
    font-style: italic;
    color: var(--dark-text);
    line-height: 1.7;
}
.testimonial-content::before {
    content: '\201C';
    font-size: 4rem;
    font-family: Georgia, serif;
    color: var(--whatsapp-green);
    opacity: 0.3;
    position: absolute;
    top: -2rem;
    left: -0.5rem;
    z-index: 0;
}
.testimonial-author {
    display: flex;
    align-items: center;
}
.author-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-text);
}
.author-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--light-text);
}

/* --- CTA Section --- */
.cta-section.global-cta {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}
.cta-section.global-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/img/cta-pattern.svg');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}
.cta-section .container {
    position: relative;
    z-index: 1;
}
.cta-content {
    max-width: 800px;
    margin: 0 auto;
}
.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}
.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}
.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}
.cta-section .btn-outline {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}
.cta-section .btn-outline:hover {
    background-color: rgba(255,255,255,0.08);
    color: white;
}

/* --- Section Headings & Tags --- */
.section-heading {
    text-align: center;
    margin-bottom: 18px;
}
.section-tag {
    display: inline-block;
    background: var(--whatsapp-green);
    color: white;
    padding: 5px 16px;
    border-radius: 30px;
    font-size: 0.88rem;
    font-weight: 600;
    margin-bottom: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* --- Responsive Tweaks --- */
@media (max-width: 600px) {
    .hero-title { font-size: 2rem; }
    .hero-visual img { max-width: 260px; }
    .benefits-grid, .solutions-grid, .testimonials-grid { grid-template-columns: 1fr; }
    .howitworks-steps { flex-direction: column; }
    .cta-section h2 { font-size: 2rem; }
}

/* =====================
   MODERN HEADER STYLES
   ===================== */

.modern-header {
    background: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
    font-family: 'Poppins', sans-serif;
}

.header-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

/* Logo Section */
.header-logo-section {
    flex-shrink: 0;
}

.header-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
}

.header-logo-img {
    height: 45px;
    width: auto;
    transition: transform 0.3s ease;
}

.header-logo-img:hover {
    transform: scale(1.05);
}

.header-logo-fallback {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #fff;
    font-size: 1.8rem;
    font-weight: 700;
}

/* Navigation */
.header-nav {
    display: none;
    align-items: center;
    gap: 35px;
}

@media (min-width: 992px) {
    .header-nav {
        display: flex;
    }
}

.nav-link {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    padding: 8px 0;
    position: relative;
    transition: all 0.3s ease;
}

.nav-link:hover {
    color: var(--whatsapp-green);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 50%;
    background: var(--whatsapp-green);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 100%;
}

/* Header Actions */
.header-actions {
    display: none;
    align-items: center;
    gap: 15px;
}

@media (min-width: 992px) {
    .header-actions {
        display: flex;
    }
}

.btn-signin {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 15px;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-signin:hover {
    background: #f9fafb;
    color: var(--whatsapp-green);
}

.btn-signup {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
    padding: 12px 24px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(126, 217, 87, 0.2);
}

.btn-signup:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(126, 217, 87, 0.3);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: flex;
    align-items: center;
}

@media (min-width: 992px) {
    .mobile-menu-toggle {
        display: none;
    }
}

.menu-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: background 0.3s ease;
}

.menu-button:hover {
    background: #f3f4f6;
}

.menu-icon {
    display: flex;
    flex-direction: column;
    width: 24px;
    height: 18px;
    justify-content: space-between;
}

.menu-icon span {
    display: block;
    height: 2px;
    width: 100%;
    background: #374151;
    border-radius: 1px;
    transition: all 0.3s ease;
}

/* Mobile Menu */
.mobile-menu {
    background: #ffffff;
    border-top: 1px solid #e5e7eb;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.mobile-menu.hidden {
    display: none;
}

.mobile-menu-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.mobile-nav-link {
    display: block;
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 15px 0;
    border-bottom: 1px solid #f3f4f6;
    transition: color 0.3s ease;
}

.mobile-nav-link:hover {
    color: var(--whatsapp-green);
}

.mobile-nav-link:last-of-type {
    border-bottom: none;
}

.mobile-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.mobile-btn-signin {
    color: #374151;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 12px 20px;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
}

.mobile-btn-signin:hover {
    background: #f9fafb;
    border-color: var(--whatsapp-green);
    color: var(--whatsapp-green);
}

.mobile-btn-signup {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 14px 20px;
    border-radius: 8px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(126, 217, 87, 0.2);
}

.mobile-btn-signup:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(126, 217, 87, 0.3);
}

/* =====================
   END MODERN HEADER STYLES
   ===================== */

.header-buttons {
    display: none; /* Hidden by default, shown on desktop */
}

@media (min-width: 768px) {
    .header-buttons {
        display: flex;
        align-items: center;
        gap: 15px;
    }
}

.btn-header-signin, .btn-header-signup {
    padding: 8px 20px;
    border-radius: 20px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-header-signin {
    background-color: transparent;
    border: 1px solid var(--light-gray);
    color: var(--light-gray);
}

.btn-header-signin:hover {
    background-color: white;
    color: var(--whatsapp-teal-dark);
}

.btn-header-signup {
    background-color: var(--whatsapp-green);
    border: 1px solid var(--whatsapp-green);
    color: white;
}

.btn-header-signup:hover {
    background-color: var(--whatsapp-green-dark);
}

.mobile-menu-button-container {
    display: block;
}

@media (min-width: 768px) {
    .mobile-menu-button-container {
        display: none;
    }
}

#mobile-menu-button {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

.mobile-menu {
    background-color: var(--whatsapp-green);
    padding: 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: absolute;
    top: 70px; /* Adjust based on header height */
    left: 0;
    right: 0;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.mobile-menu a {
    color: white;
    font-weight: 500;
    padding: 10px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.mobile-menu a:hover {
    background-color: var(--whatsapp-green-dark);
}

.hidden {
    display: none;
}

/* --- General Button Styles --- */
.btn {
    display: inline-block;
    padding: 12px 28px;
    border-radius: 50px;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-outline {
    background-color: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn-primary {
    background-color: var(--whatsapp-green);
    color: white;
    border: 2px solid var(--whatsapp-green);
}

.btn-primary:hover {
    background-color: var(--whatsapp-green-dark);
    border-color: var(--whatsapp-green-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: transparent;
    color: var(--whatsapp-teal-dark);
    border: 2px solid var(--whatsapp-teal-dark);
}

.btn-secondary:hover {
    background-color: var(--whatsapp-teal-dark);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

/* --- Hero Section --- */
.hero-section {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/img/hero-pattern.svg');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.hero-section .container {
    position: relative;
    z-index: 1;
}

.hero-section .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
}

@media (min-width: 992px) {
    .hero-section .container {
        flex-direction: row;
    }
    
    .hero-content {
        width: 50%;
        text-align: left;
    }
    
    .hero-image {
        width: 45%;
    }
    
    .hero-section p {
        margin: 0 0 30px;
    }
}

.hero-content {
    z-index: 1;
    margin-bottom: 40px;
    text-align: center;
}

.hero-image {
    z-index: 1;
    max-width: 100%;
}

.hero-image img {
    width: 100%;
    height: auto;
    filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
}

.hero-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    line-height: 1.2;
}

@media (min-width: 768px) {
    .hero-section h1 {
        font-size: 3.5rem;
    }
}

.hero-section p {
    font-size: 1.25rem;
    max-width: 700px;
    margin: 0 auto 30px;
    color: var(--light-gray);
}

.hero-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

@media (min-width: 992px) {
    .hero-buttons {
        justify-content: flex-start;
    }
}

/* --- Problem & Features Sections --- */
.problem-section, .features-section, .pricing-section {
    padding: 80px 0;
}

.problem-section, .pricing-section {
    background-color: var(--light-bg);
}

.section-heading {
    text-align: center;
    margin-bottom: 60px;
}

.section-heading h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 10px;
}

.section-heading p {
    font-size: 1.1rem;
    color: var(--light-text);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
}

.feature-card {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background-color: var(--hover-bg);
}

.feature-card .icon {
    font-size: 3rem;
    color: var(--whatsapp-green);
    margin-bottom: 20px;
}

.feature-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 15px;
}

.feature-card p {
    color: var(--light-text);
}

/* --- Features Section --- */
.features-section {
    background-color: var(--section-bg);
    padding: 80px 0;
}

/* --- Pricing Section --- */
.pricing-section {
    background-color: white;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.pricing-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/img/pricing-pattern.svg');
    background-size: cover;
    opacity: 0.05;
    z-index: 0;
}

.pricing-section .container {
    position: relative;
    z-index: 1;
}
.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.pricing-card {
    background-color: white;
    padding: 40px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border-top: 5px solid transparent;
    transition: all 0.3s ease;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-top-color: var(--whatsapp-green);
    background-color: var(--hover-bg);
}

.pricing-card h3 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--dark-text);
    margin-bottom: 15px;
}

.pricing-card .price {
    font-size: 3rem;
    font-weight: 700;
    color: var(--whatsapp-teal-dark);
    margin-bottom: 10px;
}

.pricing-card .price span {
    font-size: 1rem;
    font-weight: 500;
    color: var(--light-text);
}

.pricing-card ul {
    list-style: none;
    padding: 0;
    margin: 30px 0;
    text-align: left;
}

.pricing-card ul li {
    margin-bottom: 15px;
    color: var(--light-text);
}

.pricing-card ul li i {
    color: var(--whatsapp-green);
    margin-right: 10px;
}

/* --- Section Tags --- */
.section-tag {
    display: inline-block;
    background-color: var(--hover-bg);
    color: var(--whatsapp-green-dark);
    padding: 5px 15px;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 600;
    margin-bottom: 15px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* --- CTA Section --- */
.cta-section {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('/img/cta-pattern.svg');
    background-size: cover;
    opacity: 0.1;
    z-index: 0;
}

.cta-section .container {
    position: relative;
    z-index: 1;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
    flex-wrap: wrap;
}

.cta-section h2 {
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.cta-section p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-section .btn-secondary {
    background-color: white;
    color: var(--whatsapp-green);
    border-color: white;
}

.cta-section .btn-secondary:hover {
    background-color: #f0f0f0;
    border-color: #f0f0f0;
}

/* =====================
   NEW FOOTER STYLES
   ===================== */

.new-footer {
    background: #0f172a;
    color: #e2e8f0;
    padding: 80px 0 0;
    font-family: 'Poppins', sans-serif;
    position: relative;
    overflow: hidden;
}

.footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Footer Grid */
.footer-main {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    padding-bottom: 60px;
    position: relative;
}

.footer-main::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.1);
}

/* Brand Column */
.footer-brand {
    grid-column: span 1;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    text-decoration: none;
    color: white;
    font-size: 24px;
    font-weight: 700;
}

.footer-logo .logo-img {
    height: 32px;
    width: auto;
    display: block;
}

.footer-about {
    color: #94a3b8;
    font-size: 14px;
    line-height: 1.7;
    margin-bottom: 25px;
}

/* Social Icons */
.footer-social {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    color: #cbd5e1;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: var(--whatsapp-green);
    color: #0f172a;
    transform: translateY(-2px);
}

/* Links Groups */
.footer-links-group {
    display: flex;
    flex-direction: column;
}

.footer-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 30px;
    height: 2px;
    background: var(--whatsapp-green);
}

.footer-links-list {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-links-list a {
    color: #94a3b8;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    display: inline-block;
    position: relative;
}

.footer-links-list a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 1px;
    bottom: -2px;
    left: 0;
    background: var(--whatsapp-green);
    transition: width 0.3s ease;
}

.footer-links-list a:hover {
    color: white;
    padding-left: 5px;
}

.footer-links-list a:hover::after {
    width: 100%;
}

/* Newsletter Section */
.footer-newsletter {
    grid-column: span 1;
}

.newsletter-desc {
    color: #94a3b8;
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.6;
}

.newsletter-form {
    margin-bottom: 25px;
}

.input-group {
    position: relative;
    display: flex;
    border-radius: 8px;
    overflow: hidden;
    background: #1e293b;
    border: 1px solid #334155;
    transition: all 0.3s ease;
}

.input-group:focus-within {
    border-color: var(--whatsapp-green);
    box-shadow: 0 0 0 1px var(--whatsapp-green);
}

.input-group input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 12px 15px;
    color: white;
    font-size: 14px;
    outline: none;
}

.input-group input::placeholder {
    color: #64748b;
}

.submit-btn {
    background: var(--whatsapp-green);
    border: none;
    color: #0f172a;
    padding: 0 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.submit-btn:hover {
    background: #6bcb63;
}

/* Contact Info */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #94a3b8;
    font-size: 14px;
}

.contact-item i {
    color: var(--whatsapp-green);
    width: 16px;
    text-align: center;
}

/* Footer Bottom */
.footer-bottom {
    padding: 25px 0;
}

.footer-legal {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    color: #64748b;
    font-size: 13px;
}

.legal-links {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.legal-links a {
    color: #94a3b8;
    text-decoration: none;
    transition: color 0.2s ease;
}

.legal-links a:hover {
    color: var(--whatsapp-green);
}

.divider {
    color: #475569;
    font-size: 12px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-main {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px 30px;
    }
    
    .footer-newsletter {
        grid-column: span 2;
        max-width: 500px;
    }
}

@media (max-width: 640px) {
    .footer-main {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .footer-newsletter {
        grid-column: span 1;
    }
    
    .footer-legal {
        flex-direction: column;
        text-align: center;
    }
    
    .legal-links {
        justify-content: center;
    }
}

/* =====================
   END NEW FOOTER STYLES
   ===================== */

/* =====================
   MODERN SINGLE ROW FOOTER
   ===================== */

.modern-footer {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    color: #e2e8f0;
    padding: 60px 0 0;
    font-family: 'Poppins', sans-serif;
    position: relative;
    overflow: hidden;
}

.modern-footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
}

.footer-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Single Row Layout */
.footer-single-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 40px;
    padding-bottom: 40px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
}

/* Brand Section */
.footer-brand-section {
    flex: 0 0 280px;
    min-width: 280px;
}

.footer-logo {
    display: flex;
    align-items: center;
    text-decoration: none;
    margin-bottom: 15px;
}

.footer-logo-img {
    height: 40px;
    width: auto;
    margin-right: 12px;
}

.footer-logo-fallback {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #fff;
    font-size: 1.5rem;
    font-weight: 700;
    margin-right: 12px;
}

.footer-logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    color: #fff;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.footer-tagline {
    color: #94a3b8;
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
    max-width: 260px;
}

/* Links Sections */
.footer-links-section {
    flex: 0 0 auto;
    min-width: 140px;
}

.footer-section-title {
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    margin: 0 0 20px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
}

.footer-section-title::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -8px;
    width: 30px;
    height: 2px;
    background: var(--whatsapp-green);
    border-radius: 1px;
}

.footer-links-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.footer-link {
    color: #94a3b8;
    text-decoration: none;
    font-size: 14px;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
    padding: 4px 0;
}

.footer-link:hover {
    color: var(--whatsapp-green);
    padding-left: 8px;
}

.footer-link::before {
    content: '';
    position: absolute;
    left: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background: var(--whatsapp-green);
    transition: width 0.3s ease;
}

.footer-link:hover::before {
    width: 4px;
}

/* Contact Section */
.footer-contact-section {
    flex: 0 0 220px;
    min-width: 220px;
}

.footer-contact-info {
    margin-bottom: 25px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
    color: #94a3b8;
    font-size: 14px;
}

.contact-item i {
    color: var(--whatsapp-green);
    width: 16px;
    text-align: center;
    font-size: 14px;
}

.footer-social-icons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    color: #94a3b8;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 16px;
}

.social-icon:hover {
    background: var(--whatsapp-green);
    border-color: var(--whatsapp-green);
    color: #fff;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(126, 217, 87, 0.3);
}

/* Footer Bottom */
.footer-bottom-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.footer-copyright p {
    color: #64748b;
    font-size: 14px;
    margin: 0;
}

.footer-made-in-india {
    color: #64748b;
    font-size: 14px;
}

.footer-made-in-india i {
    color: #ff4d4d;
    margin: 0 4px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .footer-single-row {
        gap: 30px;
    }

    .footer-brand-section {
        flex: 0 0 250px;
        min-width: 250px;
    }

    .footer-contact-section {
        flex: 0 0 200px;
        min-width: 200px;
    }
}

@media (max-width: 992px) {
    .footer-single-row {
        flex-wrap: wrap;
        gap: 40px 30px;
    }

    .footer-brand-section {
        flex: 0 0 100%;
        min-width: auto;
        max-width: 400px;
        margin-bottom: 20px;
    }

    .footer-links-section {
        flex: 0 0 calc(25% - 22.5px);
        min-width: 140px;
    }

    .footer-contact-section {
        flex: 0 0 100%;
        min-width: auto;
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .modern-footer {
        padding: 40px 0 0;
    }

    .footer-single-row {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .footer-brand-section,
    .footer-links-section,
    .footer-contact-section {
        flex: none;
        min-width: auto;
        width: 100%;
    }

    .footer-links-list {
        align-items: center;
    }

    .footer-social-icons {
        justify-content: center;
    }

    .footer-bottom-bar {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .footer-container {
        padding: 0 15px;
    }

    .footer-single-row {
        gap: 25px;
    }

    .footer-logo-text {
        font-size: 1.6rem;
    }

    .footer-social-icons {
        gap: 12px;
    }

    .social-icon {
        width: 36px;
        height: 36px;
        font-size: 14px;
    }
}

/* =====================
   END MODERN SINGLE ROW FOOTER
   ===================== */

/* =====================
   MODERN HOME PAGE SECTIONS
   ===================== */

/* Section Backgrounds */
.section-white {
    background: #ffffff;
}

.section-light-green {
    background: rgba(126, 217, 87, 0.08);
}

/* Common Section Styles */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-tag {
    display: inline-block;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding: 8px 16px;
    border-radius: 20px;
    margin-bottom: 20px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 20px 0;
    line-height: 1.2;
    position: relative;
}

.highlight-text {
    color: var(--whatsapp-green);
    position: relative;
}

.title-underline {
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 2px;
    opacity: 0;
    animation: slideIn 0.8s ease 0.5s forwards;
}

@keyframes slideIn {
    from {
        width: 0;
        opacity: 0;
    }
    to {
        width: 60px;
        opacity: 1;
    }
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    max-width: 600px;
    margin: 0 auto;
}

/* =====================
   MODERN HERO SECTION
   ===================== */

.modern-hero-section {
    background: linear-gradient(135deg, #ffffff 0%, rgba(126, 217, 87, 0.05) 100%);
    padding: 40px 0 80px;
    position: relative;
    overflow: hidden;
    min-height: calc(100vh - 70px);
    display: flex;
    align-items: center;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
    z-index: 2;
    width: 100%;
}

.hero-content {
    display: grid;
    grid-template-columns: 1.2fr 0.8fr;
    gap: 60px;
    align-items: center;
    width: 100%;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(126, 217, 87, 0.1);
    border: 1px solid rgba(126, 217, 87, 0.3);
    padding: 8px 16px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    color: var(--whatsapp-green);
    margin-bottom: 30px;
}

.badge-icon {
    font-size: 16px;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1a1a1a;
    line-height: 1.15;
    margin: 0 0 25px 0;
    position: relative;
}

.hero-title .highlight-text {
    color: var(--whatsapp-green);
    position: relative;
}

.hero-title .title-underline {
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 2px;
    animation: expandLine 1s ease 1s forwards;
}

@keyframes expandLine {
    to {
        width: 100%;
    }
}

.hero-description {
    font-size: 1.2rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 480px;
}

.hero-actions {
    display: flex;
    gap: 15px;
    margin-bottom: 40px;
    align-items: center;
    flex-wrap: wrap;
}

.btn-hero-primary {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
    padding: 14px 28px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(126, 217, 87, 0.3);
    white-space: nowrap;
}

.btn-hero-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(126, 217, 87, 0.4);
}

.btn-hero-secondary {
    background: transparent;
    color: #374151;
    text-decoration: none;
    font-weight: 600;
    font-size: 15px;
    padding: 14px 28px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-hero-secondary:hover {
    border-color: var(--whatsapp-green);
    color: var(--whatsapp-green);
    background: rgba(126, 217, 87, 0.05);
}

.hero-trust {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    flex-wrap: wrap;
}

.trust-text {
    font-size: 13px;
    color: #666;
    font-weight: 500;
    margin-top: 8px;
}

.trust-logos {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.trust-item {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 10px;
    background: rgba(126, 217, 87, 0.1);
    border-radius: 6px;
    font-size: 11px;
    color: var(--whatsapp-green);
    font-weight: 500;
}

/* Hero Visual */
.hero-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.floating-card {
    position: absolute;
    background: #ffffff;
    border-radius: 12px;
    padding: 15px 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #374151;
    animation: float 3s ease-in-out infinite;
    z-index: 10;
    border: 1px solid rgba(126, 217, 87, 0.1);
}

.floating-card i {
    color: var(--whatsapp-green);
    font-size: 18px;
}

.card-1 {
    top: 15%;
    left: -15%;
    animation-delay: 0s;
}

.card-2 {
    top: 55%;
    right: -10%;
    animation-delay: 1s;
}

.card-3 {
    bottom: 15%;
    left: 10%;
    animation-delay: 2s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.hero-main-visual {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 5;
}

.phone-mockup {
    width: 260px;
    height: 520px;
    background: #1a1a1a;
    border-radius: 25px;
    padding: 15px;
    position: relative;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.25);
    margin: 0 auto;
    z-index: 5;
}

.phone-screen {
    width: 100%;
    height: 100%;
    background: #ffffff;
    border-radius: 20px;
    overflow: hidden;
    position: relative;
}

.chat-interface {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.chat-header {
    background: var(--whatsapp-green);
    color: #ffffff;
    padding: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
}

.store-avatar {
    width: 35px;
    height: 35px;
    background: #ffffff;
    border-radius: 50%;
    opacity: 0.9;
}

.chat-messages {
    flex: 1;
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: #f0f2f5;
}

.message-bubble {
    background: #ffffff;
    padding: 12px 16px;
    border-radius: 18px;
    max-width: 80%;
    font-size: 14px;
    line-height: 1.4;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-bubble.user {
    background: var(--whatsapp-green);
    color: #ffffff;
    align-self: flex-end;
    margin-left: auto;
}

/* Background Elements */
.hero-background-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
}

.bg-circle {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);
    animation: floatCircle 6s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 5%;
    animation-delay: 2s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 50%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes floatCircle {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

/* Responsive Hero */
@media (max-width: 1200px) {
    .hero-container {
        max-width: 1000px;
    }

    .hero-content {
        gap: 50px;
    }

    .hero-title {
        font-size: 3.2rem;
    }
}

@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 50px;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .floating-card {
        display: none;
    }

    .hero-description {
        max-width: 600px;
        margin: 0 auto 40px;
    }
}

@media (max-width: 768px) {
    .modern-hero-section {
        padding: 30px 0 60px;
        min-height: auto;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-description {
        font-size: 1.1rem;
        margin-bottom: 35px;
    }

    .hero-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        margin-bottom: 35px;
    }

    .btn-hero-primary,
    .btn-hero-secondary {
        justify-content: center;
        padding: 12px 24px;
    }

    .phone-mockup {
        width: 220px;
        height: 440px;
    }

    .trust-logos {
        justify-content: center;
    }

    .hero-trust {
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2.2rem;
    }

    .phone-mockup {
        width: 200px;
        height: 400px;
    }

    .trust-item {
        font-size: 10px;
        padding: 5px 8px;
    }
}

/* =====================
   SOCIAL PROOF SECTION
   ===================== */

.social-proof-section {
    padding: 100px 0;
}

.proof-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-bottom: 60px;
}

.proof-card {
    text-align: center;
    padding: 40px 20px;
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.proof-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.proof-card:hover::before {
    transform: scaleX(1);
}

.proof-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.proof-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    font-size: 32px;
    color: #ffffff;
}

.proof-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: #1a1a1a;
    margin-bottom: 10px;
}

.proof-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

.testimonial-preview {
    display: flex;
    justify-content: center;
    margin-top: 60px;
}

.testimonial-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    max-width: 600px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: 30px;
    font-size: 80px;
    color: var(--whatsapp-green);
    font-family: serif;
    line-height: 1;
}

.testimonial-content {
    font-size: 1.2rem;
    color: #374151;
    line-height: 1.6;
    margin-bottom: 30px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
}

.author-name {
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 5px;
}

.author-business {
    font-size: 14px;
    color: #666;
}

/* Responsive Social Proof */
@media (max-width: 1024px) {
    .proof-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .social-proof-section {
        padding: 80px 0;
    }

    .proof-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .proof-card {
        padding: 30px 20px;
    }

    .proof-number {
        font-size: 2rem;
    }

    .testimonial-card {
        padding: 30px 25px;
        margin: 0 20px;
    }

    .testimonial-content {
        font-size: 1.1rem;
    }
}

/* =====================
   PROBLEM SOLUTION SECTION
   ===================== */

.problem-solution-section {
    padding: 100px 0;
}

.problem-solution-grid {
    display: flex;
    flex-direction: column;
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.problem-solution-card {
    display: grid;
    grid-template-columns: auto 1fr auto auto 1fr;
    gap: 30px;
    align-items: center;
    background: #ffffff;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.problem-solution-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    flex-shrink: 0;
}

.problem-icon {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
}

.solution-icon {
    background: rgba(126, 217, 87, 0.1);
    color: var(--whatsapp-green);
}

.card-content {
    flex: 1;
}

.card-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 10px 0;
}

.card-description {
    color: #666;
    line-height: 1.5;
    margin: 0;
}

.solution-arrow {
    color: var(--whatsapp-green);
    font-size: 24px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Responsive Problem Solution */
@media (max-width: 768px) {
    .problem-solution-section {
        padding: 80px 0;
    }

    .problem-solution-card {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 20px;
        padding: 30px 20px;
    }

    .solution-arrow {
        transform: rotate(90deg);
    }
}

/* =====================
   FEATURES SECTION
   ===================== */

.features-section {
    padding: 100px 0;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
}

.feature-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 40px 30px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
    font-size: 32px;
    color: #ffffff;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 15px 0;
}

.feature-description {
    color: #666;
    line-height: 1.6;
    margin: 0 0 20px 0;
}

.feature-highlight {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    background: rgba(126, 217, 87, 0.1);
    color: var(--whatsapp-green);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.feature-highlight i {
    font-size: 12px;
}

/* Responsive Features */
@media (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

@media (max-width: 768px) {
    .features-section {
        padding: 80px 0;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .feature-card {
        padding: 30px 25px;
    }
}

/* =====================
   COMPARISON SECTION
   ===================== */

.comparison-section {
    padding: 100px 0;
}

.comparison-table {
    background: #ffffff;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 40px rgba(0, 0, 0, 0.1);
    margin-bottom: 60px;
}

.comparison-header {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 2px solid #e5e7eb;
}

.comparison-feature {
    padding: 25px 30px;
    font-weight: 600;
    color: #1a1a1a;
    font-size: 16px;
    display: flex;
    align-items: center;
}

.comparison-option {
    padding: 30px 20px;
    text-align: center;
    border-left: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    position: relative;
}

.comparison-option.highlight {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    position: relative;
}

.comparison-option.highlight::before {
    content: 'RECOMMENDED';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: #ffffff;
    font-size: 11px;
    font-weight: 700;
    padding: 6px 14px;
    border-radius: 15px;
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
    z-index: 10;
    white-space: nowrap;
}

.option-logo {
    width: 40px;
    height: 40px;
    background: rgba(126, 217, 87, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: var(--whatsapp-green);
}

.comparison-option.highlight .option-logo {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
}

.option-name {
    font-weight: 600;
    font-size: 14px;
}

.comparison-row {
    display: grid;
    grid-template-columns: 2fr repeat(4, 1fr);
    border-bottom: 1px solid #f3f4f6;
    transition: background 0.2s ease;
}

.comparison-row:hover {
    background: rgba(126, 217, 87, 0.02);
}

.comparison-row:last-child {
    border-bottom: none;
}

.comparison-value {
    padding: 20px;
    text-align: center;
    border-left: 1px solid #f3f4f6;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.comparison-value.highlight {
    background: rgba(126, 217, 87, 0.05);
    font-weight: 600;
    color: var(--whatsapp-green);
}

.comparison-value i {
    font-size: 18px;
}

.text-red {
    color: #ef4444;
}

.text-yellow {
    color: #f59e0b;
}

.text-green {
    color: var(--whatsapp-green);
}

.comparison-cta {
    text-align: center;
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(76, 175, 80, 0.1) 100%);
    border-radius: 20px;
    padding: 50px 40px;
}

.comparison-cta h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 15px 0;
}

.comparison-cta p {
    font-size: 1.1rem;
    color: #666;
    margin: 0 0 30px 0;
}

.btn-comparison-cta {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 16px 32px;
    border-radius: 12px;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(126, 217, 87, 0.3);
}

.btn-comparison-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(126, 217, 87, 0.4);
}

/* Responsive Comparison */
@media (max-width: 1024px) {
    .comparison-header,
    .comparison-row {
        grid-template-columns: 1.5fr repeat(4, 1fr);
    }

    .comparison-feature,
    .comparison-value {
        padding: 15px 10px;
        font-size: 13px;
    }

    .option-name {
        font-size: 12px;
    }

    .option-logo {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }
}

@media (max-width: 768px) {
    .comparison-section {
        padding: 80px 0;
    }

    .comparison-table {
        overflow-x: auto;
    }

    .comparison-header,
    .comparison-row {
        min-width: 600px;
    }

    .comparison-cta {
        padding: 40px 30px;
    }

    .comparison-cta h3 {
        font-size: 1.5rem;
    }
}

/* =====================
   MODERN PRICING SECTION
   ===================== */

.pricing-section {
    padding: 100px 0;
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.pricing-card.popular {
    border-color: var(--whatsapp-green);
    transform: scale(1.05);
}

.pricing-card.popular:hover {
    transform: scale(1.05) translateY(-8px);
}

.popular-badge {
    position: absolute;
    top: 20px;
    right: -30px;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: #ffffff;
    font-size: 12px;
    font-weight: 700;
    padding: 8px 40px;
    transform: rotate(45deg);
    letter-spacing: 0.5px;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.pricing-header {
    padding: 40px 30px 30px;
    text-align: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.pricing-card.popular .pricing-header {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
}

.plan-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 15px 0;
}

.pricing-card.popular .plan-name {
    color: #ffffff;
}

.plan-price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 10px;
}

.currency {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--whatsapp-green);
}

.pricing-card.popular .currency {
    color: #ffffff;
}

.amount {
    font-size: 3rem;
    font-weight: 800;
    color: #1a1a1a;
    margin: 0 5px;
}

.pricing-card.popular .amount {
    color: #ffffff;
}

.period {
    font-size: 1rem;
    color: #666;
    font-weight: 500;
}

.pricing-card.popular .period {
    color: rgba(255, 255, 255, 0.9);
}

.original-price {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
}

.strikethrough {
    font-size: 1.2rem;
    color: #999;
    text-decoration: line-through;
    font-weight: 500;
}

.pricing-card.popular .strikethrough {
    color: rgba(255, 255, 255, 0.7);
}

.discount-badge {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: #ffffff;
    font-size: 11px;
    font-weight: 700;
    padding: 4px 8px;
    border-radius: 12px;
    letter-spacing: 0.5px;
}

.plan-description {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.pricing-card.popular .plan-description {
    color: rgba(255, 255, 255, 0.9);
}

.pricing-features {
    padding: 30px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f3f4f6;
    font-size: 15px;
    color: #374151;
}

.feature-item:last-child {
    border-bottom: none;
}

.feature-item i {
    color: var(--whatsapp-green);
    font-size: 14px;
    width: 16px;
    text-align: center;
}

.pricing-footer {
    padding: 0 30px 40px;
}

.pricing-btn {
    display: block;
    width: 100%;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 14px 20px;
    border-radius: 10px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(126, 217, 87, 0.3);
}

.pricing-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(126, 217, 87, 0.4);
}

.pricing-btn.secondary {
    background: transparent;
    color: var(--whatsapp-green);
    border: 2px solid var(--whatsapp-green);
    box-shadow: none;
}

.pricing-btn.secondary:hover {
    background: var(--whatsapp-green);
    color: #ffffff;
}

/* Responsive Pricing */
@media (max-width: 1024px) {
    .pricing-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        max-width: 500px;
    }

    .pricing-card.popular {
        transform: none;
    }

    .pricing-card.popular:hover {
        transform: translateY(-8px);
    }
}

@media (max-width: 768px) {
    .pricing-section {
        padding: 80px 0;
    }

    .pricing-header {
        padding: 30px 25px 25px;
    }

    .pricing-features {
        padding: 25px;
    }

    .pricing-footer {
        padding: 0 25px 30px;
    }
}

/* =====================
   MODERN TESTIMONIALS SECTION
   ===================== */

.testimonials-section {
    padding: 100px 0;
}

.testimonials-slider {
    margin-bottom: 60px;
    overflow: hidden;
    position: relative;
}

.testimonials-track {
    display: flex;
    gap: 30px;
    animation: slideTestimonials 30s linear infinite;
    width: calc(200% + 30px);
}

@keyframes slideTestimonials {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(calc(-50% - 15px));
    }
}

.testimonials-track:hover {
    animation-play-state: paused;
}

.testimonial-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    min-width: 350px;
    flex-shrink: 0;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.testimonial-rating {
    display: flex;
    gap: 5px;
    margin-bottom: 20px;
}

.testimonial-rating i {
    color: #fbbf24;
    font-size: 16px;
}

.testimonial-content {
    font-size: 16px;
    line-height: 1.6;
    color: #374151;
    margin-bottom: 25px;
    font-style: italic;
    position: relative;
}

.testimonial-content::before {
    content: '"';
    position: absolute;
    top: -10px;
    left: -15px;
    font-size: 40px;
    color: var(--whatsapp-green);
    font-family: serif;
    opacity: 0.3;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 15px;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-weight: 700;
    font-size: 16px;
}

.author-name {
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 5px;
}

.author-business {
    font-size: 14px;
    color: #666;
}

.testimonials-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    margin-top: 60px;
}

.testimonials-stats .stat-item {
    text-align: center;
}

.testimonials-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--whatsapp-green);
    margin-bottom: 10px;
    display: block;
}

.testimonials-stats .stat-label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
}

/* Responsive Testimonials */
@media (max-width: 1024px) {
    .testimonials-track {
        grid-template-columns: repeat(2, 1fr);
    }

    .testimonials-stats {
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .testimonials-section {
        padding: 80px 0;
    }

    .testimonials-track {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .testimonial-card {
        padding: 25px;
        min-width: auto;
    }

    .testimonials-stats {
        flex-direction: column;
        gap: 30px;
    }

    .testimonials-stats .stat-number {
        font-size: 2rem;
    }
}

/* =====================
   FINAL CTA SECTION
   ===================== */

.final-cta-section {
    padding: 100px 0;
    position: relative;
    overflow: hidden;
}

.final-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(126, 217, 87, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
    z-index: 1;
}

.final-cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.cta-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(126, 217, 87, 0.15);
    border: 1px solid rgba(126, 217, 87, 0.3);
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 15px;
    font-weight: 600;
    color: var(--whatsapp-green);
    margin-bottom: 25px;
}

.badge-icon {
    font-size: 18px;
}

.cta-title {
    font-size: 3.5rem;
    font-weight: 800;
    color: #1a1a1a;
    line-height: 1.2;
    margin: 0 0 25px 0;
    position: relative;
}

.cta-title .highlight-text {
    color: var(--whatsapp-green);
}

.cta-title .title-underline {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 2px;
    animation: expandCTALine 1s ease 0.5s forwards;
}

@keyframes expandCTALine {
    to {
        width: 120px;
    }
}

.cta-description {
    font-size: 1.3rem;
    color: #666;
    line-height: 1.6;
    margin-bottom: 50px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-features {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 50px;
    flex-wrap: wrap;
}

.cta-feature {
    display: flex;
    align-items: center;
    gap: 15px;
    background: #ffffff;
    padding: 20px 25px;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    min-width: 200px;
}

.cta-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    flex-shrink: 0;
}

.feature-title {
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 5px;
    font-size: 16px;
}

.feature-desc {
    font-size: 14px;
    color: #666;
}

.cta-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.cta-btn-primary {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    text-decoration: none;
    font-weight: 700;
    font-size: 18px;
    padding: 18px 35px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 6px 20px rgba(126, 217, 87, 0.4);
    white-space: nowrap;
}

.cta-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(126, 217, 87, 0.5);
}

.cta-btn-secondary {
    background: transparent;
    color: #374151;
    text-decoration: none;
    font-weight: 600;
    font-size: 18px;
    padding: 18px 35px;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.cta-btn-secondary:hover {
    border-color: var(--whatsapp-green);
    color: var(--whatsapp-green);
    background: rgba(126, 217, 87, 0.05);
    transform: translateY(-3px);
}

.cta-guarantee {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    background: rgba(126, 217, 87, 0.1);
    border: 1px solid rgba(126, 217, 87, 0.2);
    padding: 15px 25px;
    border-radius: 12px;
    color: var(--whatsapp-green);
    font-size: 15px;
    max-width: 400px;
    margin: 0 auto;
}

.guarantee-icon {
    font-size: 20px;
}

/* Responsive Final CTA */
@media (max-width: 1024px) {
    .cta-features {
        gap: 25px;
    }

    .cta-feature {
        min-width: 180px;
        padding: 18px 20px;
    }
}

@media (max-width: 768px) {
    .final-cta-section {
        padding: 80px 0;
    }

    .cta-title {
        font-size: 2.8rem;
    }

    .cta-description {
        font-size: 1.2rem;
        margin-bottom: 40px;
    }

    .cta-features {
        flex-direction: column;
        align-items: center;
        gap: 20px;
    }

    .cta-feature {
        min-width: auto;
        width: 100%;
        max-width: 300px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .cta-btn-primary,
    .cta-btn-secondary {
        width: 100%;
        max-width: 280px;
        justify-content: center;
        font-size: 16px;
        padding: 16px 30px;
    }

    .cta-guarantee {
        flex-direction: column;
        text-align: center;
        gap: 8px;
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .cta-title {
        font-size: 2.4rem;
    }

    .cta-badge {
        padding: 8px 16px;
        font-size: 14px;
    }

    .cta-feature {
        padding: 15px 18px;
    }

    .feature-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
    }
}

/* =====================
   END MODERN HOME PAGE SECTIONS
   ===================== */
.whatsapp-footer {
    background: #1a1a1a;
    color: #fff;
    padding: 70px 0 0;
    position: relative;
    overflow: hidden;
    font-size: 15px;
    line-height: 1.7;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 50px;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 20px;
    align-items: flex-start;
}

.footer-column h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #fff;
    margin-bottom: 25px;
    position: relative;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    padding-bottom: 12px;
}

.footer-column h3::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 40px;
    height: 2px;
    background: var(--whatsapp-green);
}

.footer-links a {
    color: #a0a0a0;
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
    padding: 4px 0;
}

.footer-links a:hover {
    color: var(--whatsapp-green);
    padding-left: 12px;
}

.social-icons a {
    color: #fff;
    background: rgba(255, 255, 255, 0.08);
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.social-icons a:hover {
    background: var(--whatsapp-green);
    transform: translateY(-3px);
    border-color: var(--whatsapp-green);
    box-shadow: 0 5px 15px rgba(126, 217, 87, 0.3);
}

.newsletter-form input {
    width: 100%;
    padding: 14px 20px;
    border: 1px solid #333;
    background: #222;
    color: #fff;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    padding-right: 120px;
}

.newsletter-form input:focus {
    outline: none;
    border-color: var(--whatsapp-green);
    box-shadow: 0 0 0 2px rgba(126, 217, 87, 0.2);
}

.newsletter-form button {
    position: absolute;
    right: 4px;
    top: 4px;
    bottom: 4px;
    background: var(--whatsapp-green);
    color: #000;
    border: none;
    padding: 0 18px;
    font-weight: 600;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.newsletter-form button:hover {
    background: #6bcb63;
    transform: translateY(-1px);
}

.footer-bottom {
    text-align: center;
    padding: 25px 0;
    color: #888;
    font-size: 14px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    background: rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}

.footer-contact i {
    margin-right: 15px;
    color: var(--whatsapp-green);
    font-size: 16px;
    width: 20px;
    text-align: center;
    margin-top: 4px;
    flex-shrink: 0;
}

.footer-links li {
    margin-bottom: 12px;
    position: relative;
    padding-left: 15px;
}

.footer-links li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--whatsapp-green);
    font-size: 14px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-links li:hover::before {
    opacity: 1;
    left: -5px;
}

/* --- Testimonials Section --- */
.testimonials-section {
    background-color: var(--light-bg);
    padding: 80px 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 50px;
}

.testimonial-card {
    background-color: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.testimonial-content {
    position: relative;
    margin-bottom: 20px;
}

.testimonial-content p {
    font-style: italic;
    color: var(--dark-text);
    line-height: 1.7;
}

.testimonial-content::before {
    content: '\201C';
    font-size: 4rem;
    font-family: Georgia, serif;
    color: var(--whatsapp-green);
    opacity: 0.3;
    position: absolute;
    top: -2rem;
    left: -0.5rem;
    z-index: 0;
}

.testimonial-author {
    display: flex;
    align-items: center;
}

.author-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-text);
}

.author-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--light-text);
}

/* --- FAQ Section --- */
.faq-section {
    background: #fff;
    padding: 80px 0 40px 0;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(126,217,87,0.07);
    background: #f8fff5;
    padding: 32px 24px;
}

.faq-item {
    border-bottom: 1px solid #e0e0e0;
    padding: 18px 0;
    transition: background 0.2s;
}

.faq-item:last-child {
    border-bottom: none;
}

.faq-question {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--whatsapp-teal-dark);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    outline: none;
    padding: 0;
    transition: color 0.2s;
}

.faq-item.active .faq-question {
    color: var(--whatsapp-green-dark);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s cubic-bezier(0.4,0,0.2,1);
    color: var(--light-text);
    font-size: 1rem;
    margin-top: 0;
}

.faq-item.active .faq-answer {
    margin-top: 12px;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-question i {
    margin-left: 10px;
    transition: transform 0.3s;
}

/* --- Icon Avatar (for testimonials, etc.) --- */
.icon-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.7rem;
    color: #fff;
    background: linear-gradient(135deg, #7ED957 0%, #4CAF50 100%);
    margin-right: 12px;
    box-shadow: 0 2px 8px rgba(126,217,87,0.13);
}

/* --- Logo Fallback --- */
.logo-fallback {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #7ED957 0%, #4CAF50 100%);
    color: #fff;
    font-size: 1.7rem;
    font-weight: 700;
}

/* --- Footer Horizontal Links --- */
.footer-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 40px;
    margin-bottom: 40px;
}

.footer-column {
    min-width: 0;
    flex: 1;
    padding: 0 10px;
    box-sizing: border-box;
}

.footer-column h3 {
    margin-bottom: 18px;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
}

@media (max-width: 1024px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 40px;
    }
}

@media (max-width: 640px) {
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .footer-column {
        margin-bottom: 0;
    }
    
    .footer-heading {
        margin-bottom: 20px;
    }
    
    .footer-links li {
        margin-bottom: 10px;
    }
    
    .newsletter-form {
        max-width: 100%;
    }
    
    .social-icons {
        margin-top: 20px;
    }
    .footer-column {
        min-width: 0;
    }
}
