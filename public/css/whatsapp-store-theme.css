/* ===== WHATSAPP STORE THEME CSS ===== */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: #0b141a;
    height: 100vh;
    overflow: hidden;
}

/* Store Container */
.store-container {
    display: flex;
    height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: #111b21;
}

/* Chat Interface */
.chat-interface {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #0b141a;
    position: relative;
}

/* Header */
.chat-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background: #202c33;
    border-bottom: 1px solid #313d45;
    height: 60px;
    position: relative;
    z-index: 10;
}

.header-back-btn {
    background: none;
    border: none;
    color: #aebac1;
    cursor: pointer;
    padding: 8px;
    margin-right: 8px;
    border-radius: 50%;
    transition: background 0.2s;
}

.header-back-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.store-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 12px;
    background: #54656f;
    display: flex;
    align-items: center;
    justify-content: center;
}

.store-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.store-info {
    flex: 1;
}

.store-name {
    color: #e9edef;
    font-size: 16px;
    font-weight: 400;
    line-height: 1.2;
}

.store-status {
    color: #8696a0;
    font-size: 13px;
    margin-top: 2px;
}

.header-actions {
    display: flex;
    gap: 4px;
}

.header-action-btn {
    background: none;
    border: none;
    color: #aebac1;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background 0.2s;
}

.header-action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Chat Body */
.chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    background-image: url('/chat-background.jpg');
    background-size: cover;
    background-attachment: fixed;
    position: relative;
}

.chat-body::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(11, 20, 26, 0.85);
    pointer-events: none;
}

.chat-content {
    position: relative;
    z-index: 2;
    max-width: 100%;
}

/* Messages */
.message {
    display: flex;
    margin-bottom: 12px;
    animation: messageSlide 0.3s ease-out;
}

@keyframes messageSlide {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.outgoing {
    justify-content: flex-end;
}

.message-bubble {
    max-width: 75%;
    padding: 6px 7px 8px 9px;
    border-radius: 7.5px;
    position: relative;
    word-wrap: break-word;
}

.message-bubble.incoming {
    background: #202c33;
    color: #e9edef;
    border-top-left-radius: 0;
}

.message-bubble.outgoing {
    background: #005c4b;
    color: #e9edef;
    border-top-right-radius: 0;
}

.message-text {
    font-size: 14px;
    line-height: 1.4;
    margin-bottom: 4px;
}

.message-time {
    font-size: 11px;
    color: rgba(233, 237, 239, 0.6);
    text-align: right;
    margin-top: 4px;
}

/* Quick Reply Buttons */
.quick-replies {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 8px;
    justify-content: flex-end;
}

.quick-reply-btn {
    background: transparent;
    border: 1px solid #00a884;
    color: #00a884;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s;
    white-space: nowrap;
}

.quick-reply-btn:hover {
    background: #00a884;
    color: #fff;
}

/* Product Cards */
.product-card {
    background: #202c33;
    border-radius: 8px;
    overflow: hidden;
    margin: 8px 0;
    max-width: 280px;
}

.product-image {
    width: 100%;
    height: 160px;
    object-fit: cover;
}

.product-info {
    padding: 12px;
}

.product-name {
    color: #e9edef;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
}

.product-price {
    color: #00a884;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.product-description {
    color: #8696a0;
    font-size: 12px;
    line-height: 1.3;
}

/* Chat Input */
.chat-input {
    display: flex;
    align-items: flex-end;
    padding: 5px 16px 10px;
    background: #202c33;
    gap: 8px;
}

.input-container {
    flex: 1;
    display: flex;
    align-items: flex-end;
    background: #2a3942;
    border-radius: 21px;
    padding: 5px 12px;
    min-height: 42px;
}

.message-input {
    flex: 1;
    background: none;
    border: none;
    outline: none;
    color: #e9edef;
    font-size: 15px;
    line-height: 20px;
    padding: 9px 0;
    resize: none;
    max-height: 100px;
}

.message-input::placeholder {
    color: #8696a0;
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
}

.input-action-btn {
    background: none;
    border: none;
    color: #8696a0;
    cursor: pointer;
    padding: 4px;
    border-radius: 50%;
    transition: color 0.2s;
}

.input-action-btn:hover {
    color: #aebac1;
}

.send-btn {
    width: 42px;
    height: 42px;
    border-radius: 50%;
    background: #00a884;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.2s;
    flex-shrink: 0;
}

.send-btn:hover {
    background: #06a678;
}

.send-btn:disabled {
    background: #54656f;
    cursor: not-allowed;
}

/* Loading States */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: #0b141a;
    color: #e9edef;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #54656f;
    border-top: 3px solid #00a884;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: #8696a0;
    font-size: 14px;
}

/* Error State */
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100vh;
    background: #0b141a;
    color: #e9edef;
    text-align: center;
    padding: 20px;
}

.error-icon {
    font-size: 48px;
    color: #f15c6d;
    margin-bottom: 16px;
}

.error-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 8px;
}

.error-message {
    color: #8696a0;
    font-size: 14px;
    margin-bottom: 20px;
}

.retry-btn {
    background: #00a884;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    transition: background 0.2s;
}

.retry-btn:hover {
    background: #06a678;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 12px;
}

.typing-bubble {
    background: #202c33;
    border-radius: 7.5px;
    border-top-left-radius: 0;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: #8696a0;
    border-radius: 50%;
    animation: typingDot 1.4s infinite;
}

.typing-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDot {
    0%, 60%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    30% {
        opacity: 1;
        transform: scale(1.2);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-container {
        max-width: 100%;
    }
    
    .message-bubble {
        max-width: 85%;
    }
    
    .chat-input {
        padding: 8px 12px;
    }
    
    .product-card {
        max-width: 240px;
    }
}

/* Scrollbar Styling */
.chat-body::-webkit-scrollbar {
    width: 6px;
}

.chat-body::-webkit-scrollbar-track {
    background: transparent;
}

.chat-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.chat-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}