/* =====================
   AUTHENTICATION PAGES CSS
   ===================== */

/* CSS Variables - Consistent with main theme */
:root {
    --whatsapp-green: #7ED957;
    --whatsapp-teal: #4CAF50;
    --light-gray: #f0f2f5;
    --dark-gray: #374151;
    --border-color: #e5e7eb;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ffffff 0%, rgba(126, 217, 87, 0.05) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

/* Auth Container */
.auth-container {
    background: #ffffff;
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
    max-width: 900px;
    min-height: 500px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    position: relative;
}

/* Left Side - Branding */
.auth-branding {
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    padding: 40px 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: #ffffff;
    position: relative;
    overflow: hidden;
}

.auth-branding::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateX(0) translateY(0); }
    100% { transform: translateX(-50px) translateY(-50px); }
}

.brand-logo {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    position: relative;
    z-index: 2;
}

.brand-logo img {
    height: 60px;
    width: auto;
    margin-right: 15px;
}

.logo-fallback {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: #ffffff;
    color: var(--whatsapp-green);
    font-size: 1.8rem;
    font-weight: 800;
    margin-right: 15px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.brand-name {
    font-size: 2.2rem;
    font-weight: 800;
    color: #ffffff;
}

.brand-tagline {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
}

.brand-description {
    font-size: 0.95rem;
    line-height: 1.5;
    opacity: 0.9;
    max-width: 280px;
    position: relative;
    z-index: 2;
    margin-bottom: 20px;
}

.brand-features {
    position: relative;
    z-index: 2;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
    font-size: 13px;
    opacity: 0.9;
}

.feature-item i {
    color: #ffffff;
    font-size: 14px;
}

/* Right Side - Form */
.auth-form-container {
    padding: 40px 35px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.auth-header {
    text-align: center;
    margin-bottom: 30px;
}

.auth-title {
    font-size: 2rem;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 8px;
}

.auth-subtitle {
    font-size: 0.95rem;
    color: #666;
    line-height: 1.4;
}

.auth-form {
    width: 100%;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 16px;
    font-family: 'Poppins', sans-serif;
    transition: all 0.3s ease;
    background: #ffffff;
}

.form-input:focus {
    outline: none;
    border-color: var(--whatsapp-green);
    box-shadow: 0 0 0 3px rgba(126, 217, 87, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.password-field {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: var(--whatsapp-green);
}

.form-checkbox {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 30px;
}

.checkbox-input {
    width: 18px;
    height: 18px;
    accent-color: var(--whatsapp-green);
}

.checkbox-label {
    font-size: 14px;
    color: #666;
    cursor: pointer;
}

.checkbox-label a {
    color: var(--whatsapp-green);
    text-decoration: none;
    font-weight: 600;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

.auth-button {
    width: 100%;
    background: linear-gradient(135deg, var(--whatsapp-green) 0%, var(--whatsapp-teal) 100%);
    color: #ffffff;
    border: none;
    padding: 16px 20px;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(126, 217, 87, 0.3);
    margin-bottom: 25px;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(126, 217, 87, 0.4);
}

.auth-button:active {
    transform: translateY(0);
}

.auth-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
}

.auth-footer p {
    color: #666;
    font-size: 14px;
}

.auth-footer a {
    color: var(--whatsapp-green);
    text-decoration: none;
    font-weight: 600;
    margin-left: 5px;
}

.auth-footer a:hover {
    text-decoration: underline;
}

/* Error Messages */
.error-message {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 20px;
}

.success-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Hide desktop elements on mobile */
    .auth-branding,
    .auth-form-container {
        display: none !important;
    }
    
    .auth-container {
        grid-template-columns: 1fr !important;
        border-radius: 0 !important;
        box-shadow: none !important;
        background: transparent !important;
        min-height: 100vh !important;
        max-width: none !important;
        width: 100% !important;
    }
    
    body {
        background: linear-gradient(180deg, #25D366 0%, #075E54 100%) !important;
        padding: 0 !important;
        align-items: stretch !important;
        justify-content: stretch !important;
        min-height: 100vh !important;
    }
}

@media (max-width: 480px) {
    /* Additional mobile optimizations */
    .auth-container {
        width: 100% !important;
    }
}

/* Show desktop elements only on larger screens */
@media (min-width: 769px) {
    .mobile-auth-header,
    .mobile-form-container,
    .mobile-auth-footer {
        display: none !important;
    }
    
    /* Ensure desktop layout works properly */
    .auth-container {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        max-width: 900px !important;
        min-height: 500px !important;
        border-radius: 20px !important;
        background: #ffffff !important;
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1) !important;
        overflow: hidden !important;
        width: auto !important;
    }
    
    body {
        background: linear-gradient(135deg, #ffffff 0%, rgba(126, 217, 87, 0.05) 100%) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        min-height: 100vh !important;
    }
}
