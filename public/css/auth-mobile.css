/* =====================
   MOBILE NATIVE AUTH CSS - WhaMart
   ===================== */

/* CSS Variables for consistent mobile theming */
:root {
    --primary-brand: #25D366;
    --primary-dark: #075E54;
    --accent-green: #34B7F1;
    --background-light: #ECE5DD;
    --background-dark: #f8f9fa;
    --text-primary: #111b21;
    --text-secondary: #667781;
    --surface-white: #ffffff;
    --border-light: #d1d7db;
    --shadow-soft: 0 2px 10px rgba(0, 0, 0, 0.12);
    --radius-large: 28px;
    --radius-medium: 16px;
    --radius-small: 12px;
}

/* Global Reset for Mobile */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* Mobile-First Body Styling */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    background: var(--background-dark);
    min-height: 100vh;
    overflow-x: hidden;
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Mobile Container - Full Screen Experience */
.auth-container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, var(--primary-brand) 0%, var(--primary-dark) 100%);
    position: relative;
    overflow: hidden;
}

/* Mobile Header with Logo */
.mobile-auth-header {
    padding: 60px 24px 40px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.mobile-logo {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: var(--surface-white);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-soft);
}

.mobile-logo-text {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-brand);
}

.mobile-brand-name {
    font-size: 28px;
    font-weight: 700;
    color: var(--surface-white);
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.mobile-brand-tagline {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 400;
}

/* Mobile Form Container */
.mobile-form-container {
    flex: 1;
    background: var(--surface-white);
    border-radius: var(--radius-large) var(--radius-large) 0 0;
    padding: 32px 24px 24px;
    position: relative;
    margin-top: 20px;
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

/* Form Header */
.mobile-form-header {
    margin-bottom: 32px;
    text-align: center;
}

.mobile-form-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    letter-spacing: -0.3px;
}

.mobile-form-subtitle {
    font-size: 15px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Form Groups */
.mobile-form-group {
    margin-bottom: 20px;
}

.mobile-form-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
}

/* Input Fields - iOS/Android Style */
.mobile-form-input {
    width: 100%;
    height: 52px;
    padding: 16px;
    background: #f7f8fa;
    border: 1.5px solid var(--border-light);
    border-radius: var(--radius-medium);
    font-size: 16px;
    color: var(--text-primary);
    transition: all 0.2s ease;
    -webkit-appearance: none;
    appearance: none;
}

.mobile-form-input:focus {
    outline: none;
    border-color: var(--primary-brand);
    background: var(--surface-white);
    box-shadow: 0 0 0 3px rgba(37, 211, 102, 0.1);
}

.mobile-form-input::placeholder {
    color: var(--text-secondary);
    font-size: 15px;
}

/* Password Field */
.mobile-password-field {
    position: relative;
}

.mobile-password-toggle {
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 8px;
    font-size: 18px;
    transition: color 0.2s ease;
}

.mobile-password-toggle:hover {
    color: var(--text-primary);
}

/* Checkbox Styling - iOS/Material Design */
.mobile-checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin: 24px 0;
}

.mobile-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-light);
    border-radius: 4px;
    background: var(--surface-white);
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    margin-top: 2px;
    transition: all 0.2s ease;
}

.mobile-checkbox:checked {
    background: var(--primary-brand);
    border-color: var(--primary-brand);
}

.mobile-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.mobile-checkbox-label {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.4;
    cursor: pointer;
}

.mobile-checkbox-label a {
    color: var(--primary-brand);
    text-decoration: none;
    font-weight: 500;
}

/* Primary Button - Native App Style */
.mobile-primary-button {
    width: 100%;
    height: 52px;
    background: linear-gradient(135deg, var(--primary-brand) 0%, #22c55e 100%);
    border: none;
    border-radius: var(--radius-medium);
    font-size: 16px;
    font-weight: 600;
    color: var(--surface-white);
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 24px 0;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.3);
}

.mobile-primary-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
}

.mobile-primary-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(37, 211, 102, 0.3);
}

/* Social Login Buttons */
.mobile-social-section {
    margin: 32px 0;
    text-align: center;
}

.mobile-divider {
    display: flex;
    align-items: center;
    margin: 24px 0;
    color: var(--text-secondary);
    font-size: 14px;
}

.mobile-divider::before,
.mobile-divider::after {
    content: '';
    flex: 1;
    height: 1px;
    background: var(--border-light);
}

.mobile-divider::before {
    margin-right: 16px;
}

.mobile-divider::after {
    margin-left: 16px;
}

.mobile-social-button {
    width: 100%;
    height: 48px;
    background: var(--surface-white);
    border: 1.5px solid var(--border-light);
    border-radius: var(--radius-medium);
    font-size: 15px;
    font-weight: 500;
    color: var(--text-primary);
    cursor: pointer;
    margin-bottom: 12px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.mobile-social-button:hover {
    border-color: var(--text-secondary);
    background: #f8f9fa;
}

/* Footer Links */
.mobile-auth-footer {
    text-align: center;
    padding: 24px 0;
    margin-top: auto;
}

.mobile-auth-footer p {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.mobile-auth-footer a {
    color: var(--primary-brand);
    text-decoration: none;
    font-weight: 600;
}

.mobile-auth-footer a:hover {
    text-decoration: underline;
}

/* Alert Messages - iOS Style */
.mobile-alert {
    padding: 16px;
    border-radius: var(--radius-medium);
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.4;
}

.mobile-alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.mobile-alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

/* Loading State */
.mobile-loading {
    opacity: 0.7;
    pointer-events: none;
}

.mobile-loading .mobile-primary-button {
    background: var(--text-secondary);
    cursor: not-allowed;
}

/* Animation for smooth transitions */
.mobile-form-container {
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Desktop Override - Hide mobile styles on larger screens */
@media (min-width: 769px) {
    .mobile-auth-header,
    .mobile-form-container,
    .mobile-auth-footer {
        display: none !important;
    }
    
    .auth-container {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        min-height: 500px !important;
        max-width: 900px !important;
        margin: 0 auto !important;
        border-radius: 20px !important;
        background: var(--surface-white) !important;
        box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1) !important;
        overflow: hidden !important;
    }
    
    body {
        background: linear-gradient(135deg, #ffffff 0%, rgba(126, 217, 87, 0.05) 100%) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
    }
}

/* Medium screens - Tablet adjustments */
@media (max-width: 768px) and (min-width: 481px) {
    .mobile-auth-header {
        padding: 80px 32px 50px;
    }
    
    .mobile-form-container {
        padding: 40px 32px 32px;
        margin-top: 30px;
    }
    
    .mobile-logo {
        width: 90px;
        height: 90px;
    }
    
    .mobile-brand-name {
        font-size: 32px;
    }
    
    .mobile-form-title {
        font-size: 26px;
    }
}

/* Small screens - Enhanced mobile experience */
@media (max-width: 480px) {
    .mobile-auth-header {
        padding: 50px 20px 30px;
    }
    
    .mobile-form-container {
        padding: 28px 20px 20px;
        margin-top: 15px;
        border-radius: 24px 24px 0 0;
    }
    
    .mobile-logo {
        width: 70px;
        height: 70px;
    }
    
    .mobile-brand-name {
        font-size: 24px;
    }
    
    .mobile-form-title {
        font-size: 22px;
    }
    
    .mobile-form-input {
        height: 48px;
        padding: 14px;
    }
    
    .mobile-primary-button {
        height: 48px;
    }
}

/* Landscape Mobile Optimization */
@media (max-height: 600px) and (orientation: landscape) {
    .mobile-auth-header {
        padding: 30px 24px 20px;
    }
    
    .mobile-logo {
        width: 60px;
        height: 60px;
    }
    
    .mobile-brand-name {
        font-size: 22px;
        margin-bottom: 4px;
    }
    
    .mobile-brand-tagline {
        font-size: 14px;
    }
    
    .mobile-form-container {
        margin-top: 10px;
        padding: 24px;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .mobile-form-container {
        animation: none;
    }
    
    * {
        transition: none !important;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .mobile-form-input {
        border-width: 2px;
    }
    
    .mobile-primary-button {
        box-shadow: none;
        border: 2px solid var(--primary-dark);
    }
}
