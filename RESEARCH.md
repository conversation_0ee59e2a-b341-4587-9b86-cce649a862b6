# Feature Planning: Store Themes

This document outlines the planning and implementation strategy for the new multi-theme feature for user stores.

## 1. Goal

The primary goal is to provide users with a selection of professionally designed, category-specific themes for their single-page mini-websites. This will enhance the value of our platform by allowing users to create a unique and visually appealing online presence with just a few clicks.

## 2. Core Requirements

- **Theme Management:** An admin interface to add, update, and manage themes and their categories.
- **Theme Categorization:** Themes must be grouped into categories (e.g., 'Restaurant', 'Portfolio', 'Services', 'Retail') to help users find relevant designs.
- **User-Facing Selection:** A simple and intuitive interface for users to browse, preview, and apply a theme to their store.
- **Dynamic Rendering:** The user's public-facing store page must dynamically render using the selected theme's layout and styling.
- **Theme Uniqueness:** Each theme must have a distinct visual design, layout, and feel.
- **AI Chatbot Styling:** The AI chatbot's UI should adapt to the chosen theme's color scheme and style.

## 3. Technical Implementation Strategy

We will use the existing Laravel framework to build this feature.

### 3.1. Database Schema

We will introduce two new tables and modify the existing `stores` table (assuming a table for user stores exists).

**1. `theme_categories` table:**
```sql
CREATE TABLE theme_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

**2. `themes` table:**
```sql
CREATE TABLE themes (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    view_path VARCHAR(255) NOT NULL, -- e.g., 'themes.restaurant.index'
    preview_image_url VARCHAR(255) NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES theme_categories(id)
);
```

**3. Modify `stores` table:**

Add a `theme_id` column to associate a store with a theme.

```sql
ALTER TABLE stores ADD COLUMN theme_id BIGINT UNSIGNED NULL;
ALTER TABLE stores ADD FOREIGN KEY (theme_id) REFERENCES themes(id);
```

### 3.2. Backend (Laravel)

- **Models:** Create `Theme` and `ThemeCategory` models with their respective relationships.
  - `Theme` `belongsTo` `ThemeCategory`.
  - `ThemeCategory` `hasMany` `Theme`.
- **Controllers:**
  - `Admin\ThemeController`: For admin CRUD operations on themes and categories.
  - `User\ThemeSelectionController`: For users to view and select themes.
  - `StoreViewController`: This controller will fetch the user's store data, find the associated `theme_id`, and render the correct Blade view using the `view_path` from the `themes` table.
    ```php
    // Example logic in StoreViewController
    public function show(Store $store) {
        $theme = $store->theme ?? Theme::find(DEFAULT_THEME_ID);
        return view($theme->view_path, compact('store'));
    }
    ```

### 3.3. Frontend (Blade & CSS)

- **Theme Structure:** Each theme will be a separate folder within `resources/views/themes/`. For example, a theme named 'Moderno' in the 'Retail' category might be located at `resources/views/themes/retail/moderno/`. The main file would be `index.blade.php`.
- **Styling:** Each theme will have its own dedicated CSS file. We will create a `public/css/themes/` directory to store these.
  - `public/css/themes/retail/moderno.css`
- **Dynamic Stylesheet:** The main layout file will dynamically include the correct stylesheet based on the selected theme.

## 4. Development Roadmap

**Phase 1: Backend Foundation (1 week)**
- [ ] Create database migrations for `theme_categories` and `themes`.
- [ ] Create `Theme` and `ThemeCategory` models and relationships.
- [ ] Build the admin panel for managing themes.

**Phase 2: Initial Theme Development (2 weeks)**
- [ ] Design and develop 3-5 initial themes for key categories.
- [ ] Create the Blade views and dedicated CSS for each theme.

**Phase 3: User-Facing Interface (1 week)**
- [ ] Build the theme selection page for the user dashboard.
- [ ] Implement theme preview functionality.

**Phase 4: Dynamic Rendering Logic (3 days)**
- [ ] Update the `StoreViewController` to load the theme dynamically.
- [ ] Ensure all store data (products, text, images) renders correctly within any theme.

**Phase 5: Testing & Deployment (1 week)**
- [ ] Thoroughly test the entire feature flow.
- [ ] Test all themes for responsiveness and browser compatibility.
- [ ] Deploy to production.

## 5. Key Considerations

- **Performance:** Theme assets (CSS, images) must be optimized to ensure fast page load times.
- **Responsiveness:** All themes must be fully responsive and work flawlessly on mobile, tablet, and desktop devices.
- **Scalability:** The architecture should make it easy to add new themes and categories in the future without code changes.
