<?php

use Illuminate\Http\Request;
use App\Http\Controllers\ConfigController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\StoreController;
use App\Http\Controllers\AnalyticsController;

Route::prefix('auth')->group(function () {
    Route::post('/register', [AuthController::class, 'register']);
    Route::post('/login', [AuthController::class, 'login']);
});

Route::get('/config', ConfigController::class);

Route::middleware('auth:sanctum')->group(function () {
    // Dashboard and Store routes that frontend is expecting
    Route::get('dashboard/stats', [\App\Http\Controllers\AnalyticsController::class, 'getAnalyticsOverview']);
    Route::get('stores/info', [StoreController::class, 'getStoreInfo']);
    Route::post('stores/info', [StoreController::class, 'updateStoreInfo']);

    Route::get('/user', [AuthController::class, 'user']);
    Route::post('/logout', [AuthController::class, 'logout']);

    // Vendor specific routes moved to web.php
});
