<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Store extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'businessCategory',
        'businessSubcategory',
        'businessType',
        'productType',
        'logo',            // Changed from logoUrl to match our form
        'storeUrl',
        'isVerified',
        'isActive',
        'theme',
        'contactPhone',
        'contactEmail',
        'address',
        'facebook_url',    // Added social media fields
        'instagram_url',
        'youtube_url',
    ];
    
    /**
     * Get the user that owns the store.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
    
    /**
     * Get the products for the store.
     */
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    /**
     * Get the orders for the store.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the full URL for the store's logo.
     *
     * @return string|null
     */
    public function getLogoUrlAttribute(): ?string
    {
        // Access the raw attribute to avoid conflicts with accessors.
        $logoPath = $this->attributes['logoUrl'] ?? null;

        if ($logoPath) {
            return asset('storage/' . $logoPath);
        }
        return null;
    }

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = ['logo_url'];
}
