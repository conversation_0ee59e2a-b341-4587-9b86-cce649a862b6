<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class AcademyController extends Controller
{
    /**
     * Display the academy page with tutorials and categories.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = [
            ['id' => 'getting-started', 'name' => 'Getting Started'],
            ['id' => 'store-setup', 'name' => 'Store Setup'],
            ['id' => 'product-management', 'name' => 'Product Management'],
            ['id' => 'chat-flow', 'name' => 'Chat Flow Builder'],
            ['id' => 'orders', 'name' => 'Order Management'],
            ['id' => 'marketing', 'name' => 'Marketing & Growth'],
        ];

        $tutorials = [
            [
                'id' => 1,
                'title' => 'Welcome to Whamart',
                'description' => 'Learn about Whamart and how it can help you grow your business on WhatsApp.',
                'thumbnail' => '/images/academy/welcome-thumbnail.jpg',
                'duration' => '3:45',
                'category' => 'getting-started',
                'featured' => true,
                'videoUrl' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            ],
            [
                'id' => 2,
                'title' => 'Creating Your Account',
                'description' => 'Step-by-step guide to creating and setting up your Whamart vendor account.',
                'thumbnail' => '/images/academy/account-setup-thumbnail.jpg',
                'duration' => '5:20',
                'category' => 'getting-started',
                'featured' => false,
                'videoUrl' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            ],
            [
                'id' => 3,
                'title' => 'Setting Up Your Store',
                'description' => 'Learn how to customize your WhatsApp store with your brand information.',
                'thumbnail' => '/images/academy/store-setup-thumbnail.jpg',
                'duration' => '7:15',
                'category' => 'store-setup',
                'featured' => true,
                'videoUrl' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            ],
            [
                'id' => 4,
                'title' => 'Adding Products to Your Store',
                'description' => 'How to add products, set prices, and manage your product catalog.',
                'thumbnail' => '/images/academy/add-products-thumbnail.jpg',
                'duration' => '6:30',
                'category' => 'product-management',
                'featured' => true,
                'videoUrl' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            ],
            [
                'id' => 5,
                'title' => 'Creating Your First Chat Flow',
                'description' => 'Learn how to create automated chat flows for your customers.',
                'thumbnail' => '/images/academy/chat-flow-thumbnail.jpg',
                'duration' => '8:45',
                'category' => 'chat-flow',
                'featured' => true,
                'videoUrl' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
            ],
        ];

        return view('admin.academy', compact('categories', 'tutorials'));
    }
}
