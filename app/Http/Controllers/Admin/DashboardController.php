<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        $baseStats = [
            'vendorCount' => 120,
            'userCount' => 580,
            'chatFlowCount' => 924,
            'totalRevenue' => '₹12,430',
        ];

        $stats = [
            [
                'name' => 'Total Vendors',
                'stat' => $baseStats['vendorCount'] ?? '0',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5A.75.75 0 0114.25 12h.5a.75.75 0 01.75.75v7.5m0 0a.75.75 0 00.75-.75V12a3 3 0 00-3-3H9.75a3 3 0 00-3 3v8.25a.75.75 0 00.75.75h9.75z" /><path stroke-linecap="round" stroke-linejoin="round" d="M9.75 6.375a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v1.5a.75.75 0 01-.75.75h-3a.75.75 0 01-.75-.75v-1.5z" /><path stroke-linecap="round" stroke-linejoin="round" d="M3 9.75a.75.75 0 01.75-.75h16.5a.75.75 0 01.75.75v11.5a.75.75 0 01-2.25 1.28-4.5 4.5 0 01-8.25-3.546.75.75 0 01-1.5 0 4.5 4.5 0 01-8.25 3.546A.75.75 0 013 21.25v-11.5z" /></svg>',
                'color' => 'rgba(37, 211, 102, 0.1)',
                'iconColor' => '#25D366',
                'description' => 'Active vendors on platform'
            ],
            [
                'name' => 'Total Users',
                'stat' => $statsData['userCount'] ?? '0',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#075E54" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-8.252c0-2.676-1.421-5.085-3.587-6.572.035-.105.06-.213.08-.323.02-.11.03-.22.04-.33.01-.11.02-.22.02-.33a2.25 2.25 0 00-2.25-2.25c-.33 0-.65.08-.94.22-.29.14-.56.33-.81.57-.25.24-.48.52-.69.83-.21.31-.4.65-.56.99a9.348 9.348 0 00-4.121 8.252c0 .99.14 1.95.4 2.857M15 19.128v-3.857m0 3.857a2.25 2.25 0 002.25-2.25c0-1.242-1.008-2.25-2.25-2.25a2.25 2.25 0 00-2.25 2.25c0 1.242 1.008 2.25 2.25 2.25z" /></svg>',
                'color' => 'rgba(7, 94, 84, 0.1)',
                'description' => 'Registered users'
            ],
            [
                'name' => 'Active Chat Flows',
                'stat' => $statsData['chatFlowCount'] ?? '0',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#25D366" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193l-3.72 3.72a.75.75 0 01-1.06 0l-3.72-3.72C9.347 17.653 8.5 16.69 8.5 15.557v-4.286c0-.97.616-1.813 1.5-2.097m6.5-3.511a.75.75 0 00-1.06 0L12 5.25 9.75 3a.75.75 0 00-1.06 0l-2.25 2.25a.75.75 0 000 1.06l2.25 2.25a.75.75 0 001.06 0L12 8.25l2.25-2.25a.75.75 0 000-1.06l-2.25-2.25z" /></svg>',
                'color' => 'rgba(37, 211, 102, 0.1)',
                'description' => 'Total chat flows created'
            ],
            [
                'name' => 'Total Revenue',
                'stat' => $statsData['totalRevenue'] ?? '₹0.00',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#075E54" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h6m3-3.75-3 3m3 0-3-3m-3.75 6.75h10.5a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H4.5A2.25 2.25 0 002.25 6.75v10.5a2.25 2.25 0 002.25 2.25z" /></svg>',
                'color' => 'rgba(7, 94, 84, 0.1)',
                'description' => 'Platform earnings'
            ],
        ];

        $quickActions = [
            [
                'name' => 'Manage Vendors',
                'link' => '/admin/vendors',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#25D366" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5A.75.75 0 0114.25 12h.5a.75.75 0 01.75.75v7.5m0 0a.75.75 0 00.75-.75V12a3 3 0 00-3-3H9.75a3 3 0 00-3 3v8.25a.75.75 0 00.75.75h9.75z" /><path stroke-linecap="round" stroke-linejoin="round" d="M9.75 6.375a.75.75 0 01.75-.75h3a.75.75 0 01.75.75v1.5a.75.75 0 01-.75.75h-3a.75.75 0 01-.75-.75v-1.5z" /><path stroke-linecap="round" stroke-linejoin="round" d="M3 9.75a.75.75 0 01.75-.75h16.5a.75.75 0 01.75.75v11.5a.75.75 0 01-2.25 1.28-4.5 4.5 0 01-8.25-3.546.75.75 0 01-1.5 0 4.5 4.5 0 01-8.25 3.546A.75.75 0 013 21.25v-11.5z" /></svg>',
                'color' => 'rgba(37, 211, 102, 0.1)',
                'description' => 'View, verify, or block vendor accounts'
            ],
            [
                'name' => 'Manage Subscriptions',
                'link' => '/admin/subscriptions',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#075E54" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h6m3-3.75-3 3m3 0-3-3m-3.75 6.75h10.5a2.25 2.25 0 002.25-2.25V6.75a2.25 2.25 0 00-2.25-2.25H4.5A2.25 2.25 0 002.25 6.75v10.5a2.25 2.25 0 002.25 2.25z" /></svg>',
                'color' => 'rgba(7, 94, 84, 0.1)',
                'description' => 'Edit subscription plans and pricing'
            ],
            [
                'name' => 'Message Templates',
                'link' => '/admin/message-templates',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#25D366" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193l-3.72 3.72a.75.75 0 01-1.06 0l-3.72-3.72C9.347 17.653 8.5 16.69 8.5 15.557v-4.286c0-.97.616-1.813 1.5-2.097m6.5-3.511a.75.75 0 00-1.06 0L12 5.25 9.75 3a.75.75 0 00-1.06 0l-2.25 2.25a.75.75 0 000 1.06l2.25 2.25a.75.75 0 001.06 0L12 8.25l2.25-2.25a.75.75 0 000-1.06l-2.25-2.25z" /></svg>',
                'color' => 'rgba(37, 211, 102, 0.1)',
                'description' => 'Add or edit message templates for vendors'
            ],
            [
                'name' => 'Academy Content',
                'link' => '/admin/academy',
                'icon' => '<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#075E54" style="width:24px; height:24px;"><path stroke-linecap="round" stroke-linejoin="round" d="M4.26 10.147a60.436 60.436 0 00-.491 6.347A48.627 48.627 0 0112 20.904a48.627 48.627 0 018.232-4.41 60.46 60.46 0 00-.491-6.347m-15.482 0a50.57 50.57 0 00-2.658-.813A59.905 59.905 0 0112 3.493a59.902 59.902 0 0110.399 5.84c-.896.248-1.783.52-2.658.814m-15.482 0A50.697 50.697 0 0112 13.489a50.702 50.702 0 017.74-3.342M6.75 15a.75.75 0 100-********* 0 000 1.5z" /></svg>',
                'color' => 'rgba(7, 94, 84, 0.1)',
                'description' => 'Manage tutorial videos and guides'
            ]
        ];

        return view('admin.dashboard', compact('stats', 'recentActivities', 'quickActions'));
    }

    public function academy()
    {
        return view('admin.academy');
    }

    public function analytics()
    {
        return view('admin.analytics');
    }

    public function investment()
    {
        return view('admin.investment');
    }

    public function messageTemplates()
    {
        return view('admin.message-templates');
    }

    public function permissions()
    {
        return view('admin.permissions');
    }

    public function settings()
    {
        return view('admin.settings');
    }

    public function subscriptions()
    {
        return view('admin.subscriptions');
    }

    public function support()
    {
        return view('admin.support');
    }

    public function users()
    {
        return view('admin.users');
    }

    public function vendors()
    {
        return view('admin.vendors');
    }

    public function investorManagement()
    {
        return view('admin.investor-management');
    }

    public function marketing()
    {
        return view('admin.marketing');
    }
}
