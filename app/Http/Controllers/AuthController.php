<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    public function create(): View
    {
        return view('auth.login');
    }

    public function showRegistrationForm(): View
    {
        return view('auth.register');
    }

    public function register(Request $request)
    {
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'userType' => ['required', Rule::in(['vendor', 'admin', 'influencer'])],
        ]);

        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => Hash::make($validatedData['password']),
            'userType' => $validatedData['userType'],
        ]);

        // Check if this is a web request or API request
        if ($request->expectsJson()) {
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'access_token' => $token,
                'token_type' => 'Bearer',
                'user' => $user
            ], 201);
        } else {
            // For web requests, log the user in and redirect
            Auth::login($user);

            // Redirect based on user type
            if ($user->userType === 'vendor') {
                return redirect()->route('vendor.dashboard')->with('success', 'Account created successfully! Welcome to your Vendor Dashboard.');
            } else {
                return redirect()->route('home')->with('success', 'Account created successfully! Welcome to Whamart.');
            }
        }
    }

    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|string|email',
            'password' => 'required|string',
        ]);

        if (!Auth::attempt($credentials, $request->filled('remember'))) {
            if ($request->expectsJson()) {
                return response()->json(['message' => 'Invalid login credentials'], 401);
            } else {
                return back()->withErrors([
                    'email' => 'The provided credentials do not match our records.',
                ])->withInput($request->only('email'));
            }
        }

        $user = User::where('email', $request->email)->firstOrFail();

        if ($request->expectsJson()) {
            $token = $user->createToken('auth_token')->plainTextToken;

            return response()->json([
                'access_token' => $token,
                'token_type' => 'Bearer',
                'user' => $user
            ]);
        } else {
            // For web requests, regenerate session and redirect
            $request->session()->regenerate();

            // Redirect based on user type
            if ($user->userType === 'vendor') {
                return redirect()->intended(route('vendor.dashboard'))->with('success', 'Welcome back to your Vendor Dashboard!');
            } else {
                return redirect()->intended(route('home'))->with('success', 'Welcome back!');
            }
        }
    }

    public function logout(Request $request)
    {
        if ($request->expectsJson()) {
            $request->user()->currentAccessToken()->delete();
            return response()->json(['message' => 'Successfully logged out']);
        } else {
            Auth::logout();
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return redirect()->route('home')->with('success', 'You have been logged out successfully.');
        }
    }

    public function user(Request $request)
    {
        return response()->json($request->user());
    }
}
