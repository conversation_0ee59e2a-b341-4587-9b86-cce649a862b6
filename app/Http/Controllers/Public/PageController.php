<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display the home page.
     *
     * @return \Illuminate\View\View
     */
    public function home()
    {
        $pricingPlans = [
            [
                'name' => 'Basic Store',
                'price' => '1,500',
                'period' => 'month',
                'features' => [
                    'Up to 50 products',
                    'Basic analytics',
                    'Standard themes',
                    'Email support',
                ],
                'popular' => false,
            ],
            [
                'name' => 'Pro Store',
                'price' => '4,000',
                'period' => 'month',
                'features' => [
                    'Up to 500 products',
                    'Advanced analytics',
                    'Premium themes',
                    'Priority email & chat support',
                    'Marketing tools',
                ],
                'popular' => true,
            ],
            [
                'name' => 'Enterprise',
                'price' => '10,000',
                'period' => 'month',
                'features' => [
                    'Unlimited products',
                    'Dedicated account manager',
                    'Custom theme development',
                    '24/7 phone support',
                    'API access',
                ],
                'popular' => false,
            ],
        ];

        $testimonials = [
            [
                'quote' => 'WhaMart has revolutionized how I do business. The setup was seamless, and my sales have skyrocketed!',
                'author' => '<PERSON><PERSON><PERSON>',
                'title' => 'Founder of Desi Threads',
                'image' => '/images/testimonials/aarav.jpg',
            ],
            [
                'quote' => 'The analytics tools are a game-changer. I can finally make data-driven decisions for my store.',
                'author' => 'Priya Sharma',
                'title' => 'Owner of Spice Grove',
                'image' => '/images/testimonials/priya.jpg',
            ],
            [
                'quote' => 'Switching to WhaMart was the best decision. The platform is intuitive, and the support is top-notch.',
                'author' => 'Rohan Mehta',
                'title' => 'CEO of Modern Bazaar',
                'image' => '/images/testimonials/rohan.jpg',
            ],
        ];

        $faqs = [
            [
                'question' => 'Can I migrate my existing store to WhaMart?',
                'answer' => 'Yes, our team provides tools and support to help you easily migrate your products, customers, and order data from other platforms like Shopify, WooCommerce, and more.',
            ],
            [
                'question' => 'What are the transaction fees?',
                'answer' => 'We believe in transparent pricing. WhaMart does not charge any transaction fees on top of what the payment gateway (like Stripe or Razorpay) charges. You only pay for your monthly subscription.',
            ],
            [
                'question' => 'Can I use my own domain name?',
                'answer' => 'Absolutely! You can connect your own custom domain to your WhaMart store. We also provide a free `whamart.store` subdomain to get you started immediately.',
            ],
            [
                'question' => 'Is WhaMart suitable for beginners?',
                'answer' => 'Yes! Our platform is designed to be user-friendly and intuitive, even for those with no technical experience. Plus, our support team and help center are always available to guide you.',
            ],
        ];

        return view('public.home', compact('pricingPlans', 'testimonials', 'faqs'));
    }

    public function about()
    {
        $teamMembers = [
            [
                'name' => 'Anjali Rao',
                'title' => 'CEO',
                'description' => 'An e-commerce visionary, Anjali founded Whamart to empower small businesses in the digital age.',
                'image' => 'https://randomuser.me/api/portraits/women/68.jpg'
            ],
            [
                'name' => 'Priya Sharma',
                'title' => 'CTO',
                'description' => 'A tech innovator with expertise in mobile platforms, Priya oversees our product development and technical strategy.',
                'image' => 'https://randomuser.me/api/portraits/women/44.jpg'
            ],
            [
                'name' => 'Vikram Patel',
                'title' => 'Head of Customer Success',
                'description' => 'With a passion for helping small businesses thrive, Vikram ensures our customers get the most out of Whamart.',
                'image' => 'https://randomuser.me/api/portraits/men/68.jpg'
            ]
        ];
        return view('public.about-us', compact('teamMembers'));
    }

    public function blog()
    {
        $blogPosts = [
            [
                'id' => 1,
                'title' => 'How to Boost Your Sales with WhatsApp Marketing',
                'excerpt' => 'Learn effective strategies to increase your sales and customer engagement through WhatsApp Business and automation tools.',
                'imageUrl' => 'https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Marketing',
                'author' => 'Sarah Johnson',
                'date' => 'May 15, 2023',
                'readTime' => '5 min read'
            ],
            [
                'id' => 2,
                'title' => 'Setting Up Your First WhatsApp Store: A Complete Guide',
                'excerpt' => 'A step-by-step guide to creating your first WhatsApp store and optimizing it for maximum conversions and customer satisfaction.',
                'imageUrl' => 'https://images.unsplash.com/photo-1512428559087-560fa5ceab42?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Tutorial',
                'author' => 'Michael Chen',
                'date' => 'June 3, 2023',
                'readTime' => '8 min read'
            ],
            [
                'id' => 3,
                'title' => 'The Future of Social Commerce: WhatsApp vs Instagram',
                'excerpt' => 'An in-depth comparison of WhatsApp and Instagram as e-commerce platforms, with insights on which might be better for your business.',
                'imageUrl' => 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Trends',
                'author' => 'David Rodriguez',
                'date' => 'July 12, 2023',
                'readTime' => '6 min read'
            ],
            [
                'id' => 4,
                'title' => '5 Ways to Automate Your Customer Service with Chat Flows',
                'excerpt' => 'Learn how to set up automated chat flows that handle customer inquiries, provide product information, and streamline your support process.',
                'imageUrl' => 'https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Automation',
                'author' => 'Jessica Lee',
                'date' => 'August 20, 2023',
                'readTime' => '7 min read'
            ],
            [
                'id' => 5,
                'title' => 'Success Story: How a Small Business Tripled Sales with WhaMart',
                'excerpt' => 'Case study of a small business that transformed their customer engagement and sales using WhaMart\'s WhatsApp commerce platform.',
                'imageUrl' => 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Case Study',
                'author' => 'Anita Patel',
                'date' => 'September 5, 2023',
                'readTime' => '4 min read'
            ],
            [
                'id' => 6,
                'title' => 'WhatsApp Business API: What You Need to Know',
                'excerpt' => 'A comprehensive guide to understanding and leveraging the WhatsApp Business API for your e-commerce and marketing efforts.',
                'imageUrl' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'category' => 'Technology',
                'author' => 'Robert Williams',
                'date' => 'October 18, 2023',
                'readTime' => '9 min read'
            ]
        ];

        $categories = ['All', 'Marketing', 'Tutorial', 'Trends', 'Automation', 'Case Study', 'Technology'];

        return view('public.blog', compact('blogPosts', 'categories'));
    }

    public function careers()
    {
        $jobOpenings = [
            [
                'title' => 'Senior Frontend Developer',
                'type' => 'Full-time',
                'department' => 'Engineering',
                'location' => 'Remote'
            ],
            [
                'title' => 'Product Manager',
                'type' => 'Full-time',
                'department' => 'Product',
                'location' => 'San Francisco, CA'
            ],
            [
                'title' => 'Customer Support Specialist',
                'type' => 'Part-time',
                'department' => 'Customer Success',
                'location' => 'Remote'
            ]
        ];

        return view('public.careers', compact('jobOpenings'));
    }

    public function contact()
    {
        return view('public.contact-us');
    }

    public function handleContactForm(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // In a real app, you would send an email or save to the database.
        // For now, we'll just redirect back with a success message.
        return back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }

    public function helpCenter()
    {
        $categories = [
            ['id' => 'general', 'name' => 'General', 'icon' => '🔍'],
            ['id' => 'account', 'name' => 'Account', 'icon' => '👤'],
            ['id' => 'store', 'name' => 'Store Setup', 'icon' => '🏪'],
            ['id' => 'products', 'name' => 'Products', 'icon' => '📦'],
            ['id' => 'orders', 'name' => 'Orders', 'icon' => '🛒'],
            ['id' => 'payments', 'name' => 'Payments', 'icon' => '💳'],
            ['id' => 'chatflow', 'name' => 'Chat Flows', 'icon' => '💬'],
        ];

        $faqs = [
            'general' => [
                [
                    'question' => 'What is WhaMart?',
                    'answer' => 'WhaMart is a platform that allows businesses to create WhatsApp-based stores. It enables you to sell products and services directly through WhatsApp, with features like automated chat flows, product catalogs, and order management.',
                ],
                [
                    'question' => 'How much does WhaMart cost?',
                    'answer' => 'WhaMart offers several pricing plans, including a free starter plan and premium plans with additional features. Visit our Pricing page for detailed information about our current plans and features.',
                ],
            ],
            'account' => [
                [
                    'question' => 'How do I create a WhaMart account?',
                    'answer' => 'Click on the \'Get Started\' button on our homepage and follow the simple registration process. You\'ll need to provide your email address and create a password.',
                ],
                [
                    'question' => 'How do I reset my password?',
                    'answer' => 'On the login page, click \'Forgot Password\', enter your email address, and follow the instructions sent to your email to reset your password.',
                ],
            ],
        ];

        return view('public.help-center', compact('categories', 'faqs'));
    }

    public function press()
    {
        $pressReleases = [
            [
                'id' => 1,
                'title' => 'WhaMart Secures $5M in Seed Funding to Revolutionize WhatsApp Commerce',
                'excerpt' => 'WhaMart announces successful completion of seed funding round led by prominent tech investors to accelerate growth and product development.',
                'imageUrl' => 'https://images.unsplash.com/photo-1521791055366-0d553872125f?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'source' => 'TechCrunch',
                'date' => 'June 10, 2023',
                'link' => '#',
            ],
            [
                'id' => 2,
                'title' => 'WhaMart Partners with Major Payment Providers to Enhance WhatsApp Shopping Experience',
                'excerpt' => 'New partnerships enable seamless payment integration within Whatsart stores, allowing customers to complete purchases without leaving WhatsApp.',
                'imageUrl' => 'https://images.unsplash.com/photo-**********-0cfed4f6a45d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'source' => 'Business Insider',
                'date' => 'August 25, 2023',
                'link' => '#',
            ],
            [
                'id' => 3,
                'title' => 'WhaMart Expands Services to 10 New Countries Across South Asia',
                'excerpt' => 'Following rapid growth in its initial markets, WhaMart announces expansion to serve small businesses in 10 additional countries.',
                'imageUrl' => 'https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
                'source' => 'Financial Times',
                'date' => 'October 5, 2023',
                'link' => '#',
            ],
        ];

        $mediaCoverage = [
            [
                'id' => 1,
                'title' => 'How WhaMart is Transforming Small Business E-Commerce',
                'publication' => 'Forbes',
                'date' => 'September 18, 2023',
                'link' => '#',
                'logo' => 'https://images.unsplash.com/photo-1563986768609-322da13575f3?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            ],
            [
                'id' => 2,
                'title' => 'The Rise of Chat Commerce: WhaMart\'s Innovative Approach',
                'publication' => 'Entrepreneur',
                'date' => 'July 7, 2023',
                'link' => '#',
                'logo' => 'https://images.unsplash.com/photo-1560472355-536de3962603?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            ],
            [
                'id' => 3,
                'title' => 'WhaMart Named in \'Top 50 StartUps to Watch\' List',
                'publication' => 'Inc Magazine',
                'date' => 'May 30, 2023',
                'link' => '#',
                'logo' => 'https://images.unsplash.com/photo-1491438590914-bc09fcaaf77a?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
            ],
        ];

        return view('public.press', compact('pressReleases', 'mediaCoverage'));
    }

    public function privacyPolicy()
    {
        return view('public.privacy-policy');
    }

    public function termsOfService()
    {
        return view('public.terms-of-service');
    }

    public function storeThemeDemo()
    {
        $storeData = [
            'name' => "Fashion Store",
            'logo_url' => null, // Will use default logo
            'is_verified' => true,
            'id' => "fashion-store-123"
        ];

        $chatFlowData = null;

        return view('public.store-theme-demo', [
            'storeData' => json_encode($storeData),
            'chatFlowData' => json_encode($chatFlowData)
        ]);
    }
}
