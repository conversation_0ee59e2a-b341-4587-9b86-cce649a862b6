<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Store;
use App\Models\ChatFlow;

class StoreController extends Controller
{
    /**
     * Display the specified store page.
     *
     * @param  string  $storeUrl
     * @return \Illuminate\View\View
     */
    public function show($storeUrl)
    {
        $store = Store::where('url', $storeUrl)->firstOrFail();
        $chatFlow = ChatFlow::where('store_id', $store->id)->where('is_default', true)->first();

        $storeData = [
            'name' => $store->name,
            'logoUrl' => $store->logo_url,
            'isVerified' => $store->is_verified,
            'id' => $store->id,
            'description' => $store->description,
            'businessCategory' => $store->business_category,
            'storeUrl' => $store->url,
        ];

        return view('public.store-page', [
            'storeData' => json_encode($storeData),
            'chatFlowData' => $chatFlow ? json_encode($chatFlow->flow_data) : 'null',
        ]);
    }
}
