<?php

namespace App\Http\Controllers\Investor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the investor dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        return view('investor.dashboard');
    }

    public function capital()
    {
        return view('investor.capital');
    }

    public function equity()
    {
        return view('investor.equity');
    }

    public function investments()
    {
        return view('investor.investments');
    }

    public function management()
    {
        return view('investor.management');
    }

    public function marketExpansion()
    {
        return view('investor.market-expansion');
    }

    public function performance()
    {
        return view('investor.performance');
    }

    public function profile()
    {
        return view('investor.profile');
    }

    public function projections()
    {
        return view('investor.projections');
    }

    public function revenueAnalytics()
    {
        return view('investor.revenue-analytics');
    }

    public function roadmap()
    {
        return view('investor.roadmap');
    }

    public function vendorAnalytics()
    {
        return view('investor.vendor-analytics');
    }
}
