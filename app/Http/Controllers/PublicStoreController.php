<?php

namespace App\Http\Controllers;

use App\Models\Store;
use Illuminate\Http\Request;

class PublicStoreController extends Controller
{
    /**
     * Get a store by its public URL.
     */
    public function getStoreByUrl(string $storeUrl)
    {
        $store = Store::where('storeUrl', $storeUrl)->where('isVerified', true)->with('products')->first();

        if (!$store) {
            return response()->view('public.not-found', [], 404);
        }

        return view('public.store-page', ['store' => $store]);
    }
}
