<?php

namespace App\Http\Controllers\Influencer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the influencer dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function dashboard()
    {
        return view('influencer.dashboard');
    }

    public function campaigns()
    {
        return view('influencer.campaigns');
    }

    public function content()
    {
        return view('influencer.content');
    }

    public function earnings()
    {
        return view('influencer.earnings');
    }

    public function performance()
    {
        return view('influencer.performance');
    }

    public function profile()
    {
        return view('influencer.profile');
    }

    public function promoMaterial()
    {
        return view('influencer.promo-material');
    }

    public function referralLinks()
    {
        return view('influencer.referral-links');
    }

    public function support()
    {
        return view('influencer.support');
    }

    public function withdrawals()
    {
        return view('influencer.withdrawals');
    }
}
