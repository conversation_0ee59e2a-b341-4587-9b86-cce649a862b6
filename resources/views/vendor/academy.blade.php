@extends('layouts.vendor')

@section('content')
<div x-data="vendorAcademy()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Vendor Academy</h1>

    <!-- Featured Tutorials -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Featured Tutorials</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="tutorial in featuredTutorials" :key="tutorial.id">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="relative">
                        <img :src="tutorial.thumbnail" :alt="tutorial.title" class="w-full h-48 object-cover">
                        <div @click="toggleVideoExpansion(tutorial.id)" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer">
                            <i class="fas fa-play-circle text-white text-5xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2" x-text="tutorial.title"></h3>
                        <p class="text-gray-600" x-text="tutorial.description"></p>
                        <div x-show="expandedVideo === tutorial.id" class="mt-4">
                            <iframe :src="tutorial.videoUrl" class="w-full h-64" frameborder="0" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- All Tutorials -->
    <div>
        <h2 class="text-2xl font-bold text-gray-800 mb-4">All Tutorials</h2>
        <div class="flex flex-wrap gap-2 mb-6">
            <template x-for="category in categories" :key="category.id">
                <button @click="activeCategory = category.id" :class="{ 'bg-blue-500 text-white': activeCategory === category.id, 'bg-gray-200 text-gray-800': activeCategory !== category.id }" class="px-4 py-2 rounded-full text-sm font-semibold" x-text="category.name"></button>
            </template>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="tutorial in filteredTutorials" :key="tutorial.id">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                    <div class="relative">
                        <img :src="tutorial.thumbnail" :alt="tutorial.title" class="w-full h-48 object-cover">
                        <div @click="toggleVideoExpansion(tutorial.id)" class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer">
                            <i class="fas fa-play-circle text-white text-5xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-800 mb-2" x-text="tutorial.title"></h3>
                        <p class="text-gray-600" x-text="tutorial.description"></p>
                        <div x-show="expandedVideo === tutorial.id" class="mt-4">
                            <iframe :src="tutorial.videoUrl" class="w-full h-64" frameborder="0" allowfullscreen></iframe>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function vendorAcademy() {
    return {
        activeCategory: 'all',
        expandedVideo: null,
        categories: [
            { id: 'all', name: 'All' },
            { id: 'getting-started', name: 'Getting Started' },
            { id: 'store-setup', name: 'Store Setup' },
            { id: 'product-management', name: 'Product Management' },
            { id: 'marketing', name: 'Marketing' }
        ],
        tutorials: [
            { id: 1, title: 'Welcome to Whamart', description: 'An introduction to the Whamart platform.', thumbnail: 'https://via.placeholder.com/300', videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', category: 'getting-started', featured: true },
            { id: 2, title: 'Setting Up Your Store', description: 'Customize your store to match your brand.', thumbnail: 'https://via.placeholder.com/300', videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', category: 'store-setup', featured: true },
            { id: 3, title: 'Adding Your First Product', description: 'A step-by-step guide to adding products.', thumbnail: 'https://via.placeholder.com/300', videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', category: 'product-management', featured: true },
            { id: 4, title: 'Marketing Your Store', description: 'Learn how to attract customers to your store.', thumbnail: 'https://via.placeholder.com/300', videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', category: 'marketing', featured: false },
            { id: 5, title: 'Advanced Store Customization', description: 'Take your store to the next level.', thumbnail: 'https://via.placeholder.com/300', videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ', category: 'store-setup', featured: false }
        ],
        get featuredTutorials() {
            return this.tutorials.filter(t => t.featured);
        },
        get filteredTutorials() {
            if (this.activeCategory === 'all') {
                return this.tutorials;
            }
            return this.tutorials.filter(t => t.category === this.activeCategory);
        },
        toggleVideoExpansion(id) {
            this.expandedVideo = this.expandedVideo === id ? null : id;
        }
    }
}
</script>
@endsection

