@extends('layouts.vendor')

@section('title', 'Welcome to WhaMart')
@section('page-title', 'Setup Your Store')
@section('page-subtitle', 'Get your store online in 3 simple steps')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-onboarding-desktop.css') }}?v={{ time() }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-onboarding-mobile.css') }}?v={{ time() }}" media="(max-width: 768px)">
<style>
    /* Logo Upload Styles */
    .logo-upload-container {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .logo-preview {
        position: relative;
        width: 100px;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #ddd;
        margin-bottom: 10px;
    }
    
    .img-preview {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .remove-logo {
        position: absolute;
        top: 5px;
        right: 5px;
        background: rgba(255, 255, 255, 0.8);
        border: none;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #e53935;
    }
    
    .logo-upload-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        background-color: #f0f0f0;
        border: 1px dashed #aaa;
        border-radius: 4px;
        cursor: pointer;
        color: #555;
    }
    
    .logo-upload-button:hover {
        background-color: #e8e8e8;
    }
    
    .hidden-file-input {
        display: none;
    }
    
    /* Social Media Inputs */
    .social-media-inputs {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    
    .social-input {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .social-icon {
        font-size: 1.5rem;
        width: 24px;
    }
    
    .social-icon.facebook {
        color: #1877F2;
    }
    
    .social-icon.instagram {
        color: #E1306C;
    }
    
    .social-icon.youtube {
        color: #FF0000;
    }
    
    .form-helper-text {
        font-size: 0.8rem;
        color: #666;
        margin-top: 4px;
    }
    
    @media (max-width: 768px) {
        .social-media-inputs {
            width: 100%;
        }
        
        .social-input {
            width: 100%;
        }
    }
</style>
@endpush

@section('content')
<div x-data="onboardingWizard()" class="onboarding-container">
    <!-- Progress Bar -->
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" :style="`width: ${(currentStep / 3) * 100}%`"></div>
        </div>
        <div class="progress-steps">
            <div class="step" :class="{ 'active': currentStep >= 1, 'completed': currentStep > 1 }">
                <div class="step-number">1</div>
                <div class="step-label">Store Details</div>
            </div>
            <div class="step" :class="{ 'active': currentStep >= 2, 'completed': currentStep > 2 }">
                <div class="step-number">2</div>
                <div class="step-label">Add Products</div>
            </div>
            <div class="step" :class="{ 'active': currentStep >= 3, 'completed': currentStep > 3 }">
                <div class="step-number">3</div>
                <div class="step-label">Share Store</div>
            </div>
        </div>
    </div>

    <!-- Step 1: Store Details -->
    <div x-show="currentStep === 1" class="step-content">
        <div class="step-header">
            <h2>Create Your Store</h2>
            <p>Tell us about your business to get started</p>
        </div>
        
        <form @submit.prevent="nextStep()" class="onboarding-form" enctype="multipart/form-data">
            <div class="form-group">
                <label class="form-label required">Store Name</label>
                <input type="text" x-model="storeData.name" class="form-input" placeholder="Enter your store name" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">Store Logo</label>
                <div class="logo-upload-container">
                    <template x-if="logoPreview">
                        <div class="logo-preview">
                            <img :src="logoPreview" alt="Store Logo Preview" class="img-preview">
                            <button type="button" @click="removeLogo()" class="remove-logo">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </template>
                    <label class="logo-upload-button">
                        <i class="fas fa-cloud-upload-alt"></i> 
                        <span x-text="logoPreview ? 'Change Logo' : 'Upload Logo'"></span>
                        <input type="file" @change="handleLogoUpload($event)" accept="image/*" class="hidden-file-input">
                    </label>
                    <div class="form-helper-text">Recommended size: 300x300 pixels. Max file size: 2MB</div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Store Description</label>
                <textarea x-model="storeData.description" class="form-textarea" placeholder="Describe what you sell..."></textarea>
            </div>
            
            <div class="form-grid-2">
                <div class="form-group">
                    <label class="form-label required">Business Category</label>
                    <select x-model="storeData.businessCategory" class="form-select" @change="updateSubcategories()" required>
                        <option value="">Select Category</option>
                        <option value="electronics">Electronics</option>
                        <option value="clothing">Clothing & Fashion</option>
                        <option value="food">Food & Beverages</option>
                        <option value="health">Health & Beauty</option>
                        <option value="home">Home & Garden</option>
                        <option value="sports">Sports & Fitness</option>
                        <option value="books">Books & Media</option>
                        <option value="automotive">Automotive</option>
                        <option value="services">Services</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Sub Category</label>
                    <select x-model="storeData.businessSubcategory" class="form-select">
                        <option value="">Select Sub Category</option>
                        <template x-for="sub in subcategories" :key="sub.value">
                            <option :value="sub.value" x-text="sub.label"></option>
                        </template>
                    </select>
                </div>
            </div>
            
            <div class="form-grid-2">
                <div class="form-group">
                    <label class="form-label required">Business Type</label>
                    <select x-model="storeData.businessType" class="form-select" required>
                        <option value="product">Product Based</option>
                        <option value="service">Service Based</option>
                    </select>
                </div>
                
                <div class="form-group" x-show="storeData.businessType === 'product'">
                    <label class="form-label">Product Type</label>
                    <select x-model="storeData.productType" class="form-select">
                        <option value="physical">Physical Products</option>
                        <option value="digital">Digital Products</option>
                    </select>
                </div>
            </div>
            
            <div class="form-grid-2">
                <div class="form-group">
                    <label class="form-label">Contact Phone</label>
                    <input type="tel" x-model="storeData.contactPhone" class="form-input" placeholder="+91 98765 43210">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Contact Email</label>
                    <input type="email" x-model="storeData.contactEmail" class="form-input" placeholder="<EMAIL>">
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Store Address</label>
                <textarea x-model="storeData.address" class="form-textarea" placeholder="Enter your store address..."></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label">Social Media Profiles</label>
                <div class="social-media-inputs">
                    <div class="social-input">
                        <i class="fab fa-facebook social-icon facebook"></i>
                        <input type="url" x-model="storeData.facebook_url" class="form-input" placeholder="Facebook profile URL">
                    </div>
                    <div class="social-input">
                        <i class="fab fa-instagram social-icon instagram"></i>
                        <input type="url" x-model="storeData.instagram_url" class="form-input" placeholder="Instagram profile URL">
                    </div>
                    <div class="social-input">
                        <i class="fab fa-youtube social-icon youtube"></i>
                        <input type="url" x-model="storeData.youtube_url" class="form-input" placeholder="YouTube channel URL">
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    Continue <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Step 2: Add Products -->
    <div x-show="currentStep === 2" class="step-content">
        <div class="step-header">
            <h2>Add Your First Product</h2>
            <p>Start with one product to showcase your store</p>
        </div>
        
        <form @submit.prevent="nextStep()" class="onboarding-form">
            <div class="form-group">
                <label class="form-label required">Product Name</label>
                <input type="text" x-model="productData.name" class="form-input" placeholder="Enter product name" required>
            </div>
            
            <div class="form-group">
                <label class="form-label">Product Description</label>
                <textarea x-model="productData.description" class="form-textarea" placeholder="Describe your product features and benefits..."></textarea>
            </div>
            
            <div class="form-group">
                <label class="form-label required">Category</label>
                <select x-model="productData.category" class="form-select" required>
                    <option value="">Select Category</option>
                    <option value="electronics">Electronics</option>
                    <option value="clothing">Clothing</option>
                    <option value="home">Home & Kitchen</option>
                    <option value="beauty">Beauty & Care</option>
                    <option value="sports">Sports</option>
                    <option value="books">Books</option>
                    <option value="other">Other</option>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label required">Regular Price (₹)</label>
                <input type="number" x-model="productData.price" class="form-input" placeholder="0.00" step="0.01" min="0" required>
            </div>
            
            <div class="form-group">
                <label class="form-label required">Stock Quantity</label>
                <input type="number" x-model="productData.stock" class="form-input" placeholder="0" min="0" required>
                <div class="form-helper-text">Enter 0 for unlimited stock</div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Product Status</label>
                <div class="radio-group">
                    <label class="radio-option">
                        <input type="radio" x-model="productData.status" value="published" name="product_status">
                        <span class="radio-custom"></span>
                        <span>Published (Visible to customers)</span>
                    </label>
                    <label class="radio-option">
                        <input type="radio" x-model="productData.status" value="draft" name="product_status">
                        <span class="radio-custom"></span>
                        <span>Draft (Save for later)</span>
                    </label>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" @click="prevStep()" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back
                </button>
                <button type="submit" class="btn btn-primary">
                    Continue <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Step 3: Share Store -->
    <div x-show="currentStep === 3" class="step-content">
        <div class="step-header">
            <h2>Your Store is Ready!</h2>
            <p>Share your store link and start selling</p>
        </div>
        
        <div class="store-preview">
            <div class="preview-card">
                <div class="preview-header">
                    <div class="store-avatar">
                        <span x-text="storeData.name.charAt(0).toUpperCase()"></span>
                    </div>
                    <div class="store-info">
                        <h3 x-text="storeData.name"></h3>
                        <p x-text="storeData.description || 'Welcome to our store!'"></p>
                    </div>
                </div>
                <div class="preview-product">
                    <div class="product-name" x-text="productData.name"></div>
                    <div class="product-price">₹<span x-text="productData.price"></span></div>
                </div>
            </div>
        </div>
        
        <div class="share-section">
            <div class="store-link">
                <label class="form-label">Your Store Link</label>
                <div class="link-container">
                    <input type="text" :value="storeLink" class="form-input" readonly>
                    <button @click="copyLink()" class="copy-btn">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>
            
            <div class="share-buttons">
                <button @click="shareWhatsApp()" class="share-btn whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    Share on WhatsApp
                </button>
                <button @click="shareFacebook()" class="share-btn facebook">
                    <i class="fab fa-facebook"></i>
                    Share on Facebook
                </button>
            </div>
        </div>
        
        <div class="completion-section">
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <h3>दुकान Online है ❤️</h3>
                <p>Your store is now live and ready to receive orders!</p>
            </div>
            
            <div class="form-actions">
                <button @click="goToDashboard()" class="btn btn-primary btn-large">
                    Go to Dashboard <i class="fas fa-arrow-right"></i>
                </button>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function onboardingWizard() {
    return {
        currentStep: 1,
        logoPreview: null,
        storeData: {
            name: '',
            description: '',
            businessCategory: '',
            businessSubcategory: '',
            businessType: 'product',
            productType: 'physical',
            contactPhone: '',
            contactEmail: '',
            address: '',
            logo: null,
            facebook_url: '',
            instagram_url: '',
            youtube_url: ''
        },
        
        subcategories: [],
        
        categorySubcategories: {
            electronics: [
                {value: 'mobile', label: 'Mobile & Accessories'},
                {value: 'computers', label: 'Computers & Laptops'},
                {value: 'tv', label: 'TV & Audio'},
                {value: 'cameras', label: 'Cameras & Photography'},
                {value: 'gaming', label: 'Gaming'}
            ],
            clothing: [
                {value: 'mens', label: 'Men\'s Clothing'},
                {value: 'womens', label: 'Women\'s Clothing'},
                {value: 'kids', label: 'Kids Clothing'},
                {value: 'shoes', label: 'Shoes & Footwear'},
                {value: 'accessories', label: 'Fashion Accessories'}
            ],
            food: [
                {value: 'restaurant', label: 'Restaurant'},
                {value: 'grocery', label: 'Grocery Store'},
                {value: 'bakery', label: 'Bakery & Sweets'},
                {value: 'beverages', label: 'Beverages'},
                {value: 'organic', label: 'Organic Food'}
            ],
            health: [
                {value: 'pharmacy', label: 'Pharmacy'},
                {value: 'cosmetics', label: 'Cosmetics'},
                {value: 'skincare', label: 'Skincare'},
                {value: 'supplements', label: 'Health Supplements'},
                {value: 'fitness', label: 'Fitness Equipment'}
            ],
            home: [
                {value: 'furniture', label: 'Furniture'},
                {value: 'decor', label: 'Home Decor'},
                {value: 'kitchen', label: 'Kitchen & Dining'},
                {value: 'garden', label: 'Garden & Outdoor'},
                {value: 'appliances', label: 'Home Appliances'}
            ],
            services: [
                {value: 'repair', label: 'Repair Services'},
                {value: 'cleaning', label: 'Cleaning Services'},
                {value: 'education', label: 'Education & Training'},
                {value: 'consulting', label: 'Consulting'},
                {value: 'delivery', label: 'Delivery Services'}
            ]
        },
        productData: {
            name: '',
            description: '',
            category: '',
            price: '',
            stock: '',
            status: 'published'
        },
        
        get storeLink() {
            const storeName = this.storeData.name.toLowerCase().replace(/\s+/g, '-');
            return `${window.location.origin}/store/${storeName}`;
        },
        
        get isOnboardingComplete() {
            return this.currentStep === 3 && this.storeData.name && this.productData.name;
        },
        
        updateSubcategories() {
            this.subcategories = this.categorySubcategories[this.storeData.businessCategory] || [];
            this.storeData.businessSubcategory = '';
        },
        
        async nextStep() {
            if (this.currentStep < 3) {
                try {
                    if (this.currentStep === 1) {
                        await this.saveStoreData();
                    } else if (this.currentStep === 2) {
                        await this.saveProductData();
                    }
                    this.currentStep++;
                } catch (error) {
                    // Error already handled in save methods
                    return;
                }
            }
        },
        
        prevStep() {
            if (this.currentStep > 1) {
                this.currentStep--;
            }
        },
        
        async saveStoreData() {
            try {
                const response = await fetch('{{ route("vendor.onboarding.store") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(this.storeData)
                });
                const result = await response.json();
                if (!result.success) throw new Error('Failed to save store');
            } catch (error) {
                console.error('Error saving store:', error);
                alert('Error saving store data. Please try again.');
                throw error;
            }
        },
        
        async saveProductData() {
            try {
                const response = await fetch('{{ route("vendor.onboarding.product") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify(this.productData)
                });
                const result = await response.json();
                if (!result.success) throw new Error('Failed to save product');
            } catch (error) {
                console.error('Error saving product:', error);
                alert('Error saving product data. Please try again.');
                throw error;
            }
        },
        
        copyLink() {
            navigator.clipboard.writeText(this.storeLink);
            alert('Store link copied to clipboard!');
        },
        
        shareWhatsApp() {
            const text = `Check out my store: ${this.storeData.name}\n${this.storeLink}`;
            window.open(`https://wa.me/?text=${encodeURIComponent(text)}`);
        },
        
        shareFacebook() {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(this.storeLink)}`);
        },
        
        goToDashboard() {
            if (this.isOnboardingComplete) {
                window.location.href = '{{ route("vendor.dashboard") }}';
            } else {
                alert('Please complete all onboarding steps first.');
            }
        },
        
        // Logo handling functions
        handleLogoUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            // Check file size (2MB max)
            if (file.size > 2 * 1024 * 1024) {
                alert('File size must be less than 2MB');
                event.target.value = '';
                return;
            }
            
            // Check file type
            if (!file.type.match('image.*')) {
                alert('Please select an image file');
                event.target.value = '';
                return;
            }
            
            // Create file reader to display preview
            const reader = new FileReader();
            reader.onload = (e) => {
                this.logoPreview = e.target.result;
                this.storeData.logo = file;
            };
            reader.readAsDataURL(file);
        },
        
        removeLogo() {
            this.logoPreview = null;
            this.storeData.logo = null;
            // Reset file input
            const fileInput = document.querySelector('.hidden-file-input');
            if (fileInput) fileInput.value = '';
        }
    }
}
</script>
@endpush
@endsection