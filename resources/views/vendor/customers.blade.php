@extends('layouts.vendor')

@section('title', 'Customer Management')

@push('page-styles')
    <link rel="stylesheet" href="{{ asset('css/vendor/vendor-customers.css') }}">
    <link rel="stylesheet" href="{{ asset('css/vendor/mobile-customers.css') }}">
@endpush

@section('content')
<div x-data="customersManager()">
    <!-- Desktop Version -->
    <div class="customers-container">
        <!-- Header -->
        <div class="customers-header">
            <h1 class="customers-title">Customer Management</h1>
            <p class="customers-subtitle">Manage and track all your customer interactions</p>
        </div>

        <!-- Loading State -->
        <div x-show="loading" class="loading-container">
            <div class="loading-spinner"></div>
        </div>

        <!-- Desktop Stats Section -->
        <div x-show="!loading" class="stats-container">
            <div class="stats-card">
                <div class="stats-icon"><i class="fas fa-users"></i></div>
                <div class="stats-number" x-text="stats.total">0</div>
                <div class="stats-label">Total Customers</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon"><i class="fas fa-user-plus"></i></div>
                <div class="stats-number" x-text="stats.new">0</div>
                <div class="stats-label">New This Month</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon"><i class="fas fa-check-circle"></i></div>
                <div class="stats-number" x-text="stats.active">0</div>
                <div class="stats-label">Active Customers</div>
            </div>
            <div class="stats-card">
                <div class="stats-icon"><i class="fas fa-redo"></i></div>
                <div class="stats-number" x-text="stats.returning">0</div>
                <div class="stats-label">Returning Customers</div>
            </div>
        </div>

        <!-- Desktop Filters -->
        <div x-show="!loading" class="filters-container">
            <div class="search-container">
                <i class="fas fa-search search-icon"></i>
                <input type="text" placeholder="Search customers..." class="search-input" x-model="searchTerm" @input="currentPage = 1">
            </div>
            <div class="filter-buttons">
                <button class="filter-btn" :class="{ 'active': statusFilter === 'all' }" @click="statusFilter = 'all'; currentPage = 1">All</button>
                <button class="filter-btn" :class="{ 'active': statusFilter === 'active' }" @click="statusFilter = 'active'; currentPage = 1">Active</button>
                <button class="filter-btn" :class="{ 'active': statusFilter === 'inactive' }" @click="statusFilter = 'inactive'; currentPage = 1">Inactive</button>
                <button class="filter-btn" :class="{ 'active': dateFilter === 'month' }" @click="dateFilter = 'month'; currentPage = 1">This Month</button>
                <button class="filter-btn" :class="{ 'active': dateFilter === 'year' }" @click="dateFilter = 'year'; currentPage = 1">This Year</button>
            </div>
        </div>

        <!-- Desktop Customers Table -->
        <div x-show="!loading && filteredCustomers.length > 0" class="customers-table-container">
            <table class="customers-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Customer</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Orders</th>
                        <th>Total Spent</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="customer in paginatedCustomers" :key="customer.id">
                        <tr>
                            <td x-text="customer.id"></td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-avatar" x-text="customer.name.charAt(0).toUpperCase()"></div>
                                    <div class="customer-name" x-text="customer.name"></div>
                                </div>
                            </td>
                            <td x-text="customer.email"></td>
                            <td>
                                <span class="status-badge" :class="customer.status.toLowerCase()" x-text="customer.status"></span>
                            </td>
                            <td x-text="customer.total_orders"></td>
                            <td x-text="formatCurrency(customer.total_spent)"></td>
                            <td x-text="formatDate(customer.created_at)"></td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-sm"><i class="fas fa-eye"></i> View</button>
                                    <button class="btn btn-outline btn-sm"><i class="fas fa-edit"></i> Edit</button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Desktop Empty State -->
        <div x-show="!loading && filteredCustomers.length === 0" class="empty-container">
            <i class="fas fa-users empty-icon"></i>
            <h3 class="empty-title">No Customers Found</h3>
            <p class="empty-text">Try adjusting your search or filters</p>
        </div>

        <!-- Desktop Pagination -->
        <div x-show="!loading && totalPages > 1" class="pagination-container">
            <p class="pagination-info">Showing page <span x-text="currentPage"></span> of <span x-text="totalPages"></span></p>
            <div class="pagination-buttons">
                <button @click="previousPage" :disabled="currentPage === 1" class="pagination-btn" :class="{ 'disabled': currentPage === 1 }">
                    <i class="fas fa-chevron-left"></i> Previous
                </button>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="pagination-btn primary" :class="{ 'disabled': currentPage === totalPages }">
                    Next <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div> <!-- End of desktop container -->

    <!-- Mobile Version (Native App Style) -->
    <div class="mobile-customers-app">
        <!-- Using vendor layout header -->
    
        <!-- Mobile Search Bar -->
        <div class="mobile-search-container">
            <div class="mobile-search-bar">
                <i class="fas fa-search mobile-search-icon"></i>
                <input type="text" placeholder="Search customers..." class="mobile-search-input" x-model="searchTerm" @input="currentPage = 1">
            </div>
        </div>
        
        <!-- Mobile Loading State -->
        <div x-show="loading" class="mobile-loading" x-cloak>
            <div class="mobile-loading-spinner"></div>
            <p class="mobile-loading-text">Loading customers...</p>
        </div>
    
        <!-- Mobile Stats Section -->
        <div x-show="!loading" class="mobile-stats-section" x-cloak>
            <h2 class="mobile-stats-title">Customer Overview</h2>
            <div class="mobile-stats-grid">
                <div class="mobile-stat-card">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="mobile-stat-number" x-text="stats.total">0</div>
                    <div class="mobile-stat-label">Total Customers</div>
                </div>
                <div class="mobile-stat-card new">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="mobile-stat-number" x-text="stats.new">0</div>
                    <div class="mobile-stat-label">New This Month</div>
                </div>
                <div class="mobile-stat-card active">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="mobile-stat-number" x-text="stats.active">0</div>
                    <div class="mobile-stat-label">Active Customers</div>
                </div>
                <div class="mobile-stat-card returning">
                    <div class="mobile-stat-icon">
                        <i class="fas fa-redo"></i>
                    </div>
                    <div class="mobile-stat-number" x-text="stats.returning">0</div>
                    <div class="mobile-stat-label">Returning Customers</div>
                </div>
            </div>
        </div>
    
        <!-- Mobile Filter Chips -->
        <div x-show="!loading" class="mobile-filters-section" x-cloak>
            <div class="mobile-filter-scroll">
                <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'all' }" @click="statusFilter = 'all'; currentPage = 1">
                    All Customers
                </button>
                <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'active' }" @click="statusFilter = 'active'; currentPage = 1">
                    Active
                </button>
                <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'inactive' }" @click="statusFilter = 'inactive'; currentPage = 1">
                    Inactive
                </button>
                <button class="mobile-filter-chip" :class="{ 'active': dateFilter === 'month' }" @click="dateFilter = 'month'; currentPage = 1">
                    This Month
                </button>
                <button class="mobile-filter-chip" :class="{ 'active': dateFilter === 'year' }" @click="dateFilter = 'year'; currentPage = 1">
                    This Year
                </button>
            </div>
        </div>
    
        <!-- Mobile Customers List -->
        <div x-show="!loading && filteredCustomers.length > 0" class="mobile-customers-list" x-cloak>
            <template x-for="customer in paginatedCustomers" :key="customer.id">
                <div class="mobile-customer-card">
                    <div class="mobile-customer-header">
                        <div>
                            <div class="mobile-customer-id" x-text="'#' + customer.id"></div>
                            <div class="mobile-customer-date" x-text="formatDate(customer.created_at)">01 Jan 2023</div>
                        </div>
                        <div>
                            <div class="mobile-customer-amount" x-text="formatCurrency(customer.total_spent)">₹0</div>
                            <div class="mobile-customer-status" :class="customer.status.toLowerCase()" x-text="customer.status">Active</div>
                        </div>
                    </div>
                    <div class="mobile-customer-body">
                        <div class="mobile-customer-info">
                            <div class="mobile-customer-avatar" x-text="customer.name.charAt(0).toUpperCase()">A</div>
                            <div class="mobile-customer-details">
                                <div class="mobile-customer-name" x-text="customer.name">Customer Name</div>
                                <div class="mobile-customer-email" x-text="customer.email"><EMAIL></div>
                            </div>
                        </div>
                        
                        <div class="mobile-customer-meta">
                            <div class="mobile-meta-item">
                                <div class="mobile-meta-label">Orders</div>
                                <div class="mobile-meta-value" x-text="customer.total_orders">0</div>
                            </div>
                            <div class="mobile-meta-item">
                                <div class="mobile-meta-label">Last Order</div>
                                <div class="mobile-meta-value" x-text="customer.last_order ? formatDate(customer.last_order) : 'Never'">Never</div>
                            </div>
                        </div>
                        <div class="mobile-customer-actions">
                            <button class="mobile-action-btn primary">
                                <i class="fas fa-eye"></i>
                                <span>View</span>
                            </button>
                            <button class="mobile-action-btn secondary">
                                <i class="fas fa-edit"></i>
                                <span>Edit</span>
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    
        <!-- Mobile Empty State -->
        <div x-show="!loading && filteredCustomers.length === 0" class="mobile-empty" x-cloak>
            <i class="fas fa-users mobile-empty-icon"></i>
            <h3 class="mobile-empty-title">No Customers Found</h3>
            <p class="mobile-empty-text">Try adjusting your search or filters</p>
        </div>
        
        <!-- Mobile Pagination -->
        <div x-show="!loading && totalPages > 1" class="mobile-pagination" x-cloak>
            <div class="mobile-pagination-info" x-text="`Page ${currentPage} of ${totalPages}`">Page 1 of 2</div>
            <div class="mobile-pagination-controls">
                <button 
                    @click="previousPage" 
                    :disabled="currentPage === 1" 
                    class="mobile-pagination-btn"
                    :class="{ 'disabled': currentPage === 1 }"
                >
                    <i class="fas fa-chevron-left"></i>
                    <span>Previous</span>
                </button>
                <button 
                    @click="nextPage" 
                    :disabled="currentPage === totalPages" 
                    class="mobile-pagination-btn primary"
                    :class="{ 'disabled': currentPage === totalPages }"
                >
                    <span>Next</span>
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
    
    </div> <!-- End of Mobile Version -->
    
    <!-- Desktop Content starts again here -->
    <!-- Desktop Stats Cards -->
    <div x-show="!loading" class="stats-grid">
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="stats.total">0</h3>
                    <p>Total Customers</p>
                </div>
                <div class="stat-icon total">
                    <i class="fas fa-users"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="stats.new">0</h3>
                    <p>New This Month</p>
                </div>
                <div class="stat-icon new">
                    <i class="fas fa-user-plus"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="stats.active">0</h3>
                    <p>Active Customers</p>
                </div>
                <div class="stat-icon active">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="stats.returning">0</h3>
                    <p>Returning</p>
                </div>
                <div class="stat-icon returning">
                    <i class="fas fa-redo-alt"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div x-show="!loading" class="filters-section">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search Customers</label>
                <div class="search-input-wrapper">
                    <i class="search-icon fas fa-search"></i>
                    <input 
                        type="text" 
                        x-model.debounce.300ms="searchTerm" 
                        @input="applyFilters"
                        placeholder="Search by name, email..." 
                        class="filter-input search-input"
                    >
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" @change="applyFilters" class="filter-select">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Joined</label>
                <select x-model="dateFilter" @change="applyFilters" class="filter-select">
                    <option value="">Any Date</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="year">This Year</option>
                </select>
            </div>

            <div class="filter-group">
                <button @click="clearFilters" class="clear-filters-btn" title="Clear Filters">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Customers Table -->
    <div x-show="!loading" class="table-section">
        <div class="table-header">
            <h2 class="table-title">All Customers</h2>
            <div class="table-actions">
                <button @click="fetchCustomers" class="table-btn" title="Refresh">
                    <i class="fas fa-sync-alt"></i>
                    <span>Refresh</span>
                </button>
                <button @click="exportData" class="table-btn primary">
                    <i class="fas fa-download"></i>
                    <span>Export</span>
                </button>
            </div>
        </div>

        <!-- Table Content -->
        <div x-show="filteredCustomers.length > 0">
            <table class="customers-table">
                <thead>
                    <tr>
                        <th>Customer</th>
                        <th>Contact</th>
                        <th>Orders</th>
                        <th>Total Spent</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="customer in paginatedCustomers" :key="customer.id">
                        <tr>
                            <td>
                                <div class="customer-info">
                                    <img :src="customer.avatar" :alt="customer.name" class="customer-avatar">
                                    <div>
                                        <div class="customer-name" x-text="customer.name"></div>
                                        <div class="customer-join-date" x-text="`Joined ` + formatDate(customer.created_at)"></div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="customer-email" x-text="customer.email"></div>
                            </td>
                            <td x-text="customer.total_orders"></td>
                            <td x-text="`₹` + parseFloat(customer.total_spent).toFixed(2)"></td>
                            <td>
                                <span class="status-badge" :class="'status-' + customer.status.toLowerCase()" x-text="customer.status"></span>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <button class="action-btn" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="action-btn" title="Edit Customer">
                                        <i class="fas fa-pencil-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Empty State -->
        <div x-show="!loading && filteredCustomers.length === 0" class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-users-slash"></i>
            </div>
            <h3 class="empty-title">No Customers Found</h3>
            <p class="empty-text">No customers match your filters. Try clearing them.</p>
            <button @click="clearFilters" class="table-btn primary">Clear Filters</button>
        </div>

        <!-- Pagination -->
        <div x-show="totalPages > 1" class="pagination-section">
            <p class="pagination-info" x-text="paginationInfo"></p>
            <div class="pagination-controls">
                <button @click="previousPage" :disabled="currentPage === 1" class="pagination-btn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <div class="pagination-numbers">
                    <template x-for="page in visiblePages" :key="page">
                        <button 
                            @click="goToPage(page)" 
                            :class="{ 'active': currentPage === page, 'dots': page === '...' }" 
                            :disabled="page === '...'"
                            class="pagination-number"
                            x-text="page">
                        </button>
                    </template>
                </div>
                <button @click="nextPage" :disabled="currentPage === totalPages" class="pagination-btn">
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>
</div> <!-- End of main Alpine container -->
@endsection

@push('page-scripts')
<script>
function customersManager() {
    return {
        loading: true,
        customers: [],
        searchTerm: '',
        statusFilter: '',
        dateFilter: '',
        currentPage: 1,
        itemsPerPage: 10,

        init() {
            this.fetchCustomers();
        },

        fetchCustomers() {
            this.loading = true;
            setTimeout(() => {
                this.customers = [
                    { id: 1, name: 'Aarav Sharma', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=1', status: 'Active', total_orders: 15, total_spent: 25000, created_at: '2023-05-12T00:00:00Z' },
                    { id: 2, name: 'Vivaan Patel', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=2', status: 'Active', total_orders: 8, total_spent: 12000, created_at: '2023-08-20T00:00:00Z' },
                    { id: 3, name: 'Aditya Singh', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=3', status: 'Inactive', total_orders: 2, total_spent: 3000, created_at: '2022-11-30T00:00:00Z' },
                    { id: 4, name: 'Diya Gupta', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=4', status: 'Active', total_orders: 25, total_spent: 55000, created_at: '2023-01-15T00:00:00Z' },
                    { id: 5, name: 'Ishaan Kumar', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=5', status: 'Active', total_orders: 5, total_spent: 8000, created_at: '2023-10-02T00:00:00Z' },
                    { id: 6, name: 'Ananya Reddy', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=6', status: 'Inactive', total_orders: 1, total_spent: 500, created_at: '2023-09-18T00:00:00Z' },
                    { id: 7, name: 'Kabir Mehta', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=7', status: 'Active', total_orders: 18, total_spent: 32000, created_at: '2021-07-21T00:00:00Z' },
                    { id: 8, name: 'Myra Joshi', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=8', status: 'Active', total_orders: 11, total_spent: 19500, created_at: '2023-06-05T00:00:00Z' },
                    { id: 9, name: 'Reyansh Verma', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=9', status: 'Active', total_orders: 22, total_spent: 41000, created_at: '2022-03-10T00:00:00Z' },
                    { id: 10, name: 'Saanvi Desai', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=10', status: 'Inactive', total_orders: 0, total_spent: 0, created_at: '2023-10-25T00:00:00Z' },
                    { id: 11, name: 'Arjun Nair', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=11', status: 'Active', total_orders: 30, total_spent: 75000, created_at: '2020-12-01T00:00:00Z' },
                    { id: 12, name: 'Kiara Iyer', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=12', status: 'Active', total_orders: 7, total_spent: 9500, created_at: '2023-02-28T00:00:00Z' }
                ];
                this.loading = false;
            }, 1000);
        },

        applyFilters() {
            this.currentPage = 1;
        },

        clearFilters() {
            this.searchTerm = '';
            this.statusFilter = '';
            this.dateFilter = '';
            this.currentPage = 1;
        },

        get filteredCustomers() {
            return this.customers.filter(customer => {
                const searchMatch = this.searchTerm.toLowerCase() === '' || 
                                    customer.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                                    customer.email.toLowerCase().includes(this.searchTerm.toLowerCase());

                const statusMatch = this.statusFilter === '' || customer.status.toLowerCase() === this.statusFilter;

                const dateMatch = (() => {
                    if (this.dateFilter === '') return true;
                    const joinDate = new Date(customer.created_at);
                    const now = new Date();
                    if (this.dateFilter === 'today') return joinDate.toDateString() === now.toDateString();
                    if (this.dateFilter === 'week') {
                        const startOfWeek = new Date(now.setDate(now.getDate() - now.getDay()));
                        return joinDate >= startOfWeek;
                    }
                    if (this.dateFilter === 'month') return joinDate.getMonth() === now.getMonth() && joinDate.getFullYear() === now.getFullYear();
                    if (this.dateFilter === 'year') return joinDate.getFullYear() === now.getFullYear();
                    return true;
                })();

                return searchMatch && statusMatch && dateMatch;
            });
        },

        get stats() {
            const now = new Date();
            return {
                total: this.customers.length,
                new: this.customers.filter(c => new Date(c.created_at).getMonth() === now.getMonth()).length,
                active: this.customers.filter(c => c.status.toLowerCase() === 'active').length,
                returning: this.customers.filter(c => c.total_orders > 1).length,
            };
        },

        // Pagination logic
        get totalPages() {
            return Math.ceil(this.filteredCustomers.length / this.itemsPerPage);
        },

        get paginatedCustomers() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredCustomers.slice(start, end);
        },

        get paginationInfo() {
            const start = (this.currentPage - 1) * this.itemsPerPage + 1;
            const end = Math.min(this.currentPage * this.itemsPerPage, this.filteredCustomers.length);
            return `Showing ${start}-${end} of ${this.filteredCustomers.length} customers`;
        },

        get visiblePages() {
            const delta = 1;
            const left = this.currentPage - delta;
            const right = this.currentPage + delta + 1;
            const range = [];
            const rangeWithDots = [];
            let l;

            for (let i = 1; i <= this.totalPages; i++) {
                if (i === 1 || i === this.totalPages || (i >= left && i < right)) {
                    range.push(i);
                }
            }

            for (let i of range) {
                if (l) {
                    if (i - l === 2) {
                        rangeWithDots.push(l + 1);
                    } else if (i - l !== 1) {
                        rangeWithDots.push('...');
                    }
                }
                rangeWithDots.push(i);
                l = i;
            }
            return rangeWithDots;
        },

        previousPage() {
            if (this.currentPage > 1) this.currentPage--;
        },

        nextPage() {
            if (this.currentPage < this.totalPages) this.currentPage++;
        },

        goToPage(page) {
            if (typeof page === 'number') this.currentPage = page;
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN', { day: 'numeric', month: 'short', year: 'numeric' });
        },
        
        formatCurrency(amount) {
            return '₹' + parseFloat(amount).toFixed(2);
        },

        exportData() {
            // Basic CSV export logic
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "ID,Name,Email,Status,Total Orders,Total Spent,Joined\n";
            this.filteredCustomers.forEach(c => {
                csvContent += `${c.id},${c.name},${c.email},${c.status},${c.total_orders},${c.total_spent},${c.created_at}\n`;
            });
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "customers.csv");
            document.body.appendChild(link); 
            link.click();
            document.body.removeChild(link);
        }
    }
}
</script>
@endpush
