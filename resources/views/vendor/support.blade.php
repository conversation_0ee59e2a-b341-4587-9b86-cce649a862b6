@extends('layouts.vendor')

@section('title', 'Support')
@section('page-title', 'Help & Support')
@section('page-subtitle', 'Get help with your store and find answers to common questions')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-support-desktop.css') }}?v={{ time() }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-support-mobile.css') }}?v={{ time() }}" media="(max-width: 768px)">
@endpush

@section('content')
<div x-data="vendorSupport()">
    <!-- Support Header -->
    <div class="support-header">
        <h1 class="support-title">We're Here to Help</h1>
        <p class="support-subtitle">Choose how you'd like to get support for your store</p>
    </div>

    <!-- Contact Methods -->
    <div class="contact-grid">
        <div class="contact-card">
            <div class="contact-icon">
                <i class="fas fa-envelope"></i>
            </div>
            <h3 class="contact-title">Email Support</h3>
            <p class="contact-info"><EMAIL></p>
        </div>
        <div class="contact-card">
            <div class="contact-icon">
                <i class="fas fa-phone"></i>
            </div>
            <h3 class="contact-title">Phone Support</h3>
            <p class="contact-info">+91 ************</p>
        </div>
        <div class="contact-card">
            <div class="contact-icon">
                <i class="fas fa-comments"></i>
            </div>
            <h3 class="contact-title">Live Chat</h3>
            <p class="contact-info">Available 24/7</p>
        </div>
    </div>

    <div class="support-content">
        <!-- FAQ Section -->
        <div class="faq-section">
            <h2 class="faq-title">Frequently Asked Questions</h2>
            <div>
                <template x-for="(faq, index) in faqs" :key="index">
                    <div class="faq-item">
                        <button @click="toggleFaq(index)" class="faq-question">
                            <span x-text="faq.question"></span>
                            <i :class="openFaq === index ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i>
                        </button>
                        <div x-show="openFaq === index" x-transition class="faq-answer">
                            <p x-text="faq.answer"></p>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Support Request Form -->
        <div class="support-form-section">
            <h2 class="form-title">Submit a Request</h2>
            <form @submit.prevent="handleSubmit">
                <div class="form-group">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" id="name" x-model="formData.name" class="form-input" placeholder="Your full name">
                </div>
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" id="email" x-model="formData.email" class="form-input" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="subject" class="form-label">Subject</label>
                    <input type="text" id="subject" x-model="formData.subject" class="form-input" placeholder="Brief description of your issue">
                </div>
                <div class="form-group">
                    <label for="message" class="form-label">Message</label>
                    <textarea id="message" x-model="formData.message" class="form-textarea" placeholder="Describe your issue in detail..."></textarea>
                </div>
                <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    Submit Request
                </button>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
function vendorSupport() {
    return {
        openFaq: null,
        formData: { name: '', email: '', subject: '', message: '' },
        faqs: [
            { question: 'How do I add products to my store?', answer: 'Navigate to Products > Add New Product and fill in all required details including images, pricing, and descriptions.' },
            { question: 'How do I process and manage orders?', answer: 'Go to the Orders section to view all customer orders. You can update order status, print invoices, and track shipments.' },
            { question: 'How do payments work?', answer: 'Payments are processed securely through our payment gateway and transferred to your linked bank account within 2-3 business days.' },
            { question: 'How can I update my store information?', answer: 'Visit Settings > Store Settings to update your store name, description, contact details, and business information.' },
            { question: 'What are the commission rates?', answer: 'Commission rates vary by product category. Check your vendor agreement or contact support for specific rates.' },
            { question: 'How do I handle returns and refunds?', answer: 'Returns can be managed through the Orders section. Follow our return policy guidelines and process refunds as needed.' }
        ],

        toggleFaq(index) {
            this.openFaq = this.openFaq === index ? null : index;
        },

        handleSubmit() {
            if (!this.formData.name || !this.formData.email || !this.formData.subject || !this.formData.message) {
                alert('Please fill in all fields');
                return;
            }
            alert('Support request submitted successfully! We\'ll get back to you within 24 hours.');
            this.formData = { name: '', email: '', subject: '', message: '' };
        }
    }
}
</script>
@endpush
@endsection

