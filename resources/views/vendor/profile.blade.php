@extends('layouts.vendor.app')

@section('title', 'Vendor Profile')

@push('styles')
    <link rel="stylesheet" href="{{ asset('css/vendor/profile-desktop.css') }}" media="(min-width: 768px)">
    <link rel="stylesheet" href="{{ asset('css/vendor/profile-mobile.css') }}" media="(max-width: 767px)">
@endpush

@section('content')
<div class="profile-container">
    <div class="profile-header">
        <h1>My Profile</h1>
    </div>

    <div class="profile-card">
        <form action="#" method="POST">
            @csrf
            <div class="form-section">
                <h2>Personal Information</h2>
                <div class="form-group">
                    <label for="name">Full Name</label>
                    <input type="text" id="name" name="name" value="{{ auth()->user()->name ?? 'Vendor Name' }}">
                </div>
                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" value="{{ auth()->user()->email ?? '<EMAIL>' }}" readonly>
                    <small>Your email address cannot be changed.</small>
                </div>
                <div class="form-group">
                    <label for="mobile">Mobile Number</label>
                    <input type="tel" id="mobile" name="mobile" value="{{ auth()->user()->mobile ?? '+1234567890' }}">
                </div>
            </div>

            <div class="form-section">
                <h2>Security</h2>
                <div class="form-group">
                    <label>Password</label>
                    <a href="#" class="btn btn-secondary">Change Password</a>
                </div>
            </div>

            <div class="profile-actions">
                <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
        </form>
    </div>
</div>
@endsection
