@extends('layouts.vendor')

@section('content')
<div x-data="vendorMessages()" class="flex h-[calc(100vh-120px)] bg-white rounded-lg shadow-lg">
    <!-- Chat list -->
    <div class="w-1/3 border-r border-gray-200 overflow-y-auto">
        <div class="p-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Messages</h2>
        </div>
        <ul class="divide-y divide-gray-200">
            <template x-for="chat in chats" :key="chat.id">
                <li @click="selectedChatId = chat.id" class="cursor-pointer hover:bg-gray-50" :class="{ 'bg-gray-50': selectedChatId === chat.id }">
                    <div class="px-4 py-4 flex items-center">
                        <div class="flex-shrink-0"><i class="fas fa-user-circle text-gray-400 text-3xl"></i></div>
                        <div class="ml-3 flex-1">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900" x-text="chat.name"></p>
                                <p class="text-xs text-gray-500" x-text="chat.time"></p>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-sm text-gray-500 truncate" x-text="chat.lastMessage"></p>
                                <template x-if="chat.unread > 0">
                                    <span class="inline-flex items-center justify-center h-5 w-5 rounded-full bg-green-500 text-xs font-medium text-white" x-text="chat.unread"></span>
                                </template>
                            </div>
                        </div>
                    </div>
                </li>
            </template>
        </ul>
    </div>

    <!-- Chat messages -->
    <div class="flex-1 flex flex-col">
        <template x-if="selectedChat">
            <div class="flex-1 flex flex-col">
                <!-- Chat header -->
                <div class="p-4 border-b border-gray-200 flex items-center">
                    <i class="fas fa-user-circle text-gray-400 text-2xl"></i>
                    <div class="ml-3"><h2 class="text-lg font-medium text-gray-900" x-text="selectedChat.name"></h2></div>
                </div>

                <!-- Messages -->
                <div class="flex-1 p-4 overflow-y-auto bg-gray-50">
                    <div class="space-y-4">
                        <template x-for="message in messages" :key="message.id">
                            <div class="flex" :class="message.sender === 'vendor' ? 'justify-end' : 'justify-start'">
                                <div class="max-w-xs px-4 py-2 rounded-lg" :class="message.sender === 'vendor' ? 'bg-green-500 text-white' : 'bg-white border border-gray-200'">
                                    <p class="text-sm" x-text="message.text"></p>
                                    <p class="text-xs mt-1 text-right" :class="message.sender === 'vendor' ? 'text-gray-100' : 'text-gray-500'" x-text="message.time"></p>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Message input -->
                <div class="p-4 border-t border-gray-200">
                    <form @submit.prevent="sendMessage()" class="flex items-center">
                        <input type="text" x-model="messageText" class="flex-1 border border-gray-300 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent" placeholder="Type a message...">
                        <button type="submit" class="ml-2 p-2 rounded-full bg-green-500 text-white hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"><i class="fas fa-paper-plane"></i></button>
                    </form>
                </div>
            </div>
        </template>
        <template x-if="!selectedChat">
            <div class="flex-1 flex items-center justify-center text-gray-500">Select a chat to start messaging</div>
        </template>
    </div>
</div>

<script>
function vendorMessages() {
    return {
        selectedChatId: 1,
        messageText: '',
        chats: [
            { id: 1, name: 'John Doe', lastMessage: 'Do you have this in blue?', unread: 2, time: '10:30 AM' },
            { id: 2, name: 'Jane Smith', lastMessage: 'Thanks for the quick delivery!', unread: 0, time: 'Yesterday' },
            { id: 3, name: 'Mike Johnson', lastMessage: 'I would like to order 5 more.', unread: 1, time: 'Yesterday' },
        ],
        messages: [
            { id: 1, sender: 'customer', text: 'Hi, I am interested in your products.', time: '10:15 AM' },
            { id: 2, sender: 'vendor', text: 'Hello! How can I help you?', time: '10:17 AM' },
            { id: 3, sender: 'customer', text: 'Do you have the t-shirt in blue?', time: '10:20 AM' },
        ],

        get selectedChat() {
            return this.chats.find(c => c.id === this.selectedChatId);
        },

        sendMessage() {
            if (this.messageText.trim() === '') return;
            this.messages.push({ id: Date.now(), sender: 'vendor', text: this.messageText, time: new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'}) });
            this.messageText = '';
        }
    }
}
</script>
@endsection

