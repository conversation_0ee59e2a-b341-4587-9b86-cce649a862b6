@extends('layouts.vendor')

@section('title', 'Payment Settings')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-payments-desktop.css') }}?v={{ time() }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-payments-mobile.css') }}?v={{ time() }}" media="(max-width: 768px)">
@endpush

@push('scripts')
<script>
    function paymentSettingsManager() {
        return {
            codEnabled: true,
            upiEnabled: false,
            upiId: '',
        }
    }
</script>
@endpush

@section('content')
<div class="settings-container" x-data="paymentSettingsManager()">
    <!-- Header -->
    <div class="settings-header">
        <h1 class="settings-title">Payment Settings</h1>
        <p class="settings-subtitle">Configure how you receive payments from customers</p>
    </div>

    <!-- Settings Cards -->
    <div class="settings-grid">
        <!-- Payment Acceptance Card -->
        <div class="settings-card">
            <div class="card-header">
                <h2 class="card-title">Payment Acceptance</h2>
                <p class="card-subtitle">Enable or disable core payment methods.</p>
            </div>
            <div class="card-body">
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Cash on Delivery (COD)</h3>
                        <p class="setting-description">Allow customers to pay in cash upon delivery.</p>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" x-model="codEnabled">
                            <span class="slider round"></span>
                        </label>
                    </div>
                </div>
                <div class="setting-item">
                    <div class="setting-info">
                        <h3 class="setting-name">Accept UPI Payments</h3>
                        <p class="setting-description">Enable payments through any UPI app.</p>
                    </div>
                    <div class="setting-control">
                        <label class="switch">
                            <input type="checkbox" x-model="upiEnabled">
                            <span class="slider round"></span>
                        </label>
                    </div>
                </div>
                <div x-show="upiEnabled" class="setting-item-details" x-transition>
                    <label for="upiId" class="form-label">Your UPI ID</label>
                    <input type="text" id="upiId" x-model="upiId" class="form-input" placeholder="yourname@bank">
                </div>
            </div>
        </div>

        <!-- Payment Gateways Card -->
        <div class="settings-card">
            <div class="card-header">
                <h2 class="card-title">Payment Gateways</h2>
                <p class="card-subtitle">Integrate with popular payment gateways.</p>
            </div>
            <div class="card-body">
                <div class="gateway-notice">
                    <i class="fas fa-cogs"></i>
                    <p>More payment gateways coming soon!</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="settings-actions">
        <button class="btn btn-secondary">Cancel</button>
        <button class="btn btn-primary">Save Settings</button>
    </div>
</div>
@endsection