@extends('layouts.vendor')

@section('title', 'Products')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/products-desktop.css') }}" media="(min-width: 769px)">
<link rel="stylesheet" href="{{ asset('css/vendor/products-mobile.css') }}" media="(max-width: 768px)">
<style>
  .d-desktop { display: block; }
  .d-mobile { display: none; }
  @media (max-width: 768px) {
    .d-desktop { display: none !important; }
    .d-mobile { display: block !important; }
    .mobile-products-app {
      background: #f8fafc !important;
      min-height: 100vh !important;
      position: relative;
      z-index: 10;
    }
    /* Ensure mobile-products-app and children are visible */
    .mobile-products-app, .mobile-products-container, .mobile-product-card, .mobile-product-details {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
    /* Remove any accidental hidden or display:none */
    [style*="display: none"], [style*="visibility: hidden"] {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }
</style>
@endpush

@section('content')
<!-- SINGLE ALPINE WRAPPER FOR BOTH LAYOUTS -->
<div x-data="vendorProducts()" x-init="init()">
  <!-- ===== DESKTOP PRODUCT MANAGEMENT ===== -->
  <div class="vendor-content d-desktop">
      <!-- Modern Page Header -->
      <div class="products-header">
          <div class="products-header-left">
              <h1>Product Catalog</h1>
              <p>Manage your inventory, pricing, and product visibility</p>
          </div>
          <div class="products-actions">
              <button class="btn btn-outline-secondary" @click="exportProducts()">
                  <i class="fas fa-download"></i>
                  <span>Export</span>
              </button>
              <button class="btn btn-outline-secondary" @click="bulkActions()">
                  <i class="fas fa-tasks"></i>
                  <span>Bulk Actions</span>
              </button>
              <button @click="openCreateModal()" class="btn btn-primary">
                  <i class="fas fa-plus"></i>
                  <span>Add Product</span>
              </button>
          </div>
      </div>

      <!-- Modern Filters Section -->
      <div class="products-filters">
          <div class="filters-row">
              <!-- Search Input -->
              <div class="filter-group">
                  <label class="filter-label">Search Products</label>
                  <div class="search-input-container">
                      <i class="fas fa-search search-icon"></i>
                      <input type="text" 
                             class="filter-input search-input" 
                             x-model="searchTerm" 
                             placeholder="Search by name, SKU, or description..."
                             @keyup.enter="applyFilters">
                  </div>
              </div>
              
              <!-- Category Filter -->
              <div class="filter-group">
                  <label class="filter-label">Category</label>
                  <select class="filter-select" x-model="categoryFilter">
                      <option value="">All Categories</option>
                      <option value="electronics">Electronics</option>
                      <option value="clothing">Clothing & Fashion</option>
                      <option value="home">Home & Kitchen</option>
                      <option value="beauty">Beauty & Personal Care</option>
                      <option value="sports">Sports & Outdoors</option>
                      <option value="books">Books & Media</option>
                  </select>
              </div>
              
              <!-- Status Filter -->
              <div class="filter-group">
                  <label class="filter-label">Status</label>
                  <select class="filter-select" x-model="statusFilter">
                      <option value="">All Status</option>
                      <option value="published">Published</option>
                      <option value="draft">Draft</option>
                      <option value="inactive">Inactive</option>
                  </select>
              </div>
              
              <!-- Stock Filter -->
              <div class="filter-group">
                  <label class="filter-label">Stock Level</label>
                  <select class="filter-select" x-model="stockFilter">
                      <option value="">All Stock</option>
                      <option value="in-stock">In Stock (>10)</option>
                      <option value="low-stock">Low Stock (1-10)</option>
                      <option value="out-of-stock">Out of Stock</option>
                  </select>
              </div>
              
              <!-- Filter Actions -->
              <div class="filter-group">
                  <label class="filter-label">&nbsp;</label>
                  <div style="display: flex; gap: 0.5rem;">
                      <button class="btn btn-outline-secondary" @click="resetFilters()">
                          <i class="fas fa-sync-alt"></i>
                          Reset
                      </button>
                      <button class="btn btn-outline-secondary" @click="toggleAdvancedFilters()">
                          <i class="fas fa-sliders-h"></i>
                          Advanced
                      </button>
                  </div>
              </div>
          </div>
          
          <!-- Advanced Filters (Hidden by default) -->
          <div x-show="showAdvancedFilters" x-transition class="filters-row" style="margin-top: 1rem; padding-top: 1rem; border-top: 1px solid var(--vendor-border);">
              <div class="filter-group">
                  <label class="filter-label">Price Range</label>
                  <div style="display: flex; gap: 0.5rem; align-items: center;">
                      <input type="number" class="filter-input" x-model="priceMin" placeholder="Min" style="width: 80px;">
                      <span>-</span>
                      <input type="number" class="filter-input" x-model="priceMax" placeholder="Max" style="width: 80px;">
                  </div>
              </div>
              
              <div class="filter-group">
                  <label class="filter-label">Date Added</label>
                  <select class="filter-select" x-model="dateFilter">
                      <option value="">All Time</option>
                      <option value="today">Today</option>
                      <option value="week">This Week</option>
                      <option value="month">This Month</option>
                      <option value="quarter">This Quarter</option>
                  </select>
              </div>
              
              <div class="filter-group">
                  <label class="filter-label">Sort By</label>
                  <select class="filter-select" x-model="sortBy">
                      <option value="name">Name (A-Z)</option>
                      <option value="name_desc">Name (Z-A)</option>
                      <option value="price">Price (Low to High)</option>
                      <option value="price_desc">Price (High to Low)</option>
                      <option value="stock">Stock (Low to High)</option>
                      <option value="created_at">Recently Added</option>
                  </select>
              </div>
          </div>
      </div>

      <!-- Modern Products Table -->
      <div class="products-table-container" x-show="paginatedProducts.length > 0">
          <div class="table-wrapper">
              <table class="products-table">
                  <thead>
                      <tr>
                          <th class="table-checkbox">
                              <label class="checkbox-label">
                                  <input type="checkbox" class="checkbox" @change="toggleSelectAll($event)">
                                  <span class="checkbox-custom"></span>
                              </label>
                          </th>
                          <th class="table-product">Product</th>
                          <th class="table-category">Category</th>
                          <th class="table-price">Price</th>
                          <th class="table-stock">Stock</th>
                          <th class="table-status">Status</th>
                          <th class="table-actions">Actions</th>
                      </tr>
                  </thead>
                  <tbody>
                      <template x-for="product in paginatedProducts" :key="product.id">
                          <tr class="table-row">
                              <td class="table-checkbox">
                                  <label class="checkbox-label">
                                      <input type="checkbox" class="checkbox" :value="product.id" x-model="selectedProducts">
                                      <span class="checkbox-custom"></span>
                                  </label>
                              </td>
                              <td class="table-product">
                                  <div class="product-info-cell">
                                      <div class="product-image-thumb">
                                          <template x-if="product.image">
                                              <img :src="product.image" :alt="product.name" class="product-thumb" loading="lazy">
                                          </template>
                                          <template x-if="!product.image">
                                              <div class="product-thumb-placeholder">
                                                  <i class="fas fa-image"></i>
                                              </div>
                                          </template>
                                      </div>
                                      <div class="product-details">
                                          <h4 class="product-name" x-text="product.name"></h4>
                                      </div>
                                  </div>
                              </td>
                              <td class="table-category">
                                  <div class="category-badge">
                                      <i class="fas fa-tag"></i>
                                      <span x-text="product.category || 'Uncategorized'"></span>
                                  </div>
                              </td>
                              <td class="table-price">
                                  <span x-text="'₹' + formatPrice(product.price)"></span>
                              </td>
                              <td class="table-stock">
                                  <span x-text="product.stock + ' units'"></span>
                              </td>
                              <td class="table-status">
                                  <div class="status-badge" :class="getStatusClass(product.status)">
                                      <span x-text="formatStatus(product.status)"></span>
                                  </div>
                              </td>
                              <td class="table-actions">
                                  <div class="action-buttons">
                                      <button class="action-btn view-btn" @click="viewProduct(product)" title="View Product">
                                          <i class="fas fa-eye"></i>
                                      </button>
                                      <button class="action-btn edit-btn" @click="openEditModal(product)" title="Edit Product">
                                          <i class="fas fa-edit"></i>
                                      </button>
                                      <button class="action-btn duplicate-btn" @click="duplicateProduct(product)" title="Duplicate Product">
                                          <i class="fas fa-copy"></i>
                                      </button>
                                      <button class="action-btn delete-btn" @click="deleteProduct(product)" title="Delete Product">
                                          <i class="fas fa-trash"></i>
                                      </button>
                                  </div>
                              </td>
                          </tr>
                      </template>
                  </tbody>
              </table>
          </div>
      </div>

      <!-- Modern Empty State -->
      <div x-show="filteredProducts.length === 0" class="empty-state-container">
          <div class="empty-state">
              <div class="empty-icon">
                  <i class="fas fa-box-open"></i>
              </div>
              <h3>No products found</h3>
              <p>We couldn't find any products matching your search criteria. Try adjusting your filters or add your first product.</p>
              <div class="empty-actions">
                  <button class="btn btn-outline-secondary" @click="resetFilters()">
                      <i class="fas fa-sync-alt"></i>
                      <span>Reset Filters</span>
                  </button>
                  <a href="{{ route('vendor.products.create') }}" class="btn btn-primary">
                      <i class="fas fa-plus"></i>
                      <span>Add First Product</span>
                  </a>
              </div>
          </div>
      </div>

      <!-- Modern Pagination -->
      <div x-show="filteredProducts.length > 0" class="pagination-container">
          <div class="pagination-info">
              <span x-text="'Showing ' + ((currentPage - 1) * itemsPerPage + 1) + '-' + Math.min(currentPage * itemsPerPage, filteredProducts.length) + ' of ' + filteredProducts.length + ' products'"></span>
          </div>
          
          <div class="pagination-controls">
              <button class="pagination-btn" 
                      :class="{ 'disabled': currentPage === 1 }" 
                      @click="previousPage()"
                      :disabled="currentPage === 1">
                  <i class="fas fa-chevron-left"></i>
                  <span>Previous</span>
              </button>
              
              <div class="pagination-numbers">
                  <template x-for="page in visiblePages" :key="page">
                      <button class="pagination-number" 
                              :class="{ 'active': page === currentPage }" 
                              @click="goToPage(page)" 
                              x-text="page"></button>
                  </template>
              </div>
              
              <button class="pagination-btn" 
                      :class="{ 'disabled': currentPage === totalPages }" 
                      @click="nextPage()"
                      :disabled="currentPage === totalPages">
                  <span>Next</span>
                  <i class="fas fa-chevron-right"></i>
              </button>
          </div>
          
          <div class="items-per-page">
              <label>Show:</label>
              <select x-model="itemsPerPage" @change="currentPage = 1" class="items-select">
                  <option value="12">12</option>
                  <option value="24">24</option>
                  <option value="48">48</option>
                  <option value="96">96</option>
              </select>
              <span>per page</span>
          </div>
      </div>
      
      <!-- Create Product Modal -->
    <div x-show="showCreateModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="modal-overlay" 
         @click="closeCreateModal()">
        <div class="modal-container" @click.stop>
            <div class="modal-header">
                <h2>Add New Product</h2>
                <button @click="closeCreateModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form @submit.prevent="createProduct()">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Product Name *</label>
                            <input type="text" x-model="createForm.name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Category *</label>
                            <select x-model="createForm.category" class="form-select" required>
                                <option value="">Select Category</option>
                                <option value="electronics">Electronics</option>
                                <option value="clothing">Clothing</option>
                                <option value="home">Home & Kitchen</option>
                                <option value="beauty">Beauty</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Price *</label>
                            <input type="number" x-model="createForm.price" class="form-input" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Stock *</label>
                            <input type="number" x-model="createForm.stock" class="form-input" required>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">Product Images (1-4 images) *</label>
                            <div class="image-upload-section">
                                <input type="file" x-ref="createImageInput" @change="handleCreateImages($event)" accept="image/*" multiple class="hidden">
                                <div class="image-gallery-modal">
                                    <template x-for="(image, index) in createForm.images" :key="index">
                                        <div class="image-item-modal">
                                            <img :src="image.url" class="image-preview-modal">
                                            <button type="button" @click="removeCreateImage(index)" class="image-remove-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </template>
                                    <div x-show="createForm.images.length < 4" class="image-upload-btn" @click="$refs.createImageInput.click()">
                                        <i class="fas fa-plus"></i>
                                        <span>Add Image</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">Description</label>
                            <textarea x-model="createForm.description" class="form-textarea" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" @click="closeCreateModal()" class="btn btn-outline-secondary">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Product Modal -->
    <div x-show="showEditModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="modal-overlay" 
         @click="closeEditModal()">
        <div class="modal-container" @click.stop>
            <div class="modal-header">
                <h2>Edit Product</h2>
                <button @click="closeEditModal()" class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-content">
                <form @submit.prevent="updateProduct()">
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Product Name *</label>
                            <input type="text" x-model="editForm.name" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Category *</label>
                            <select x-model="editForm.category" class="form-select" required>
                                <option value="">Select Category</option>
                                <option value="electronics">Electronics</option>
                                <option value="clothing">Clothing</option>
                                <option value="home">Home & Kitchen</option>
                                <option value="beauty">Beauty</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Price *</label>
                            <input type="number" x-model="editForm.price" class="form-input" step="0.01" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Stock *</label>
                            <input type="number" x-model="editForm.stock" class="form-input" required>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">Product Images (1-4 images) *</label>
                            <div class="image-upload-section">
                                <input type="file" x-ref="editImageInput" @change="handleEditImages($event)" accept="image/*" multiple class="hidden">
                                <div class="image-gallery-modal">
                                    <template x-for="(image, index) in editForm.images" :key="index">
                                        <div class="image-item-modal">
                                            <img :src="image.url || image" class="image-preview-modal">
                                            <button type="button" @click="removeEditImage(index)" class="image-remove-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </template>
                                    <div x-show="editForm.images.length < 4" class="image-upload-btn" @click="$refs.editImageInput.click()">
                                        <i class="fas fa-plus"></i>
                                        <span>Add Image</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group full-width">
                            <label class="form-label">Description</label>
                            <textarea x-model="editForm.description" class="form-textarea" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-actions">
                        <button type="button" @click="closeEditModal()" class="btn btn-outline-secondary">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Product</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
  </div>
  <!-- ===== MOBILE NATIVE APP INTERFACE ===== -->
  <div class="mobile-products-app d-mobile">
      <!-- Mobile Loading -->
      <!-- <div x-show="isLoading" class="mobile-loading">
          <div class="mobile-loading-spinner"></div>
          <p class="mobile-loading-text">Loading products...</p>
      </div> -->

      <!-- Mobile Stats Cards -->
      <div x-show="!isLoading" class="mobile-stats-section">
          <h2 class="mobile-stats-title">Overview</h2>
          <div class="mobile-stats-grid">
              <div class="mobile-stat-card total">
                  <div class="mobile-stat-number" x-text="filteredProducts.length">0</div>
                  <div class="mobile-stat-label">Total Products</div>
                  <i class="mobile-stat-icon fas fa-box"></i>
              </div>
              <div class="mobile-stat-card instock">
                  <div class="mobile-stat-number" x-text="filteredProducts.filter(p=>p.stock>10).length">0</div>
                  <div class="mobile-stat-label">In Stock</div>
                  <i class="mobile-stat-icon fas fa-check"></i>
              </div>
              <div class="mobile-stat-card lowstock">
                  <div class="mobile-stat-number" x-text="filteredProducts.filter(p=>p.stock>0&&p.stock<=10).length">0</div>
                  <div class="mobile-stat-label">Low Stock</div>
                  <i class="mobile-stat-icon fas fa-exclamation"></i>
              </div>
              <div class="mobile-stat-card outofstock">
                  <div class="mobile-stat-number" x-text="filteredProducts.filter(p=>p.stock===0).length">0</div>
                  <div class="mobile-stat-label">Out of Stock</div>
                  <i class="mobile-stat-icon fas fa-times"></i>
              </div>
          </div>
      </div>

      <!-- Mobile Filter Chips -->
      <div x-show="!isLoading" class="mobile-filters">
          <h3 class="mobile-filters-title">Filter by Status</h3>
          <div class="mobile-filter-chips">
              <button class="mobile-filter-chip" :class="{ 'active': statusFilter === '' }" @click="statusFilter = ''; applyFilters()">All</button>
              <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'published' }" @click="statusFilter = 'published'; applyFilters()">Published</button>
              <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'draft' }" @click="statusFilter = 'draft'; applyFilters()">Draft</button>
              <button class="mobile-filter-chip" :class="{ 'active': statusFilter === 'inactive' }" @click="statusFilter = 'inactive'; applyFilters()">Inactive</button>
          </div>
      </div>

      <!-- Mobile Products List as Cards -->
      <div x-show="!isLoading" class="mobile-products-container">
          <template x-if="filteredProducts.length > 0">
              <template x-for="product in paginatedProducts" :key="product.id">
                  <div class="mobile-product-card">
                      <img :src="product.image || '/images/placeholder.png'" class="mobile-product-image" :alt="product.name">
                      <div class="mobile-product-details">
                          <div class="mobile-product-name" x-text="product.name"></div>
                          <div class="mobile-product-category" x-text="product.category || 'Uncategorized'"></div>
                          <div class="mobile-product-price" x-text="'₹' + formatPrice(product.price)"></div>
                          <div class="mobile-product-stock" x-text="formatStock(product.stock)"></div>
                          <div class="mobile-product-status" :class="getStatusClass(product.status)">
                              <span x-text="formatStatus(product.status)"></span>
                          </div>
                          <div class="mobile-product-actions">
                              <button @click="viewProduct(product)" class="mobile-product-action-btn" title="View"><i class="fas fa-eye"></i></button>
                              <button @click="openEditModal(product)" class="mobile-product-action-btn" title="Edit"><i class="fas fa-edit"></i></button>
                              <button @click="deleteProduct(product)" class="mobile-product-action-btn" title="Delete"><i class="fas fa-trash"></i></button>
                          </div>
                      </div>
                  </div>
              </template>
          </template>
          <!-- <div x-show="filteredProducts.length === 0" class="mobile-empty-state">
              <div class="mobile-empty-icon">
                  <i class="fas fa-box"></i>
              </div>
              <h3 class="mobile-empty-title">No products found</h3>
              <p class="mobile-empty-description">Try adjusting your filters or add your first product.</p>
          </div> -->
      </div>

      <!-- Mobile Pagination -->
      <div x-show="filteredProducts.length > itemsPerPage" class="mobile-pagination">
          <div class="mobile-pagination-info">
              <span x-text="'Page ' + currentPage + ' of ' + totalPages + ' • ' + filteredProducts.length + ' total products'"></span>
          </div>
          <div class="mobile-pagination-controls">
              <button @click="previousPage()" :disabled="currentPage === 1" class="mobile-pagination-btn" :class="{ 'disabled': currentPage === 1 }">
                  <i class="fas fa-chevron-left"></i> Previous
              </button>
              <button @click="nextPage()" :disabled="currentPage === totalPages" class="mobile-pagination-btn primary" :class="{ 'disabled': currentPage === totalPages }">
                  Next <i class="fas fa-chevron-right"></i>
              </button>
          </div>
      </div>

      <!-- Floating Add Product Button -->
      <button class="fab-add-product" @click="openCreateModal()">
          <i class="fas fa-plus"></i>
      </button>
  </div>
</div>

@push('scripts')
<script>
function vendorProducts() {
    return {
        // Data Properties
        products: [],
        searchTerm: '',
        categoryFilter: '',
        statusFilter: '',
        stockFilter: '',
        priceMin: '',
        priceMax: '',
        dateFilter: '',
        sortBy: 'name',
        currentPage: 1,
        itemsPerPage: 12,
        showAdvancedFilters: false,
        isLoading: false,
        selectedProducts: [],
        showCreateModal: false,
        showEditModal: false,
        editingProduct: null,
        createForm: {
            name: '',
            category: '',
            price: '',
            stock: '',
            description: '',
            images: []
        },
        editForm: {},
        isMobileView: window.innerWidth <= 768,
        
        // Initialize component
        init() {
            this.fetchProducts();
            window.addEventListener('resize', () => {
                this.isMobileView = window.innerWidth <= 768;
            });
        },
        
        // Fetch products from API
        fetchProducts() {
            this.isLoading = true;
            // Simulate API delay and add dummy products
            setTimeout(() => {
                this.products = [
                    {
                        id: 1,
                        name: 'Apple iPhone 15',
                        category: 'electronics',
                        price: 79999,
                        stock: 25,
                        status: 'published',
                        description: 'Latest iPhone with A17 chip',
                        image: 'https://dummyimage.com/120x120/10b981/fff&text=IPhone'
                    },
                    {
                        id: 2,
                        name: 'Nike Running Shoes',
                        category: 'clothing',
                        price: 4999,
                        stock: 8,
                        status: 'draft',
                        description: 'Comfortable running shoes',
                        image: 'https://dummyimage.com/120x120/6366f1/fff&text=Nike'
                    },
                    {
                        id: 3,
                        name: 'Prestige Pressure Cooker',
                        category: 'home',
                        price: 1899,
                        stock: 0,
                        status: 'inactive',
                        description: '5L Stainless Steel Cooker',
                        image: 'https://dummyimage.com/120x120/f59e0b/fff&text=Cooker'
                    },
                    {
                        id: 4,
                        name: 'Dove Beauty Soap',
                        category: 'beauty',
                        price: 45,
                        stock: 120,
                        status: 'published',
                        description: 'Moisturizing beauty bar',
                        image: 'https://dummyimage.com/120x120/ef4444/fff&text=Dove'
                    }
                ];
                this.isLoading = false;
            }, 100); // Reduce delay to 100ms for near-instant load
        },

        
        // Computed property for filtered products
        get filteredProducts() {
            if (this.isLoading) return [];
            let filtered = this.products.filter(product => {
                // Search term filter
                const searchTerm = this.searchTerm.toLowerCase();
                const matchesSearch = !searchTerm || 
                    (product.name && product.name.toLowerCase().includes(searchTerm)) ||
                    (product.description && product.description.toLowerCase().includes(searchTerm)) ||
                    (product.category && product.category.toLowerCase().includes(searchTerm));
                // Category filter
                const matchesCategory = !this.categoryFilter || 
                    (product.category && product.category.toLowerCase() === this.categoryFilter.toLowerCase());
                // Status filter
                const matchesStatus = !this.statusFilter || 
                    (product.status && product.status.toLowerCase() === this.statusFilter.toLowerCase());
                // Stock filter
                const matchesStock = !this.stockFilter || 
                    (this.stockFilter === 'in-stock' && product.stock > 10) ||
                    (this.stockFilter === 'low-stock' && product.stock > 0 && product.stock <= 10) ||
                    (this.stockFilter === 'out-of-stock' && product.stock === 0);
                // Price range filter
                const matchesPrice = (!this.priceMin || product.price >= this.priceMin) &&
                                   (!this.priceMax || product.price <= this.priceMax);
                return matchesSearch && matchesCategory && matchesStatus && matchesStock && matchesPrice;
            });
            // Apply sorting
            return this.sortProducts(filtered);
        },
        
        // Computed property for paginated products
        get paginatedProducts() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredProducts.slice(start, end);
        },
        
        // Computed property for pagination
        get totalPages() {
            return Math.ceil(this.filteredProducts.length / this.itemsPerPage);
        },
        
        get visiblePages() {
            const total = this.totalPages;
            const current = this.currentPage;
            const delta = 2;
            const range = [];
            const rangeWithDots = [];
            
            for (let i = Math.max(2, current - delta); i <= Math.min(total - 1, current + delta); i++) {
                range.push(i);
            }
            
            if (current - delta > 2) {
                rangeWithDots.push(1, '...');
            } else {
                rangeWithDots.push(1);
            }
            
            rangeWithDots.push(...range);
            
            if (current + delta < total - 1) {
                rangeWithDots.push('...', total);
            } else if (total > 1) {
                rangeWithDots.push(total);
            }
            
            return rangeWithDots.filter((v, i, a) => a.indexOf(v) === i);
        },
        
        // Sort products
        sortProducts(products) {
            const sorted = [...products];
            
            switch (this.sortBy) {
                case 'name':
                    return sorted.sort((a, b) => a.name.localeCompare(b.name));
                case 'name_desc':
                    return sorted.sort((a, b) => b.name.localeCompare(a.name));
                case 'price':
                    return sorted.sort((a, b) => a.price - b.price);
                case 'price_desc':
                    return sorted.sort((a, b) => b.price - a.price);
                case 'stock':
                    return sorted.sort((a, b) => a.stock - b.stock);
                case 'created_at':
                    return sorted.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
                default:
                    return sorted;
            }
        },
        
        // Reset all filters
        resetFilters() {
            this.searchTerm = '';
            this.categoryFilter = '';
            this.statusFilter = '';
            this.stockFilter = '';
            this.priceMin = '';
            this.priceMax = '';
            this.dateFilter = '';
            this.sortBy = 'name';
            this.currentPage = 1;
        },
        
        // Toggle advanced filters
        toggleAdvancedFilters() {
            this.showAdvancedFilters = !this.showAdvancedFilters;
        },
        
        // Apply filters (for search on enter)
        applyFilters() {
            this.currentPage = 1; // Reset to first page when filtering
        },
        
        // Pagination methods
        goToPage(page) {
            if (page !== '...' && page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },
        
        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },
        
        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },
        
        // Modal actions
        openCreateModal() {
            this.showCreateModal = true;
        },
        
        closeCreateModal() {
            this.showCreateModal = false;
            this.createForm = {
                name: '',
                category: '',
                price: '',
                stock: '',
                description: '',
                images: []
            };
        },
        
        openEditModal(product) {
            this.editingProduct = product;
            this.editForm = { 
                ...product,
                images: product.image ? [product.image] : []
            };
            this.showEditModal = true;
        },
        
        closeEditModal() {
            this.showEditModal = false;
            this.editingProduct = null;
            this.editForm = {};
        },
        
        createProduct() {
            if (this.createForm.images.length === 0) {
                alert('Please upload at least 1 image');
                return;
            }
            console.log('Creating product:', this.createForm);
            alert('Product created successfully!');
            this.closeCreateModal();
            this.fetchProducts();
        },
        
        handleCreateImages(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (this.createForm.images.length < 4 && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.createForm.images.push({
                            file: file,
                            url: e.target.result
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
            event.target.value = '';
        },
        
        removeCreateImage(index) {
            this.createForm.images.splice(index, 1);
        },
        
        handleEditImages(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (this.editForm.images.length < 4 && file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.editForm.images.push({
                            file: file,
                            url: e.target.result
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
            event.target.value = '';
        },
        
        removeEditImage(index) {
            this.editForm.images.splice(index, 1);
        },
        
        updateProduct() {
            if (this.editForm.images.length === 0) {
                alert('Please upload at least 1 image');
                return;
            }
            console.log('Updating product:', this.editForm);
            alert('Product updated successfully!');
            this.closeEditModal();
            this.fetchProducts();
        },
        
        // Product actions
        viewProduct(product) {
            window.location.href = `/vendor/products/${product.id}`;
        },
        
        duplicateProduct(product) {
            if (confirm(`Are you sure you want to duplicate "${product.name}"?`)) {
                // API call to duplicate product
                console.log('Duplicating product:', product.id);
                // Show success message
                this.showNotification('Product duplicated successfully!', 'success');
            }
        },
        
        deleteProduct(product) {
            if (confirm(`Are you sure you want to delete "${product.name}"? This action cannot be undone.`)) {
                // API call to delete product
                this.products = this.products.filter(p => p.id !== product.id);
                this.showNotification('Product deleted successfully!', 'success');
            }
        },
        
        // Bulk actions
        bulkActions() {
            console.log('Opening bulk actions menu');
        },
        
        exportProducts() {
            console.log('Exporting products...');
            this.showNotification('Export started! You will receive an email when ready.', 'info');
        },
        
        // Selection methods
        toggleSelectAll(event) {
            if (event.target.checked) {
                this.selectedProducts = this.paginatedProducts.map(p => p.id);
            } else {
                this.selectedProducts = [];
            }
        },
        
        // Utility methods
        showNotification(message, type = 'info') {
            // Simple notification - replace with your notification system
            alert(message);
        },
        
        // Format price
        formatPrice(price) {
            return new Intl.NumberFormat('en-IN', {
                maximumFractionDigits: 0
            }).format(price);
        },
        
        // Get status badge class
        getStatusClass(status) {
            const statusMap = {
                'published': 'active',
                'draft': 'draft',
                'inactive': 'inactive'
            };
            return statusMap[status] || 'inactive';
        },
        
        // Format status text
        formatStatus(status) {
            const statusMap = {
                'published': 'Published',
                'draft': 'Draft',
                'inactive': 'Inactive'
            };
            return statusMap[status] || 'Unknown';
        },
        
        // Get stock badge class
        getStockClass(stock) {
            if (stock === 0) return 'out-of-stock';
            if (stock <= 10) return 'low-stock';
            return 'in-stock';
        },
        
        // Format stock text
        formatStock(stock) {
            if (stock === 0) return 'Out of Stock';
            if (stock <= 10) return `Low Stock (${stock})`;
            return `In Stock (${stock})`;
        }
    };
}
</script>
@endpush
@endsection

