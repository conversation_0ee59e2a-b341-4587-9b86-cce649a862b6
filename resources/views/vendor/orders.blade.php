@extends('layouts.vendor')

@section('title', 'Order Management')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-orders.css') }}">
<link rel="stylesheet" href="{{ asset('css/vendor/mobile-orders.css') }}">
@endpush

@section('content')
<div class="orders-container" x-data="ordersManager()">
    
    <!-- Header -->
    <div class="orders-header">
        <h1 class="orders-title">Order Management</h1>
        <p class="orders-subtitle">Manage and track all your store orders</p>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="loading-container">
        <div class="loading-spinner"></div>
    </div>

    <!-- Stats Cards -->
    <div x-show="!loading" class="stats-grid">
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="statusCounts.total">0</h3>
                    <p>Total Orders</p>
                </div>
                <div class="stat-icon total">
                    <i class="fas fa-shopping-cart"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="statusCounts.pending">0</h3>
                    <p>Pending Orders</p>
                </div>
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="statusCounts.processing">0</h3>
                    <p>Processing</p>
                </div>
                <div class="stat-icon processing">
                    <i class="fas fa-cog"></i>
                </div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="statusCounts.completed">0</h3>
                    <p>Completed</p>
                </div>
                <div class="stat-icon completed">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div x-show="!loading" class="filters-section">
        <div class="filters-grid">
            <div class="filter-group">
                <label class="filter-label">Search Orders</label>
                <div class="search-input-wrapper">
                    <i class="search-icon fas fa-search"></i>
                    <input 
                        type="text" 
                        x-model="searchTerm" 
                        @input="applyFilters()" 
                        placeholder="Search by order ID, customer name..." 
                        class="filter-input search-input"
                    >
                </div>
            </div>

            <div class="filter-group">
                <label class="filter-label">Status</label>
                <select x-model="statusFilter" @change="applyFilters()" class="filter-select">
                    <option value="">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="processing">Processing</option>
                    <option value="shipped">Shipped</option>
                    <option value="delivered">Delivered</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Date Range</label>
                <select x-model="dateFilter" @change="applyFilters()" class="filter-select">
                    <option value="">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
            </div>

            <div class="filter-group">
                <label class="filter-label">Amount</label>
                <select x-model="amountFilter" @change="applyFilters()" class="filter-select">
                    <option value="">All Amounts</option>
                    <option value="low">Under ₹1,000</option>
                    <option value="medium">₹1,000 - ₹10,000</option>
                    <option value="high">Above ₹10,000</option>
                </select>
            </div>

            <div class="filter-group">
                <button @click="clearFilters()" class="clear-filters-btn" title="Clear Filters">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Orders Table -->
    <div x-show="!loading" class="table-section">
        <div class="table-header">
            <h2 class="table-title">Recent Orders</h2>
            <div class="table-actions">
                <button @click="refreshOrders()" class="table-btn">
                    <i class="fas fa-sync-alt"></i>
                    Refresh
                </button>
                <button @click="exportOrders()" class="table-btn primary">
                    <i class="fas fa-download"></i>
                    Export
                </button>
            </div>
        </div>

        <!-- Table Content -->
        <div x-show="filteredOrders.length > 0">
            <table class="orders-table">
                <thead>
                    <tr>
                        <th>Order ID</th>
                        <th>Customer</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <template x-for="order in paginatedOrders" :key="order.id">
                        <tr>
                            <td>
                                <span class="order-id" x-text="order.id"></span>
                            </td>
                            <td>
                                <div class="customer-info">
                                    <div class="customer-name" x-text="order.customerName"></div>
                                    <div class="customer-email" x-text="order.customerEmail"></div>
                                </div>
                            </td>
                            <td>
                                <span class="order-amount" x-text="'₹' + order.total.toLocaleString()"></span>
                            </td>
                            <td>
                                <span class="order-status" :class="order.status" x-text="order.status"></span>
                            </td>
                            <td>
                                <span class="order-date" x-text="formatDate(order.created_at)"></span>
                            </td>
                            <td>
                                <div class="order-actions">
                                    <button @click="viewOrder(order)" class="action-btn view" title="View Order">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button @click="updateOrderStatus(order)" class="action-btn edit" title="Update Status">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>

            <!-- Pagination -->
            <div class="pagination" x-show="filteredOrders.length > itemsPerPage">
                <div class="pagination-info">
                    <span x-text="paginationInfo"></span>
                </div>
                <div class="pagination-controls">
                    <button 
                        @click="previousPage()" 
                        :disabled="currentPage === 1" 
                        class="pagination-btn"
                        title="Previous Page"
                    >
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    
                    <template x-for="page in visiblePages" :key="page">
                        <button 
                            @click="goToPage(page)" 
                            :class="{ 'active': page === currentPage }"
                            class="pagination-btn"
                            x-text="page"
                            x-show="typeof page === 'number'"
                        ></button>
                        <span 
                            x-show="page === '...'" 
                            class="pagination-dots"
                            x-text="page"
                        ></span>
                    </template>
                    
                    <button 
                        @click="nextPage()" 
                        :disabled="currentPage === totalPages" 
                        class="pagination-btn"
                        title="Next Page"
                    >
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Empty State -->
        <div x-show="filteredOrders.length === 0" class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="empty-title">No orders found</h3>
            <p class="empty-description" x-show="hasActiveFilters">
                Try adjusting your filters to find what you're looking for.
            </p>
            <p class="empty-description" x-show="!hasActiveFilters">
                Orders will appear here once customers start purchasing from your store.
            </p>
        </div>
    </div>

    <!-- ===== DESKTOP MODALS ===== -->
    <!-- Order Details Modal - Desktop -->
    <div x-show="showOrderModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="desktop-modal-overlay"
         @click.self="closeOrderModal()"
         x-cloak>
        
        <div class="desktop-modal"
             x-transition:enter="transition ease-out duration-300 delay-100"
             x-transition:enter-start="transform scale-95 opacity-0"
             x-transition:enter-end="transform scale-100 opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="transform scale-100 opacity-100"
             x-transition:leave-end="transform scale-95 opacity-0">
            
            <!-- Modal Header -->
            <div class="desktop-modal-header">
                <div class="desktop-modal-title-section">
                    <h3 class="desktop-modal-title">Order Details</h3>
                    <p class="desktop-modal-subtitle" x-show="selectedOrder" x-text="'Order ' + selectedOrder?.id"></p>
                </div>
                <button class="desktop-modal-close" @click="closeOrderModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="desktop-modal-content" x-show="selectedOrder">
                
                <!-- Order Overview Cards -->
                <div class="desktop-modal-grid">
                    
                    <!-- Order Information Card -->
                    <div class="desktop-detail-card">
                        <div class="desktop-detail-card-header">
                            <i class="fas fa-clipboard-list"></i>
                            <h4>Order Information</h4>
                        </div>
                        <div class="desktop-detail-card-body">
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Order ID</span>
                                <span class="desktop-detail-value mono" x-text="selectedOrder?.id"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Date Created</span>
                                <span class="desktop-detail-value" x-text="formatDate(selectedOrder?.created_at)"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Status</span>
                                <span class="order-status" :class="selectedOrder?.status" x-text="selectedOrder?.status"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Total Amount</span>
                                <span class="desktop-detail-value amount" x-text="'₹' + (selectedOrder?.total || 0).toLocaleString()"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Customer Information Card -->
                    <div class="desktop-detail-card">
                        <div class="desktop-detail-card-header">
                            <i class="fas fa-user"></i>
                            <h4>Customer Details</h4>
                        </div>
                        <div class="desktop-detail-card-body">
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Name</span>
                                <span class="desktop-detail-value" x-text="selectedOrder?.customerName"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Email</span>
                                <span class="desktop-detail-value" x-text="selectedOrder?.customerEmail"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Phone</span>
                                <span class="desktop-detail-value mono" x-text="selectedOrder?.customerPhone || '+91 98765 43210'"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Address</span>
                                <span class="desktop-detail-value" x-text="selectedOrder?.shippingAddress || 'Default shipping address'"></span>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Order Items Section -->
                <div class="desktop-detail-card full-width">
                    <div class="desktop-detail-card-header">
                        <i class="fas fa-box"></i>
                        <h4>Order Items</h4>
                    </div>
                    <div class="desktop-detail-card-body">
                        <div class="desktop-items-table">
                            <div class="desktop-items-header">
                                <span>Product</span>
                                <span>Quantity</span>
                                <span>Price</span>
                                <span>Total</span>
                            </div>
                            <template x-for="item in getOrderItems(selectedOrder)" :key="item.id">
                                <div class="desktop-items-row">
                                    <div class="desktop-item-product">
                                        <div class="desktop-item-icon" x-text="item.icon">📱</div>
                                        <span class="desktop-item-name" x-text="item.name"></span>
                                    </div>
                                    <span class="desktop-item-qty" x-text="item.quantity + ' x'"></span>
                                    <span class="desktop-item-price" x-text="'₹' + item.price.toLocaleString()"></span>
                                    <span class="desktop-item-total" x-text="'₹' + (item.quantity * item.price).toLocaleString()"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>

                <!-- Payment & Summary Section -->
                <div class="desktop-modal-grid">
                    
                    <!-- Payment Information -->
                    <div class="desktop-detail-card">
                        <div class="desktop-detail-card-header">
                            <i class="fas fa-credit-card"></i>
                            <h4>Payment Details</h4>
                        </div>
                        <div class="desktop-detail-card-body">
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Payment Method</span>
                                <span class="desktop-detail-value" x-text="selectedOrder?.paymentMethod || 'UPI Payment'"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Payment Status</span>
                                <span class="desktop-detail-value success">Paid</span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Transaction ID</span>
                                <span class="desktop-detail-value mono" x-text="selectedOrder?.transactionId || 'TXN' + selectedOrder?.id"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="desktop-detail-card">
                        <div class="desktop-detail-card-header">
                            <i class="fas fa-calculator"></i>
                            <h4>Order Summary</h4>
                        </div>
                        <div class="desktop-detail-card-body">
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Subtotal</span>
                                <span class="desktop-detail-value" x-text="'₹' + (selectedOrder?.total || 0).toLocaleString()"></span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Shipping</span>
                                <span class="desktop-detail-value">₹0</span>
                            </div>
                            <div class="desktop-detail-row">
                                <span class="desktop-detail-label">Tax (GST)</span>
                                <span class="desktop-detail-value">₹0</span>
                            </div>
                            <div class="desktop-detail-row total">
                                <span class="desktop-detail-label">Total Amount</span>
                                <span class="desktop-detail-value" x-text="'₹' + (selectedOrder?.total || 0).toLocaleString()"></span>
                            </div>
                        </div>
                    </div>

                </div>

            </div>

            <!-- Modal Footer -->
            <div class="desktop-modal-footer">
                <button class="desktop-modal-btn secondary" @click="closeOrderModal()">
                    <i class="fas fa-times"></i>
                    Close
                </button>
                <button class="desktop-modal-btn primary" @click="updateOrderStatus(selectedOrder)">
                    <i class="fas fa-edit"></i>
                    Update Status
                </button>
            </div>

        </div>
    </div>

    <!-- Status Update Modal - Desktop -->
    <div x-show="showStatusModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="desktop-modal-overlay"
         @click.self="closeStatusModal()"
         x-cloak>
        
        <div class="desktop-modal smaller"
             x-transition:enter="transition ease-out duration-300 delay-100"
             x-transition:enter-start="transform scale-95 opacity-0"
             x-transition:enter-end="transform scale-100 opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="transform scale-100 opacity-100"
             x-transition:leave-end="transform scale-95 opacity-0">
            
            <!-- Modal Header -->
            <div class="desktop-modal-header">
                <div class="desktop-modal-title-section">
                    <h3 class="desktop-modal-title">Update Order Status</h3>
                    <p class="desktop-modal-subtitle" x-show="selectedOrder" x-text="'Order ' + selectedOrder?.id"></p>
                </div>
                <button class="desktop-modal-close" @click="closeStatusModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="desktop-modal-content" x-show="selectedOrder">
                
                <!-- Current Status Display -->
                <div class="desktop-current-status">
                    <div class="desktop-current-status-badge">
                        <span class="order-status" :class="selectedOrder?.status" x-text="selectedOrder?.status"></span>
                    </div>
                    <div class="desktop-current-status-info">
                        <h4>Current Status</h4>
                        <p>Select a new status to update this order</p>
                    </div>
                </div>

                <!-- Status Options -->
                <div class="desktop-status-options">
                    <template x-for="status in availableStatuses" :key="status.key">
                        <div class="desktop-status-option" 
                             :class="{ 'selected': newStatus === status.key, 'current': selectedOrder?.status === status.key }"
                             @click="selectStatus(status.key)">
                            <div class="desktop-status-radio">
                                <div class="desktop-status-radio-inner" x-show="newStatus === status.key"></div>
                            </div>
                            <div class="desktop-status-info">
                                <div class="desktop-status-name" x-text="status.name"></div>
                                <div class="desktop-status-description" x-text="status.description"></div>
                            </div>
                            <div class="desktop-status-icon" x-text="status.icon"></div>
                        </div>
                    </template>
                </div>

            </div>

            <!-- Modal Footer -->
            <div class="desktop-modal-footer">
                <button class="desktop-modal-btn secondary" @click="closeStatusModal()">
                    <i class="fas fa-times"></i>
                    Cancel
                </button>
                <button class="desktop-modal-btn primary" 
                        @click="confirmStatusUpdate()" 
                        :disabled="!newStatus || newStatus === selectedOrder?.status">
                    <i class="fas fa-check"></i>
                    Update Status
                </button>
            </div>

        </div>
    </div>

</div>

<!-- ===== MOBILE NATIVE APP INTERFACE ===== -->
<div class="mobile-orders-app" x-data="ordersManager()">
    
    <!-- Mobile Loading -->
    <div x-show="loading" class="mobile-loading">
        <div class="mobile-loading-spinner"></div>
        <p class="mobile-loading-text">Loading orders...</p>
    </div>

    <!-- Mobile Stats Cards -->
    <div x-show="!loading" class="mobile-stats-section">
        <h2 class="mobile-stats-title">Overview</h2>
        <div class="mobile-stats-grid">
            <div class="mobile-stat-card">
                <div class="mobile-stat-number" x-text="statusCounts.total">0</div>
                <div class="mobile-stat-label">Total Orders</div>
                <i class="mobile-stat-icon fas fa-shopping-cart"></i>
            </div>
            
            <div class="mobile-stat-card pending">
                <div class="mobile-stat-number" x-text="statusCounts.pending">0</div>
                <div class="mobile-stat-label">Pending</div>
                <i class="mobile-stat-icon fas fa-clock"></i>
            </div>
            
            <div class="mobile-stat-card processing">
                <div class="mobile-stat-number" x-text="statusCounts.processing">0</div>
                <div class="mobile-stat-label">Processing</div>
                <i class="mobile-stat-icon fas fa-cog"></i>
            </div>
            
            <div class="mobile-stat-card completed">
                <div class="mobile-stat-number" x-text="statusCounts.completed">0</div>
                <div class="mobile-stat-label">Completed</div>
                <i class="mobile-stat-icon fas fa-check-circle"></i>
            </div>
        </div>
    </div>

    <!-- Mobile Filter Chips -->
    <div x-show="!loading" class="mobile-filters">
        <h3 class="mobile-filters-title">Filter by Status</h3>
        <div class="mobile-filter-chips">
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === '' }"
                @click="statusFilter = ''; applyFilters()"
            >
                All
            </button>
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === 'pending' }"
                @click="statusFilter = 'pending'; applyFilters()"
            >
                Pending
            </button>
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === 'processing' }"
                @click="statusFilter = 'processing'; applyFilters()"
            >
                Processing
            </button>
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === 'shipped' }"
                @click="statusFilter = 'shipped'; applyFilters()"
            >
                Shipped
            </button>
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === 'delivered' }"
                @click="statusFilter = 'delivered'; applyFilters()"
            >
                Delivered
            </button>
            <button 
                class="mobile-filter-chip" 
                :class="{ 'active': statusFilter === 'cancelled' }"
                @click="statusFilter = 'cancelled'; applyFilters()"
            >
                Cancelled
            </button>
        </div>
    </div>

    <!-- Mobile Orders List -->
    <div x-show="!loading" class="mobile-orders-list">
        <div class="mobile-orders-header">
            <h3 class="mobile-orders-count" x-text="filteredOrders.length + ' Orders'">0 Orders</h3>
            <button class="mobile-sort-btn">
                <i class="fas fa-sort"></i>
                Sort
            </button>
        </div>

        <!-- Mobile Order Cards -->
        <div x-show="filteredOrders.length > 0">
            <template x-for="order in paginatedOrders" :key="order.id">
                <div class="mobile-order-card mobile-animate-in">
                    <div class="mobile-order-header">
                        <div>
                            <div class="mobile-order-id" x-text="order.id"></div>
                            <div class="mobile-order-date" x-text="formatDate(order.created_at)"></div>
                        </div>
                        <div class="mobile-order-amount">
                            <div class="mobile-order-price" x-text="'₹' + order.total.toLocaleString()"></div>
                            <div class="mobile-order-status" :class="order.status" x-text="order.status"></div>
                        </div>
                    </div>
                    
                    <div class="mobile-order-body">
                        <div class="mobile-customer-info">
                            <div class="mobile-customer-avatar" x-text="order.customerName.charAt(0).toUpperCase()"></div>
                            <div class="mobile-customer-details">
                                <div class="mobile-customer-name" x-text="order.customerName"></div>
                                <div class="mobile-customer-email" x-text="order.customerEmail"></div>
                            </div>
                        </div>
                        
                        <div class="mobile-order-actions">
                            <button @click="viewOrder(order)" class="mobile-action-btn primary">
                                <i class="fas fa-eye"></i>
                                View Details
                            </button>
                            <button @click="updateOrderStatus(order)" class="mobile-action-btn secondary">
                                <i class="fas fa-edit"></i>
                                Update
                            </button>
                        </div>
                    </div>
                </div>
            </template>
        </div>

        <!-- Mobile Empty State -->
        <div x-show="filteredOrders.length === 0" class="mobile-empty-state">
            <div class="mobile-empty-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <h3 class="mobile-empty-title">No orders found</h3>
            <p class="mobile-empty-description" x-show="hasActiveFilters">
                Try adjusting your filters to find what you're looking for.
            </p>
            <p class="mobile-empty-description" x-show="!hasActiveFilters">
                Orders will appear here once customers start purchasing from your store.
            </p>
        </div>

        <!-- Mobile Load More -->
        <div x-show="filteredOrders.length > itemsPerPage" class="mobile-pagination">
            <div class="mobile-pagination-info">
                <span x-text="'Page ' + currentPage + ' of ' + totalPages + ' • ' + filteredOrders.length + ' total orders'"></span>
            </div>
            <div class="mobile-pagination-controls">
                <button @click="previousPage()" 
                        :disabled="currentPage === 1" 
                        class="mobile-pagination-btn"
                        :class="{ 'disabled': currentPage === 1 }">
                    <i class="fas fa-chevron-left"></i>
                    Previous
                </button>
                <button @click="nextPage()" 
                        :disabled="currentPage === totalPages" 
                        class="mobile-pagination-btn primary"
                        :class="{ 'disabled': currentPage === totalPages }">
                    Next
                    <i class="fas fa-chevron-right"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Safe Area -->
    <div class="mobile-safe-bottom"></div>

    <!-- ===== ORDER DETAILS MODAL ===== -->
    <div class="mobile-modal-overlay" 
         x-show="showOrderModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         style="display: none;">
        <div class="mobile-modal" 
             x-transition:enter="transition ease-out duration-300 delay-100"
             x-transition:enter-start="transform translate-y-full"
             x-transition:enter-end="transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="transform translate-y-0"
             x-transition:leave-end="transform translate-y-full"
             @click.away="closeOrderModal()">
        <div class="mobile-modal-header">
            <h3 class="mobile-modal-title">Order Details</h3>
            <button class="mobile-modal-close" @click="closeOrderModal()">×</button>
        </div>
        <div class="mobile-modal-content" x-show="selectedOrder">
            
            <!-- Order Basic Info -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">📋</div>
                    Order Information
                </div>
                <div class="mobile-detail-card">
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Order ID</span>
                        <span class="mobile-detail-value mono" x-text="selectedOrder?.id"></span>
                    </div>
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Date</span>
                        <span class="mobile-detail-value" x-text="formatDate(selectedOrder?.created_at)"></span>
                    </div>
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Status</span>
                        <span class="mobile-order-status" :class="selectedOrder?.status" x-text="selectedOrder?.status"></span>
                    </div>
                </div>
            </div>

            <!-- Customer Info -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">👤</div>
                    Customer Details
                </div>
                <div class="mobile-detail-card">
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Name</span>
                        <span class="mobile-detail-value" x-text="selectedOrder?.customerName"></span>
                    </div>
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Email</span>
                        <span class="mobile-detail-value" x-text="selectedOrder?.customerEmail"></span>
                    </div>
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Phone</span>
                        <span class="mobile-detail-value mono" x-text="selectedOrder?.customerPhone || '+91 98765 43210'"></span>
                    </div>
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Address</span>
                        <span class="mobile-detail-value" x-text="selectedOrder?.shippingAddress || 'Default shipping address'"></span>
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">📦</div>
                    Order Items
                </div>
                <template x-for="item in getOrderItems(selectedOrder)" :key="item.id">
                    <div class="mobile-product-item">
                        <div class="mobile-product-image" x-text="item.icon">📱</div>
                        <div class="mobile-product-details">
                            <div class="mobile-product-name" x-text="item.name"></div>
                            <div class="mobile-product-meta">
                                <span class="mobile-product-qty" x-text="'Qty: ' + item.quantity"></span>
                                <span class="mobile-product-price" x-text="'₹' + item.price.toLocaleString()"></span>
                            </div>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Payment Info -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">💳</div>
                    Payment Details
                </div>
                <div class="mobile-detail-card">
                    <div class="mobile-detail-row">
                        <span class="mobile-detail-label">Method</span>
                        <span class="mobile-detail-value" x-text="selectedOrder?.paymentMethod || 'UPI Payment'"></span>
                    </div>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">💰</div>
                    Order Summary
                </div>
                <div class="mobile-order-summary">
                    <div class="mobile-summary-row">
                        <span class="mobile-summary-label">Subtotal</span>
                        <span class="mobile-summary-value" x-text="'₹' + (selectedOrder?.total || 0).toLocaleString()"></span>
                    </div>
                    <div class="mobile-summary-row">
                        <span class="mobile-summary-label">Shipping</span>
                        <span class="mobile-summary-value">₹0</span>
                    </div>
                    <div class="mobile-summary-row">
                        <span class="mobile-summary-label">Tax</span>
                        <span class="mobile-summary-value">₹0</span>
                    </div>
                    <div class="mobile-summary-row">
                        <span class="mobile-summary-label">Total</span>
                        <span class="mobile-summary-value" x-text="'₹' + (selectedOrder?.total || 0).toLocaleString()"></span>
                    </div>
                </div>
            </div>
            
        </div>
    </div>
</div>    <!-- ===== STATUS UPDATE MODAL ===== -->
    <div class="mobile-modal-overlay" 
         x-show="showStatusModal" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         style="display: none;">
        <div class="mobile-modal" 
             x-transition:enter="transition ease-out duration-300 delay-100"
             x-transition:enter-start="transform translate-y-full"
             x-transition:enter-end="transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="transform translate-y-0"
             x-transition:leave-end="transform translate-y-full"
             @click.away="closeStatusModal()">
        <div class="mobile-modal-header">
            <h3 class="mobile-modal-title">Update Order Status</h3>
            <button class="mobile-modal-close" @click="closeStatusModal()">×</button>
        </div>
        <div class="mobile-modal-content" x-show="selectedOrder">
            
            <!-- Current Status -->
            <div class="mobile-current-status">
                <div class="mobile-current-status-icon" x-text="getStatusIcon(selectedOrder?.status)">🔄</div>
                <div class="mobile-current-status-info">
                    <div class="mobile-current-status-label">Current Status</div>
                    <div class="mobile-current-status-name" x-text="selectedOrder?.status || 'Unknown'"></div>
                </div>
            </div>

            <!-- Status Options -->
            <div class="mobile-order-detail-section">
                <div class="mobile-detail-section-title">
                    <div class="mobile-detail-section-icon">🔄</div>
                    Select New Status
                </div>
                <div class="mobile-status-options">
                    <template x-for="status in availableStatuses" :key="status.key">
                        <div class="mobile-status-option" 
                             :class="[status.key, { selected: newStatus === status.key }]"
                             @click="selectStatus(status.key)">
                            <div class="mobile-status-radio"></div>
                            <div class="mobile-status-info">
                                <div class="mobile-status-name" x-text="status.name"></div>
                                <div class="mobile-status-description" x-text="status.description"></div>
                            </div>
                            <div class="mobile-status-icon" x-text="status.icon"></div>
                        </div>
                    </template>
                </div>
            </div>
            
        </div>
        <div class="mobile-modal-actions">
            <button class="mobile-modal-btn secondary" @click="closeStatusModal()">
                Cancel
            </button>
            <button class="mobile-modal-btn primary" 
                    @click="confirmStatusUpdate()" 
                    :disabled="!newStatus || newStatus === selectedOrder?.status">
                <i class="fas fa-check"></i>
                Update Status
            </button>
        </div>
    </div>
</div>

</div>

@push('page-scripts')
<script>
function ordersManager() {
    return {
        loading: true,
        orders: [],
        filteredOrders: [],
        searchTerm: '',
        statusFilter: '',
        dateFilter: '',
        amountFilter: '',
        currentPage: 1,
        itemsPerPage: 5, // Mobile view - 5 orders per page
        
        // Modal states
        showOrderModal: false,
        showStatusModal: false,
        selectedOrder: null,
        newStatus: '',
        availableStatuses: [
            { key: 'pending', name: 'Pending', description: 'Awaiting processing', icon: '⏳' },
            { key: 'processing', name: 'Processing', description: 'Being prepared', icon: '🔧' },
            { key: 'shipped', name: 'Shipped', description: 'On the way', icon: '📦' },
            { key: 'delivered', name: 'Delivered', description: 'Delivered to customer', icon: '✅' },
            { key: 'cancelled', name: 'Cancelled', description: 'Order cancelled', icon: '❌' }
        ],

        init() {
            console.log('Orders Manager initialized');
            this.fetchOrders();
        },

        async fetchOrders() {
            this.loading = true;
            
            // Simulate API call - replace with actual API endpoint
            setTimeout(() => {
                this.orders = [
                    {
                        id: 'ORD-2025001',
                        customerName: 'Rahul Sharma',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 98765 43210',
                        shippingAddress: '123, MG Road, Sector 14, Gurgaon, Haryana - 122001',
                        total: 15000,
                        status: 'delivered',
                        paymentMethod: 'UPI (PhonePe)',
                        created_at: '2025-01-08T10:30:00Z',
                        items: [
                            { id: 1, name: 'Samsung Galaxy M34', quantity: 1, price: 13500, icon: '📱' },
                            { id: 2, name: 'Phone Case & Screen Guard', quantity: 1, price: 1500, icon: '🛡️' }
                        ]
                    },
                    {
                        id: 'ORD-2025002',
                        customerName: 'Priya Patel',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 87654 32109',
                        shippingAddress: '456, Park Street, Civil Lines, Delhi - 110054',
                        total: 2500,
                        status: 'processing',
                        paymentMethod: 'Cash on Delivery',
                        created_at: '2025-01-08T08:30:00Z',
                        items: [
                            { id: 3, name: 'Traditional Kurti Set', quantity: 2, price: 1250, icon: '👗' }
                        ]
                    },
                    {
                        id: 'ORD-2025003',
                        customerName: 'Amit Kumar',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 76543 21098',
                        shippingAddress: '789, Lake View Apartments, Banjara Hills, Hyderabad - 500034',
                        total: 8500,
                        status: 'shipped',
                        paymentMethod: 'Credit Card',
                        created_at: '2025-01-07T15:20:00Z',
                        items: [
                            { id: 4, name: 'Wireless Bluetooth Headphones', quantity: 1, price: 5000, icon: '🎧' },
                            { id: 5, name: 'Power Bank 10000mAh', quantity: 1, price: 3500, icon: '🔋' }
                        ]
                    },
                    {
                        id: 'ORD-2025004',
                        customerName: 'Sneha Gupta',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 65432 10987',
                        shippingAddress: '321, Tower Road, Koramangala, Bangalore - 560034',
                        total: 3200,
                        status: 'pending',
                        paymentMethod: 'UPI (GPay)',
                        created_at: '2025-01-06T11:45:00Z',
                        items: [
                            { id: 6, name: 'Ethnic Jewelry Set', quantity: 1, price: 3200, icon: '💍' }
                        ]
                    },
                    {
                        id: 'ORD-2025005',
                        customerName: 'Vikash Singh',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 54321 09876',
                        shippingAddress: '987, Mall Road, Shimla, Himachal Pradesh - 171001',
                        total: 12000,
                        status: 'processing',
                        paymentMethod: 'Net Banking',
                        created_at: '2025-01-05T09:15:00Z',
                        items: [
                            { id: 7, name: 'Winter Jacket Collection', quantity: 2, price: 6000, icon: '🧥' }
                        ]
                    },
                    {
                        id: 'ORD-2025006',
                        customerName: 'Anjali Mehta',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 43210 98765',
                        shippingAddress: '654, Beach Road, Marine Drive, Mumbai - 400002',
                        total: 750,
                        status: 'cancelled',
                        paymentMethod: 'UPI (Paytm)',
                        created_at: '2025-01-04T14:30:00Z',
                        items: [
                            { id: 8, name: 'Mobile Accessories Kit', quantity: 1, price: 750, icon: '📱' }
                        ]
                    },
                    {
                        id: 'ORD-2025007',
                        customerName: 'Ravi Jain',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 32109 87654',
                        shippingAddress: '147, City Center, Connaught Place, Delhi - 110001',
                        total: 5800,
                        status: 'delivered',
                        paymentMethod: 'Credit Card',
                        created_at: '2025-01-03T16:45:00Z',
                        items: [
                            { id: 9, name: 'Laptop Accessories Bundle', quantity: 1, price: 5800, icon: '💻' }
                        ]
                    },
                    {
                        id: 'ORD-2025008',
                        customerName: 'Kavya Reddy',
                        customerEmail: '<EMAIL>',
                        customerPhone: '+91 21098 76543',
                        shippingAddress: '258, Tech Park, HITEC City, Hyderabad - 500081',
                        total: 18500,
                        status: 'shipped',
                        paymentMethod: 'UPI (PhonePe)',
                        created_at: '2025-01-02T12:15:00Z',
                        items: [
                            { id: 10, name: 'Smart Watch Pro', quantity: 1, price: 15000, icon: '⌚' },
                            { id: 11, name: 'Wireless Earbuds', quantity: 1, price: 3500, icon: '🎧' }
                        ]
                    }
                ];
                
                this.applyFilters();
                this.loading = false;
            }, 500);
        },

        applyFilters() {
            this.filteredOrders = this.orders.filter(order => {
                const searchMatch = !this.searchTerm ||
                    order.id.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerName.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    order.customerEmail.toLowerCase().includes(this.searchTerm.toLowerCase());

                const statusMatch = !this.statusFilter || order.status === this.statusFilter;

                let amountMatch = true;
                if (this.amountFilter === 'low') amountMatch = order.total < 1000;
                else if (this.amountFilter === 'medium') amountMatch = order.total >= 1000 && order.total <= 10000;
                else if (this.amountFilter === 'high') amountMatch = order.total > 10000;

                let dateMatch = true;
                if (this.dateFilter) {
                    const orderDate = new Date(order.created_at);
                    const now = new Date();
                    
                    switch(this.dateFilter) {
                        case 'today':
                            dateMatch = orderDate.toDateString() === now.toDateString();
                            break;
                        case 'week':
                            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                            dateMatch = orderDate >= weekAgo;
                            break;
                        case 'month':
                            dateMatch = orderDate.getMonth() === now.getMonth() && orderDate.getFullYear() === now.getFullYear();
                            break;
                        case 'quarter':
                            const currentQuarter = Math.floor(now.getMonth() / 3);
                            const orderQuarter = Math.floor(orderDate.getMonth() / 3);
                            dateMatch = orderQuarter === currentQuarter && orderDate.getFullYear() === now.getFullYear();
                            break;
                    }
                }

                return searchMatch && statusMatch && amountMatch && dateMatch;
            });

            this.currentPage = 1;
        },

        clearFilters() {
            this.searchTerm = '';
            this.statusFilter = '';
            this.dateFilter = '';
            this.amountFilter = '';
            this.applyFilters();
        },

        refreshOrders() {
            this.fetchOrders();
            // Show toast notification if available
            if (window.showToast) {
                window.showToast('Orders refreshed successfully', 'success');
            }
        },

        exportOrders() {
            // Export functionality
            console.log('Exporting orders...');
            if (window.showToast) {
                window.showToast('Export feature coming soon', 'info');
            }
        },

        viewOrder(order) {
            console.log('=== VIEW ORDER DEBUG ===');
            console.log('Order received:', order);
            console.log('Current showOrderModal:', this.showOrderModal);
            console.log('Current selectedOrder:', this.selectedOrder);
            
            this.selectedOrder = order;
            this.showOrderModal = true;
            document.body.style.overflow = 'hidden';
            
            console.log('After setting - showOrderModal:', this.showOrderModal);
            console.log('After setting - selectedOrder:', this.selectedOrder);
            console.log('=== END VIEW ORDER DEBUG ===');
            
            // Force a re-render by using $nextTick
            this.$nextTick(() => {
                console.log('NextTick - Modal should be visible now');
                const modalElement = document.querySelector('.desktop-modal-overlay');
                if (modalElement) {
                    console.log('Modal element found:', modalElement);
                    console.log('Modal computed style display:', window.getComputedStyle(modalElement).display);
                } else {
                    console.log('Modal element NOT found');
                }
            });
        },

        closeOrderModal() {
            this.showOrderModal = false;
            this.selectedOrder = null;
            document.body.style.overflow = '';
        },

        updateOrderStatus(order) {
            console.log('=== UPDATE ORDER STATUS DEBUG ===');
            console.log('Order received:', order);
            console.log('Current showStatusModal:', this.showStatusModal);
            
            this.selectedOrder = order;
            this.newStatus = order.status;
            this.showStatusModal = true;
            document.body.style.overflow = 'hidden';
            
            console.log('After setting - showStatusModal:', this.showStatusModal);
            console.log('After setting - selectedOrder:', this.selectedOrder);
            console.log('=== END UPDATE ORDER STATUS DEBUG ===');
        },

        // Modal Functions
        closeStatusModal() {
            this.showStatusModal = false;
            this.selectedOrder = null;
            this.newStatus = null;
            document.body.style.overflow = '';
        },

        selectStatus(status) {
            this.newStatus = status;
        },

        confirmStatusUpdate() {
            if (this.selectedOrder && this.newStatus && this.newStatus !== this.selectedOrder.status) {
                const oldStatus = this.selectedOrder.status;
                this.selectedOrder.status = this.newStatus;
                
                // Update the order in the orders array
                const orderIndex = this.orders.findIndex(o => o.id === this.selectedOrder.id);
                if (orderIndex !== -1) {
                    this.orders[orderIndex].status = this.newStatus;
                }
                
                // Re-apply filters to refresh the view
                this.applyFilters();
                
                this.showToast(`Order ${this.selectedOrder.id} status updated from ${oldStatus} to ${this.newStatus}`, 'success');
                this.closeStatusModal();
            }
        },

        getOrderItems(order) {
            return order?.items || [
                { id: 1, name: 'Sample Product', quantity: 1, price: order?.total || 0, icon: '📦' }
            ];
        },

        getStatusIcon(status) {
            const icons = {
                'pending': '⏳',
                'processing': '🔄',
                'shipped': '🚚',
                'delivered': '✅',
                'cancelled': '❌'
            };
            return icons[status] || '📦';
        },

        get availableStatuses() {
            return [
                {
                    key: 'pending',
                    name: 'Pending',
                    description: 'Order received and waiting for confirmation',
                    icon: '⏳'
                },
                {
                    key: 'processing',
                    name: 'Processing',
                    description: 'Order is being prepared and packed',
                    icon: '🔄'
                },
                {
                    key: 'shipped',
                    name: 'Shipped',
                    description: 'Order has been dispatched for delivery',
                    icon: '🚚'
                },
                {
                    key: 'delivered',
                    name: 'Delivered',
                    description: 'Order has been successfully delivered',
                    icon: '✅'
                },
                {
                    key: 'cancelled',
                    name: 'Cancelled',
                    description: 'Order has been cancelled',
                    icon: '❌'
                }
            ];
        },

        showToast(message, type = 'info') {
            // Create toast notification
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 12px 24px;
                border-radius: 24px;
                font-size: 14px;
                font-weight: 600;
                z-index: 2000;
                box-shadow: 0 4px 16px rgba(0,0,0,0.2);
                animation: slideDown 0.3s ease-out;
                max-width: 90%;
                text-align: center;
            `;
            toast.textContent = message;
            
            // Add animation styles
            if (!document.getElementById('toast-styles')) {
                const style = document.createElement('style');
                style.id = 'toast-styles';
                style.textContent = `
                    @keyframes slideDown {
                        from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                        to { transform: translateX(-50%) translateY(0); opacity: 1; }
                    }
                    @keyframes slideUp {
                        to { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                    }
                `;
                document.head.appendChild(style);
            }
            
            document.body.appendChild(toast);
            
            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.style.animation = 'slideUp 0.3s ease-out forwards';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        },

        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        // Pagination
        get totalPages() {
            return Math.ceil(this.filteredOrders.length / this.itemsPerPage);
        },

        get paginatedOrders() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredOrders.slice(start, end);
        },

        get visiblePages() {
            const delta = 2;
            const range = [];
            const rangeWithDots = [];

            for (let i = Math.max(2, this.currentPage - delta); 
                 i <= Math.min(this.totalPages - 1, this.currentPage + delta); 
                 i++) {
                range.push(i);
            }

            if (this.currentPage - delta > 2) {
                rangeWithDots.push(1, '...');
            } else {
                rangeWithDots.push(1);
            }

            rangeWithDots.push(...range);

            if (this.currentPage + delta < this.totalPages - 1) {
                rangeWithDots.push('...', this.totalPages);
            } else {
                rangeWithDots.push(this.totalPages);
            }

            return rangeWithDots.filter((v, i, a) => a.indexOf(v) === i && v <= this.totalPages);
        },

        get paginationInfo() {
            const start = (this.currentPage - 1) * this.itemsPerPage + 1;
            const end = Math.min(this.currentPage * this.itemsPerPage, this.filteredOrders.length);
            return `Showing ${start}-${end} of ${this.filteredOrders.length} orders`;
        },

        previousPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.currentPage++;
            }
        },

        goToPage(page) {
            if (typeof page === 'number' && page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        get statusCounts() {
            const counts = this.orders.reduce((acc, order) => {
                acc[order.status] = (acc[order.status] || 0) + 1;
                return acc;
            }, { pending: 0, processing: 0, shipped: 0, delivered: 0, cancelled: 0 });

            counts.total = this.orders.length;
            counts.completed = (counts.delivered || 0) + (counts.shipped || 0);

            return counts;
        },

        get hasActiveFilters() {
            return this.searchTerm || this.statusFilter || this.dateFilter || this.amountFilter;
        },

        // Mobile Load More
        loadMore() {
            this.itemsPerPage += 10;
        },

        // Order Items Helper
        getOrderItems(order) {
            // Return actual order items if available, otherwise return fallback
            return order?.items || [
                { id: 1, name: 'Product 1', quantity: 1, price: 500, icon: '📱' },
                { id: 2, name: 'Product 2', quantity: 2, price: 1500, icon: '💻' },
                { id: 3, name: 'Product 3', quantity: 1, price: 2500, icon: '🎧' }
            ];
        },

        // Status Icon Helper
        getStatusIcon(status) {
            const icons = {
                pending: '⏳',
                processing: '🔧',
                shipped: '📦',
                delivered: '✅',
                cancelled: '❌'
            };
            return icons[status] || '❓';
        }
    }
}
</script>
@endpush
@endsection
