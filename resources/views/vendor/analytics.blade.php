@extends('layouts.vendor')

@section('title', 'Analytics')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/vendor-analytics.css') }}">
<link rel="stylesheet" href="{{ asset('css/vendor/mobile-analytics.css') }}">
@endpush

@section('content')
<div class="analytics-container" x-data="vendorAnalyticsComponent()">
    
    <!-- Mobile Date Range Selector -->
    <div class="mobile-date-range">
        <div class="mobile-date-title">Select Time Range</div>
        <div class="mobile-date-buttons">
            <button class="mobile-date-btn active" @click="setTimeRange('day')">Today</button>
            <button class="mobile-date-btn" @click="setTimeRange('week')">7 Days</button>
            <button class="mobile-date-btn" @click="setTimeRange('month')">30 Days</button>
            <button class="mobile-date-btn" @click="setTimeRange('quarter')">90 Days</button>
            <button class="mobile-date-btn" @click="setTimeRange('year')">1 Year</button>
        </div>
    </div>

    <!-- Loading State -->
    <div x-show="loading" class="mobile-loading">
        <div class="mobile-loading-spinner"></div>
        <div class="mobile-loading-text">Loading analytics...</div>
    </div>

    <!-- Mobile Stats Section -->
    <div x-show="!loading" class="mobile-stats-section">
        <div class="mobile-stats-title">Analytics Overview</div>
        <div class="mobile-stats-grid">
            <!-- Mobile Revenue Stats -->
            <div class="mobile-stat-card revenue">
                <div class="mobile-stat-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="mobile-stat-value" x-text="formatCurrency(analyticsData?.revenue || 0)">₹25000</div>
                <div class="mobile-stat-label">Total Revenue</div>
                <div class="mobile-stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.revenueChange || '+15%'">+15%</span>
                </div>
            </div>

            <!-- Mobile Orders Stats -->
            <div class="mobile-stat-card orders">
                <div class="mobile-stat-icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="mobile-stat-value" x-text="analyticsData?.orders || 0">156</div>
                <div class="mobile-stat-label">Total Orders</div>
                <div class="mobile-stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.ordersChange || '+8%'">+8%</span>
                </div>
            </div>

            <!-- Mobile Customers Stats -->
            <div class="mobile-stat-card customers">
                <div class="mobile-stat-icon">
                    <i class="fas fa-users"></i>
                </div>
                <div class="mobile-stat-value" x-text="analyticsData?.customers || 0">45</div>
                <div class="mobile-stat-label">New Customers</div>
                <div class="mobile-stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.customersChange || '+12%'">+12%</span>
                </div>
            </div>

            <!-- Mobile Conversion Rate Stats -->
            <div class="mobile-stat-card conversion">
                <div class="mobile-stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="mobile-stat-value" x-text="(analyticsData?.conversionRate || 0) + '%'">3.2%</div>
                <div class="mobile-stat-label">Conversion Rate</div>
                <div class="mobile-stat-trend down">
                    <i class="fas fa-arrow-down"></i>
                    <span x-text="analyticsData?.conversionChange || '+2%'">+2%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Chart Section -->
    <div x-show="!loading" class="mobile-chart-container">
        <div class="mobile-chart-card">
            <div class="mobile-chart-header">
                <h3 class="mobile-chart-title" x-text="getChartTitle()">Revenue Trends</h3>
                <div class="mobile-chart-controls">
                    <button class="mobile-chart-control" :class="{ 'active': chartMetric === 'revenue' }" @click="setChartMetric('revenue')">Revenue</button>
                    <button class="mobile-chart-control" :class="{ 'active': chartMetric === 'orders' }" @click="setChartMetric('orders')">Orders</button>
                    <button class="mobile-chart-control" :class="{ 'active': chartMetric === 'customers' }" @click="setChartMetric('customers')">Customers</button>
                </div>
            </div>
            <div class="mobile-chart-body">
                <div x-show="loading" class="mobile-loading">
                    <div class="mobile-loading-spinner"></div>
                </div>
                <div class="mobile-chart-canvas" x-show="!loading">
                    <canvas id="mobileRevenueChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Desktop Header (Hidden on mobile) -->
    <div class="analytics-header">
        <h1 class="analytics-title">Store Analytics</h1>
        <p class="analytics-subtitle">Track your performance and grow your business with data-driven insights</p>
        
        <div class="date-range-selector">
            <button class="date-range-btn active" @click="setTimeRange('day')">Today</button>
            <button class="date-range-btn" @click="setTimeRange('week')">7 Days</button>
            <button class="date-range-btn" @click="setTimeRange('month')">30 Days</button>
            <button class="date-range-btn" @click="setTimeRange('quarter')">90 Days</button>
            <button class="date-range-btn" @click="setTimeRange('year')">1 Year</button>
        </div>
    </div>

    <!-- Desktop Stats Cards (Hidden on mobile) -->
    <div x-show="!loading" class="stats-grid">
        <!-- Revenue Stats -->
        <div class="stat-card revenue">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="formatCurrency(analyticsData?.revenue || 0)">₹0</h3>
                    <p>Total Revenue</p>
                </div>
                <div class="stat-icon revenue">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.revenueChange || '+0%'">+0%</span>
                </div>
            </div>
        </div>

        <!-- Orders Stats -->
        <div class="stat-card orders">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="analyticsData?.orders || 0">0</h3>
                    <p>Total Orders</p>
                </div>
                <div class="stat-icon orders">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.ordersChange || '+0%'">+0%</span>
                </div>
            </div>
        </div>

        <!-- Customers Stats -->
        <div class="stat-card customers">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="analyticsData?.customers || 0">0</h3>
                    <p>New Customers</p>
                </div>
                <div class="stat-icon customers">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-trend up">
                    <i class="fas fa-arrow-up"></i>
                    <span x-text="analyticsData?.customersChange || '+0%'">+0%</span>
                </div>
            </div>
        </div>

        <!-- Conversion Rate Stats -->
        <div class="stat-card conversion">
            <div class="stat-content">
                <div class="stat-info">
                    <h3 x-text="(analyticsData?.conversionRate || 0) + '%'">0%</h3>
                    <p>Conversion Rate</p>
                </div>
                <div class="stat-icon conversion">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-trend down">
                    <i class="fas fa-arrow-down"></i>
                    <span x-text="analyticsData?.conversionChange || '+0%'">+0%</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Desktop Chart Grid (Hidden on mobile) -->
    <div x-show="!loading" class="chart-grid">
        <!-- Revenue Chart -->
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title" x-text="getChartTitle()">Revenue Trends</h3>
                <div class="chart-controls">
                    <button class="chart-control" :class="{ 'active': chartMetric === 'revenue' }" @click="setChartMetric('revenue')">Revenue</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'orders' }" @click="setChartMetric('orders')">Orders</button>
                    <button class="chart-control" :class="{ 'active': chartMetric === 'customers' }" @click="setChartMetric('customers')">Customers</button>
                </div>
            </div>
            <div class="chart-body">
                <div x-show="loading" class="chart-loading">
                    <div class="loading-spinner"></div>
                </div>
                <div class="chart-container" x-show="!loading">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Top Products -->
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">Top Performing Products</h3>
                <a href="{{ route('vendor.products.index') }}" class="chart-link">View all</a>
            </div>
            <div class="chart-body">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <h3>No Product Data</h3>
                    <p>Product performance data will appear here once you have sales.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Desktop Analytics Tables Section (Hidden on mobile) -->
    <div x-show="!loading" class="table-section">
        <div class="table-grid">
            <!-- Traffic Sources -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">Traffic Sources</h3>
                    <a href="#" class="table-link">View details</a>
                </div>
                <div class="table-body">
                    <table class="analytics-table">
                        <thead>
                            <tr>
                                <th>Source</th>
                                <th>Visitors</th>
                                <th>Conversion</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="3" class="empty-cell">No traffic data available</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Customer Demographics -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">Customer Demographics</h3>
                    <a href="#" class="table-link">View details</a>
                </div>
                <div class="table-body">
                    <table class="analytics-table">
                        <thead>
                            <tr>
                                <th>Age Group</th>
                                <th>Customers</th>
                                <th>Revenue</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="3" class="empty-cell">No customer data available</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Desktop Export Analytics Section (Hidden on mobile) -->
    <div x-show="!loading" class="export-section">
        <div class="export-card">
            <div class="export-header">
                <div class="export-content">
                    <h3 class="export-title">Export Analytics Data</h3>
                    <p class="export-subtitle">Download your analytics data in various formats for further analysis</p>
                </div>
                <div class="export-actions">
                    <button class="export-btn">
                        <i class="fas fa-file-csv"></i>
                        <span>CSV</span>
                    </button>
                    <button class="export-btn">
                        <i class="fas fa-file-excel"></i>
                        <span>Excel</span>
                    </button>
                    <button class="export-btn">
                        <i class="fas fa-file-pdf"></i>
                        <span>PDF</span>
                    </button>
                    <button class="export-btn primary">
                        <i class="fas fa-download"></i>
                        <span>Download</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Analytics Sections -->
    <div x-show="!loading" class="mobile-analytics-sections">
        
        <!-- Mobile Traffic Sources -->
        <div class="mobile-analytics-section">
            <div class="mobile-analytics-header">
                <h3 class="mobile-analytics-section-title">Traffic Sources</h3>
                <a href="#" class="mobile-analytics-link">View All</a>
            </div>
            <div class="mobile-analytics-body">
                <div class="mobile-traffic-sources">
                    <template x-for="(source, index) in trafficSources" :key="index">
                        <div class="mobile-traffic-item">
                            <div class="mobile-traffic-icon">
                                <i :class="source.icon"></i>
                            </div>
                            <div class="mobile-traffic-info">
                                <div class="mobile-traffic-name" x-text="source.name"></div>
                                <div class="mobile-traffic-stats">
                                    <div class="mobile-traffic-stat">
                                        <div class="mobile-traffic-stat-value" x-text="formatNumber(source.visitors)"></div>
                                        <div class="mobile-traffic-stat-label">Visitors</div>
                                    </div>
                                    <div class="mobile-traffic-stat">
                                        <div class="mobile-traffic-stat-value" x-text="formatPercentage(source.conversion)"></div>
                                        <div class="mobile-traffic-stat-label">Conversion</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        
        <!-- Mobile Demographics -->
        <div class="mobile-analytics-section">
            <div class="mobile-analytics-header">
                <h3 class="mobile-analytics-section-title">Customer Demographics</h3>
                <a href="#" class="mobile-analytics-link">View All</a>
            </div>
            <div class="mobile-analytics-body">
                <div class="mobile-demographics-grid">
                    <template x-for="(demo, index) in demographics" :key="index">
                        <div class="mobile-demographic-card">
                            <div class="mobile-demographic-age" x-text="demo.ageGroup"></div>
                            <div class="mobile-demographic-stats">
                                <span x-text="demo.customers"></span>
                                <span x-text="formatCurrency(demo.revenue)"></span>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
        
        <!-- Mobile Best Performing Products -->
        <div class="mobile-analytics-section">
            <div class="mobile-analytics-header">
                <h3 class="mobile-analytics-section-title">Best Performing Products</h3>
                <a href="{{ route('vendor.products.index') }}" class="mobile-analytics-link">View All</a>
            </div>
            <div class="mobile-analytics-body">
                <div class="mobile-best-products">
                    <template x-for="(product, index) in bestProducts" :key="index">
                        <div class="mobile-product-item">
                            <div class="mobile-product-rank" x-text="index + 1"></div>
                            <div class="mobile-product-image">
                                <i :class="product.icon"></i>
                            </div>
                            <div class="mobile-product-info">
                                <div class="mobile-product-name" x-text="product.name"></div>
                                <div class="mobile-product-stats">
                                    <div class="mobile-product-stat">
                                        <div class="mobile-product-stat-value" x-text="product.sales"></div>
                                        <div class="mobile-product-stat-label">Sales</div>
                                    </div>
                                    <div class="mobile-product-stat">
                                        <div class="mobile-product-stat-value" x-text="formatCurrency(product.revenue)"></div>
                                        <div class="mobile-product-stat-label">Revenue</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
function vendorAnalyticsComponent() {
    return {
        loading: false,  // Set to false initially so content shows
        timeRange: 'day',
        chartMetric: 'revenue',
        analyticsData: {
            revenue: 25000,  // Add some dummy data
            revenueChange: '+15%',
            orders: 156,
            ordersChange: '+8%',
            customers: 45,
            customersChange: '+12%',
            conversionRate: 3.2,
            conversionChange: '+2%'
        },
        
        trafficSources: [
            { name: 'Google Search', icon: 'fab fa-google', visitors: 1234, conversion: 12.5 },
            { name: 'Facebook', icon: 'fab fa-facebook', visitors: 876, conversion: 8.3 },
            { name: 'Instagram', icon: 'fab fa-instagram', visitors: 543, conversion: 15.2 },
            { name: 'Direct Traffic', icon: 'fas fa-globe', visitors: 321, conversion: 18.7 }
        ],
        
        demographics: [
            { ageGroup: '18-25', customers: 45, revenue: 12500 },
            { ageGroup: '26-35', customers: 78, revenue: 25300 },
            { ageGroup: '36-45', customers: 32, revenue: 18750 },
            { ageGroup: '46+', customers: 15, revenue: 8200 }
        ],
        
        bestProducts: [
            { name: 'Premium Wireless Headphones', icon: 'fas fa-headphones', sales: 156, revenue: 78000 },
            { name: 'Smartphone Case Collection', icon: 'fas fa-mobile-alt', sales: 89, revenue: 26700 },
            { name: 'Laptop Accessories Bundle', icon: 'fas fa-laptop', sales: 67, revenue: 40200 },
            { name: 'Smart Watch Series', icon: 'fas fa-watch', sales: 45, revenue: 22500 },
            { name: 'Digital Camera Kit', icon: 'fas fa-camera', sales: 32, revenue: 19200 }
        ],
        
        init() {
            this.initializeCharts();
        },
        
        setTimeRange(range) {
            this.timeRange = range;
            // Update desktop buttons
            document.querySelectorAll('.date-range-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            // Update mobile buttons
            document.querySelectorAll('.mobile-date-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            this.initializeCharts();
        },
        
        setChartMetric(metric) {
            this.chartMetric = metric;
            this.initializeCharts();
        },
        
        getChartTitle() {
            const titles = {
                revenue: 'Revenue Trends',
                orders: 'Order Trends', 
                customers: 'Customer Growth'
            };
            return titles[this.chartMetric] || 'Analytics';
        },
        
        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                maximumFractionDigits: 0
            }).format(amount);
        },
        
        formatNumber(number) {
            return new Intl.NumberFormat('en-IN').format(number);
        },
        
        formatPercentage(value) {
            return value + '%';
        },
        
        initializeCharts() {
            // Desktop Chart
            const ctx = document.getElementById('revenueChart');
            if (ctx && typeof Chart !== 'undefined') {
                Chart.getChart(ctx)?.destroy();
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: this.getChartLabels(),
                        datasets: [{
                            label: this.getChartTitle(),
                            data: this.getChartData(),
                            borderColor: '#00a884',
                            backgroundColor: 'rgba(0, 168, 132, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0,0,0,0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
            
            // Mobile Chart
            const mobileCtx = document.getElementById('mobileRevenueChart');
            if (mobileCtx && typeof Chart !== 'undefined') {
                Chart.getChart(mobileCtx)?.destroy();
                
                new Chart(mobileCtx, {
                    type: 'line',
                    data: {
                        labels: this.getChartLabels(),
                        datasets: [{
                            label: this.getChartTitle(),
                            data: this.getChartData(),
                            borderColor: '#10b981',
                            backgroundColor: 'rgba(16, 185, 129, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0,0,0,0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        },
        
        getChartLabels() {
            const labels = {
                day: ['12 AM', '2 AM', '4 AM', '6 AM', '8 AM', '10 AM', '12 PM', '2 PM', '4 PM', '6 PM', '8 PM', '10 PM'],
                week: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                month: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                quarter: ['Q1', 'Q2', 'Q3'],
                year: ['2020', '2021', '2022', '2023', '2024']
            };
            
            return labels[this.timeRange] || labels.day;
        },
        
        getChartData() {
            const baseData = {
                day: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                week: [0, 0, 0, 0, 0, 0, 0],
                month: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
                quarter: [0, 0, 0],
                year: [0, 0, 0, 0, 0]
            };
            
            return baseData[this.timeRange] || baseData.day;
        }
    }
}
</script>
@endpush

@section('page-title', 'Analytics')
@section('page-subtitle', 'Track your performance and grow your business')

@endsection
