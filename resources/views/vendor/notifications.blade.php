@extends('layouts.vendor')

@section('title', 'Notifications')

@push('page-styles')
<link rel="stylesheet" href="{{ asset('css/vendor/mobile-notifications.css') }}">
@endpush

@section('content')
<div x-data="vendorNotifications()">
    
    <!-- ===== MOBILE LAYOUT (SHOW ONLY ON MOBILE) ===== -->
    <div class="mobile-notifications-container" style="display: none;">
        
        <!-- Filter Chips -->
        <div class="mobile-filter-section">
            <div class="mobile-filter-chips">
                <button @click="setFilter('all')" 
                        :class="{ 'active': currentFilter === 'all' }"
                        class="mobile-filter-chip">
                    All
                </button>
                <button @click="setFilter('unread')" 
                        :class="{ 'active': currentFilter === 'unread' }"
                        class="mobile-filter-chip">
                    Unread
                </button>
                <button @click="setFilter('orders')" 
                        :class="{ 'active': currentFilter === 'orders' }"
                        class="mobile-filter-chip">
                    Orders
                </button>
                <button @click="setFilter('reviews')" 
                        :class="{ 'active': currentFilter === 'reviews' }"
                        class="mobile-filter-chip">
                    Reviews
                </button>
                <button @click="setFilter('payments')" 
                        :class="{ 'active': currentFilter === 'payments' }"
                        class="mobile-filter-chip">
                    Payments
                </button>
                <button @click="setFilter('alerts')" 
                        :class="{ 'active': currentFilter === 'alerts' }"
                        class="mobile-filter-chip">
                    Alerts
                </button>
            </div>
        </div>

        <!-- Pull to Refresh Indicator -->
        <div class="mobile-pull-refresh" x-show="pullToRefreshActive">
            <i class="mobile-refresh-icon fas fa-sync-alt"></i>
            <span>Pull to refresh</span>
        </div>

        <!-- Mobile Notifications List -->
        <div class="mobile-notifications-list native-scroll-hide">
            <template x-for="notification in filteredNotifications" :key="notification.id">
                <div class="mobile-notification-card" 
                     :class="{ 'unread': !notification.read, 'urgent': notification.urgent }"
                     @click="openNotification(notification)">
                    
                    <!-- Notification Card Content -->
                    <div class="mobile-card-content">
                        <div class="mobile-notification-icon" :class="getIconClass(notification.type)">
                            <i :class="getIconName(notification.type)"></i>
                        </div>
                        
                        <div class="mobile-notification-body">
                            <div class="mobile-notification-header">
                                <div class="mobile-notification-title" x-text="notification.title"></div>
                                <div class="mobile-notification-time" x-text="formatTime(notification.time)"></div>
                            </div>
                            <div class="mobile-notification-message" x-text="notification.message"></div>
                            <div class="mobile-notification-meta">
                                <span class="mobile-notification-type" x-text="notification.type.toUpperCase()"></span>
                                <span x-show="!notification.read" class="mobile-unread-dot"></span>
                                <span x-show="notification.urgent" class="mobile-urgent-badge">URGENT</span>
                            </div>
                        </div>
                        
                        <div class="mobile-notification-actions">
                            <button @click.stop="toggleRead(notification)" class="mobile-quick-action" 
                                    :title="notification.read ? 'Mark as unread' : 'Mark as read'">
                                <i :class="notification.read ? 'fas fa-envelope-open' : 'fas fa-envelope'"></i>
                            </button>
                            <button @click.stop="deleteNotification(notification)" class="mobile-quick-action delete">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Empty State -->
            <div x-show="filteredNotifications.length === 0" class="mobile-notifications-empty">
                <div class="mobile-empty-icon">
                    <i class="fas fa-bell-slash"></i>
                </div>
                <div class="mobile-empty-title">No notifications</div>
                <div class="mobile-empty-description">You're all caught up! Check back later for new updates.</div>
            </div>

            <!-- Loading State -->
            <template x-show="loading">
                <div class="mobile-loading-card" x-for="i in 3">
                    <div class="mobile-loading-icon"></div>
                    <div class="mobile-loading-content">
                        <div class="mobile-loading-bar long"></div>
                        <div class="mobile-loading-bar medium"></div>
                        <div class="mobile-loading-bar short"></div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- ===== DESKTOP LAYOUT ===== -->
    <div class="desktop-notifications-container">
        <div class="notifications-header">
            <div class="notifications-header-left">
                <h1 class="notifications-title">Notifications</h1>
                <p class="notifications-subtitle">Stay updated with your store activities</p>
            </div>
            <div class="notifications-header-right">
                <button @click="markAllAsRead()" class="btn btn-secondary" :disabled="!hasUnreadNotifications">
                    <i class="fas fa-check-double"></i>
                    Mark All Read
                </button>
                <button @click="toggleNotificationSettings()" class="btn btn-primary">
                    <i class="fas fa-cog"></i>
                    Settings
                </button>
            </div>
        </div>

        <!-- Desktop Notifications List -->
        <div class="desktop-notifications-list">
            <template x-for="notification in filteredNotifications" :key="notification.id">
                <div class="desktop-notification-card" 
                     :class="{ 'unread': !notification.read, 'urgent': notification.urgent }"
                     @click="openNotification(notification)">
                    
                    <div class="notification-icon" :class="getIconClass(notification.type)">
                        <i :class="getIconName(notification.type)"></i>
                    </div>
                    
                    <div class="notification-content">
                        <div class="notification-header">
                            <h3 class="notification-title" x-text="notification.title"></h3>
                            <span class="notification-time" x-text="formatTime(notification.time)"></span>
                        </div>
                        <p class="notification-message" x-text="notification.message"></p>
                        <div class="notification-footer">
                            <span class="notification-type" x-text="notification.type.toUpperCase()"></span>
                            <div class="notification-actions">
                                <button @click.stop="toggleRead(notification)" class="btn-icon" 
                                        :title="notification.read ? 'Mark as unread' : 'Mark as read'">
                                    <i :class="notification.read ? 'fas fa-envelope-open' : 'fas fa-envelope'"></i>
                                </button>
                                <button @click.stop="deleteNotification(notification)" class="btn-icon delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function vendorNotifications() {
    return {
        // State
        loading: false,
        isMobile: false,
        currentFilter: 'all',
        pullToRefreshActive: false,
        notifications: [],
        
        // Initialize
        init() {
            console.log('Vendor Notifications initialized');
            this.detectMobile();
            this.initMobileLayout();
            this.loading = false;
            this.fetchNotifications();
            this.initMobileInteractions();
        },
        
        detectMobile() {
            this.isMobile = window.innerWidth <= 768;
            window.addEventListener('resize', () => {
                this.isMobile = window.innerWidth <= 768;
                this.initMobileLayout();
            });
        },
        
        initMobileLayout() {
            const mobileContainer = document.querySelector('.mobile-notifications-container');
            const desktopContainer = document.querySelector('.desktop-notifications-container');
            
            if (this.isMobile) {
                if (mobileContainer) mobileContainer.style.display = 'block';
                if (desktopContainer) desktopContainer.style.display = 'none';
            } else {
                if (mobileContainer) mobileContainer.style.display = 'none';
                if (desktopContainer) desktopContainer.style.display = 'block';
            }
        },
        
        initMobileInteractions() {
            if (!this.isMobile) return;
            
            // Add pull-to-refresh functionality
            let startY = 0;
            let currentY = 0;
            let isPulling = false;
            
            const notificationsContainer = document.querySelector('.mobile-notifications-list');
            if (notificationsContainer) {
                notificationsContainer.addEventListener('touchstart', (e) => {
                    startY = e.touches[0].clientY;
                    isPulling = notificationsContainer.scrollTop === 0;
                }, { passive: true });
                
                notificationsContainer.addEventListener('touchmove', (e) => {
                    if (!isPulling) return;
                    currentY = e.touches[0].clientY;
                    const pullDistance = currentY - startY;
                    
                    if (pullDistance > 0 && pullDistance < 100) {
                        this.pullToRefreshActive = true;
                    }
                }, { passive: true });
                
                notificationsContainer.addEventListener('touchend', () => {
                    if (this.pullToRefreshActive) {
                        this.refreshNotifications();
                    }
                    isPulling = false;
                    this.pullToRefreshActive = false;
                }, { passive: true });
            }
        },
        
        // Computed properties
        get filteredNotifications() {
            return this.notifications.filter(notification => {
                switch (this.currentFilter) {
                    case 'unread':
                        return !notification.read;
                    case 'orders':
                        return notification.type === 'order';
                    case 'reviews':
                        return notification.type === 'review';
                    case 'payments':
                        return notification.type === 'payment';
                    case 'alerts':
                        return notification.type === 'alert';
                    default:
                        return true;
                }
            });
        },
        
        get hasUnreadNotifications() {
            return this.notifications.filter(n => !n.read).length > 0;
        },
        
        // Methods
        setFilter(filter) {
            this.currentFilter = filter;
        },
        
        fetchNotifications() {
            console.log('Fetching notifications...');
            this.loading = true;
            
            setTimeout(() => {
                // Mock notification data
                this.notifications = [
                    {
                        id: 1,
                        type: 'order',
                        title: 'New Order Received',
                        message: 'Order #1234 placed by Rahul Sharma for ₹2,499',
                        time: new Date(Date.now() - 2 * 60 * 1000),
                        read: false,
                        urgent: true
                    },
                    {
                        id: 2,
                        type: 'review',
                        title: 'New Review Posted',
                        message: '5-star review on "Premium Cotton T-Shirt" by Priya Singh',
                        time: new Date(Date.now() - 5 * 60 * 1000),
                        read: false,
                        urgent: false
                    },
                    {
                        id: 3,
                        type: 'alert',
                        title: 'Low Stock Alert',
                        message: 'Product "Denim Jeans" has only 3 items left in stock',
                        time: new Date(Date.now() - 1 * 60 * 60 * 1000),
                        read: false,
                        urgent: true
                    },
                    {
                        id: 4,
                        type: 'payment',
                        title: 'Payment Received',
                        message: '₹2,500 payment received for order #1233',
                        time: new Date(Date.now() - 3 * 60 * 60 * 1000),
                        read: true,
                        urgent: false
                    },
                    {
                        id: 5,
                        type: 'order',
                        title: 'Order Status Updated',
                        message: 'Order #1232 has been shipped to customer',
                        time: new Date(Date.now() - 5 * 60 * 60 * 1000),
                        read: true,
                        urgent: false
                    },
                    {
                        id: 6,
                        type: 'review',
                        title: 'Customer Feedback',
                        message: 'New customer registered: Sarah Johnson',
                        time: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                        read: true,
                        urgent: false
                    }
                ];
                this.loading = false;
            }, 1000);
        },
        
        refreshNotifications() {
            this.loading = true;
            setTimeout(() => {
                this.fetchNotifications();
                if (window.showToast) {
                    window.showToast('Notifications refreshed successfully', 'success');
                }
            }, 1000);
        },
        
        openNotification(notification) {
            // Mark as read when opened
            if (!notification.read) {
                notification.read = true;
            }
            
            // Handle different notification types
            switch (notification.type) {
                case 'order':
                    // Navigate to orders page
                    window.location.href = '{{ route("vendor.orders") }}';
                    break;
                case 'review':
                    // Navigate to reviews
                    alert('Reviews feature coming soon!');
                    break;
                case 'payment':
                    // Navigate to payments
                    alert('Payments feature coming soon!');
                    break;
                case 'alert':
                    // Navigate to products
                    alert('Products feature coming soon!');
                    break;
                default:
                    console.log('Opening notification:', notification);
            }
        },
        
        toggleRead(notification) {
            notification.read = !notification.read;
            const action = notification.read ? 'marked as read' : 'marked as unread';
            if (window.showToast) {
                window.showToast(`Notification ${action}`, 'success');
            }
        },
        
        deleteNotification(notification) {
            if (confirm('Are you sure you want to delete this notification?')) {
                const index = this.notifications.findIndex(n => n.id === notification.id);
                if (index > -1) {
                    this.notifications.splice(index, 1);
                    if (window.showToast) {
                        window.showToast('Notification deleted', 'success');
                    }
                }
            }
        },
        
        markAllAsRead() {
            this.notifications.forEach(notification => {
                notification.read = true;
            });
            if (window.showToast) {
                window.showToast('All notifications marked as read', 'success');
            }
        },
        
        toggleNotificationSettings() {
            alert('Notification settings feature coming soon!');
        },
        
        // Helper methods
        getIconClass(type) {
            const classes = {
                'order': 'order-icon',
                'review': 'review-icon',
                'payment': 'payment-icon',
                'alert': 'alert-icon'
            };
            return classes[type] || 'default-icon';
        },
        
        getIconName(type) {
            const icons = {
                'order': 'fas fa-shopping-bag',
                'review': 'fas fa-star',
                'payment': 'fas fa-rupee-sign',
                'alert': 'fas fa-exclamation-triangle'
            };
            return icons[type] || 'fas fa-bell';
        },
        
        formatTime(time) {
            const now = new Date();
            const notificationTime = new Date(time);
            const diffInMinutes = Math.floor((now - notificationTime) / (1000 * 60));
            
            if (diffInMinutes < 1) {
                return 'Just now';
            } else if (diffInMinutes < 60) {
                return `${diffInMinutes}m ago`;
            } else if (diffInMinutes < 1440) {
                return `${Math.floor(diffInMinutes / 60)}h ago`;
            } else {
                return `${Math.floor(diffInMinutes / 1440)}d ago`;
            }
        }
    };
}
</script>
@endsection
