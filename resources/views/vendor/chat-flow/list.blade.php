@extends('layouts.vendor')

@section('content')
<div x-data="chatFlowList()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Welcome Section -->
    <div class="bg-green-600 text-white p-8 rounded-lg shadow-lg mb-8">
        <h1 class="text-4xl font-bold mb-2">WhatsApp Chat Flows</h1>
        <p class="text-lg">Create and manage automated chat flows for your WhatsApp store.</p>
    </div>

    <!-- Chat Flows Section -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-bold text-gray-800">Your Chat Flows</h2>
            <div class="flex items-center gap-4">
                <div class="relative">
                    <input type="text" x-model="searchQuery" placeholder="Search chat flows..." class="pl-10 pr-4 py-2 border rounded-md">
                    <i class="fas fa-search absolute left-3 top-1/2 -translate-y-1/2 text-gray-400"></i>
                </div>
                <a href="{{ route('vendor.chat-flow.builder') }}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                    <i class="fas fa-plus mr-2"></i> Create New Flow
                </a>
            </div>
        </div>

        <!-- Chat Flow List -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="flow in filteredChatFlows" :key="flow.id">
                <div class="bg-gray-50 p-4 rounded-lg shadow-md flex flex-col justify-between">
                    <div>
                        <h3 class="font-bold text-lg" x-text="flow.name"></h3>
                        <p class="text-gray-600 text-sm" x-text="flow.description"></p>
                        <p class="text-xs text-gray-500 mt-2">Last updated on <span x-text="formatDate(flow.updatedAt)"></span></p>
                    </div>
                    <div class="flex justify-end gap-2 mt-4">
                        <a :href="'/vendor/chat-flow/builder/' + flow.id" class="text-blue-600 hover:text-blue-900"><i class="fas fa-edit"></i></a>
                        <button @click="confirmDeleteFlow(flow)" class="text-red-600 hover:text-red-900"><i class="fas fa-trash"></i></button>
                    </div>
                </div>
            </template>
        </div>

        <template x-if="filteredChatFlows.length === 0">
            <div class="text-center py-12">
                <i class="fas fa-comments text-5xl text-gray-300"></i>
                <h3 class="mt-4 text-lg font-medium">No chat flows found</h3>
                <p class="text-gray-500">Get started by creating a new chat flow.</p>
            </div>
        </template>
    </div>

    <!-- Quick Guide Section -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Quick Guide</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-lg">What are Chat Flows?</h3>
                <p class="text-gray-600">Automated conversation paths to guide customers on WhatsApp.</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-lg">How to Create a Flow</h3>
                <p class="text-gray-600">Use our drag-and-drop builder to design conversation nodes.</p>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-lg">Best Practices</h3>
                <p class="text-gray-600">Keep flows simple, use clear language, and test thoroughly.</p>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div x-show="deleteModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Delete Chat Flow</h3>
            <p class="text-sm text-gray-500 mb-6">Are you sure you want to delete the chat flow "<span x-text="flowToDelete ? flowToDelete.name : ''"></span>"? This action cannot be undone.</p>
            <div class="flex justify-end gap-3">
                <button @click="deleteModalOpen = false" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">Cancel</button>
                <button @click="handleDeleteFlow" class="px-4 py-2 text-sm font-medium text-white bg-red-500 rounded-md hover:bg-red-600">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
function chatFlowList() {
    return {
        chatFlows: [],
        searchQuery: '',
        deleteModalOpen: false,
        flowToDelete: null,

        init() {
            this.chatFlows = [
                { id: 1, name: 'Welcome Flow', description: 'Greets new customers and provides main menu options.', updatedAt: '2023-10-26T10:00:00Z' },
                { id: 2, name: 'Order Status', description: 'Allows customers to check their order status.', updatedAt: '2023-10-25T14:30:00Z' },
                { id: 3, name: 'FAQ Bot', description: 'Answers frequently asked questions automatically.', updatedAt: '2023-10-24T09:00:00Z' },
            ];
        },

        get filteredChatFlows() {
            if (!this.searchQuery) {
                return this.chatFlows;
            }
            return this.chatFlows.filter(flow =>
                flow.name.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                flow.description.toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        },

        confirmDeleteFlow(flow) {
            this.flowToDelete = flow;
            this.deleteModalOpen = true;
        },

        handleDeleteFlow() {
            if (this.flowToDelete) {
                this.chatFlows = this.chatFlows.filter(flow => flow.id !== this.flowToDelete.id);
                this.deleteModalOpen = false;
                this.flowToDelete = null;
            }
        },

        formatDate(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
        }
    }
}
</script>
@endsection

