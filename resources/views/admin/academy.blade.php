@extends('layouts.admin')

@section('page-title', 'Academy Content')



@section('content')
<div x-data="{
    activeCategory: 'all',
    tutorials: {{ json_encode($tutorials) }},
    categories: {{ json_encode($categories) }},
    isAddEditModalOpen: false,
    isAdding: false,
    editingTutorial: null,
    previewModalOpen: false,
    previewingTutorial: null,
    formData: {
        title: '',
        description: '',
        category: '',
        videoUrl: '',
        thumbnail: '',
        duration: '',
        featured: false
    },
    openAddModal() {
        this.isAdding = true;
        this.editingTutorial = null;
        this.formData = { title: '', description: '', category: '', videoUrl: '', thumbnail: '', duration: '', featured: false };
        this.isAddEditModalOpen = true;
    },
    openEditModal(tutorial) {
        this.isAdding = false;
        this.editingTutorial = tutorial;
        this.formData = { ...tutorial };
        this.isAddEditModalOpen = true;
    },
    openPreviewModal(tutorial) {
        this.previewingTutorial = tutorial;
        this.previewModalOpen = true;
    }
}">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold text-gray-700">Academy</h2>
        <button @click="openAddModal" class="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
            Add New Tutorial
        </button>
    </div>

    <!-- Category Filters -->
    <div class="mb-6">
        <div class="flex space-x-4 border-b">
            <button @click="activeCategory = 'all'" :class="{ 'border-green-500 text-green-600': activeCategory === 'all', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeCategory !== 'all' }" class="px-3 py-2 font-medium text-sm leading-5 border-b-2">
                All
            </button>
            @foreach ($categories as $category)
                <button @click="activeCategory = '{{ $category['id'] }}'" :class="{ 'border-green-500 text-green-600': activeCategory === '{{ $category['id'] }}', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeCategory !== '{{ $category['id'] }}' }" class="px-3 py-2 font-medium text-sm leading-5 border-b-2">
                    {{ $category['name'] }}
                </button>
            @endforeach
        </div>
    </div>

    <!-- Tutorials Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <template x-for="tutorial in tutorials.filter(t => activeCategory === 'all' || t.category === activeCategory)" :key="tutorial.id">
            <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <div class="relative">
                    <img :src="tutorial.thumbnail" alt="Thumbnail" class="w-full h-48 object-cover">
                    <div class="absolute inset-0 bg-black bg-opacity-25 flex items-center justify-center">
                        <button @click="openPreviewModal(tutorial)" class="bg-white text-gray-800 rounded-full p-3">
                            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.348a1.125 1.125 0 010 1.972l-11.54 6.347a1.125 1.125 0 01-1.667-.986V5.653z" /></svg>
                        </button>
                    </div>
                    <span class="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded" x-text="tutorial.duration"></span>
                </div>
                <div class="p-4">
                    <h3 class="text-lg font-semibold text-gray-800" x-text="tutorial.title"></h3>
                    <p class="text-gray-600 mt-2 text-sm" x-text="tutorial.description"></p>
                    <div class="mt-4 flex justify-between items-center">
                        <span class="text-xs font-medium text-gray-500" x-text="categories.find(c => c.id === tutorial.category)?.name || tutorial.category"></span>
                        <div class="flex space-x-2">
                            <button @click="openEditModal(tutorial)" class="text-gray-400 hover:text-blue-500">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L6.832 19.82a4.5 4.5 0 01-1.897 1.13l-2.685.8.8-2.685a4.5 4.5 0 011.13-1.897L16.863 4.487zm0 0L19.5 7.125" /></svg>
                            </button>
                            <button class="text-gray-400 hover:text-red-500">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.134-2.09-2.134H8.09c-1.18 0-2.09.954-2.09 2.134v.916m7.5 0a48.667 48.667 0 00-7.5 0" /></svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Add/Edit Tutorial Modal -->
    <div x-show="isAddEditModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg max-w-2xl w-full p-6 m-4" @click.away="isAddEditModalOpen = false">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" x-text="isAdding ? 'Add New Tutorial' : 'Edit Tutorial'"></h3>
                <button @click="isAddEditModalOpen = false" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
            </div>
            <div>
                <div class="grid grid-cols-1 gap-6">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                        <input type="text" x-model="formData.title" id="title" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                        <textarea x-model="formData.description" id="description" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                        <select x-model="formData.category" id="category" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            <option value="">Select a category</option>
                            @foreach ($categories as $category)
                                <option value="{{ $category['id'] }}">{{ $category['name'] }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <label for="videoUrl" class="block text-sm font-medium text-gray-700">Video URL</label>
                        <input type="text" x-model="formData.videoUrl" id="videoUrl" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="duration" class="block text-sm font-medium text-gray-700">Duration (e.g., 5:20)</label>
                        <input type="text" x-model="formData.duration" id="duration" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Thumbnail</label>
                        <div class="mt-1 flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 pt-5 pb-6">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true"><path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
                                <div class="flex text-sm text-gray-600">
                                    <label for="file-upload" class="relative cursor-pointer rounded-md bg-white font-medium text-indigo-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2 hover:text-indigo-500">
                                        <span>Upload a file</span>
                                        <input id="file-upload" name="file-upload" type="file" class="sr-only">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" x-model="formData.featured" id="featured" class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        <label for="featured" class="ml-2 block text-sm text-gray-900">Featured Tutorial</label>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button @click="isAddEditModalOpen = false" class="rounded-md border border-gray-300 bg-white py-2 px-4 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">Cancel</button>
                    <button class="rounded-md border border-transparent bg-green-600 py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-green-700" x-text="isAdding ? 'Add Tutorial' : 'Save Changes'"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Preview Modal -->
    <div x-show="previewModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" x-cloak>
        <div class="bg-white rounded-lg max-w-3xl w-full p-6 m-4" @click.away="previewModalOpen = false">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900" x-text="previewingTutorial?.title"></h3>
                <button @click="previewModalOpen = false" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
            </div>
            <div class="aspect-w-16 aspect-h-9">
                <iframe :src="previewingTutorial?.videoUrl" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
            <p class="mt-4 text-gray-600" x-text="previewingTutorial?.description"></p>
        </div>
    </div>
</div>
@endsection
