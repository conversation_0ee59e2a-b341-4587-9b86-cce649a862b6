@extends('layouts.admin')

@section('content')
<div x-data="adminSettings()" class="p-6">
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800">System Configuration</h1>
        <p class="text-gray-600">Configure platform settings, email notifications, payment options, and security preferences.</p>
    </div>

    <!-- Settings Tabs -->
    <div class="flex border-b border-gray-200 mb-6">
        <button @click="activeTab = 'general'" :class="{ 'border-green-500 text-green-600': activeTab === 'general', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'general' }" class="flex items-center px-4 py-2 border-b-2 font-medium text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
            General
        </button>
        <button @click="activeTab = 'email'" :class="{ 'border-green-500 text-green-600': activeTab === 'email', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'email' }" class="flex items-center px-4 py-2 border-b-2 font-medium text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
            Email
        </button>
        <button @click="activeTab = 'notifications'" :class="{ 'border-green-500 text-green-600': activeTab === 'notifications', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'notifications' }" class="flex items-center px-4 py-2 border-b-2 font-medium text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" /></svg>
            Notifications
        </button>
        <button @click="activeTab = 'payment'" :class="{ 'border-green-500 text-green-600': activeTab === 'payment', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'payment' }" class="flex items-center px-4 py-2 border-b-2 font-medium text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" /></svg>
            Payment
        </button>
        <button @click="activeTab = 'security'" :class="{ 'border-green-500 text-green-600': activeTab === 'security', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'security' }" class="flex items-center px-4 py-2 border-b-2 font-medium text-sm">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
            Security
        </button>
    </div>

    <!-- Tab Content -->
    <div class="bg-white p-6 rounded-lg shadow">
        <!-- General Settings -->
        <div x-show="activeTab === 'general'">
            <h2 class="text-xl font-semibold mb-4">General Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input type="text" name="siteName" x-model="generalSettings.siteName" class="form-input" placeholder="Site Name">
                <input type="text" name="contactEmail" x-model="generalSettings.contactEmail" class="form-input" placeholder="Contact Email">
                <textarea name="siteDescription" x-model="generalSettings.siteDescription" class="form-textarea md:col-span-2" placeholder="Site Description" rows="3"></textarea>
                <input type="text" name="contactPhone" x-model="generalSettings.contactPhone" class="form-input" placeholder="Contact Phone">
                <input type="text" name="address" x-model="generalSettings.address" class="form-input" placeholder="Address">
            </div>
            <div class="mt-6 text-right">
                <button @click="handleSaveSettings('general')" class="bg-green-600 text-white px-4 py-2 rounded-md">Save General Settings</button>
            </div>
        </div>

        <!-- Email Settings -->
        <div x-show="activeTab === 'email'">
            <h2 class="text-xl font-semibold mb-4">Email Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input type="text" name="smtpServer" x-model="emailSettings.smtpServer" class="form-input" placeholder="SMTP Server">
                <input type="text" name="smtpPort" x-model="emailSettings.smtpPort" class="form-input" placeholder="SMTP Port">
                <input type="text" name="smtpUsername" x-model="emailSettings.smtpUsername" class="form-input" placeholder="SMTP Username">
                <input type="password" name="smtpPassword" x-model="emailSettings.smtpPassword" class="form-input" placeholder="SMTP Password">
                <input type="text" name="senderName" x-model="emailSettings.senderName" class="form-input" placeholder="Sender Name">
                <input type="email" name="senderEmail" x-model="emailSettings.senderEmail" class="form-input" placeholder="Sender Email">
                <div class="flex items-center md:col-span-2">
                    <input type="checkbox" id="enableEmailNotifications" name="enableEmailNotifications" x-model="emailSettings.enableEmailNotifications" class="form-checkbox">
                    <label for="enableEmailNotifications" class="ml-2">Enable Email Notifications</label>
                </div>
            </div>
            <div class="mt-6 text-right">
                <button @click="handleSaveSettings('email')" class="bg-green-600 text-white px-4 py-2 rounded-md">Save Email Settings</button>
            </div>
        </div>

        <!-- Notification Settings -->
        <div x-show="activeTab === 'notifications'">
            <h2 class="text-xl font-semibold mb-4">Notification Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <template x-for="(value, key) in notificationSettings" :key="key">
                    <label class="flex items-center bg-gray-50 p-3 rounded-md">
                        <input type="checkbox" :name="key" x-model="notificationSettings[key]" class="form-checkbox">
                        <span class="ml-3 text-sm" x-text="key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()) + ' Notifications'"></span>
                    </label>
                </template>
            </div>
            <div class="mt-6 text-right">
                <button @click="handleSaveSettings('notification')" class="bg-green-600 text-white px-4 py-2 rounded-md">Save Notification Settings</button>
            </div>
        </div>

        <!-- Payment Settings -->
        <div x-show="activeTab === 'payment'">
            <h2 class="text-xl font-semibold mb-4">Payment Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input type="text" name="currency" x-model="paymentSettings.currency" class="form-input" placeholder="Currency (e.g., INR)">
                <input type="text" name="currencySymbol" x-model="paymentSettings.currencySymbol" class="form-input" placeholder="Currency Symbol (e.g., ₹)">
                <input type="number" name="taxRate" x-model.number="paymentSettings.taxRate" class="form-input" placeholder="Tax Rate (%)">
                <input type="number" name="minimumWithdrawalAmount" x-model.number="paymentSettings.minimumWithdrawalAmount" class="form-input" placeholder="Minimum Withdrawal Amount">
                <input type="number" name="withdrawalProcessingDays" x-model.number="paymentSettings.withdrawalProcessingDays" class="form-input" placeholder="Withdrawal Processing Days">
                <div class="flex items-center">
                    <input type="checkbox" id="enableTax" name="enableTax" x-model="paymentSettings.enableTax" class="form-checkbox">
                    <label for="enableTax" class="ml-2">Enable Tax</label>
                </div>
            </div>
            <div class="mt-6 text-right">
                <button @click="handleSaveSettings('payment')" class="bg-green-600 text-white px-4 py-2 rounded-md">Save Payment Settings</button>
            </div>
        </div>

        <!-- Security Settings -->
        <div x-show="activeTab === 'security'">
            <h2 class="text-xl font-semibold mb-4">Security Settings</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <input type="number" name="passwordExpiryDays" x-model.number="securitySettings.passwordExpiryDays" class="form-input" placeholder="Password Expiry (Days)">
                <input type="number" name="maxLoginAttempts" x-model.number="securitySettings.maxLoginAttempts" class="form-input" placeholder="Max Login Attempts">
                <input type="number" name="sessionTimeoutMinutes" x-model.number="securitySettings.sessionTimeoutMinutes" class="form-input" placeholder="Session Timeout (Minutes)">
                <div class="flex items-center">
                    <input type="checkbox" id="enableTwoFactorAuth" name="enableTwoFactorAuth" x-model="securitySettings.enableTwoFactorAuth" class="form-checkbox">
                    <label for="enableTwoFactorAuth" class="ml-2">Enable Two-Factor Authentication</label>
                </div>
                <div class="flex items-center">
                    <input type="checkbox" id="requireStrongPasswords" name="requireStrongPasswords" x-model="securitySettings.requireStrongPasswords" class="form-checkbox">
                    <label for="requireStrongPasswords" class="ml-2">Require Strong Passwords</label>
                </div>
            </div>
            <div class="mt-6 text-right">
                <button @click="handleSaveSettings('security')" class="bg-green-600 text-white px-4 py-2 rounded-md">Save Security Settings</button>
            </div>
        </div>
    </div>
</div>

<script>
function adminSettings() {
    return {
        activeTab: 'general',
        generalSettings: {
            siteName: 'WhaMart',
            siteDescription: 'WhatsApp Store Platform for Small Businesses',
            logoUrl: '/WhaMart_Logo.png',
            contactEmail: '<EMAIL>',
            contactPhone: '+91 9876543210',
            address: 'Mumbai, Maharashtra, India'
        },
        emailSettings: {
            smtpServer: 'smtp.example.com',
            smtpPort: '587',
            smtpUsername: '<EMAIL>',
            smtpPassword: '********',
            senderName: 'WhaMart Support',
            senderEmail: '<EMAIL>',
            enableEmailNotifications: true
        },
        notificationSettings: {
            newVendorRegistration: true,
            newOrder: true,
            subscriptionPurchase: true,
            subscriptionExpiry: true,
            lowBalance: false,
            systemUpdates: true
        },
        paymentSettings: {
            currency: 'INR',
            currencySymbol: '₹',
            taxRate: 18,
            enableTax: true,
            minimumWithdrawalAmount: 1000,
            withdrawalProcessingDays: 3
        },
        securitySettings: {
            enableTwoFactorAuth: false,
            passwordExpiryDays: 90,
            maxLoginAttempts: 5,
            sessionTimeoutMinutes: 60,
            requireStrongPasswords: true
        },
        handleSaveSettings(settingType) {
            console.log(`Saving ${settingType} settings...`);
            // In a real app, you would send this data to the server.
            alert(`${settingType.charAt(0).toUpperCase() + settingType.slice(1)} settings saved successfully!`);
        }
    };
}
</script>
@endsection

