@extends('layouts.admin')

@section('content')
<div x-data="adminAnalytics()" x-init="fetchAnalyticsData()" class="p-6">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Admin Analytics</h1>
        <div class="flex items-center space-x-2">
            <select x-model="timeRange" @change="fetchAnalyticsData()" class="form-select rounded-md shadow-sm border-gray-300 focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50">
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
            </select>
            <button @click="fetchAnalyticsData()" class="p-2 rounded-md hover:bg-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h5M20 20v-5h-5" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4l16 16"/>
                </svg>
            </button>
        </div>
    </div>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64">
            <div class="loader ease-linear rounded-full border-8 border-t-8 border-gray-200 h-32 w-32"></div>
        </div>
    </template>

    <template x-if="!loading && analyticsData">
        <div>
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <template x-for="card in getStatsCards()" :key="card.name">
                    <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-500" x-text="card.name"></p>
                            <p class="text-2xl font-bold text-gray-800" x-text="card.value"></p>
                            <div class="flex items-center text-sm mt-1" :class="card.trend === 'up' ? 'text-green-500' : 'text-red-500'">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path x-show="card.trend === 'up'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                    <path x-show="card.trend === 'down'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                                </svg>
                                <span x-text="card.change + '%'"></span>
                            </div>
                        </div>
                        <div class="p-3 rounded-full" :style="{ backgroundColor: card.color }">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" :style="{ color: card.iconColor }" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-html="card.icon"></svg>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Charts -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                <!-- Vendor Growth -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Vendor Growth</h3>
                    <div class="h-64 flex items-end space-x-2">
                        <template x-for="item in analyticsData.vendorGrowth" :key="item.month">
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-green-200 rounded-t-md" :style="{ height: (item.count / 150 * 100) + '%' }"></div>
                                <span class="text-xs text-gray-500 mt-1" x-text="item.month"></span>
                            </div>
                        </template>
                    </div>
                </div>
                <!-- Revenue by Month -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Revenue by Month</h3>
                    <div class="h-64 flex items-end space-x-2">
                        <template x-for="item in analyticsData.revenueByMonth" :key="item.month">
                            <div class="flex-1 flex flex-col items-center">
                                <div class="w-full bg-blue-200 rounded-t-md" :style="{ height: (item.revenue / 200000 * 100) + '%' }"></div>
                                <span class="text-xs text-gray-500 mt-1" x-text="item.month"></span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>

            <!-- Top Vendors & User Acquisition -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Top Vendors -->
                <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Top Performing Vendors</h3>
                    <table class="w-full text-left">
                        <thead>
                            <tr class="border-b">
                                <th class="py-2">Vendor</th>
                                <th class="py-2">Revenue</th>
                                <th class="py-2">Orders</th>
                            </tr>
                        </thead>
                        <tbody>
                            <template x-for="vendor in analyticsData.topVendors" :key="vendor.id">
                                <tr class="border-b border-gray-100">
                                    <td class="py-3" x-text="vendor.name"></td>
                                    <td class="py-3" x-text="formatCurrency(vendor.revenue)"></td>
                                    <td class="py-3" x-text="vendor.orders"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <!-- User Acquisition -->
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">User Acquisition</h3>
                    <div class="flex flex-col md:flex-row items-center">
                        <div class="w-full md:w-1/2 h-48">
                            <svg viewBox="0 0 100 100" class="w-full h-full">
                                <circle cx="50" cy="50" r="50" fill="#128C7E"/>
                                <path d="M 50 50 L 50 0 A 50 50 0 0 1 97.55 34.55 Z" fill="#25D366" />
                                <path d="M 50 50 L 97.55 34.55 A 50 50 0 0 1 82.14 91.35 Z" fill="#075E54" />
                            </svg>
                        </div>
                        <div class="w-full md:w-1/2 mt-6 md:mt-0 md:pl-6">
                            <div class="space-y-4">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 mr-2" style="background-color: #25D366"></div>
                                    <span class="text-sm">Organic (<span x-text="analyticsData.userAcquisition.organic + '%'"></span>)</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 mr-2" style="background-color: #075E54"></div>
                                    <span class="text-sm">Referral (<span x-text="analyticsData.userAcquisition.referral + '%'"></span>)</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-4 h-4 mr-2" style="background-color: #128C7E"></div>
                                    <span class="text-sm">Social (<span x-text="analyticsData.userAcquisition.social + '%'"></span>)</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
    function adminAnalytics() {
        return {
            loading: false,
            timeRange: 'month',
            analyticsData: null,

            fetchAnalyticsData() {
                this.loading = true;
                // Mock API call
                setTimeout(() => {
                    this.analyticsData = {
                        overview: {
                            totalRevenue: { value: 1245600, change: 12.5, trend: 'up' },
                            totalVendors: { value: 120, change: 8.3, trend: 'up' },
                            totalUsers: { value: 580, change: 15.2, trend: 'up' },
                            totalOrders: { value: 3245, change: -2.1, trend: 'down' }
                        },
                        vendorGrowth: [
                            { month: 'Jan', count: 45 }, { month: 'Feb', count: 52 }, { month: 'Mar', count: 61 },
                            { month: 'Apr', count: 67 }, { month: 'May', count: 75 }, { month: 'Jun', count: 84 },
                            { month: 'Jul', count: 91 }, { month: 'Aug', count: 98 }, { month: 'Sep', count: 105 },
                            { month: 'Oct', count: 112 }, { month: 'Nov', count: 118 }, { month: 'Dec', count: 120 }
                        ],
                        revenueByMonth: [
                            { month: 'Jan', revenue: 78500 }, { month: 'Feb', revenue: 92400 }, { month: 'Mar', revenue: 105600 },
                            { month: 'Apr', revenue: 87300 }, { month: 'May', revenue: 91200 }, { month: 'Jun', revenue: 102500 },
                            { month: 'Jul', revenue: 108700 }, { month: 'Aug', revenue: 115400 }, { month: 'Sep', revenue: 124600 },
                            { month: 'Oct', revenue: 132800 }, { month: 'Nov', revenue: 128900 }, { month: 'Dec', revenue: 145600 }
                        ],
                        topVendors: [
                            { id: 1, name: 'Rahul Electronics', revenue: 245600, orders: 128 },
                            { id: 2, name: 'Fashion Hub', revenue: 189200, orders: 256 },
                            { id: 3, name: 'Tech Solutions', revenue: 178500, orders: 95 },
                            { id: 4, name: 'Beauty Essentials', revenue: 152800, orders: 142 },
                            { id: 5, name: 'Spice Garden', revenue: 132400, orders: 87 }
                        ],
                        userAcquisition: { organic: 65, referral: 20, social: 15 }
                    };
                    this.loading = false;
                }, 500);
            },

            formatCurrency(amount) {
                return new Intl.NumberFormat('en-IN', {
                    style: 'currency',
                    currency: 'INR',
                    maximumFractionDigits: 0
                }).format(amount);
            },

            getStatsCards() {
                if (!this.analyticsData) return [];
                const overview = this.analyticsData.overview;
                return [
                    {
                        name: 'Total Revenue',
                        value: this.formatCurrency(overview.totalRevenue.value),
                        change: overview.totalRevenue.change,
                        trend: overview.totalRevenue.trend,
                        icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v.01" />`,
                        color: 'rgba(37, 211, 102, 0.1)',
                        iconColor: '#25D366'
                    },
                    {
                        name: 'Total Vendors',
                        value: overview.totalVendors.value,
                        change: overview.totalVendors.change,
                        trend: overview.totalVendors.trend,
                        icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />`,
                        color: 'rgba(59, 130, 246, 0.1)',
                        iconColor: '#3B82F6'
                    },
                    {
                        name: 'Total Users',
                        value: overview.totalUsers.value,
                        change: overview.totalUsers.change,
                        trend: overview.totalUsers.trend,
                        icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />`,
                        color: 'rgba(239, 68, 68, 0.1)',
                        iconColor: '#EF4444'
                    },
                    {
                        name: 'Total Orders',
                        value: overview.totalOrders.value,
                        change: overview.totalOrders.change,
                        trend: overview.totalOrders.trend,
                        icon: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />`,
                        color: 'rgba(245, 158, 11, 0.1)',
                        iconColor: '#F59E0B'
                    }
                ];
            }
        };
    }
</script>
@endsection

