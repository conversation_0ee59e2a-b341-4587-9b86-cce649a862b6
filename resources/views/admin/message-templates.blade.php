@extends('layouts.admin')

@section('content')
<div x-data="adminMessageTemplates()" x-init="fetchTemplatesAndCategories()" class="p-6">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Message Templates</h1>

    <div class="flex space-x-8">
        <!-- Categories Sidebar -->
        <aside class="w-1/4">
            <div class="bg-white p-4 rounded-lg shadow">
                <h3 class="font-semibold text-gray-800 mb-3">Categories</h3>
                <ul class="space-y-2">
                    <li>
                        <button @click="activeCategory = 'all'" :class="{ 'bg-indigo-100 text-indigo-700': activeCategory === 'all' }" class="w-full text-left px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100">All Templates</button>
                    </li>
                    <template x-for="category in categories" :key="category.id">
                        <li>
                            <button @click="activeCategory = category.id" :class="{ 'bg-indigo-100 text-indigo-700': activeCategory === category.id }" class="w-full text-left px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100" x-text="category.name"></button>
                        </li>
                    </template>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="w-3/4">
            <template x-if="!editingTemplate && !isAddingTemplate">
                <div>
                    <div class="flex justify-end mb-4">
                        <button @click="handleAddTemplate()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" /></svg>
                            Add New Template
                        </button>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <template x-for="template in filteredTemplates" :key="template.id">
                            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                                <div class="p-5">
                                    <h4 class="font-bold text-lg text-gray-800" x-text="template.name"></h4>
                                    <p class="text-sm text-gray-600 mt-1" x-text="template.description"></p>
                                    <p class="text-xs text-gray-500 mt-2 bg-gray-100 inline-block px-2 py-1 rounded" x-text="getCategoryName(template.category)"></p>
                                </div>
                                <div class="px-5 py-3 bg-gray-50 flex justify-end space-x-3">
                                    <button @click="handlePreviewTemplate(template)" class="text-sm text-blue-600 hover:text-blue-800">Preview</button>
                                    <button @click="handleEditTemplate(template)" class="text-sm text-indigo-600 hover:text-indigo-800">Edit</button>
                                    <button @click="handleDeleteTemplate(template.id)" class="text-sm text-red-600 hover:text-red-800">Delete</button>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </template>

            <!-- Add/Edit Form -->
            <template x-if="editingTemplate || isAddingTemplate">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6" x-text="isAddingTemplate ? 'Add New Template' : 'Edit Template'"></h2>
                    <div class="space-y-4">
                        <input type="text" placeholder="Template Name" x-model="formData.name" class="w-full form-input">
                        <textarea placeholder="Description" x-model="formData.description" class="w-full form-textarea" rows="2"></textarea>
                        <select x-model="formData.category" class="w-full form-select">
                            <option value="">Select Category</option>
                            <template x-for="category in categories" :key="category.id">
                                <option :value="category.id" x-text="category.name"></option>
                            </template>
                        </select>
                        <input type="text" placeholder="Header (optional)" x-model="formData.header" class="w-full form-input">
                        <textarea placeholder="Content" x-model="formData.content" class="w-full form-textarea" rows="4"></textarea>
                        <input type="text" placeholder="Footer (optional)" x-model="formData.footer" class="w-full form-input">
                        
                        <!-- Button Management -->
                        <div>
                            <h4 class="font-semibold mb-2">Buttons</h4>
                            <div class="space-y-2 mb-4">
                                <template x-for="(button, index) in formData.buttons" :key="index">
                                    <div class="flex items-center justify-between bg-gray-50 p-2 rounded">
                                        <span class="font-medium text-sm" x-text="button.text"></span>
                                        <button @click="handleRemoveButton(index)" class="text-red-500 hover:text-red-700">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd" /></svg>
                                        </button>
                                    </div>
                                </template>
                            </div>
                            <div class="bg-gray-100 p-4 rounded-lg space-y-3">
                                <select x-model="newButton.type" class="w-full form-select">
                                    <option value="quick_reply">Quick Reply</option>
                                    <option value="url">URL</option>
                                    <option value="call">Call</option>
                                </select>
                                <input type="text" placeholder="Button Text" x-model="newButton.text" class="w-full form-input">
                                <input x-show="newButton.type === 'quick_reply'" type="text" placeholder="Value" x-model="newButton.value" class="w-full form-input">
                                <input x-show="newButton.type === 'url'" type="text" placeholder="URL" x-model="newButton.url" class="w-full form-input">
                                <input x-show="newButton.type === 'call'" type="text" placeholder="Phone Number" x-model="newButton.phoneNumber" class="w-full form-input">
                                <button @click="handleAddButton" class="bg-blue-500 text-white px-3 py-1 rounded-md text-sm">Add Button</button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button @click="handleCancelEdit" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-md">Cancel</button>
                        <button @click="handleSaveTemplate" class="bg-green-600 text-white px-4 py-2 rounded-md" x-text="isAddingTemplate ? 'Add Template' : 'Save Changes'"></button>
                    </div>
                </div>
            </template>
        </main>
    </div>

    <!-- Preview Modal -->
    <div x-show="previewTemplate" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="bg-white rounded-lg max-w-sm w-full p-4 m-4">
            <div class="bg-gray-100 p-4 rounded-lg">
                <div class="whatsapp-message-preview bg-green-100 p-3 rounded-lg shadow-sm relative" style="max-width: 320px; margin: auto;">
                    <template x-if="previewTemplate?.header">
                        <div class="font-bold mb-1" x-text="previewTemplate.header"></div>
                    </template>
                    <p class="text-sm" x-text="previewTemplate?.content"></p>
                    <template x-if="previewTemplate?.footer">
                        <div class="text-xs text-gray-500 mt-1" x-text="previewTemplate.footer"></div>
                    </template>
                    <div class="text-xs text-gray-400 text-right mt-1">10:30 AM</div>
                </div>
                <template x-if="previewTemplate?.buttons?.length > 0">
                    <div class="mt-2 space-y-1">
                        <template x-for="button in previewTemplate.buttons" :key="button.text">
                            <div class="bg-white text-center text-blue-500 p-2 rounded-lg border border-gray-200 text-sm cursor-pointer" x-text="button.text"></div>
                        </template>
                    </div>
                </template>
            </div>
            <button @click="previewTemplate = null" class="w-full mt-4 bg-gray-200 text-gray-800 py-2 rounded-md">Close</button>
        </div>
    </div>
</div>

<script>
function adminMessageTemplates() {
    return {
        templates: [],
        categories: [],
        loading: false,
        activeCategory: 'all',
        editingTemplate: null,
        isAddingTemplate: false,
        previewTemplate: null,
        formData: {},
        newButton: { type: 'quick_reply', text: '', value: '', url: '', phoneNumber: '' },

        fetchTemplatesAndCategories() {
            this.loading = true;
            this.categories = [
                { id: 'retail', name: 'Retail & E-commerce' },
                { id: 'food', name: 'Food & Restaurants' },
                { id: 'services', name: 'Services' },
                { id: 'education', name: 'Education' }
            ];
            this.templates = [
                { id: 1, category: 'retail', name: 'Welcome Message', description: 'Initial greeting for new customers', content: 'Welcome to our store! How can I help you today?', buttons: [{ type: 'quick_reply', text: 'View Products', value: 'products' }, { type: 'quick_reply', text: 'Check Order', value: 'order' }, { type: 'call', text: 'Call Support', phoneNumber: '+91123456789' }], header: 'Welcome', footer: 'Reply to continue' },
                { id: 2, category: 'retail', name: 'Product Catalog', description: 'Show product categories', content: 'Here are our product categories. Please select one to browse:', buttons: [{ type: 'quick_reply', text: 'Clothing', value: 'clothing' }, { type: 'quick_reply', text: 'Electronics', value: 'electronics' }, { type: 'quick_reply', text: 'Home Goods', value: 'home' }] },
                { id: 3, category: 'food', name: 'Restaurant Menu', description: 'Show restaurant menu categories', content: 'Here\'s our menu. What would you like to order today?', buttons: [{ type: 'quick_reply', text: 'Starters', value: 'starters' }, { type: 'quick_reply', text: 'Main Course', value: 'main' }, { type: 'quick_reply', text: 'Desserts', value: 'desserts' }], header: 'Our Menu' },
                { id: 4, category: 'services', name: 'Appointment Booking', description: 'Help customers book appointments', content: 'Would you like to book an appointment with us?', buttons: [{ type: 'quick_reply', text: 'Book Now', value: 'book' }, { type: 'quick_reply', text: 'View Availability', value: 'availability' }, { type: 'url', text: 'Visit Website', url: 'https://example.com/booking' }] }
            ];
            this.loading = false;
        },

        get filteredTemplates() {
            if (this.activeCategory === 'all') {
                return this.templates;
            }
            return this.templates.filter(t => t.category === this.activeCategory);
        },

        getCategoryName(categoryId) {
            const category = this.categories.find(c => c.id === categoryId);
            return category ? category.name : 'Uncategorized';
        },

        handleEditTemplate(template) {
            this.isAddingTemplate = false;
            this.editingTemplate = template;
            this.formData = JSON.parse(JSON.stringify(template));
        },

        handleAddTemplate() {
            this.editingTemplate = null;
            this.isAddingTemplate = true;
            this.formData = { id: null, name: '', description: '', category: '', content: '', header: '', footer: '', buttons: [] };
        },

        handleCancelEdit() {
            this.editingTemplate = null;
            this.isAddingTemplate = false;
            this.formData = {};
        },

        handleSaveTemplate() {
            if (this.isAddingTemplate) {
                this.formData.id = Date.now(); // Mock ID
                this.templates.push(this.formData);
            } else {
                const index = this.templates.findIndex(t => t.id === this.editingTemplate.id);
                if (index !== -1) {
                    this.templates[index] = this.formData;
                }
            }
            this.handleCancelEdit();
        },

        handleDeleteTemplate(templateId) {
            if (confirm('Are you sure you want to delete this template?')) {
                this.templates = this.templates.filter(t => t.id !== templateId);
            }
        },

        handleAddButton() {
            if (!this.newButton.text) return;
            this.formData.buttons.push({ ...this.newButton });
            this.newButton = { type: 'quick_reply', text: '', value: '', url: '', phoneNumber: '' };
        },

        handleRemoveButton(index) {
            this.formData.buttons.splice(index, 1);
        },

        handlePreviewTemplate(template) {
            this.previewTemplate = template;
        }
    };
}
</script>
@endsection

