@extends('layouts.admin')

@section('content')
<div x-data="adminPermissions()" x-init="fetchRolesAndPermissions()" class="p-6">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Roles & Permissions</h1>

    <div class="grid grid-cols-3 gap-8">
        <!-- Roles List -->
        <div class="col-span-1 bg-white p-4 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
                <h3 class="font-semibold text-gray-800">Roles</h3>
                <button @click="handleAddRole()" class="text-green-600 hover:text-green-800" title="Add New Role">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
                </button>
            </div>
            <ul class="space-y-2">
                <template x-for="role in roles" :key="role.id">
                    <li>
                        <button @click="handleSelectRole(role)" :class="{ 'bg-indigo-100 text-indigo-700': selectedRole && selectedRole.id === role.id }" class="w-full text-left px-3 py-2 rounded-md text-sm font-medium hover:bg-gray-100">
                            <span x-text="role.name"></span>
                        </button>
                    </li>
                </template>
            </ul>
        </div>

        <!-- Role Details / Edit Form -->
        <div class="col-span-2 bg-white p-6 rounded-lg shadow">
            <template x-if="!isAddingRole && !isEditingRole && !selectedRole">
                <div class="text-center py-12">
                    <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
                    <h3 class="mt-2 text-sm font-medium text-gray-900">Select a Role</h3>
                    <p class="mt-1 text-sm text-gray-500">Select a role to see its details or add a new one.</p>
                </div>
            </template>

            <template x-if="selectedRole && !isEditingRole">
                <div>
                    <div class="flex justify-between items-start">
                        <div>
                            <h2 class="text-2xl font-bold text-gray-800" x-text="selectedRole.name"></h2>
                            <p class="text-sm text-gray-600 mt-1" x-text="selectedRole.description"></p>
                        </div>
                        <div class="flex space-x-2">
                            <button @click="handleEditRole()" class="text-indigo-600 hover:text-indigo-800" title="Edit Role">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" /><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" /></svg>
                            </button>
                            <button @click="handleDeleteRole()" class="text-red-600 hover:text-red-800" title="Delete Role">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>
                            </button>
                        </div>
                    </div>
                    <div class="mt-6">
                        <h4 class="font-semibold mb-2">Permissions</h4>
                        <ul class="bg-gray-50 p-4 rounded-md space-y-2">
                            <template x-for="permissionId in selectedRole.permissions" :key="permissionId">
                                <li class="flex items-center text-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
                                    <span x-text="getPermissionName(permissionId)"></span>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </template>

            <template x-if="isAddingRole || isEditingRole">
                <div>
                    <h2 class="text-2xl font-bold text-gray-800 mb-6" x-text="isAddingRole ? 'Add New Role' : 'Edit Role'"></h2>
                    <div class="space-y-4">
                        <input type="text" placeholder="Role Name" x-model="formData.name" class="w-full form-input">
                        <textarea placeholder="Description" x-model="formData.description" class="w-full form-textarea" rows="2"></textarea>
                        <div>
                            <h4 class="font-semibold mb-2">Permissions</h4>
                            <div class="grid grid-cols-2 gap-4 bg-gray-50 p-4 rounded-md">
                                <template x-for="permission in permissions" :key="permission.id">
                                    <label class="flex items-center space-x-3">
                                        <input type="checkbox" :value="permission.id" x-model="formData.permissions" class="form-checkbox">
                                        <span class="text-sm" x-text="permission.description"></span>
                                    </label>
                                </template>
                            </div>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button @click="handleCancel()" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-md">Cancel</button>
                        <button @click="handleSaveRole()" class="bg-green-600 text-white px-4 py-2 rounded-md">Save Role</button>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- All Permissions Section -->
    <div class="mt-10">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Available Permissions</h2>
        <div class="bg-white shadow rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permission</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="permission in permissions" :key="permission.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="permission.name"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="permission.description"></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
function adminPermissions() {
    return {
        loading: false,
        roles: [],
        permissions: [],
        selectedRole: null,
        isAddingRole: false,
        isEditingRole: false,
        formData: { name: '', description: '', permissions: [] },

        fetchRolesAndPermissions() {
            this.loading = true;
            this.permissions = [
                { id: 1, name: 'view_dashboard', description: 'View dashboard' },
                { id: 2, name: 'manage_users', description: 'Create, edit, and delete users' },
                { id: 3, name: 'manage_vendors', description: 'Manage vendor accounts' },
                { id: 4, name: 'manage_products', description: 'Manage products' },
                { id: 5, name: 'manage_orders', description: 'Manage orders' },
                { id: 6, name: 'manage_subscriptions', description: 'Manage subscription plans' },
                { id: 7, name: 'view_analytics', description: 'View analytics data' },
                { id: 8, name: 'manage_templates', description: 'Manage message templates' },
                { id: 9, name: 'manage_academy', description: 'Manage academy content' },
                { id: 10, name: 'manage_settings', description: 'Manage platform settings' },
                { id: 11, name: 'manage_permissions', description: 'Manage roles and permissions' }
            ];
            this.roles = [
                { id: 1, name: 'Super Admin', description: 'Full access to all features', permissions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11] },
                { id: 2, name: 'Admin', description: 'Administrative access with some restrictions', permissions: [1, 2, 3, 4, 5, 7, 8, 9] },
                { id: 3, name: 'Content Manager', description: 'Manages content and templates', permissions: [1, 8, 9] },
                { id: 4, name: 'Support Staff', description: 'Customer support access', permissions: [1, 3, 5] }
            ];
            this.loading = false;
        },

        handleSelectRole(role) {
            this.selectedRole = role;
            this.isAddingRole = false;
            this.isEditingRole = false;
        },

        handleAddRole() {
            this.selectedRole = null;
            this.isAddingRole = true;
            this.isEditingRole = false;
            this.formData = { name: '', description: '', permissions: [] };
        },

        handleEditRole() {
            if (!this.selectedRole) return;
            this.isAddingRole = false;
            this.isEditingRole = true;
            this.formData = JSON.parse(JSON.stringify(this.selectedRole));
        },

        handleDeleteRole() {
            if (!this.selectedRole) return;
            if (confirm(`Are you sure you want to delete the "${this.selectedRole.name}" role?`)) {
                this.roles = this.roles.filter(r => r.id !== this.selectedRole.id);
                this.selectedRole = null;
            }
        },

        handleSaveRole() {
            if (this.isAddingRole) {
                const newRole = { ...this.formData, id: Date.now() };
                this.roles.push(newRole);
                this.selectedRole = newRole;
            } else if (this.isEditingRole) {
                const index = this.roles.findIndex(r => r.id === this.selectedRole.id);
                if (index !== -1) {
                    this.roles[index] = this.formData;
                    this.selectedRole = this.formData;
                }
            }
            this.handleCancel();
        },

        handleCancel() {
            this.isAddingRole = false;
            this.isEditingRole = false;
            this.formData = { name: '', description: '', permissions: [] };
        },

        getPermissionName(permissionId) {
            const permission = this.permissions.find(p => p.id === permissionId);
            return permission ? permission.description : 'Unknown Permission';
        }
    };
}
</script>
@endsection

