@extends('layouts.admin')

@section('content')
<div x-data="marketingDashboard()" class="p-6">
    <!-- Welcome Section -->
    <div class="relative bg-green-600 rounded-lg p-8 mb-8 overflow-hidden">
        <div class="absolute top-0 right-0 -mt-16 -mr-16 w-48 h-48 bg-green-500 rounded-full opacity-50"></div>
        <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-40 h-40 bg-green-500 rounded-full opacity-50"></div>
        <div class="relative z-10">
            <span class="text-white bg-white/20 px-3 py-1 rounded-full text-sm font-medium">Marketing Dashboard</span>
            <h1 class="text-4xl font-bold text-white mt-4">Manage Marketing Campaigns</h1>
            <p class="text-green-100 mt-2">Monitor and manage all your marketing campaigns across different platforms from one place.</p>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="mb-8">
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Campaign Overview</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <template x-for="stat in platformStats" :key="stat.name">
                <div class="bg-white p-6 rounded-lg shadow-md flex items-start">
                    <div class="mr-4 flex-shrink-0">
                        <div class="w-12 h-12 flex items-center justify-center rounded-full" :style="{ backgroundColor: stat.color }">
                            <svg class="h-6 w-6" :style="{ color: stat.iconColor }" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-html="stat.icon"></svg>
                        </div>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-gray-500" x-text="stat.name"></p>
                        <p class="text-2xl font-bold text-gray-900" x-text="stat.stat"></p>
                        <p class="text-sm text-gray-500 mt-1">
                            <span :class="stat.trendType === 'up' ? 'text-green-500' : 'text-red-500'">
                                <span x-text="stat.trendType === 'up' ? '↑' : '↓'"></span>
                                <span x-text="stat.trend"></span>
                            </span>
                            vs last week
                        </p>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- Platform Performance -->
    <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-semibold text-gray-800">Platform Performance</h2>
            <a href="#" class="text-indigo-600 hover:text-indigo-800 font-medium text-sm flex items-center">
                View detailed analytics
                <svg class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3" /></svg>
            </a>
        </div>
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impressions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clicks</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cost</th>
                        <th class="relative px-6 py-3"><span class="sr-only">Actions</span></th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="stat in campaignStats" :key="stat.platform">
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="stat.platform"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="stat.impressions"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="stat.clicks"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="stat.ctr"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="stat.cost"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a :href="'#'" class="text-indigo-600 hover:text-indigo-900">View Details</a>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div>
        <h2 class="text-2xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-start">
                <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 mr-4">
                     <svg class="h-6 w-6 text-blue-800" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Facebook Ads</h3>
                    <p class="text-sm text-gray-600 mt-1">Create and manage Facebook ad campaigns.</p>
                </div>
            </a>
            <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-start">
                <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 mr-4">
                     <svg class="h-6 w-6 text-green-800" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">WhatsApp Ads</h3>
                    <p class="text-sm text-gray-600 mt-1">Set up and monitor WhatsApp campaigns.</p>
                </div>
            </a>
            <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-start">
                <div class="w-12 h-12 flex items-center justify-center rounded-full bg-pink-100 mr-4">
                     <svg class="h-6 w-6 text-pink-800" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Instagram Ads</h3>
                    <p class="text-sm text-gray-600 mt-1">Manage Instagram marketing campaigns.</p>
                </div>
            </a>
        </div>
    </div>
</div>

<script>
function marketingDashboard() {
    return {
        platformStats: [
            { name: 'Total Impressions', stat: '2,250', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />', color: 'rgba(59, 130, 246, 0.1)', iconColor: '#3B82F6', trend: '12%', trendType: 'up' },
            { name: 'Engagement Rate', stat: '8.5%', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.085a2 2 0 00-1.736.97l-2.714 4.223-2.714-4.223A2 2 0 005.085 3H5a2 2 0 00-2 2v5m7 4h10V5H2v10h2.5" />', color: 'rgba(16, 185, 129, 0.1)', iconColor: '#10B981', trend: '3.2%', trendType: 'up' },
            { name: 'Total Clicks', stat: '1,150', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6.002l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />', color: 'rgba(139, 92, 246, 0.1)', iconColor: '#8B5CF6', trend: '5.7%', trendType: 'up' },
            { name: 'Conversion Rate', stat: '3.2%', icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />', color: 'rgba(236, 72, 153, 0.1)', iconColor: '#EC4899', trend: '1.1%', trendType: 'down' }
        ],
        campaignStats: [
            { platform: 'Facebook', impressions: '1,000', clicks: '100', ctr: '10%', cost: '₹5,000' },
            { platform: 'WhatsApp', impressions: '750', clicks: '150', ctr: '20%', cost: '₹3,500' },
            { platform: 'Instagram', impressions: '500', clicks: '75', ctr: '15%', cost: '₹2,500' }
        ]
    };
}
</script>
@endsection

