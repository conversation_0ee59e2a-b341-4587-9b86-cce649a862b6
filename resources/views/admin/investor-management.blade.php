@extends('layouts.admin')

@section('content')
<div x-data="investorManagement()" class="p-6">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Investor Management</h1>
        <p class="text-gray-600">Review board activities, governance, and company leadership.</p>
    </div>

    <!-- Tabs -->
    <div class="mb-6 border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button @click="activeTab = 'board'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'board', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'board' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Board Members
            </button>
            <button @click="activeTab = 'management'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'management', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'management' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Management Team
            </button>
            <button @click="activeTab = 'meetings'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'meetings', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'meetings' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Board Meetings
            </button>
            <button @click="activeTab = 'governance'" :class="{ 'border-indigo-500 text-indigo-600': activeTab === 'governance', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'governance' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                Governance & Voting
            </button>
        </nav>
    </div>

    <!-- Content -->
    <div>
        <!-- Board Members -->
        <div x-show="activeTab === 'board'">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="member in managementData.boardMembers" :key="member.id">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-4">
                            <img :src="member.image" alt="" class="h-16 w-16 rounded-full mr-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900" x-text="member.name"></h3>
                                <p class="text-sm text-gray-600" x-text="member.role"></p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-700 mb-4" x-text="member.bio"></p>
                        <div class="text-sm">
                            <p><strong>Equity:</strong> <span x-text="member.equity"></span>%</p>
                            <p><strong>Voting Rights:</strong> <span x-text="member.votingRights"></span>%</p>
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Management Team -->
        <div x-show="activeTab === 'management'">
             <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="member in managementData.managementTeam" :key="member.id">
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="flex items-center mb-4">
                            <img :src="member.image" alt="" class="h-16 w-16 rounded-full mr-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900" x-text="member.name"></h3>
                                <p class="text-sm text-gray-600" x-text="member.role"></p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-700" x-text="member.bio"></p>
                    </div>
                </template>
            </div>
        </div>

        <!-- Board Meetings -->
        <div x-show="activeTab === 'meetings'">
            <div class="bg-white rounded-lg shadow-md">
                <ul class="divide-y divide-gray-200">
                    <template x-for="meeting in managementData.boardMeetings" :key="meeting.id">
                        <li class="p-4 flex justify-between items-center">
                            <div>
                                <p class="font-semibold text-gray-800" x-text="meeting.title"></p>
                                <p class="text-sm text-gray-600" x-text="meeting.date"></p>
                            </div>
                            <a :href="meeting.agendaLink" target="_blank" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">View Agenda</a>
                        </li>
                    </template>
                </ul>
            </div>
        </div>

        <!-- Governance & Voting -->
        <div x-show="activeTab === 'governance'">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Proposals for Voting</h3>
                    <div class="space-y-4">
                        <template x-for="proposal in managementData.boardProposals" :key="proposal.id">
                            <div class="bg-white p-4 rounded-lg shadow-sm border">
                                <h4 class="font-semibold" x-text="proposal.title"></h4>
                                <p class="text-sm text-gray-600 my-2" x-text="proposal.description"></p>
                                <div class="flex justify-between items-center text-sm">
                                    <span :class="{'text-green-600': proposal.status === 'Open', 'text-gray-500': proposal.status !== 'Open'}" x-text="'Status: ' + proposal.status"></span>
                                    <button @click="openVoteModal(proposal)" x-show="proposal.status === 'Open' && !proposal.voted" class="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">Vote</button>
                                    <span x-show="proposal.voted" class="px-3 py-1 bg-gray-200 text-gray-700 rounded-md">Voted</span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                <div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-4">Governance Documents</h3>
                    <div class="bg-white rounded-lg shadow-md">
                        <ul class="divide-y divide-gray-200">
                            <template x-for="doc in managementData.governanceDocuments" :key="doc.id">
                                <li class="p-4 flex justify-between items-center">
                                    <p class="font-medium text-gray-800" x-text="doc.title"></p>
                                    <a :href="doc.link" target="_blank" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">Download</a>
                                </li>
                            </template>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Vote Modal -->
    <div x-show="isVoteModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="closeVoteModal()">
        <div class="bg-white rounded-lg max-w-lg w-full p-6">
            <template x-if="selectedProposal">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Vote on Proposal</h3>
                    <p class="text-gray-700 font-semibold mb-4" x-text="selectedProposal.title"></p>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Your Vote</label>
                            <div class="mt-2 flex space-x-4">
                                <label class="flex items-center"><input type="radio" x-model="voteValue" value="for" class="mr-2"> For</label>
                                <label class="flex items-center"><input type="radio" x-model="voteValue" value="against" class="mr-2"> Against</label>
                                <label class="flex items-center"><input type="radio" x-model="voteValue" value="abstain" class="mr-2"> Abstain</label>
                            </div>
                        </div>
                        <div>
                            <label for="comment" class="block text-sm font-medium text-gray-700">Comment (optional)</label>
                            <textarea x-model="voteComment" id="comment" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></textarea>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button @click="closeVoteModal()" class="px-4 py-2 border rounded-md">Cancel</button>
                        <button @click="submitVote()" :disabled="!voteValue" class="px-4 py-2 bg-indigo-600 text-white rounded-md disabled:opacity-50">Submit Vote</button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function investorManagement() {
    return {
        activeTab: 'board',
        isVoteModalOpen: false,
        selectedProposal: null,
        voteValue: null,
        voteComment: '',
        managementData: {},

        init() {
            this.managementData = {
                boardMembers: [
                    { id: 1, name: 'Karan Solanki', role: 'Founder & CEO', equity: 60, votingRights: 60, image: 'https://randomuser.me/api/portraits/men/1.jpg', bio: 'Founder and CEO of WhaMart.' },
                    { id: 2, name: 'Priya Sharma', role: 'CTO', equity: 0, votingRights: 0, image: 'https://randomuser.me/api/portraits/women/2.jpg', bio: 'Technology leader with 10+ years of experience.' },
                    { id: 3, name: 'Rajiv Mehta', role: 'Strategic Investor', equity: 10, votingRights: 10, image: 'https://randomuser.me/api/portraits/men/3.jpg', bio: 'Angel investor with portfolio of 20+ startups.' },
                    { id: 5, name: 'Current Investor', role: 'Investor', equity: 5, votingRights: 5, image: 'https://randomuser.me/api/portraits/men/5.jpg', bio: 'Your position on the board.' }
                ],
                managementTeam: [
                    { id: 1, name: 'Karan Solanki', role: 'Founder & CEO', image: 'https://randomuser.me/api/portraits/men/1.jpg', bio: 'Founder and CEO of WhaMart.' },
                    { id: 2, name: 'Priya Sharma', role: 'CTO', image: 'https://randomuser.me/api/portraits/women/2.jpg', bio: 'Technology leader with 10+ years of experience.' },
                    { id: 6, name: 'Vikram Singh', role: 'COO', image: 'https://randomuser.me/api/portraits/men/6.jpg', bio: 'Operations expert in logistics and supply chain.' },
                ],
                boardMeetings: [
                    { id: 1, title: 'Q3 2024 Board Meeting', date: 'October 15, 2024', agendaLink: '#' },
                    { id: 2, title: 'Q2 2024 Board Meeting', date: 'July 20, 2024', agendaLink: '#' },
                ],
                boardProposals: [
                    { id: 1, title: 'Approve Q3 Budget', description: 'Proposal to approve the operating budget for the third quarter.', status: 'Open', voted: false },
                    { id: 2, title: 'New Stock Option Plan', description: 'Introduce a new stock option plan for key employees.', status: 'Open', voted: false },
                    { id: 3, title: 'Expansion into EU Market', description: 'Strategic decision on expanding operations into the European Union.', status: 'Closed', voted: true },
                ],
                governanceDocuments: [
                    { id: 1, title: 'Articles of Incorporation', link: '#' },
                    { id: 2, title: 'Bylaws', link: '#' },
                    { id: 3, title: 'Shareholder Agreement', link: '#' },
                ]
            };
        },

        openVoteModal(proposal) {
            this.selectedProposal = proposal;
            this.isVoteModalOpen = true;
        },

        closeVoteModal() {
            this.isVoteModalOpen = false;
            this.selectedProposal = null;
            this.voteValue = null;
            this.voteComment = '';
        },

        submitVote() {
            if (!this.voteValue) return;
            console.log(`Voted on proposal ${this.selectedProposal.id}: ${this.voteValue} with comment: ${this.voteComment}`);
            const index = this.managementData.boardProposals.findIndex(p => p.id === this.selectedProposal.id);
            if(index !== -1) {
                this.managementData.boardProposals[index].voted = true;
            }
            this.closeVoteModal();
        }
    };
}
</script>
@endsection

