@extends('layouts.admin')

@section('content')
<div x-data="adminInvestment()" class="p-6 bg-gray-50 min-h-screen">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Investment Dashboard</h1>

    <!-- Investment Stats -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white p-5 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-500">Total Capital Raised</h3>
            <p class="text-2xl font-bold text-gray-800" x-text="formatCurrency(investmentStats.totalCapitalRaised)"></p>
        </div>
        <div class="bg-white p-5 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-500">Current Valuation</h3>
            <p class="text-2xl font-bold text-gray-800" x-text="formatCurrency(investmentStats.currentValuation)"></p>
        </div>
        <div class="bg-white p-5 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-500">Total Investors</h3>
            <p class="text-2xl font-bold text-gray-800" x-text="investmentStats.totalInvestors"></p>
        </div>
        <div class="bg-white p-5 rounded-lg shadow">
            <h3 class="text-sm font-medium text-gray-500">Equity Diluted</h3>
            <p class="text-2xl font-bold text-gray-800" x-text="investmentStats.equityDiluted + '%'"></p>
        </div>
    </div>

    <!-- Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Monthly Investment</h3>
            <div class="h-64 flex items-end space-x-2">
                <template x-for="item in monthlyInvestmentData" :key="item.month">
                    <div class="flex-1 flex flex-col items-center">
                        <div class="w-full bg-blue-300 rounded-t-md" :style="{ height: (item.amount / 600000 * 100) + '%' }"></div>
                        <span class="text-xs text-gray-500 mt-1" x-text="item.month"></span>
                    </div>
                </template>
            </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-semibold text-gray-700 mb-4">Investor Types</h3>
            <div class="flex items-center h-64">
                <div class="w-1/2">
                    <svg viewBox="0 0 100 100">
                        <circle cx="50" cy="50" r="50" fill="#FF8042"/>
                        <path d="M 50 50 L 50 0 A 50 50 0 0 1 93.3 25 Z" fill="#0088FE"></path>
                        <path d="M 50 50 L 93.3 25 A 50 50 0 0 1 65.36 96.59 Z" fill="#00C49F"></path>
                        <path d="M 50 50 L 65.36 96.59 A 50 50 0 0 1 5.88 70.71 Z" fill="#FFBB28"></path>
                    </svg>
                </div>
                <div class="w-1/2 pl-6 space-y-2">
                    <template x-for="(type, index) in investorTypeData" :key="type.name">
                        <div class="flex items-center">
                            <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: pieColors[index] }"></div>
                            <span class="text-sm text-gray-600" x-text="type.name + ' (' + type.value + '%'"></span>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <div class="bg-white rounded-lg shadow">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6" aria-label="Tabs">
                <button @click="tab = 'investors'" :class="{ 'border-indigo-500 text-indigo-600': tab === 'investors', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': tab !== 'investors' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Investors
                </button>
                <button @click="tab = 'team'" :class="{ 'border-indigo-500 text-indigo-600': tab === 'team', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': tab !== 'team' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Management Team
                </button>
            </nav>
        </div>

        <!-- Investors Table -->
        <div x-show="tab === 'investors'" class="p-6">
            <div class="flex justify-end mb-4">
                <button @click="openInvestorDialog()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" /></svg>
                    Add Investor
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equity</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <template x-for="investor in investorData" :key="investor.id">
                            <tr>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="investor.name"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="formatCurrency(investor.amount)"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="investor.equity + '%'"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="investor.date"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="investor.type"></td>
                                <td class="py-4 px-6 whitespace-nowrap">
                                    <span :class="investor.status === 'Active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" x-text="investor.status"></span>
                                </td>
                                <td class="py-4 px-6 whitespace-nowrap">
                                    <button @click="openInvestorDialog(investor)" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                    <button @click="confirmDelete('investor', investor.id)" class="text-red-600 hover:text-red-900 ml-4">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Management Team Table -->
        <div x-show="tab === 'team'" class="p-6">
            <div class="flex justify-end mb-4">
                <button @click="openTeamDialog()" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z" clip-rule="evenodd" /></svg>
                    Add Member
                </button>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Position</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equity</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-200">
                        <template x-for="member in managementTeam" :key="member.id">
                            <tr>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="member.name"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="member.position"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="member.equity + '%'"></td>
                                <td class="py-4 px-6 whitespace-nowrap" x-text="member.joined"></td>
                                <td class="py-4 px-6 whitespace-nowrap">
                                    <button @click="openTeamDialog(member)" class="text-indigo-600 hover:text-indigo-900">Edit</button>
                                    <button @click="confirmDelete('team', member.id)" class="text-red-600 hover:text-red-900 ml-4">Delete</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Investor Modal -->
    <div x-show="investorModalOpen" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="investorModalOpen" @click="investorModalOpen = false" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="saveInvestor">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title" x-text="modalInvestor.id ? 'Edit Investor' : 'Add New Investor'"></h3>
                        <div class="mt-4 space-y-4">
                            <input type="text" placeholder="Name" x-model="modalInvestor.name" class="w-full form-input" required>
                            <input type="number" placeholder="Amount" x-model="modalInvestor.amount" class="w-full form-input" required>
                            <input type="number" placeholder="Equity" x-model="modalInvestor.equity" class="w-full form-input" required step="0.1">
                            <input type="date" x-model="modalInvestor.date" class="w-full form-input" required>
                            <input type="text" placeholder="Type" x-model="modalInvestor.type" class="w-full form-input" required>
                            <select x-model="modalInvestor.status" class="w-full form-select">
                                <option value="Active">Active</option>
                                <option value="Inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 sm:ml-3 sm:w-auto sm:text-sm">Save</button>
                        <button type="button" @click="investorModalOpen = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:w-auto sm:text-sm">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Team Member Modal -->
    <div x-show="teamModalOpen" class="fixed z-10 inset-0 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div x-show="teamModalOpen" @click="teamModalOpen = false" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <form @submit.prevent="saveTeamMember">
                    <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="modalTeamMember.id ? 'Edit Team Member' : 'Add New Team Member'"></h3>
                        <div class="mt-4 space-y-4">
                            <input type="text" placeholder="Name" x-model="modalTeamMember.name" class="w-full form-input" required>
                            <input type="text" placeholder="Position" x-model="modalTeamMember.position" class="w-full form-input" required>
                            <input type="number" placeholder="Equity" x-model="modalTeamMember.equity" class="w-full form-input" required step="0.1">
                            <input type="date" x-model="modalTeamMember.joined" class="w-full form-input" required>
                            <textarea placeholder="Bio" x-model="modalTeamMember.bio" class="w-full form-textarea" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 sm:ml-3 sm:w-auto sm:text-sm">Save</button>
                        <button type="button" @click="teamModalOpen = false" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:w-auto sm:text-sm">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div x-show="deleteModalOpen" class="fixed z-10 inset-0 overflow-y-auto" style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75"></div>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900">Delete <span x-text="itemToDelete.type"></span></h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">Are you sure you want to delete? This action cannot be undone.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="handleDelete" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 sm:ml-3 sm:w-auto sm:text-sm">Delete</button>
                    <button @click="deleteModalOpen = false" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 sm:mt-0 sm:w-auto sm:text-sm">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function adminInvestment() {
    return {
        tab: 'investors',
        investmentStats: {
            totalInvestors: 57,
            activeInvestors: 42,
            totalCapitalRaised: 2500000,
            currentValuation: 10000000,
            equityDiluted: 25,
            remainingEquity: 75,
            avgInvestment: 43859.65
        },
        monthlyInvestmentData: [
            { month: 'Jan', amount: 150000 }, { month: 'Feb', amount: 50000 }, { month: 'Mar', amount: 300000 },
            { month: 'Apr', amount: 200000 }, { month: 'May', amount: 100000 }, { month: 'Jun', amount: 450000 },
            { month: 'Jul', amount: 375000 }, { month: 'Aug', amount: 225000 }, { month: 'Sep', amount: 550000 },
            { month: 'Oct', amount: 100000 }, { month: 'Nov', amount: 0 }, { month: 'Dec', amount: 0 },
        ],
        investorTypeData: [
            { name: 'Angel Investors', value: 25 }, { name: 'Venture Capital', value: 40 },
            { name: 'Private Equity', value: 30 }, { name: 'Institutional', value: 5 },
        ],
        pieColors: ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'],
        investorData: [
            { id: 1, name: 'John Smith', amount: 200000, equity: 5, date: '2022-01-15', type: 'Angel Investor', status: 'Active' },
            { id: 2, name: 'Acme Ventures', amount: 500000, equity: 10, date: '2022-06-20', type: 'Venture Capital', status: 'Active' },
            { id: 3, name: 'Tech Growth Fund', amount: 750000, equity: 7.5, date: '2023-03-10', type: 'Private Equity', status: 'Active' },
            { id: 4, name: 'Sarah Johnson', amount: 50000, equity: 1, date: '2022-02-28', type: 'Angel Investor', status: 'Active' },
            { id: 5, name: 'Future Investments LLC', amount: 100000, equity: 1.5, date: '2022-07-15', type: 'Institutional', status: 'Inactive' }
        ],
        managementTeam: [
            { id: 1, name: 'Jane Doe', position: 'CEO', equity: 15, joined: '2021-05-01', bio: 'Experienced executive with 15 years in tech' },
            { id: 2, name: 'Michael Chen', position: 'CTO', equity: 12, joined: '2021-05-01', bio: 'Former lead engineer at Google and Amazon' },
            { id: 3, name: 'Priya Sharma', position: 'COO', equity: 10, joined: '2021-07-15', bio: 'Operations expert with MBA from Stanford' },
            { id: 4, name: 'David Wilson', position: 'CFO', equity: 8, joined: '2022-01-10', bio: 'Financial specialist with CPA certification' }
        ],
        investorModalOpen: false,
        teamModalOpen: false,
        deleteModalOpen: false,
        modalInvestor: {},
        modalTeamMember: {},
        itemToDelete: { type: '', id: null },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        openInvestorDialog(investor = null) {
            this.modalInvestor = investor ? { ...investor } : { id: null, name: '', amount: '', equity: '', date: new Date().toISOString().split('T')[0], type: '', status: 'Active' };
            this.investorModalOpen = true;
        },

        saveInvestor() {
            if (this.modalInvestor.id) {
                const index = this.investorData.findIndex(i => i.id === this.modalInvestor.id);
                this.investorData[index] = this.modalInvestor;
            } else {
                this.modalInvestor.id = Date.now();
                this.investorData.push(this.modalInvestor);
            }
            this.investorModalOpen = false;
        },

        openTeamDialog(member = null) {
            this.modalTeamMember = member ? { ...member } : { id: null, name: '', position: '', equity: '', joined: new Date().toISOString().split('T')[0], bio: '' };
            this.teamModalOpen = true;
        },

        saveTeamMember() {
            if (this.modalTeamMember.id) {
                const index = this.managementTeam.findIndex(m => m.id === this.modalTeamMember.id);
                this.managementTeam[index] = this.modalTeamMember;
            } else {
                this.modalTeamMember.id = Date.now();
                this.managementTeam.push(this.modalTeamMember);
            }
            this.teamModalOpen = false;
        },

        confirmDelete(type, id) {
            this.itemToDelete = { type, id };
            this.deleteModalOpen = true;
        },

        handleDelete() {
            if (this.itemToDelete.type === 'investor') {
                this.investorData = this.investorData.filter(i => i.id !== this.itemToDelete.id);
            } else if (this.itemToDelete.type === 'team') {
                this.managementTeam = this.managementTeam.filter(m => m.id !== this.itemToDelete.id);
            }
            this.deleteModalOpen = false;
        }
    };
}
</script>
@endsection

