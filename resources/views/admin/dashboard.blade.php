{{-- Admin Dashboard Page --}}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #25D366;
            --primary-light: rgba(37, 211, 102, 0.1);
            --accent: #075E54;
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --gray: #6C757D;
            --dark: #212529;
            --border-radius-lg: 16px;
            --border-radius-md: 12px;
            --border-radius-sm: 8px;
            --spacing-xl: 32px;
            --spacing-lg: 24px;
            --spacing-md: 16px;
            --shadow-sm: 0 2px 8px rgba(0,0,0,0.05);
        }
        body {
            font-family: 'Poppins', 'Segoe UI', sans-serif;
            background-color: #E9F7EF;
            color: var(--dark);
            margin: 0;
        }
        .vendor-dashboard {
            padding: var(--spacing-lg);
            max-width: 100%;
            overflow-x: hidden;
        }
        .welcome-section {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            padding: var(--spacing-xl);
            margin-bottom: var(--spacing-xl);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            border-left: 4px solid var(--primary);
        }
        .welcome-badge {
            display: inline-block;
            padding: 6px 12px;
            background-color: var(--primary-light);
            color: var(--primary);
            border-radius: 9999px;
            font-size: 13px;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
        }
        .welcome-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: var(--spacing-md);
            color: var(--dark);
        }
        .welcome-subtitle {
            font-size: 15px;
            color: var(--gray);
            margin-bottom: 0;
            max-width: 600px;
        }
        .stats-section {
            margin-bottom: var(--spacing-md);
        }
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
        }
        .section-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--dark);
            margin: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: var(--spacing-md);
        }
        .stat-card {
            background-color: var(--white);
            border-radius: var(--border-radius-md);
            padding: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            text-decoration: none;
            color: inherit;
        }
        .stat-icon-container {
            width: 48px;
            height: 48px;
            border-radius: var(--border-radius-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        .stat-content {
            flex: 1;
        }
        .stat-label {
            font-size: 14px;
            font-weight: 600;
            color: var(--gray);
            margin: 0 0 4px 0;
        }
        .stat-value {
            font-size: 22px;
            font-weight: 700;
            color: var(--dark);
            margin: 0 0 4px 0;
        }
        .stat-description {
            font-size: 12px;
            color: var(--gray);
            margin: 0;
        }
        .card {
            background-color: var(--white);
            border-radius: var(--border-radius-lg);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }
        .card-header {
            padding: var(--spacing-md) var(--spacing-lg);
            border-bottom: 1px solid #e5e7eb;
        }
        .card-title {
            font-size: 18px;
            font-weight: 700;
            color: var(--dark);
            margin: 0;
        }
        .activity-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .activity-item {
            border-bottom: 1px solid #e5e7eb;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-link {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-md) var(--spacing-lg);
            text-decoration: none;
            color: inherit;
        }
        @media (max-width: 1024px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        @media (max-width: 640px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .welcome-section {
                padding: var(--spacing-lg);
            }
            .vendor-dashboard {
                padding: var(--spacing-md);
            }
        }
    </style>
</head>
<body>

<div class="vendor-dashboard">
    <div class="welcome-section">
        <div class="welcome-badge">Admin Access</div>
        <h2 class="welcome-title">Welcome back, {{ Auth::user()->name ?? 'Admin' }}!</h2>
        <p class="welcome-subtitle">Here's a snapshot of your platform's performance. You can manage all aspects of Whamart from this central dashboard.</p>
    </div>

    <main>
        <div class="stats-section">
            <div class="stats-grid">
                @foreach ($stats as $item)
                    <div class="stat-card">
                        <div class="stat-icon-container" style="background-color: {{ $item['color'] }}; color: {{ $item['iconColor'] ?? 'inherit' }};">
                            {!! $item['icon'] !!}
                        </div>
                        <div class="stat-content">
                            <p class="stat-label">{{ $item['name'] }}</p>
                            <p class="stat-value">{{ $item['stat'] }}</p>
                            <p class="stat-description">{{ $item['description'] }}</p>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

        <div style="display: grid; grid-template-columns: 2fr 1fr; gap: var(--spacing-md);">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Recent Activity</h3>
                </div>
                <ul class="activity-list">
                    @forelse ($recentActivities as $activity)
                        <li class="activity-item">
                            <a href="#" class="activity-link">
                                <div>
                                    <p style="margin:0; font-weight: 500;">
                                        @switch($activity['type'])
                                            @case('vendor_registration')
                                                New Vendor: <strong>{{ $activity['user'] }}</strong> registered.
                                                @break
                                            @case('subscription_purchase')
                                                <strong>{{ $activity['user'] }}</strong> purchased the <strong>{{ $activity['plan'] }}</strong> plan.
                                                @break
                                            @case('store_verification')
                                                Store verification for <strong>{{ $activity['user'] }}</strong> is pending.
                                                @break
                                            @case('account_blocked')
                                                Account for <strong>{{ $activity['user'] }}</strong> has been blocked.
                                                @break
                                            @case('template_added')
                                                New template <strong>'{{ $activity['template'] }}'</strong> was added.
                                                @break
                                        @endswitch
                                    </p>
                                </div>
                                <span style="font-size: 13px; color: var(--gray); white-space: nowrap;">{{ $activity['time'] }}</span>
                            </a>
                        </li>
                    @empty
                        <li class="activity-item" style="padding: var(--spacing-lg); text-align: center; color: var(--gray);">
                            No recent activities.
                        </li>
                    @endforelse
                </ul>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Quick Actions</h3>
                </div>
                <ul class="activity-list">
                    @foreach ($quickActions as $action)
                        <li class="activity-item">
                            <a href="{{ $action['link'] }}" class="activity-link">
                                <div style="display: flex; align-items: center; gap: var(--spacing-md);">
                                    <div class="stat-icon-container" style="background-color: {{ $action['color'] }}; color: {{ $action['iconColor'] ?? '#25D366' }}; width: 40px; height: 40px;">
                                        {!! $action['icon'] !!}
                                    </div>
                                    <div>
                                        <p style="margin:0; font-weight: 600;">{{ $action['name'] }}</p>
                                        <p style="margin:0; font-size: 13px; color: var(--gray);">{{ $action['description'] }}</p>
                                    </div>
                                </div>
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" style="width:16px; height:16px; color: var(--gray);">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                                </svg>
                            </a>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
    </main>
</div>

</body>
</html>
