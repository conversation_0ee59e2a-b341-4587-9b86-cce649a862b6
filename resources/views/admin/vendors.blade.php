@extends('layouts.admin')

@section('content')
<div x-data="adminVendors()" class="p-6">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Vendor Management</h1>
        <p class="text-gray-600">Oversee and manage all vendors on the platform.</p>
    </div>

    <!-- Filters and Search -->
    <div class="mb-4 bg-white p-4 rounded-lg shadow-md flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <input type="text" x-model="searchTerm" @input.debounce.300ms="applyFilters()" placeholder="Search by store or name..." class="form-input w-64">
            <select x-model="filterStatus" @change="applyFilters()" class="form-select">
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="verified">Verified</option>
                <option value="unverified">Unverified</option>
            </select>
        </div>
    </div>

    <!-- Vendors Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Store Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Verification</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <template x-for="vendor in paginatedVendors" :key="vendor.id">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900" x-text="vendor.storeName"></div>
                            <div class="text-sm text-gray-500" x-text="vendor.businessCategory"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-gray-900" x-text="vendor.name"></div>
                            <div class="text-sm text-gray-500" x-text="vendor.email"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="vendor.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                x-text="vendor.isActive ? 'Active' : 'Inactive'"></span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="vendor.isVerified ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'"
                                x-text="vendor.isVerified ? 'Verified' : 'Unverified'"></span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="vendor.registeredDate"></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button @click="viewVendor(vendor)" class="text-indigo-600 hover:text-indigo-900">View</button>
                            <template x-if="!vendor.isVerified">
                                <button @click="verifyVendor(vendor.id)" class="text-green-600 hover:text-green-900 ml-4">Verify</button>
                            </template>
                            <button @click="deleteVendor(vendor.id)" class="text-red-600 hover:text-red-900 ml-4">Delete</button>
                        </td>
                    </tr>
                </template>
                <template x-if="paginatedVendors.length === 0">
                    <tr><td colspan="6" class="text-center py-4">No vendors found.</td></tr>
                </template>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-4 flex justify-between items-center">
        <span class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * vendorsPerPage + 1"></span> to <span x-text="Math.min(currentPage * vendorsPerPage, filteredVendors.length)"></span> of <span x-text="filteredVendors.length"></span> results</span>
        <div>
            <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 border rounded disabled:opacity-50">Prev</button>
            <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 border rounded disabled:opacity-50 ml-2">Next</button>
        </div>
    </div>

    <!-- View Vendor Details Modal -->
    <div x-show="isDetailsModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="closeModal()">
        <div class="bg-white rounded-lg max-w-3xl w-full p-6">
            <template x-if="selectedVendor">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="selectedVendor.storeName"></h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Vendor Info</h4>
                            <p><span class="font-medium">Name:</span> <span x-text="selectedVendor.name"></span></p>
                            <p><span class="font-medium">Email:</span> <span x-text="selectedVendor.email"></span></p>
                            <p><span class="font-medium">Phone:</span> <span x-text="selectedVendor.phone"></span></p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Store Details</h4>
                            <p><span class="font-medium">Category:</span> <span x-text="selectedVendor.businessCategory"></span></p>
                            <p><span class="font-medium">URL:</span> <span x-text="selectedVendor.storeUrl"></span></p>
                            <p><span class="font-medium">Status:</span> <span x-text="selectedVendor.isActive ? 'Active' : 'Inactive'"></span></p>
                            <p><span class="font-medium">Verification:</span> <span x-text="selectedVendor.isVerified ? 'Verified' : 'Unverified'"></span></p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Subscription</h4>
                            <p><span class="font-medium">Plan:</span> <span x-text="selectedVendor.subscription.plan"></span></p>
                            <p><span class="font-medium">Status:</span> <span x-text="selectedVendor.subscription.status"></span></p>
                            <p><span class="font-medium">Expires:</span> <span x-text="selectedVendor.subscription.endDate"></span></p>
                        </div>
                    </div>
                    <div class="mt-6 pt-4 border-t">
                        <h4 class="text-sm font-medium text-gray-500 mb-2">Store Statistics</h4>
                        <div class="flex space-x-8">
                            <p><span class="font-medium">Products:</span> <span x-text="selectedVendor.stats.products"></span></p>
                            <p><span class="font-medium">Orders:</span> <span x-text="selectedVendor.stats.orders"></span></p>
                            <p><span class="font-medium">Revenue:</span> <span x-text="selectedVendor.stats.revenue"></span></p>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button @click="closeModal()" class="px-4 py-2 border rounded-md">Close</button>
                        <button @click="toggleStatus(selectedVendor.id)" class="px-4 py-2 text-white rounded-md" :class="selectedVendor.isActive ? 'bg-red-600' : 'bg-green-600'" x-text="selectedVendor.isActive ? 'Deactivate' : 'Activate'"></button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function adminVendors() {
    return {
        vendors: [],
        filteredVendors: [],
        paginatedVendors: [],
        searchTerm: '',
        filterStatus: 'all',
        currentPage: 1,
        totalPages: 1,
        vendorsPerPage: 10,
        isDetailsModalOpen: false,
        selectedVendor: null,

        init() {
            this.vendors = [
                { id: 1, name: 'Rahul Sharma', email: '<EMAIL>', phone: '+91 9876543210', storeName: 'Rahul Electronics', storeUrl: 'rahul-electronics', businessCategory: 'Electronics', isVerified: true, isActive: true, registeredDate: '2023-05-15', subscription: { plan: 'Gold', endDate: '2024-05-14', status: 'active' }, stats: { products: 45, orders: 128, revenue: '₹45,600' } },
                { id: 2, name: 'Priya Patel', email: '<EMAIL>', phone: '+91 9876543211', storeName: 'Fashion Hub', storeUrl: 'fashion-hub', businessCategory: 'Clothing', isVerified: true, isActive: true, registeredDate: '2023-06-20', subscription: { plan: 'Premium', endDate: '2024-06-19', status: 'active' }, stats: { products: 78, orders: 256, revenue: '₹89,200' } },
                { id: 3, name: 'Amit Kumar', email: '<EMAIL>', phone: '+91 9876543212', storeName: 'Spice Garden', storeUrl: 'spice-garden', businessCategory: 'Food', isVerified: false, isActive: true, registeredDate: '2023-07-10', subscription: { plan: 'Standard', endDate: '2024-07-09', status: 'active' }, stats: { products: 25, orders: 87, revenue: '₹32,400' } },
                { id: 4, name: 'Neha Singh', email: '<EMAIL>', phone: '+91 9876543213', storeName: 'Beauty Essentials', storeUrl: 'beauty-essentials', businessCategory: 'Beauty', isVerified: true, isActive: false, registeredDate: '2023-08-05', subscription: { plan: 'Gold', endDate: '2024-08-04', status: 'suspended' }, stats: { products: 56, orders: 142, revenue: '₹52,800' } },
            ];
            this.applyFilters();
        },

        applyFilters() {
            let tempVendors = this.vendors;
            if (this.searchTerm) {
                tempVendors = tempVendors.filter(v => 
                    v.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    v.storeName.toLowerCase().includes(this.searchTerm.toLowerCase())
                );
            }
            if (this.filterStatus !== 'all') {
                if (this.filterStatus === 'active') tempVendors = tempVendors.filter(v => v.isActive);
                if (this.filterStatus === 'inactive') tempVendors = tempVendors.filter(v => !v.isActive);
                if (this.filterStatus === 'verified') tempVendors = tempVendors.filter(v => v.isVerified);
                if (this.filterStatus === 'unverified') tempVendors = tempVendors.filter(v => !v.isVerified);
            }
            this.filteredVendors = tempVendors;
            this.currentPage = 1;
            this.paginateVendors();
        },

        paginateVendors() {
            this.totalPages = Math.ceil(this.filteredVendors.length / this.vendorsPerPage);
            const start = (this.currentPage - 1) * this.vendorsPerPage;
            const end = start + this.vendorsPerPage;
            this.paginatedVendors = this.filteredVendors.slice(start, end);
        },

        viewVendor(vendor) {
            this.selectedVendor = vendor;
            this.isDetailsModalOpen = true;
        },

        closeModal() {
            this.isDetailsModalOpen = false;
            this.selectedVendor = null;
        },

        verifyVendor(vendorId) {
            const index = this.vendors.findIndex(v => v.id === vendorId);
            if (index !== -1) {
                this.vendors[index].isVerified = true;
                this.applyFilters();
            }
        },

        toggleStatus(vendorId) {
            const index = this.vendors.findIndex(v => v.id === vendorId);
            if (index !== -1) {
                this.vendors[index].isActive = !this.vendors[index].isActive;
                if(this.selectedVendor && this.selectedVendor.id === vendorId) {
                    this.selectedVendor.isActive = this.vendors[index].isActive;
                }
                this.applyFilters();
            }
        },

        deleteVendor(vendorId) {
            if (confirm('Are you sure you want to delete this vendor?')) {
                this.vendors = this.vendors.filter(v => v.id !== vendorId);
                this.applyFilters();
            }
        },

        $watch: { 'currentPage': 'paginateVendors' }
    };
}
</script>
@endsection

