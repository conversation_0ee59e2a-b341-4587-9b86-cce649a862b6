@extends('layouts.admin')

@section('content')
<div x-data="adminUsers()" class="p-6">
    <div class="mb-6 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">User Management</h1>
            <p class="text-gray-600">Manage all users on the platform.</p>
        </div>
        <button @click="openAddModal()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd" /></svg>
            Add User
        </button>
    </div>

    <!-- Filters and Search -->
    <div class="mb-4 bg-white p-4 rounded-lg shadow-md flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <input type="text" x-model="searchTerm" @input.debounce.300ms="applyFilters()" placeholder="Search by name or email..." class="form-input w-64">
            <select x-model="filterStatus" @change="applyFilters()" class="form-select">
                <option value="all">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
            <select x-model="filterRole" @change="applyFilters()" class="form-select">
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="vendor">Vendor</option>
                <option value="influencer">Influencer</option>
            </select>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <template x-for="user in paginatedUsers" :key="user.id">
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900" x-text="user.name"></div>
                            <div class="text-sm text-gray-500" x-text="user.email"></div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" 
                                :class="{ 'bg-purple-100 text-purple-800': user.userType === 'admin', 'bg-blue-100 text-blue-800': user.userType === 'vendor', 'bg-yellow-100 text-yellow-800': user.userType === 'influencer' }"
                                x-text="user.userType.charAt(0).toUpperCase() + user.userType.slice(1)"></span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="user.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                x-text="user.isActive ? 'Active' : 'Inactive'"></span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="user.registeredDate"></td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button @click="viewUser(user)" class="text-indigo-600 hover:text-indigo-900">View</button>
                            <button @click="openEditModal(user)" class="text-yellow-600 hover:text-yellow-900 ml-4">Edit</button>
                            <button @click="toggleStatus(user.id)" class="ml-4" :class="user.isActive ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'" x-text="user.isActive ? 'Deactivate' : 'Activate'"></button>
                            <button @click="deleteUser(user.id)" class="text-red-600 hover:text-red-900 ml-4">Delete</button>
                        </td>
                    </tr>
                </template>
                <template x-if="paginatedUsers.length === 0">
                    <tr><td colspan="5" class="text-center py-4">No users found.</td></tr>
                </template>
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    <div class="mt-4 flex justify-between items-center">
        <span class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * usersPerPage + 1"></span> to <span x-text="Math.min(currentPage * usersPerPage, filteredUsers.length)"></span> of <span x-text="filteredUsers.length"></span> results</span>
        <div>
            <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 border rounded disabled:opacity-50">Prev</button>
            <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 border rounded disabled:opacity-50 ml-2">Next</button>
        </div>
    </div>

    <!-- Add/Edit User Modal -->
    <div x-show="isModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="closeModal()">
        <div class="bg-white rounded-lg max-w-lg w-full p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4" x-text="isEditing ? 'Edit User' : 'Add New User'"></h3>
            <form @submit.prevent="saveUser()">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium">Name</label>
                        <input type="text" x-model="formData.name" class="form-input w-full mt-1" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium">Email</label>
                        <input type="email" x-model="formData.email" class="form-input w-full mt-1" required>
                    </div>
                    <div x-show="!isEditing">
                        <label class="block text-sm font-medium">Password</label>
                        <input type="password" x-model="formData.password" class="form-input w-full mt-1" :required="!isEditing">
                    </div>
                    <div>
                        <label class="block text-sm font-medium">User Role</label>
                        <select x-model="formData.userType" class="form-select w-full mt-1">
                            <option value="vendor">Vendor</option>
                            <option value="influencer">Influencer</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" x-model="formData.isActive" class="form-checkbox">
                            <span class="ml-2">Active</span>
                        </label>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" @click="closeModal()" class="px-4 py-2 border rounded-md">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white rounded-md" x-text="isEditing ? 'Save Changes' : 'Add User'"></button>
                </div>
            </form>
        </div>
    </div>

    <!-- View User Details Modal -->
    <div x-show="isDetailsModalOpen" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="closeModal()">
        <div class="bg-white rounded-lg max-w-2xl w-full p-6">
            <template x-if="selectedUser">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-4">User Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Basic Information</h4>
                            <p><span class="font-medium">Name:</span> <span x-text="selectedUser.name"></span></p>
                            <p><span class="font-medium">Email:</span> <span x-text="selectedUser.email"></span></p>
                            <p><span class="font-medium">Role:</span> <span x-text="selectedUser.userType"></span></p>
                            <p><span class="font-medium">Status:</span> <span x-text="selectedUser.isActive ? 'Active' : 'Inactive'"></span></p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Account Information</h4>
                            <p><span class="font-medium">Registered:</span> <span x-text="selectedUser.registeredDate"></span></p>
                            <p><span class="font-medium">Last Login:</span> <span x-text="selectedUser.lastLogin"></span></p>
                        </div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button @click="closeModal()" class="px-4 py-2 border rounded-md">Close</button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function adminUsers() {
    return {
        users: [],
        filteredUsers: [],
        paginatedUsers: [],
        searchTerm: '',
        filterStatus: 'all',
        filterRole: 'all',
        currentPage: 1,
        totalPages: 1,
        usersPerPage: 10,
        
        isModalOpen: false,
        isDetailsModalOpen: false,
        isEditing: false,
        selectedUser: null,
        formData: { id: null, name: '', email: '', password: '', userType: 'vendor', isActive: true },

        init() {
            this.users = [
                { id: 1, name: 'Admin User', email: '<EMAIL>', userType: 'admin', isActive: true, registeredDate: '2023-01-15', lastLogin: '2023-10-25' },
                { id: 2, name: 'Rahul Sharma', email: '<EMAIL>', userType: 'vendor', isActive: true, registeredDate: '2023-05-15', lastLogin: '2023-10-24' },
                { id: 3, name: 'Priya Patel', email: '<EMAIL>', userType: 'vendor', isActive: true, registeredDate: '2023-06-20', lastLogin: '2023-10-23' },
                { id: 4, name: 'Amit Kumar', email: '<EMAIL>', userType: 'vendor', isActive: true, registeredDate: '2023-07-10', lastLogin: '2023-10-22' },
                { id: 5, name: 'Neha Singh', email: '<EMAIL>', userType: 'vendor', isActive: false, registeredDate: '2023-08-05', lastLogin: '2023-09-15' },
                { id: 6, name: 'Vikram Joshi', email: '<EMAIL>', userType: 'vendor', isActive: true, registeredDate: '2023-09-15', lastLogin: '2023-10-20' },
                { id: 7, name: 'Influencer One', email: '<EMAIL>', userType: 'influencer', isActive: true, registeredDate: '2023-08-10', lastLogin: '2023-10-18' },
                { id: 8, name: 'Influencer Two', email: '<EMAIL>', userType: 'influencer', isActive: false, registeredDate: '2023-09-05', lastLogin: '2023-10-15' },
            ];
            this.applyFilters();
        },

        applyFilters() {
            let tempUsers = this.users;
            if (this.searchTerm) {
                tempUsers = tempUsers.filter(user => 
                    user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    user.email.toLowerCase().includes(this.searchTerm.toLowerCase())
                );
            }
            if (this.filterStatus !== 'all') {
                tempUsers = tempUsers.filter(user => user.isActive === (this.filterStatus === 'active'));
            }
            if (this.filterRole !== 'all') {
                tempUsers = tempUsers.filter(user => user.userType === this.filterRole);
            }
            this.filteredUsers = tempUsers;
            this.currentPage = 1;
            this.paginateUsers();
        },

        paginateUsers() {
            this.totalPages = Math.ceil(this.filteredUsers.length / this.usersPerPage);
            const start = (this.currentPage - 1) * this.usersPerPage;
            const end = start + this.usersPerPage;
            this.paginatedUsers = this.filteredUsers.slice(start, end);
        },

        resetForm() {
            this.formData = { id: null, name: '', email: '', password: '', userType: 'vendor', isActive: true };
        },

        openAddModal() {
            this.isEditing = false;
            this.resetForm();
            this.isModalOpen = true;
        },

        openEditModal(user) {
            this.isEditing = true;
            this.formData = { ...user, password: '' };
            this.isModalOpen = true;
        },

        viewUser(user) {
            this.selectedUser = user;
            this.isDetailsModalOpen = true;
        },

        closeModal() {
            this.isModalOpen = false;
            this.isDetailsModalOpen = false;
            this.selectedUser = null;
        },

        saveUser() {
            if (this.isEditing) {
                const index = this.users.findIndex(u => u.id === this.formData.id);
                if (index !== -1) {
                    this.users[index] = { ...this.users[index], ...this.formData, password: this.users[index].password }; // don't update password on edit
                }
            } else {
                this.users.unshift({ ...this.formData, id: Date.now(), registeredDate: new Date().toISOString().slice(0,10), lastLogin: null });
            }
            this.applyFilters();
            this.closeModal();
        },

        deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                this.users = this.users.filter(u => u.id !== userId);
                this.applyFilters();
            }
        },

        toggleStatus(userId) {
            const index = this.users.findIndex(u => u.id === userId);
            if (index !== -1) {
                this.users[index].isActive = !this.users[index].isActive;
                this.applyFilters();
            }
        },

        $watch: { 'currentPage': 'paginateUsers' }
    };
}
</script>
@endsection

