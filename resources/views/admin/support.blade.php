@extends('layouts.admin')

@section('content')
<div x-data="adminSupport()" class="p-6">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Support Center</h1>
        <p class="text-gray-600">Manage and respond to vendor support tickets.</p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Tickets List -->
        <div class="lg:col-span-1 bg-white rounded-lg shadow-md h-full flex flex-col">
            <div class="p-4 border-b">
                <input type="text" x-model="searchTerm" @input.debounce.300ms="filterTickets()" placeholder="Search tickets..." class="form-input w-full">
                <div class="flex justify-around mt-3 text-sm">
                    <button @click="filterStatus = 'all'; filterTickets()" :class="{ 'text-green-600 font-bold': filterStatus === 'all' }">All</button>
                    <button @click="filterStatus = 'open'; filterTickets()" :class="{ 'text-green-600 font-bold': filterStatus === 'open' }">Open</button>
                    <button @click="filterStatus = 'in_progress'; filterTickets()" :class="{ 'text-green-600 font-bold': filterStatus === 'in_progress' }">In Progress</button>
                    <button @click="filterStatus = 'closed'; filterTickets()" :class="{ 'text-green-600 font-bold': filterStatus === 'closed' }">Closed</button>
                </div>
            </div>
            <div class="flex-grow overflow-y-auto">
                <template x-for="ticket in paginatedTickets" :key="ticket.id">
                    <div @click="selectedTicket = ticket" class="p-4 border-b cursor-pointer hover:bg-gray-50" :class="{ 'bg-green-50': selectedTicket && selectedTicket.id === ticket.id }">
                        <div class="flex justify-between items-start">
                            <p class="font-semibold text-gray-800 truncate" x-text="ticket.subject"></p>
                            <span class="text-xs px-2 py-1 rounded-full" :class="getStatusBadgeColor(ticket.status)" x-text="ticket.status.replace('_', ' ')"></span>
                        </div>
                        <p class="text-sm text-gray-600" x-text="`From: ${ticket.user.name}`"></p>
                        <p class="text-xs text-gray-400 mt-1" x-text="`Last updated: ${formatDate(ticket.updatedAt)}`"></p>
                    </div>
                </template>
                <template x-if="paginatedTickets.length === 0">
                    <p class="p-4 text-gray-500">No tickets found.</p>
                </template>
            </div>
            <!-- Pagination -->
            <div class="p-4 border-t flex justify-between items-center text-sm">
                <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 border rounded disabled:opacity-50">Prev</button>
                <span>Page <span x-text="currentPage"></span> of <span x-text="totalPages"></span></span>
                <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 border rounded disabled:opacity-50">Next</button>
            </div>
        </div>

        <!-- Ticket Details -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow-md h-full flex flex-col">
            <template x-if="selectedTicket">
                <div class="flex flex-col h-full">
                    <div class="p-4 border-b">
                        <h2 class="text-xl font-bold" x-text="selectedTicket.subject"></h2>
                        <p class="text-sm text-gray-600">Ticket #<span x-text="selectedTicket.id"></span> from <span x-text="selectedTicket.user.name"></span></p>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="text-xs px-2 py-1 rounded-full" :class="getStatusBadgeColor(selectedTicket.status)" x-text="selectedTicket.status.replace('_', ' ')"></span>
                            <span class="text-xs px-2 py-1 rounded-full" :class="getPriorityBadgeColor(selectedTicket.priority)" x-text="selectedTicket.priority"></span>
                        </div>
                    </div>
                    <div class="flex-grow overflow-y-auto p-4 space-y-4">
                        <!-- Original Message -->
                        <div class="flex items-start">
                            <div class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-bold text-gray-600" x-text="selectedTicket.user.name.charAt(0)"></div>
                            <div class="ml-3 bg-gray-100 p-3 rounded-lg">
                                <p class="text-sm text-gray-800" x-text="selectedTicket.message"></p>
                                <p class="text-xs text-gray-500 mt-1 text-right" x-text="formatDate(selectedTicket.createdAt)"></p>
                            </div>
                        </div>
                        <!-- Replies -->
                        <template x-for="reply in selectedTicket.replies" :key="reply.id">
                            <div class="flex items-start" :class="{ 'justify-end': reply.user.userType === 'admin' }">
                                <div x-show="reply.user.userType !== 'admin'" class="flex-shrink-0 w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center font-bold text-gray-600" x-text="reply.user.name.charAt(0)"></div>
                                <div class="mx-3 p-3 rounded-lg" :class="reply.user.userType === 'admin' ? 'bg-green-100' : 'bg-gray-100'">
                                    <p class="text-sm text-gray-800" x-text="reply.message"></p>
                                    <p class="text-xs text-gray-500 mt-1 text-right" x-text="formatDate(reply.createdAt)"></p>
                                </div>
                                <div x-show="reply.user.userType === 'admin'" class="flex-shrink-0 w-10 h-10 rounded-full bg-green-500 flex items-center justify-center font-bold text-white">A</div>
                            </div>
                        </template>
                    </div>
                    <div class="p-4 border-t">
                        <textarea x-model="replyText" placeholder="Type your reply..." rows="3" class="form-input w-full mb-2"></textarea>
                        <div class="flex justify-between items-center">
                            <div class="flex space-x-2">
                                <button @click="handleChangeStatus('open')" class="px-3 py-1 text-sm border rounded hover:bg-gray-100">Set as Open</button>
                                <button @click="handleChangeStatus('in_progress')" class="px-3 py-1 text-sm border rounded hover:bg-gray-100">Set as In Progress</button>
                                <button @click="handleChangeStatus('closed')" class="px-3 py-1 text-sm border rounded hover:bg-gray-100">Set as Closed</button>
                            </div>
                            <button @click="handleSendReply()" :disabled="!replyText.trim()" class="bg-green-600 text-white px-4 py-2 rounded-md disabled:opacity-50">Send Reply</button>
                        </div>
                    </div>
                </div>
            </template>
            <template x-if="!selectedTicket">
                <div class="flex flex-col items-center justify-center h-full text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" /></svg>
                    <p>Select a ticket to view details</p>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function adminSupport() {
    return {
        tickets: [],
        filteredTickets: [],
        paginatedTickets: [],
        selectedTicket: null,
        replyText: '',
        searchTerm: '',
        filterStatus: 'all',
        currentPage: 1,
        totalPages: 1,
        ticketsPerPage: 10,

        init() {
            this.tickets = [
                { id: 1, subject: 'Payment issue', message: 'Payment failed.', status: 'open', priority: 'high', createdAt: '2023-10-20T10:30:00', updatedAt: '2023-10-20T10:30:00', user: { id: 2, name: 'Rahul Sharma', userType: 'vendor' }, replies: [] },
                { id: 2, subject: 'How to add products?', message: 'I need help adding products.', status: 'open', priority: 'medium', createdAt: '2023-10-19T14:45:00', updatedAt: '2023-10-19T14:45:00', user: { id: 3, name: 'Priya Patel', userType: 'vendor' }, replies: [] },
                { id: 3, subject: 'Chat flow not working', message: 'My chat flow is broken.', status: 'in_progress', priority: 'high', createdAt: '2023-10-18T09:15:00', updatedAt: '2023-10-18T11:30:00', user: { id: 4, name: 'Amit Kumar', userType: 'vendor' }, replies: [{ id: 1, message: 'We are looking into it.', createdAt: '2023-10-18T11:30:00', user: { id: 1, name: 'Admin', userType: 'admin' } }] },
                { id: 4, subject: 'Refund request', message: 'I want a refund.', status: 'closed', priority: 'medium', createdAt: '2023-10-15T16:20:00', updatedAt: '2023-10-17T14:10:00', user: { id: 5, name: 'Neha Singh', userType: 'vendor' }, replies: [{ id: 2, message: 'Refund processed.', createdAt: '2023-10-17T14:10:00', user: { id: 1, name: 'Admin', userType: 'admin' } }] },
            ];
            this.filterTickets();
        },

        filterTickets() {
            let tempTickets = this.tickets;

            if (this.filterStatus !== 'all') {
                tempTickets = tempTickets.filter(t => t.status === this.filterStatus);
            }

            if (this.searchTerm.trim() !== '') {
                tempTickets = tempTickets.filter(t => 
                    t.subject.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                    t.user.name.toLowerCase().includes(this.searchTerm.toLowerCase())
                );
            }

            this.filteredTickets = tempTickets;
            this.currentPage = 1;
            this.paginateTickets();
        },

        paginateTickets() {
            this.totalPages = Math.ceil(this.filteredTickets.length / this.ticketsPerPage);
            const start = (this.currentPage - 1) * this.ticketsPerPage;
            const end = start + this.ticketsPerPage;
            this.paginatedTickets = this.filteredTickets.slice(start, end);
        },

        handleSendReply() {
            if (!this.replyText.trim() || !this.selectedTicket) return;
            const newReply = {
                id: Date.now(),
                message: this.replyText,
                createdAt: new Date().toISOString(),
                user: { id: 1, name: 'Admin', userType: 'admin' }
            };
            this.selectedTicket.replies.push(newReply);
            this.selectedTicket.updatedAt = new Date().toISOString();
            if(this.selectedTicket.status === 'open') {
                this.selectedTicket.status = 'in_progress';
            }
            this.replyText = '';
            this.filterTickets(); // To update status color in the list
        },

        handleChangeStatus(newStatus) {
            if (!this.selectedTicket) return;
            this.selectedTicket.status = newStatus;
            this.selectedTicket.updatedAt = new Date().toISOString();
            this.filterTickets();
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleString();
        },

        getStatusBadgeColor(status) {
            switch (status) {
                case 'open': return 'bg-red-100 text-red-800';
                case 'in_progress': return 'bg-yellow-100 text-yellow-800';
                case 'closed': return 'bg-green-100 text-green-800';
                default: return 'bg-gray-100 text-gray-800';
            }
        },

        getPriorityBadgeColor(priority) {
            switch (priority) {
                case 'high': return 'bg-red-200 text-red-900';
                case 'medium': return 'bg-yellow-200 text-yellow-900';
                case 'low': return 'bg-blue-200 text-blue-900';
                default: return 'bg-gray-200 text-gray-900';
            }
        },

        $watch: { 'currentPage': 'paginateTickets' }
    };
}
</script>
@endsection

