@extends('layouts.admin')

@section('content')
<div x-data="adminSubscriptions()" class="p-6">
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-800">Subscription Plans</h1>
            <p class="text-gray-600">Manage subscription plans for your vendors.</p>
        </div>
        <button @click="openAddModal()" class="bg-green-600 text-white px-4 py-2 rounded-md flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
            Add New Plan
        </button>
    </div>

    <!-- Subscription Plans Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <template x-for="plan in subscriptionPlans" :key="plan.id">
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col" :class="{ 'border-2 border-green-500': plan.isPopular }">
                <div class="flex-grow">
                    <h3 class="text-xl font-semibold text-gray-800" x-text="plan.name"></h3>
                    <p class="text-gray-500 mt-1" x-text="plan.description"></p>
                    <div class="my-4">
                        <span class="text-4xl font-bold text-gray-900" x-text="`₹${plan.price}`"></span>
                        <span class="text-gray-500" x-text="`/${plan.interval}`"></span>
                        <template x-if="plan.originalPrice">
                            <span class="ml-2 text-gray-400 line-through" x-text="`₹${plan.originalPrice}`"></span>
                        </template>
                    </div>
                    <ul class="space-y-2 text-gray-600">
                        <template x-for="feature in plan.features" :key="feature">
                            <li class="flex items-center">
                                <svg class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                                <span x-text="feature"></span>
                            </li>
                        </template>
                    </ul>
                </div>
                <div class="mt-6 flex justify-between items-center">
                    <span class="text-sm font-medium px-3 py-1 rounded-full" :class="plan.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="plan.isActive ? 'Active' : 'Inactive'"></span>
                    <div class="flex space-x-2">
                        <button @click="openEditModal(plan)" class="text-gray-500 hover:text-blue-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" /><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" /></svg>
                        </button>
                        <button @click="handleDeletePlan(plan.id)" class="text-gray-500 hover:text-red-600">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- Add/Edit Plan Modal -->
    <div x-show="isModalOpen" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full" x-cloak>
        <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="isAddingPlan ? 'Add New Subscription Plan' : 'Edit Subscription Plan'"></h3>
                <div class="mt-2 px-7 py-3">
                    <form @submit.prevent="handleSavePlan">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                            <input type="text" x-model="formData.name" placeholder="Plan Name" class="form-input">
                            <input type="number" x-model.number="formData.price" placeholder="Price" class="form-input">
                            <input type="number" x-model.number="formData.originalPrice" placeholder="Original Price (Optional)" class="form-input">
                            <select x-model="formData.interval" class="form-select">
                                <option value="monthly">Monthly</option>
                                <option value="yearly">Yearly</option>
                            </select>
                            <input type="number" x-model.number="formData.maxProducts" placeholder="Max Products" class="form-input">
                            <input type="number" x-model.number="formData.maxChatFlows" placeholder="Max Chat Flows" class="form-input">
                            <div class="md:col-span-2">
                                <label class="block text-sm font-medium text-gray-700 mb-1">Features</label>
                                <div class="flex space-x-2 mb-2">
                                    <input type="text" x-model="newFeature" @keydown.enter.prevent="addFeature()" placeholder="Add a feature and press Enter" class="form-input flex-grow">
                                    <button type="button" @click="addFeature()" class="bg-green-500 text-white px-3 py-1 rounded-md">Add</button>
                                </div>
                                <ul class="space-y-1">
                                    <template x-for="(feature, index) in formData.features" :key="index">
                                        <li class="flex justify-between items-center bg-gray-100 p-2 rounded-md">
                                            <span x-text="feature"></span>
                                            <button type="button" @click="removeFeature(index)" class="text-red-500 hover:text-red-700">&times;</button>
                                        </li>
                                    </template>
                                </ul>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="isActive" x-model="formData.isActive" class="form-checkbox">
                                <label for="isActive" class="ml-2">Active</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="isPopular" x-model="formData.isPopular" class="form-checkbox">
                                <label for="isPopular" class="ml-2">Mark as Popular</label>
                            </div>
                        </div>
                        <div class="items-center px-4 py-3 mt-4">
                            <button type="submit" class="px-4 py-2 bg-green-600 text-white text-base font-medium rounded-md w-auto shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500">
                                Save Plan
                            </button>
                            <button type="button" @click="isModalOpen = false" class="px-4 py-2 bg-gray-200 text-gray-800 text-base font-medium rounded-md w-auto ml-2 hover:bg-gray-300">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
function adminSubscriptions() {
    return {
        subscriptionPlans: [],
        isModalOpen: false,
        isAddingPlan: false,
        editingPlanId: null,
        formData: {},
        newFeature: '',
        init() {
            // Mock data, replace with API call in a real application
            this.subscriptionPlans = [
                { id: 1, name: 'Standard', description: 'Perfect for small businesses', price: 1499, originalPrice: 1999, interval: 'yearly', maxProducts: 20, maxChatFlows: 1, features: ['20 products', '1 chat flow', 'Basic support'], isActive: true, isPopular: false },
                { id: 2, name: 'Gold', description: 'For growing businesses', price: 3000, originalPrice: 4000, interval: 'yearly', maxProducts: 50, maxChatFlows: 5, features: ['50 products', '5 chat flows', 'Priority support', 'Analytics'], isActive: true, isPopular: true },
                { id: 3, name: 'Premium', description: 'For established businesses', price: 4999, originalPrice: 6666, interval: 'yearly', maxProducts: 100, maxChatFlows: 15, features: ['100 products', '15 chat flows', 'Advanced analytics'], isActive: false, isPopular: false },
            ];
            this.resetFormData();
        },
        resetFormData() {
            this.formData = {
                name: '', description: '', price: '', originalPrice: '', interval: 'yearly',
                maxProducts: '', maxChatFlows: '', features: [], isActive: true, isPopular: false
            };
        },
        openAddModal() {
            this.isAddingPlan = true;
            this.editingPlanId = null;
            this.resetFormData();
            this.isModalOpen = true;
        },
        openEditModal(plan) {
            this.isAddingPlan = false;
            this.editingPlanId = plan.id;
            this.formData = JSON.parse(JSON.stringify(plan)); // Deep copy
            this.isModalOpen = true;
        },
        addFeature() {
            if (this.newFeature.trim() !== '') {
                this.formData.features.push(this.newFeature.trim());
                this.newFeature = '';
            }
        },
        removeFeature(index) {
            this.formData.features.splice(index, 1);
        },
        handleSavePlan() {
            if (this.isAddingPlan) {
                // Add new plan
                const newPlan = { ...this.formData, id: Date.now() };
                this.subscriptionPlans.push(newPlan);
            } else {
                // Update existing plan
                const index = this.subscriptionPlans.findIndex(p => p.id === this.editingPlanId);
                if (index !== -1) {
                    this.subscriptionPlans[index] = { ...this.formData, id: this.editingPlanId };
                }
            }
            this.isModalOpen = false;
        },
        handleDeletePlan(planId) {
            if (confirm('Are you sure you want to delete this plan?')) {
                this.subscriptionPlans = this.subscriptionPlans.filter(p => p.id !== planId);
            }
        }
    };
}
</script>
@endsection

