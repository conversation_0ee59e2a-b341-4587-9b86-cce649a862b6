<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#7ED957">

    <title>@yield('title', 'Vendor Dashboard') - {{ config('app.name', 'WhaMart') }}</title>
    <meta name="description" content="@yield('description', 'Modern vendor dashboard for WhaMart - Manage your store, products, orders, and analytics')">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" as="style">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://unpkg.com/boxicons@2.1.4/css/boxicons.min.css" rel="stylesheet">

    <!-- Global Preloader CSS -->
    <link rel="stylesheet" href="{{ asset('css/preloader.css') }}?v={{ time() }}">

    <!-- Modern Vendor Layout CSS - Desktop and Mobile separated -->
    <link rel="stylesheet" href="{{ asset('css/vendor/vendor-layout.css') }}?v={{ time() }}&red=heart" media="(min-width: 769px)">
    <link rel="stylesheet" href="{{ asset('css/vendor/vendor-layout-mobile.css') }}?v={{ time() }}&red=heart" media="(max-width: 768px)">

    <!-- Page-specific CSS -->
    @stack('page-styles')

    <!-- Chart Libraries (load before Alpine.js) -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Vendor Analytics Component -->
    <script src="{{ asset('js/vendor-analytics.js') }}"></script>

    <!-- Additional Scripts (load before Alpine.js) -->
    @stack('head-scripts')

    <!-- Alpine.js -->
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
</head>
<body class="vendor-body"
      x-data="vendorApp()"
      x-init="initApp()"
      :class="{
          'mobile-sidebar-open': mobileSidebarOpen,
          'sidebar-minimized': sidebarMinimized
      }"
      @resize.window="handleResize()"
      @keydown.escape="closeMobileSidebar()"
      @click.away="closeDropdowns()">

    <!-- Mobile Sidebar Overlay -->
    <div x-show="mobileSidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click="closeMobileSidebar()"
         class="vendor-mobile-overlay"
         :class="{ 'active': mobileSidebarOpen }"></div>

    <!-- Main App Container -->
    <div class="vendor-app">

        <!-- Vendor Sidebar -->
        <aside class="vendor-sidebar"
               :class="{ 'mobile-open': mobileSidebarOpen, 'collapsed': isOnboardingPage || sidebarMinimized }"
               x-show="!isOnboardingPage">

            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="{{ route('vendor.dashboard') }}" class="sidebar-brand">
                    <div class="brand-text">
                        <div class="brand-title">WhaMart</div>
                        <div class="brand-subtitle">Vendor Panel</div>
                    </div>
                </a>
            </div>

            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <!-- Desktop Menu (Shows all items on desktop) -->
                <ul class="nav-list desktop-menu">
                    <li class="nav-item">
                        <a href="{{ route('vendor.dashboard') }}"
                           class="nav-link {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}"
                           data-tooltip="Dashboard">
                            <i class="nav-icon fas fa-home"></i>
                            <span class="nav-item-text">Dashboard</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.analytics') }}"
                           class="nav-link {{ request()->routeIs('vendor.analytics') ? 'active' : '' }}"
                           data-tooltip="Analytics">
                            <i class="nav-icon fas fa-chart-line"></i>
                            <span class="nav-item-text">Analytics</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.orders') }}"
                           class="nav-link {{ request()->routeIs('vendor.orders*') ? 'active' : '' }}"
                           data-tooltip="Orders">
                            <i class="nav-icon fas fa-shopping-bag"></i>
                            <span class="nav-item-text">Orders</span>
                            <span class="nav-badge">{{ Auth::user()->store->orders()->count() }}</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.products.index') }}"
                           class="nav-link {{ request()->routeIs('vendor.products*') ? 'active' : '' }}"
                           data-tooltip="Products">
                            <i class="nav-icon fas fa-box"></i>
                            <span class="nav-item-text">Products</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.customers') }}"
                           class="nav-link {{ request()->routeIs('vendor.customers*') ? 'active' : '' }}"
                           data-tooltip="Customers">
                            <i class="nav-icon fas fa-users"></i>
                            <span class="nav-item-text">Customers</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <div x-data="{ open: {{ request()->routeIs('vendor.store-settings') || request()->routeIs('vendor.payment-settings') || request()->routeIs('vendor.security-settings') ? 'true' : 'false' }} }">
                            <a @click="open = !open" class="nav-link dropdown-toggle {{ request()->routeIs('vendor.store-settings') || request()->routeIs('vendor.payment-settings') || request()->routeIs('vendor.security-settings') ? 'active' : '' }}" data-tooltip="Settings">
                                <i class="nav-icon fas fa-cogs"></i>
                                <span class="nav-item-text">Settings</span>
                                <i class="fas fa-chevron-down dropdown-arrow" :class="{ 'rotate-180': open }"></i>
                            </a>
                            <div x-show="open" class="settings-submenu" style="display: none;" x-transition>
                                <a href="{{ route('vendor.store-settings') }}" class="nav-link sub-link {{ request()->routeIs('vendor.store-settings') ? 'active' : '' }}">Store Settings</a>
                                <a href="{{ route('vendor.payment-settings') }}" class="nav-link sub-link {{ request()->routeIs('vendor.payment-settings') ? 'active' : '' }}">Payment</a>
                                <a href="{{ route('vendor.security-settings') }}" class="nav-link sub-link {{ request()->routeIs('vendor.security-settings') ? 'active' : '' }}">Security</a>
                            </div>
                        </div>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.support') }}"
                           class="nav-link {{ request()->routeIs('vendor.support*') ? 'active' : '' }}"
                           data-tooltip="Support">
                            <i class="nav-icon fas fa-life-ring"></i>
                            <span class="nav-item-text">Support</span>
                        </a>
                    </li>
                </ul>

                <!-- Mobile Menu (Shows profile + specific items not in bottom nav) -->
                <ul class="nav-list mobile-menu">
                    <!-- Mobile Profile Section -->
                    <li class="nav-item mobile-profile">
                        <div class="mobile-profile-card">
                            <div class="mobile-user-info">
                                <div class="mobile-user-name">{{ auth()->user()->name ?? 'Vendor' }}</div>
                                <div class="mobile-user-role">Vendor Dashboard</div>
                                <div class="mobile-user-email">{{ auth()->user()->email ?? '<EMAIL>' }}</div>
                            </div>
                            <div class="mobile-profile-badge">
                                <i class="fas fa-check-circle"></i>
                            </div>
                        </div>
                    </li>

                    <!-- Mobile Menu Divider -->
                    <li class="nav-item mobile-divider">
                        <div class="nav-divider"></div>
                    </li>

                    <!-- Mobile-specific navigation items -->
                    <li class="nav-item">
                        <a href="{{ route('vendor.profile') }}"
                           class="nav-link {{ request()->routeIs('vendor.profile*') ? 'active' : '' }}"
                           @click="closeMobileSidebar()">
                            <i class="nav-icon fas fa-user"></i>
                            <span class="nav-item-text">My Profile</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.store-settings') }}"
                           class="nav-link {{ request()->routeIs('vendor.store-settings') || request()->routeIs('vendor.payment-settings') || request()->routeIs('vendor.security-settings') ? 'active' : '' }}"
                           @click="closeMobileSidebar()">
                            <i class="nav-icon fas fa-cog"></i>
                            <span class="nav-item-text">Settings</span>
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="{{ route('vendor.support') }}"
                           class="nav-link {{ request()->routeIs('vendor.support*') ? 'active' : '' }}"
                           @click="closeMobileSidebar()">
                            <i class="nav-icon fas fa-life-ring"></i>
                            <span class="nav-item-text">Help & Support</span>
                        </a>
                    </li>

                    <!-- Additional Mobile Options -->
                    <li class="nav-item mobile-divider">
                        <div class="nav-divider"></div>
                    </li>

                    <!-- Billing Menu - Hidden on Desktop via CSS -->
                    <li class="nav-item mobile-billing-item" style="display: none !important;">
                        <a href="#" onclick="alert('Billing feature coming soon!')"
                           class="nav-link billing-nav-link"
                           @click="closeMobileSidebar()">
                            <i class="nav-icon fas fa-credit-card"></i>
                            <span class="nav-item-text">Billing</span>
                        </a>
                    </li>

                    <!-- Logout Section for Mobile -->
                    <li class="nav-item mobile-divider">
                        <div class="nav-divider"></div>
                    </li>

                    <li class="nav-item">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="nav-link logout-btn" @click="closeMobileSidebar()">
                                <i class="nav-icon fas fa-sign-out-alt"></i>
                                <span class="nav-item-text">Logout</span>
                            </button>
                        </form>
                    </li>
                </ul>

                <!-- Desktop Sidebar Footer -->
                <div class="sidebar-footer">
                    <div class="sidebar-footer-content">
                        <div class="version-info">
                            <span class="version-text">WhaMart Version 1.10</span>
                        </div>
                        <div class="made-in-india">
                            <i class="fas fa-heart"></i>
                            <span>Made with love in India</span>
                        </div>
                    </div>
                </div>

                <!-- Mobile App Footer -->
                <div class="mobile-app-footer">
                    <div class="app-version">
                        <div class="app-name">WhaMart Vendor</div>
                        <div class="version-info">Version 1.10</div>
                    </div>
                    <div class="made-in-india">
                        <i class="fas fa-heart"></i>
                        <span>Made with love in India</span>
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTMiIHZpZXdCb3g9IjAgMCAyMCAxMyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjIwIiBoZWlnaHQ9IjQiIGZpbGw9IiNGRjk5MzMiLz4KPHJlY3QgeT0iNCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjUiIGZpbGw9IiNGRkZGRkYiLz4KPHJlY3QgeT0iOSIgd2lkdGg9IjIwIiBoZWlnaHQ9IjQiIGZpbGw9IiMxMzhBMzYiLz4KPGNpcmNsZSBjeD0iMTAiIGN5PSI2LjUiIHI9IjEuNSIgZmlsbD0iIzAwMDA4QiIvPgo8L3N2Zz4K" alt="India Flag" class="india-flag">
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content Wrapper -->
        <div class="vendor-main-wrapper">
            <!-- Mobile Header (Shows only on mobile) -->
            <header class="mobile-header">
                <div class="mobile-header-content">
                    <div class="mobile-header-left">
                        <!-- Mobile Menu Toggle -->
                        <button @click="toggleMobileSidebar()" class="mobile-header-menu-btn">
                            <i class="fas fa-bars"></i>
                        </button>
                    </div>

                    <div class="mobile-header-center">
                        <!-- Brand Text (Centered) -->
                        <span class="mobile-brand-text">WhaMart</span>
                    </div>

                    <div class="mobile-header-right">
                        <!-- Notifications - Direct Link to Notifications Page -->
                        <a href="{{ route('vendor.notifications') }}" class="mobile-header-action-btn">
                            <i class="fas fa-bell"></i>
                            <span class="mobile-notification-badge">3</span>
                        </a>
                    </div>
                </div>
            </header>

            <!-- Desktop Header (Shows only on desktop) -->
            <header class="vendor-header desktop-header">
                <div class="header-left">
                    <!-- Sidebar Toggle -->
                    <button @click="toggleSidebar()" class="header-hamburger">
                        <i class="fas fa-bars"></i>
                    </button>

                    <!-- Page Title -->
                    <div class="header-title" style="display: flex; align-items: center; min-height: 64px;">
                        <h1 class="page-title" style="margin: 0;">@yield('page-title', 'Dashboard')</h1>
                    </div>
                </div>

                <div class="header-right">
                        <!-- Notifications -->
                        <div class="dropdown" x-data="{ open: false }" @click.away="open = false">
                            <button @click="open = !open" class="header-action">
                                <i class="fas fa-bell"></i>
                                <span class="notification-badge">5</span>
                            </button>
                            <div x-show="open" 
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="dropdown-menu notification-dropdown">
                                <div class="dropdown-header">
                                    <h3>Recent Notifications</h3>
                                    <span class="notification-count">5 new</span>
                                </div>
                                
                                <!-- Notification Card 1 -->
                                <div class="notification-card">
                                    <div class="notification-icon">
                                        <i class="fas fa-shopping-bag text-primary"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">New order received</div>
                                        <div class="notification-message">Order #1234 from customer John Doe</div>
                                        <div class="notification-time">2 minutes ago</div>
                                    </div>
                                    <div class="notification-badge-dot"></div>
                                </div>
                                
                                <!-- Notification Card 2 -->
                                <div class="notification-card">
                                    <div class="notification-icon">
                                        <i class="fas fa-star text-warning"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">New review posted</div>
                                        <div class="notification-message">5-star review on "Premium T-Shirt"</div>
                                        <div class="notification-time">5 minutes ago</div>
                                    </div>
                                    <div class="notification-badge-dot"></div>
                                </div>
                                
                                <!-- Notification Card 3 -->
                                <div class="notification-card">
                                    <div class="notification-icon">
                                        <i class="fas fa-box text-info"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">Low stock alert</div>
                                        <div class="notification-message">Product "Jeans" has only 3 items left</div>
                                        <div class="notification-time">1 hour ago</div>
                                    </div>
                                    <div class="notification-badge-dot"></div>
                                </div>
                                
                                <!-- Notification Card 4 -->
                                <div class="notification-card">
                                    <div class="notification-icon">
                                        <i class="fas fa-rupee-sign text-success"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">Payment received</div>
                                        <div class="notification-message">₹2,500 payment for order #1233</div>
                                        <div class="notification-time">3 hours ago</div>
                                    </div>
                                </div>
                                
                                <!-- Notification Card 5 -->
                                <div class="notification-card">
                                    <div class="notification-icon">
                                        <i class="fas fa-users text-primary"></i>
                                    </div>
                                    <div class="notification-content">
                                        <div class="notification-title">New customer registered</div>
                                        <div class="notification-message">Welcome new customer: Sarah Smith</div>
                                        <div class="notification-time">5 hours ago</div>
                                    </div>
                                </div>
                                
                                <div class="dropdown-footer">
                                    <a href="#" class="view-all-btn">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- User Profile -->
                        <div class="dropdown" x-data="{ open: false }" @click.away="open = false">
                            <button @click="open = !open" class="header-profile">
                                <div class="profile-avatar">
                                    {{ substr(auth()->user()->name ?? 'V', 0, 1) }}
                                </div>
                                <div class="profile-info">
                                    <div class="profile-name">{{ auth()->user()->name ?? 'Vendor' }}</div>
                                    <div class="profile-role">Vendor Dashboard</div>
                                </div>
                            </button>
                            <div x-show="open" 
                                 x-transition:enter="transition ease-out duration-200"
                                 x-transition:enter-start="opacity-0 transform scale-95"
                                 x-transition:enter-end="opacity-100 transform scale-100"
                                 x-transition:leave="transition ease-in duration-75"
                                 x-transition:leave-start="opacity-100 transform scale-100"
                                 x-transition:leave-end="opacity-0 transform scale-95"
                                 class="dropdown-menu profile-dropdown">
                                <div class="dropdown-header">
                                    <div class="profile-header">
                                        <div class="profile-avatar-large">
                                            {{ substr(auth()->user()->name ?? 'V', 0, 1) }}
                                        </div>
                                        <div class="profile-details">
                                            <div class="profile-name-large">{{ auth()->user()->name ?? 'Vendor' }}</div>
                                            <div class="profile-email">{{ auth()->user()->email ?? '<EMAIL>' }}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a href="{{ route('vendor.profile') }}" class="dropdown-item">
                                    <i class="fas fa-user"></i>
                                    <span>My Profile</span>
                                </a>
                                <a href="#" onclick="alert('Billing feature coming soon!')" class="dropdown-item">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Billing</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item logout-item">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <span>Logout</span>
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
            </header>

            <!-- Main Content -->
            <main class="vendor-main" :class="{'ml-0': {{ request()->routeIs('vendor.onboarding') ? 'true' : 'false' }}}">
                <div class="page-content">
                    @yield('content')
                </div>
            </main>
        </div>
        
        <!-- Modern Native Mobile Bottom Navigation -->
        <nav class="bottom-nav" role="navigation" aria-label="Main navigation">
            <a href="{{ route('vendor.dashboard') }}" 
               class="bottom-nav-item {{ request()->routeIs('vendor.dashboard') ? 'active' : '' }}"
               aria-label="Dashboard"
               aria-current="{{ request()->routeIs('vendor.dashboard') ? 'page' : 'false' }}">
                <span class="bottom-nav-icon">🏠</span>
                <span class="bottom-nav-label">Home</span>
            </a>
            
            <a href="{{ route('vendor.orders') }}" 
               class="bottom-nav-item {{ request()->routeIs('vendor.orders*') ? 'active' : '' }}"
               aria-label="Orders"
               aria-current="{{ request()->routeIs('vendor.orders*') ? 'page' : 'false' }}">
                <span class="bottom-nav-icon">🛒</span>
                <span class="bottom-nav-label">Orders</span>
                @if(true) {{-- Replace with actual order count check --}}
                    <span class="bottom-nav-badge" aria-label="12 new orders">12</span>
                @endif
            </a>
            
            <a href="{{ route('vendor.products.index') }}" 
               class="bottom-nav-item {{ request()->routeIs('vendor.products*') ? 'active' : '' }}"
               aria-label="Products"
               aria-current="{{ request()->routeIs('vendor.products*') ? 'page' : 'false' }}">
                <span class="bottom-nav-icon">📦</span>
                <span class="bottom-nav-label">Products</span>
            </a>
            
            <a href="{{ route('vendor.analytics') }}" 
               class="bottom-nav-item {{ request()->routeIs('vendor.analytics') ? 'active' : '' }}"
               aria-label="Analytics"
               aria-current="{{ request()->routeIs('vendor.analytics') ? 'page' : 'false' }}">
                <span class="bottom-nav-icon">📊</span>
                <span class="bottom-nav-label">Stats</span>
            </a>
            
            <a href="{{ route('vendor.customers') }}" 
               class="bottom-nav-item {{ request()->routeIs('vendor.customers*') ? 'active' : '' }}"
               aria-label="Customers"
               aria-current="{{ request()->routeIs('vendor.customers*') ? 'page' : 'false' }}">
                <span class="bottom-nav-icon">👥</span>
                <span class="bottom-nav-label">Customers</span>
            </a>
        </nav>
    </div>

    <!-- Page Scripts -->
    @stack('page-scripts')

    <!-- Vendor App JavaScript -->
    <script>
        function vendorApp() {
            return {
                activeSection: new URLSearchParams(window.location.search).get('section') || 'store',
                mobileSidebarOpen: false,
                sidebarMinimized: false,
                isMobile: false,
                isOnboardingPage: {{ request()->routeIs('vendor.onboarding') ? 'true' : 'false' }},

                initApp() {
                    // Initialize app
                    this.handleResize();
                    this.isMobile = window.innerWidth <= 768;
                    
                    // Always minimize sidebar on onboarding page
                    if (this.isOnboardingPage) {
                        this.sidebarMinimized = true;
                    } else {
                        // Load sidebar state from localStorage for other pages
                        this.sidebarMinimized = localStorage.getItem('vendor-sidebar-minimized') === 'true';
                    }
                    
                    // Add mobile-specific event listeners
                    this.initializeMobileFeatures();
                    
                    // Add smooth scrolling for mobile
                    this.initializeSmoothScrolling();
                    
                    // Initialize modern mobile navigation
                    this.initializeMobileNavigation();
                },

                initializeMobileNavigation() {
                    // Add modern mobile navigation features
                    if (this.isMobile) {
                        // Add active state animations
                        this.enhanceNavigationStates();
                        
                        // Add navigation item load animations
                        this.animateNavigationLoad();
                    }
                },

                enhanceNavigationStates() {
                    // Add staggered animation for navigation items
                    const navItems = document.querySelectorAll('.bottom-nav-item');
                    navItems.forEach((item, index) => {
                        item.style.opacity = '0';
                        item.style.transform = 'translateY(20px)';
                        
                        setTimeout(() => {
                            item.style.transition = 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                            item.style.opacity = '1';
                            item.style.transform = 'translateY(0)';
                        }, index * 100);
                    });
                },

                animateNavigationLoad() {
                    // Animate bottom nav appearance
                    const bottomNav = document.querySelector('.bottom-nav');
                    if (bottomNav) {
                        bottomNav.style.transform = 'translateY(100%)';
                        bottomNav.style.opacity = '0';
                        
                        setTimeout(() => {
                            bottomNav.style.transition = 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                            bottomNav.style.transform = 'translateY(0)';
                            bottomNav.style.opacity = '1';
                        }, 300);
                    }

                    // Animate FAB appearance
                    const fab = document.querySelector('.fab');
                    if (fab) {
                        fab.style.transform = 'scale(0) rotate(180deg)';
                        fab.style.opacity = '0';
                        
                        setTimeout(() => {
                            fab.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
                            fab.style.transform = 'scale(1) rotate(0deg)';
                            fab.style.opacity = '1';
                        }, 800);
                    }
                },

                initializeMobileFeatures() {
                    // Add pull-to-refresh for mobile
                    if (this.isMobile) {
                        this.initializePullToRefresh();
                        this.initializeMobileHeaderEffects();
                    }
                    
                    // Add swipe gestures for sidebar
                    this.initializeSwipeGestures();
                    
                    // Add haptic feedback for touch devices
                    this.initializeHapticFeedback();
                },

                initializeMobileHeaderEffects() {
                    // Add scroll effects to mobile header
                    let lastScrollY = 0;
                    const mobileHeader = document.querySelector('.mobile-header');
                    
                    if (mobileHeader) {
                        window.addEventListener('scroll', () => {
                            const scrollY = window.scrollY;
                            
                            // Add shadow on scroll
                            if (scrollY > 10) {
                                mobileHeader.classList.add('scrolled');
                            } else {
                                mobileHeader.classList.remove('scrolled');
                            }
                            
                            // Auto-hide header on scroll down, show on scroll up
                            if (scrollY > lastScrollY && scrollY > 100) {
                                mobileHeader.style.transform = 'translateY(-100%)';
                            } else {
                                mobileHeader.style.transform = 'translateY(0)';
                            }
                            
                            lastScrollY = scrollY;
                        });
                    }
                },

                initializePullToRefresh() {
                    let startY = 0;
                    let currentY = 0;
                    let pullDistance = 0;
                    const threshold = 80;
                    
                    document.addEventListener('touchstart', (e) => {
                        if (window.scrollY === 0) {
                            startY = e.touches[0].clientY;
                        }
                    });
                    
                    document.addEventListener('touchmove', (e) => {
                        if (window.scrollY === 0 && startY) {
                            currentY = e.touches[0].clientY;
                            pullDistance = currentY - startY;
                            
                            if (pullDistance > 0 && pullDistance < threshold) {
                                // Add visual feedback
                                document.body.style.transform = `translateY(${pullDistance * 0.3}px)`;
                            }
                        }
                    });
                    
                    document.addEventListener('touchend', () => {
                        if (pullDistance > threshold) {
                            // Trigger refresh
                            this.refreshPage();
                        }
                        
                        // Reset
                        document.body.style.transform = '';
                        startY = 0;
                        pullDistance = 0;
                    });
                },

                initializeSwipeGestures() {
                    let startX = 0;
                    let currentX = 0;
                    
                    document.addEventListener('touchstart', (e) => {
                        startX = e.touches[0].clientX;
                    });
                    
                    document.addEventListener('touchmove', (e) => {
                        currentX = e.touches[0].clientX;
                    });
                    
                    document.addEventListener('touchend', () => {
                        const diffX = currentX - startX;
                        
                        // Swipe right to open sidebar
                        if (diffX > 50 && startX < 50 && !this.mobileSidebarOpen) {
                            this.toggleMobileSidebar();
                        }
                        
                        // Swipe left to close sidebar
                        if (diffX < -50 && this.mobileSidebarOpen) {
                            this.toggleMobileSidebar();
                        }
                        
                        startX = 0;
                        currentX = 0;
                    });
                },

                initializeHapticFeedback() {
                    // Enhanced haptic feedback for modern mobile navigation
                    if ('vibrate' in navigator) {
                        // Different vibration patterns for different interactions
                        const patterns = {
                            light: [10],
                            medium: [20],
                            success: [50, 100, 50],
                            nav: [15]
                        };

                        document.addEventListener('click', (e) => {
                            const element = e.target.closest('.bottom-nav-item, .fab, .quick-action-card, .stats-card');
                            
                            if (element) {
                                if (element.classList.contains('bottom-nav-item')) {
                                    navigator.vibrate(patterns.nav);
                                } else if (element.classList.contains('fab')) {
                                    navigator.vibrate(patterns.medium);
                                } else {
                                    navigator.vibrate(patterns.light);
                                }
                            }
                        });

                        // Add vibration for active state changes
                        document.addEventListener('touchstart', (e) => {
                            if (e.target.closest('.bottom-nav-item')) {
                                navigator.vibrate(5); // Light touch feedback
                            }
                        });
                    }

                    // Add visual feedback for touch interactions
                    this.initializeTouchFeedback();
                },

                initializeTouchFeedback() {
                    // Add modern touch feedback for bottom navigation
                    document.querySelectorAll('.bottom-nav-item').forEach(item => {
                        item.addEventListener('touchstart', function(e) {
                            this.style.transform = 'translateY(1px) scale(0.98)';
                        });

                        item.addEventListener('touchend', function(e) {
                            setTimeout(() => {
                                this.style.transform = '';
                            }, 150);
                        });

                        item.addEventListener('touchcancel', function(e) {
                            this.style.transform = '';
                        });
                    });

                    // FAB touch feedback
                    const fab = document.querySelector('.fab');
                    if (fab) {
                        fab.addEventListener('touchstart', function(e) {
                            this.style.transform = 'scale(0.95)';
                        });

                        fab.addEventListener('touchend', function(e) {
                            setTimeout(() => {
                                this.style.transform = '';
                            }, 200);
                        });
                    }
                },

                initializeSmoothScrolling() {
                    // Smooth scrolling for anchor links
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const target = document.querySelector(this.getAttribute('href'));
                            if (target) {
                                target.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        });
                    });
                },

                toggleMobileSidebar() {
                    this.mobileSidebarOpen = !this.mobileSidebarOpen;
                    
                    // Prevent body scroll when sidebar is open
                    if (this.mobileSidebarOpen) {
                        document.body.style.overflow = 'hidden';
                    } else {
                        document.body.style.overflow = '';
                    }
                },

                toggleSidebar() {
                    if (this.isMobile) {
                        this.toggleMobileSidebar();
                    } else {
                        this.sidebarMinimized = !this.sidebarMinimized;
                        localStorage.setItem('vendor-sidebar-minimized', this.sidebarMinimized);
                    }
                },

                closeMobileSidebar() {
                    this.mobileSidebarOpen = false;
                    document.body.style.overflow = '';
                },

                closeDropdowns() {
                    // Close any open dropdowns
                },

                handleResize() {
                    const wasMobile = this.isMobile;
                    this.isMobile = window.innerWidth <= 768;
                    
                    if (wasMobile !== this.isMobile) {
                        // Reset states when switching between mobile/desktop
                        this.closeMobileSidebar();
                        document.body.style.overflow = '';
                    }
                    
                    if (window.innerWidth >= 1024) {
                        this.mobileSidebarOpen = false;
                        document.body.style.overflow = '';
                    }
                },

                refreshPage() {
                    // Add loading indicator
                    const loading = document.createElement('div');
                    loading.innerHTML = `
                        <div style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); background: #7ED957; color: white; padding: 8px 16px; border-radius: 20px; z-index: 1000; font-size: 14px;">
                            <i class="fas fa-sync fa-spin"></i> Refreshing...
                        </div>
                    `;
                    document.body.appendChild(loading);
                    
                    // Simulate refresh delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 800);
                }
            }
        }
    </script>

    <!-- Global Preloader JavaScript -->
    <script src="{{ asset('js/preloader.js') }}?v={{ time() }}"></script>

    <!-- Page Scripts -->
    @stack('scripts')

</body>
</html>