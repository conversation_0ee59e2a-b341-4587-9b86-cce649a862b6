<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="scroll-smooth">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="theme-color" content="#7ED957">

        <title>Whamart - Empowering Bharat's Local Businesses</title>
        <meta name="description" content="Empower your business with Whamart: India's most modern WhatsApp commerce platform. Sell, automate, and grow with zero commission.">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="{{ asset('favicon.ico') }}">

        <!-- Google Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        
        <!-- Font Awesome -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        
        <!-- AOS Animation -->
        <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
        
        <!-- Global Preloader CSS -->
        <link rel="stylesheet" href="{{ asset('css/preloader.css') }}?v={{ time() }}">
        
        <!-- Main CSS -->
        <link rel="stylesheet" href="{{ asset('css/style.css') }}">

        @stack('head')
    </head>
    <body class="whamart-global-bg">
        <div class="global-bg-wrapper">
            @include('components.header')
            <main id="main-content">
                @yield('content')
            </main>
            @include('components.footer')
        </div> <!-- end global-bg-wrapper -->

        @stack('scripts')

        <!-- Global Preloader JavaScript -->
        <script src="{{ asset('js/preloader.js') }}?v={{ time() }}"></script>

        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const menuButton = document.getElementById('mobile-menu-button');
                const mobileMenu = document.getElementById('mobile-menu');
                const openIcon = document.getElementById('menu-open-icon');
                const closeIcon = document.getElementById('menu-close-icon');

                if (menuButton && mobileMenu && openIcon && closeIcon) {
                    menuButton.addEventListener('click', () => {
                        mobileMenu.classList.toggle('hidden');
                        openIcon.classList.toggle('hidden');
                        closeIcon.classList.toggle('hidden');
                    });
                }
            });
        </script>
        <script>
        document.addEventListener('DOMContentLoaded', function () {
            const menuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');

            if (menuButton && mobileMenu) {
                menuButton.addEventListener('click', function () {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>
</body>
</html>
