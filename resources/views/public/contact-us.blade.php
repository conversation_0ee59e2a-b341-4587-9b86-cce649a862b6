@extends('layouts.app')

@section('title', 'Contact Us')

@section('content')
<div class="font-sans bg-white text-gray-900">

    {{-- Hero Section --}}
    <section class="bg-green-50 text-center py-20 px-5">
        <div class="max-w-3xl mx-auto">
            <h1 class="text-4xl md:text-6xl font-extrabold text-gray-900 leading-tight mb-4">Get in Touch</h1>
            <p class="max-w-2xl mx-auto text-lg md:text-xl text-gray-600">
                We'd love to hear from you! Whether you have a question about features, trials, or anything else, our team is ready to answer all your questions.
            </p>
        </div>
    </section>

    {{-- Main Content --}}
    <main class="bg-white py-16 md:py-24 px-5">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">

                {{-- Contact Form --}}
                <div class="bg-gray-50 p-8 rounded-2xl shadow-lg">
                    @if(session('success'))
                        <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-6 rounded-md" role="alert">
                            <p class="font-bold">Success!</p>
                            <p>{{ session('success') }}</p>
                        </div>
                    @else
                        <h2 class="text-3xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                        <form action="{{ route('public.contact.submit') }}" method="POST" class="space-y-6">
                            @csrf
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                <input type="text" name="name" id="name" value="{{ old('name') }}" required class="mt-1 block w-full px-4 py-3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 @error('name') border-red-500 @enderror">
                                @error('name')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                <input type="email" name="email" id="email" value="{{ old('email') }}" required class="mt-1 block w-full px-4 py-3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 @error('email') border-red-500 @enderror">
                                @error('email')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                                <input type="text" name="subject" id="subject" value="{{ old('subject') }}" required class="mt-1 block w-full px-4 py-3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 @error('subject') border-red-500 @enderror">
                                @error('subject')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                                <textarea id="message" name="message" rows="5" required class="mt-1 block w-full px-4 py-3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 @error('message') border-red-500 @enderror">{{ old('message') }}</textarea>
                                @error('message')
                                    <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                            <div>
                                <button type="submit" class="w-full flex justify-center py-3 px-4 border border-transparent rounded-full shadow-sm text-lg font-medium text-white bg-green-500 hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-300">
                                    Send Message
                                </button>
                            </div>
                        </form>
                    @endif
                </div>

                {{-- Contact Information --}}
                <div class="text-gray-700">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">Contact Information</h2>
                    <p class="text-lg mb-6">You can also reach us through the following channels:</p>
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <span class="mt-1 mr-4 text-green-500"> <!-- Icon placeholder --> </span>
                            <div>
                                <h3 class="font-semibold text-lg">Email Us</h3>
                                <a href="mailto:<EMAIL>" class="text-green-600 hover:underline"><EMAIL></a>
                            </div>
                        </div>
                        <div class="flex items-start">
                             <span class="mt-1 mr-4 text-green-500"> <!-- Icon placeholder --> </span>
                            <div>
                                <h3 class="font-semibold text-lg">Call Us</h3>
                                <p>+1 (555) 123-4567</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                             <span class="mt-1 mr-4 text-green-500"> <!-- Icon placeholder --> </span>
                            <div>
                                <h3 class="font-semibold text-lg">Visit Us</h3>
                                <p>123 Commerce St, Business City, 12345</p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-8 pt-8 border-t border-gray-200">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h3>
                        <p class="text-lg">Have a question? You might find an answer in our <a href="{{ url('/help-center') }}" class="text-green-600 hover:underline">Help Center</a>.</p>
                    </div>
                </div>
            </div>
        </div>
    </main>

    {{-- Map Section --}}
    <section class="w-full h-96 bg-gray-200">
        <iframe 
            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3153.019551481055!2d-122.41941548468115!3d37.77492957975895!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x80858064a763f925%3A0x4992537d806c59e8!2sSan%20Francisco%2C%20CA%2C%20USA!5e0!3m2!1sen!2s!4v1620211161391!5m2!1sen!2s"
            width="100%" 
            height="100%" 
            style="border:0;" 
            allowfullscreen="" 
            loading="lazy">
        </iframe>
    </section>

</div>
@endsection
