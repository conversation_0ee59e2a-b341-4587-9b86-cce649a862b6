@php
    // This block can be used for any server-side logic if needed in the future.
@endphp
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhaMart Store</title>
    <link rel="stylesheet" href="{{ asset('css/whatsapp-store-theme.css') }}">
    <style>
        /* Additional custom styles */

    </style>
</head>
<body>

    <div class="store-container" id="store-container">
        <!-- Loading State -->
        <div id="loading-state" class="loading-container">
            <img src="/WhaMart_Logo.png" alt="WhaMart Logo" style="width: 120px; margin-bottom: 20px;" />
            <div class="loading-spinner"></div>
            <div class="loading-text">Loading Store...</div>
            <div style="margin-top: 10px; color: #8696a0; font-size: 12px;">दुकान Online है ❤️</div>
        </div>

        <!-- Error State -->
        <div id="error-state" class="error-container" style="display: none;">
            <div class="error-icon">⚠️</div>
            <div class="error-title">Store Not Found</div>
            <div class="error-message" id="error-message">The store you're looking for might not exist or is temporarily unavailable.</div>
            <button class="retry-btn" onclick="window.location.reload()">Try Again</button>
        </div>

        <!-- Chat Interface -->
        <div id="chat-interface" class="chat-interface" style="display: none;">
            <!-- Chat Header -->
            <div class="chat-header" id="chat-header">
                <!-- Header content will be inserted by JS -->
            </div>
            
            <!-- Chat Body -->
            <div class="chat-body" id="chat-body">
                <div class="chat-content" id="chat-content">
                    <!-- Messages will be inserted here by JS -->
                </div>
                <div id="messages-end-ref"></div>
            </div>

            <!-- Typing Indicator -->
            <div id="typing-indicator" class="typing-indicator" style="display: none;">
                <div class="typing-bubble">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="chat-input" id="chat-input">
                <!-- Input will be inserted by JS -->
            </div>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function () {
        // Element references
        const chatInterface = document.getElementById('chat-interface');
        const loadingState = document.getElementById('loading-state');
        const errorState = document.getElementById('error-state');
        const errorMessageEl = document.getElementById('error-message');
        const chatHeader = document.getElementById('chat-header');
        const chatContent = document.getElementById('chat-content');
        const chatInput = document.getElementById('chat-input');
        const typingIndicator = document.getElementById('typing-indicator');
        const messagesEndRef = document.getElementById('messages-end-ref');

        // Data from Controller
        const storeData = JSON.parse('{!! addslashes($storeData) !!}');
        const chatFlowData = {!! $chatFlowData ?? 'null' !!}; // Use null if chatFlowData is not passed
        let messages = [];

        // --- UI Rendering Functions ---
        function renderChatHeader(store) {
            const logoUrl = store.logo_url || '/store-logo.png';
            chatHeader.innerHTML = `
                <button class="header-back-btn" onclick="history.back()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
                <div class="store-avatar">
                    <img src="${logoUrl}" alt="${store.name}" onerror="this.style.display='none'; this.parentNode.innerHTML='${store.name.charAt(0).toUpperCase()}'"/>
                </div>
                <div class="store-info">
                    <div class="store-name">${store.name} ${store.is_verified ? '✓' : ''}</div>
                    <div class="store-status">Online now</div>
                </div>
                <div class="header-actions">
                    <button class="header-action-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
                        </svg>
                    </button>
                    <button class="header-action-btn">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"/>
                        </svg>
                    </button>
                </div>
            `;
        }

        function renderChatInput() {
            chatInput.innerHTML = `
                <div class="input-container">
                    <textarea id="message-input" class="message-input" placeholder="Type a message" rows="1"></textarea>
                </div>
                <button id="send-button" class="send-btn" disabled>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            `;
            
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            
            messageInput.addEventListener('input', function() {
                sendButton.disabled = !this.value.trim();
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });
            
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    handleSendMessage();
                }
            });
            
            sendButton.addEventListener('click', handleSendMessage);
        }

        function renderMessages() {
            chatContent.innerHTML = messages.map(msg => {
                const messageClass = msg.sender === 'visitor' ? 'outgoing' : 'incoming';
                const bubbleClass = msg.sender === 'visitor' ? 'outgoing' : 'incoming';
                
                return `
                <div class="message ${messageClass}">
                    <div class="message-bubble ${bubbleClass}">
                        <div class="message-text">${msg.content}</div>
                        ${msg.buttons ? `<div class="quick-replies">${msg.buttons.map(btn => `<button class="quick-reply-btn" data-next-node-id="${btn.nextNodeId}">${btn.text}</button>`).join('')}</div>` : ''}
                        <div class="message-time">${new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</div>
                    </div>
                </div>
            `}).join('');
            messagesEndRef.scrollIntoView({ behavior: 'smooth' });
        }

        // --- Event Handlers ---
        function handleSendMessage() {
            const input = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const content = input.value.trim();
            if (!content) return;

            const userMessage = {
                id: Date.now(),
                content: content,
                sender: 'visitor',
                timestamp: new Date(),
            };
            messages.push(userMessage);
            renderMessages();
            input.value = '';
            input.style.height = 'auto';
            sendButton.disabled = true;
            
            showTypingIndicator();
            setTimeout(() => {
                hideTypingIndicator();
                handleBotResponse(userMessage);
            }, 1500);
        }

        function handleQuickReply(e) {
            if (!e.target.classList.contains('quick-reply-btn')) return;
            const text = e.target.textContent;
            const nextNodeId = e.target.dataset.nextNodeId;

            const userMessage = {
                id: Date.now(),
                content: text,
                sender: 'visitor',
                timestamp: new Date(),
            };
            messages.push(userMessage);
            renderMessages();
            
            if (chatFlowData && nextNodeId) {
                const nextNode = chatFlowData.flow_data.nodes.find(node => node.id === nextNodeId);
                if (nextNode) {
                    setTimeout(() => {
                        const botMessage = createMessageFromNode(nextNode);
                        if (botMessage) {
                            messages.push(botMessage);
                            renderMessages();
                        }
                    }, 500);
                }
            }
        }
        chatContent.addEventListener('click', handleQuickReply);

        function handleBotResponse(userMessage) {
            const responseMessage = {
                id: Date.now(),
                content: "Thanks for your message! I'm a simple bot and can only respond to buttons for now.",
                sender: 'store',
                timestamp: new Date(),
            };
            messages.push(responseMessage);
            renderMessages();
        }

        function createMessageFromNode(node) {
            if (!node || !node.data || !node.data.content) return null;
            const message = {
                id: node.id,
                content: node.data.content,
                sender: 'store',
                timestamp: new Date(),
                nodeId: node.id
            };

            const outgoingEdges = chatFlowData.flow_data.edges.filter(edge => edge.source === node.id);
            if (outgoingEdges.length > 0) {
                message.buttons = outgoingEdges.map(edge => {
                    const targetNode = chatFlowData.flow_data.nodes.find(n => n.id === edge.target);
                    return {
                        text: edge.label || (targetNode && targetNode.data ? targetNode.data.buttonLabel : 'Option'),
                        nextNodeId: edge.target
                    };
                });
            }
            return message;
        }

        // --- Main Initialization Logic ---
        function initializeStore() {
            try {
                if (!storeData) {
                    throw new Error("Store data is not available.");
                }
                document.title = `${storeData.name} - WhaMart Store`;
                renderChatHeader(storeData);
                renderChatInput();

                if (chatFlowData && chatFlowData.flow_data && chatFlowData.flow_data.nodes) {
                    const startNode = chatFlowData.flow_data.nodes.find(node => node.type === 'startNode') || chatFlowData.flow_data.nodes[0];
                    if (startNode) {
                        const firstMessage = createMessageFromNode(startNode);
                        if (firstMessage) {
                            messages.push(firstMessage);
                            renderMessages();
                        }
                    }
                } else {
                    const noFlowMessage = {
                        id: 'no-flow',
                        content: `Welcome to ${storeData.name}! How can we help you today?`,
                        sender: 'store',
                        timestamp: new Date(),
                    };
                    messages.push(noFlowMessage);
                    renderMessages();
                }

                loadingState.style.display = 'none';
                chatInterface.style.display = 'flex';

            } catch (err) {
                console.error('Failed to initialize store:', err);
                errorMessageEl.textContent = 'Failed to initialize store. There might be an issue with the data or the store may not exist.';
                loadingState.style.display = 'none';
                errorState.style.display = 'flex';
            }
        }

        // Typing indicator functions
        function showTypingIndicator() {
            typingIndicator.style.display = 'flex';
            messagesEndRef.scrollIntoView({ behavior: 'smooth' });
        }
        
        function hideTypingIndicator() {
            typingIndicator.style.display = 'none';
        }

        initializeStore();
    });
    </script>
</body>
</html>
