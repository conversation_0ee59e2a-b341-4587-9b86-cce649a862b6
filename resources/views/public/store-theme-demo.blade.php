<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhaMart Store Demo</title>
    <style>
        /* Basic Reset & Fonts */
        body, html {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #f0f2f5;
            overflow: hidden; /* Full page experience */
            height: 100%;
        }

        /* Store Theme Container */
        .store-theme {
            display: flex;
            flex-direction: column;
            height: 100vh;
            width: 100%;
            max-width: 800px; /* Max-width for larger screens */
            margin: 0 auto;
            background-color: #e5ddd5;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        /* Chat Header */
        .chat-header {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: white;
            color: black;
            height: 64px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 10;
        }
        .chat-header .icon-button {
            background: none; border: none; cursor: pointer; padding: 2px; display: flex; align-items: center; justify-content: center; width: 28px; height: 28px; margin: 0 4px; color: #54656f;
        }
        .chat-header .store-info {
            display: flex; align-items: center; flex: 1; margin-left: 2px; height: 100%;
        }
        .chat-header .logo-container {
            width: 40px; height: 40px; border-radius: 50%; overflow: hidden; background-color: #f0f0f0; flex-shrink: 0; margin-right: 12px; display: flex; align-items: center; justify-content: center;
        }
        .chat-header .logo-container img {
            width: 100%; height: 100%; object-fit: cover;
        }
        .chat-header .store-name {
            font-weight: 600; font-size: 16px; color: #111b21;
        }
        .verification-badge {
            width: 16px; height: 16px; margin-left: 6px;
        }

        /* Chat Body */
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
            background-image: url('/chat-background.jpg');
            background-repeat: repeat;
            background-size: 300px auto;
            position: relative;
        }
        .chat-body-overlay {
            position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(230, 222, 178, 0.15); pointer-events: none; z-index: 1;
        }
        .chat-content {
            position: relative; z-index: 2;
        }

        /* Chat Message */
        .chat-message {
            display: flex; margin-bottom: 8px;
        }
        .chat-message.visitor {
            justify-content: flex-end;
        }
        .message-bubble {
            max-width: 75%; padding: 8px 12px; border-radius: 8px; font-size: 14px; line-height: 1.4;
        }
        .message-bubble.store {
            background-color: white; border-top-left-radius: 0;
        }
        .message-bubble.visitor {
            background-color: #dcf8c6; border-top-right-radius: 0;
        }
        .message-time {
            font-size: 11px; color: #8696a0; text-align: right; margin-top: 4px;
        }

        /* Quick Reply Buttons */
        .quick-replies {
            display: flex; flex-wrap: wrap; justify-content: flex-end; margin-top: 8px;
        }
        .quick-reply-btn {
            background-color: #fff; border: 1px solid #00a884; color: #00a884; padding: 8px 12px; border-radius: 20px; font-size: 14px; cursor: pointer; margin: 4px;
        }

        /* Chat Input Bar */
        .chat-input-bar {
            display: flex; align-items: center; padding: 6px 8px; background-color: transparent;
        }
        .input-container {
            display: flex; align-items: center; background-color: white; border-radius: 20px; padding: 6px 10px; flex: 1; margin-right: 8px; box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }
        .input-container input {
            flex: 1; border: none; outline: none; background-color: transparent; font-size: 14px; padding: 0 6px; height: 28px;
        }
        .send-btn {
            width: 36px; height: 36px; border-radius: 50%; background-color: #00a884; color: white; border: none; cursor: pointer; display: flex; align-items: center; justify-content: center; box-shadow: 0 1px 2px rgba(0,0,0,0.2); flex-shrink: 0;
        }

    </style>
</head>
<body>

    <div id="store-theme" class="store-theme">
        <!-- Chat Header will be inserted here by JS -->
        <header id="chat-header-container"></header>
        
        <!-- Chat Body -->
        <div class="chat-body" id="chat-body">
            <div class="chat-body-overlay"></div>
            <div class="chat-content" id="chat-content">
                <!-- Messages will be inserted here by JS -->
            </div>
            <div id="messages-end-ref"></div>
        </div>

        <!-- Chat Input Bar -->
        <footer id="chat-input-container"></footer>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // --- Data Initialization ---
            const storeData = JSON.parse('{!! addslashes($storeData) !!}');
            const chatFlowData = {!! $chatFlowData ?? 'null' !!};

            // --- DOM Elements ---
            const chatHeaderContainer = document.getElementById('chat-header-container');
            const chatContent = document.getElementById('chat-content');
            const chatInputContainer = document.getElementById('chat-input-container');
            const messagesEndRef = document.getElementById('messages-end-ref');

            let messages = [];

            // --- UI Rendering Functions ---
            function renderChatHeader(store) {
                const logoUrl = store.logo_url ? store.logo_url : '/store-logo.png';
                const verifiedBadge = store.is_verified ? `<img src="/verified-badge.svg" alt="Verified" class="verification-badge">` : '';
                chatHeaderContainer.innerHTML = `
                    <div class="chat-header">
                        <div class="store-info">
                            <div class="logo-container">
                                <img src="${logoUrl}" alt="${store.name} Logo">
                            </div>
                            <div>
                                <div class="store-name">${store.name}${verifiedBadge}</div>
                            </div>
                        </div>
                        <button class="icon-button"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z" /></svg></button>
                    </div>
                `;
            }

            function renderChatInputBar() {
                chatInputContainer.innerHTML = `
                    <div class="chat-input-bar">
                        <div class="input-container">
                            <input type="text" id="message-input" placeholder="Type a message..." autocomplete="off">
                        </div>
                        <button class="send-btn" id="send-btn"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-6 h-6"><path d="M3.478 2.405a.75.75 0 00-.926.94l2.432 7.905H13.5a.75.75 0 010 1.5H4.984l-2.432 7.905a.75.75 0 00.926.94 60.519 60.519 0 0018.445-8.986.75.75 0 000-1.218A60.517 60.517 0 003.478 2.405z" /></svg></button>
                    </div>
                `;
                document.getElementById('send-btn').addEventListener('click', handleSendMessage);
                document.getElementById('message-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') handleSendMessage();
                });
            }

            function renderMessages() {
                chatContent.innerHTML = '';
                messages.forEach(msg => {
                    const msgEl = document.createElement('div');
                    msgEl.className = `chat-message ${msg.sender}`;
                    const formattedTime = new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                    let buttonsHtml = '';
                    if (msg.buttons && msg.buttons.length > 0) {
                        buttonsHtml = renderButtons(msg.buttons);
                    }

                    msgEl.innerHTML = `
                        <div class="message-bubble ${msg.sender}">
                            <div>${msg.content}</div>
                            ${buttonsHtml}
                            <div class="message-time">${formattedTime}</div>
                        </div>
                    `;
                    chatContent.appendChild(msgEl);
                });
                messagesEndRef.scrollIntoView({ behavior: 'smooth' });
            }

            function renderButtons(buttons) {
                return `<div class="quick-replies">${buttons.map(btn => `<button class="quick-reply-btn" data-next-node-id="${btn.nextNodeId}">${btn.text}</button>`).join('')}</div>`;
            }

            // --- Event Handlers ---
            function handleSendMessage() {
                const input = document.getElementById('message-input');
                const content = input.value.trim();
                if (!content) return;

                const userMessage = {
                    id: Date.now(),
                    content: content,
                    sender: 'visitor',
                    timestamp: new Date(),
                };
                messages.push(userMessage);
                renderMessages();
                input.value = '';

                // Simulate bot response for demo
                setTimeout(() => {
                    const botMessage = {
                        id: Date.now() + 1,
                        content: "This is a demo. In a real store, the bot would respond to your message. For now, there's no dynamic chat flow.",
                        sender: 'store',
                        timestamp: new Date(),
                    };
                    messages.push(botMessage);
                    renderMessages();
                }, 500);
            }

            function handleQuickReply(e) {
                if (!e.target.classList.contains('quick-reply-btn')) return;
                if (!chatFlowData) return; // No chat flow in demo

                const text = e.target.textContent;
                const nextNodeId = e.target.dataset.nextNodeId;

                const userMessage = {
                    id: Date.now(),
                    content: text,
                    sender: 'visitor',
                    timestamp: new Date(),
                };
                messages.push(userMessage);

                const nextNode = chatFlowData.nodes.find(node => node.id === nextNodeId);
                if (nextNode) {
                    setTimeout(() => {
                        const botMessage = createMessageFromNode(nextNode);
                        if (botMessage) {
                            messages.push(botMessage);
                        }
                        renderMessages();
                    }, 500);
                } else {
                    renderMessages();
                }
            }
            chatContent.addEventListener('click', handleQuickReply);

            function createMessageFromNode(node) {
                if (!node || !node.data || !node.data.content) return null;
                const message = {
                    id: node.id,
                    content: node.data.content,
                    sender: 'store',
                    timestamp: new Date(),
                };

                const outgoingEdges = chatFlowData.edges.filter(edge => edge.source === node.id);
                if (outgoingEdges.length > 0) {
                    message.buttons = outgoingEdges.map(edge => ({
                        text: edge.label,
                        nextNodeId: edge.target
                    }));
                }
                return message;
            }

            // --- Main Initialization Logic ---
            function initializeChat() {
                renderChatHeader(storeData);
                renderChatInputBar();

                if (chatFlowData && chatFlowData.nodes && chatFlowData.nodes.length > 0) {
                    const startNode = chatFlowData.nodes.find(node => node.type === 'startNode');
                    if (startNode) {
                        const firstMessage = createMessageFromNode(startNode);
                        if (firstMessage) {
                            messages.push(firstMessage);
                        }
                    }
                } else {
                    // Show a default welcome message if no chat flow is available
                    messages.push({
                        id: 'welcome-message',
                        content: 'Welcome to our store! This is a demonstration of the chat interface. Type anything to see a sample response.',
                        sender: 'store',
                        timestamp: new Date(),
                    });
                }
                renderMessages();
            }

            initializeChat();
        });
    </script>
</body>
</html>
