@extends('layouts.app')

@section('title', 'Careers')



@section('content')
{{-- Hero Section --}}
<section class="bg-green-50 text-center py-20 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-extrabold text-gray-800">Join Our Team</h1>
        <p class="mt-4 text-lg text-gray-600">Help us build the future of commerce. We're looking for passionate people to join us on our mission.</p>
    </div>
</section>

{{-- Main Content --}}
<div class="bg-white py-16 sm:py-24 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">

        {{-- Company Culture Section --}}
        <section class="mb-16">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-6">Why Work With Us?</h2>
                <p class="text-lg text-gray-600 leading-relaxed">
                    At Whamart, we're more than just a company – we're a community. We foster a culture of innovation, collaboration, and growth. We believe in empowering our team members to do their best work and make a real impact.
                </p>
            </div>
        </section>

        {{-- Job Openings Section --}}
        <section>
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 text-center mb-12">Current Openings</h2>
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <ul class="divide-y divide-gray-200">
                    @forelse($jobOpenings as $job)
                        <li>
                            <a href="#" class="block hover:bg-gray-50">
                                <div class="px-4 py-4 sm:px-6">
                                    <div class="flex items-center justify-between">
                                        <p class="text-lg font-medium text-green-600 truncate">{{ $job['title'] }}</p>
                                        <div class="ml-2 flex-shrink-0 flex">
                                            <p class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                                {{ $job['type'] }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="mt-2 sm:flex sm:justify-between">
                                        <div class="sm:flex">
                                            <p class="flex items-center text-sm text-gray-500">
                                                <!-- Heroicon name: solid/users -->
                                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0010 9a5 5 0 00-3.5 1.67 6.97 6.97 0 00-1.5 4.33c0 .34.024.673.07 1h7.86zM6.5 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 003 9a5 5 0 00-3.5 1.67 6.97 6.97 0 00-1.5 4.33c0 .34.024.673.07 1h7.86z" />
                                                </svg>
                                                {{ $job['department'] }}
                                            </p>
                                            <p class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                                                <!-- Heroicon name: solid/location-marker -->
                                                <svg class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                                                </svg>
                                                {{ $job['location'] }}
                                            </p>
                                        </div>
                                        <div class="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                            <!-- Heroicon name: solid/chevron-right -->
                                            <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </a>
                        </li>
                    @empty
                        <li>
                            <div class="px-4 py-4 sm:px-6 text-center">
                                <p class="text-gray-500">No open positions at the moment. Please check back later.</p>
                            </div>
                        </li>
                    @endforelse
                </ul>
            </div>
        </section>

    </div>
</div>

{{-- CTA Section --}}
<section class="bg-gray-50 py-20 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">Don't See a Role For You?</h2>
        <p class="text-lg text-gray-600 mb-8">
            We're always looking for talented people. Send us your resume and we'll keep it on file for future openings.
        </p>
        <a href="mailto:<EMAIL>" class="inline-block bg-green-500 text-white font-bold py-3 px-8 rounded-full hover:bg-green-600 transition duration-300 shadow-lg">
            Submit Your Resume
        </a>
    </div>
</section>
@endsection

