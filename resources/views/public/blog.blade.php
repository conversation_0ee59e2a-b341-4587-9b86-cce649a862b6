@extends('layouts.app')

@section('title', 'Blog')

@section('content')
<div class="font-sans text-gray-800 bg-white">

    {{-- Hero Section --}}
    <section class="bg-green-50 text-center py-20 px-5">
        <div class="max-w-3xl mx-auto">
            <h1 class="text-4xl md:text-6xl font-extrabold text-gray-900 leading-tight mb-4">WhaMart Blog</h1>
            <p class="max-w-2xl mx-auto text-lg md:text-xl text-gray-600">
                Insights, tutorials, and success stories on the future of WhatsApp commerce.
            </p>
        </div>
    </section>

    {{-- Main Content --}}
    <main class="bg-white py-16 md:py-24 px-5">
        <div class="max-w-7xl mx-auto">
            {{-- Category Filters --}}
            <div class="mb-12">
                <div id="category-filters" class="flex flex-wrap justify-center gap-2 md:gap-4 mb-12">
                    @foreach ($categories as $category)
                        <button class="px-4 py-2 text-sm md:text-base rounded-full {{ $loop->first ? 'bg-green-500 text-white' : 'bg-white text-gray-700' }} font-semibold shadow-md transition-transform transform hover:scale-105">{{ $category }}</button>
                    @endforeach
                </div>
            </div>

            {{-- Blog Posts Grid --}}
            <div id="blog-posts-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($blogPosts as $post)
                    <div class="blog-post bg-white rounded-lg shadow-lg overflow-hidden transform hover:-translate-y-2 transition-transform duration-300 ease-in-out" data-category="{{ $post['category'] }}">
                        <a href="#" class="block">
                            <img src="{{ $post['imageUrl'] }}" alt="{{ $post['title'] }}" class="w-full h-56 object-cover">
                        </a>
                        <div class="p-6">
                            <p class="text-sm text-green-600 font-semibold">{{ $post['category'] }}</p>
                            <h3 class="mt-2 text-xl font-bold text-gray-900 h-20">
                                <a href="#" class="hover:text-green-600">{{ $post['title'] }}</a>
                            </h3>
                            <p class="mt-3 text-gray-700 h-24 overflow-hidden">{{ $post['excerpt'] }}</p>
                            <div class="mt-4 flex items-center text-sm text-gray-500">
                                <span>{{ $post['author'] }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ $post['date'] }}</span>
                                <span class="mx-2">•</span>
                                <span>{{ $post['readTime'] }}</span>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Pagination (optional) --}}
            <div class="mt-16 text-center">
                <a href="#" class="inline-block bg-gray-800 text-white font-bold py-3 px-8 rounded-full hover:bg-gray-700 transition duration-300 shadow-lg">
                    Load More Posts
                </a>
            </div>
        </div>
    </main>

    {{-- CTA Section --}}
    <section class="bg-green-50 py-16 md:py-24 px-5 text-center">
        <div class="max-w-2xl mx-auto">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Ready to Start Selling?</h2>
            <p class="text-lg text-gray-700 mb-8">
                Join thousands of businesses and start your e-commerce journey on WhatsApp today.
            </p>
            <a href="{{ url('/register') }}" class="inline-block bg-green-500 text-white font-bold py-4 px-8 rounded-full hover:bg-green-600 transition duration-300 shadow-lg text-lg">
                Get Started for Free
            </a>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const filterContainer = document.getElementById('category-filters');
        const postsGrid = document.getElementById('blog-posts-grid');
        const posts = Array.from(postsGrid.getElementsByClassName('blog-post'));
        const buttons = Array.from(filterContainer.getElementsByClassName('category-button'));

        filterContainer.addEventListener('click', function (e) {
            if (e.target.tagName !== 'BUTTON') return;

            const category = e.target.dataset.category;

            // Update button styles
            buttons.forEach(button => {
                button.classList.remove('bg-green-500', 'text-white', 'shadow-md');
                button.classList.add('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');
            });
            e.target.classList.add('bg-green-500', 'text-white', 'shadow-md');
            e.target.classList.remove('bg-gray-100', 'text-gray-700', 'hover:bg-gray-200');

            // Filter posts
            posts.forEach(post => {
                if (category === 'All' || post.dataset.category === category) {
                    post.style.display = 'block';
                } else {
                    post.style.display = 'none';
                }
            });
        });
    });
</script>
@endpush

