@extends('layouts.app')

@section('title', 'About Us')

@php
$teamMembers = [
    [
        'name' => '<PERSON><PERSON><PERSON>',
        'title' => 'CEO & Co-Founder',
        'bio' => 'A visionary leader with a background in e-commerce, <PERSON><PERSON><PERSON> is dedicated to building tools that empower entrepreneurs.',
        'image' => 'https://randomuser.me/api/portraits/men/46.jpg',
    ],
    [
        'name' => '<PERSON><PERSON>',
        'title' => 'CTO',
        'bio' => 'A tech innovator with expertise in mobile platforms, <PERSON><PERSON> oversees our product development and technical strategy.',
        'image' => 'https://randomuser.me/api/portraits/women/44.jpg',
    ],
    [
        'name' => '<PERSON><PERSON><PERSON>',
        'title' => 'Head of Customer Success',
        'bio' => 'With a passion for helping small businesses thrive, <PERSON><PERSON><PERSON> ensures our customers get the most out of Whamart.',
        'image' => 'https://randomuser.me/api/portraits/men/68.jpg',
    ],
];

$navigation = [
    'product' => [
        ['name' => 'Features', 'href' => '/#features'],
        ['name' => 'How It Works', 'href' => '/#how-it-works'],
        ['name' => 'Pricing', 'href' => '/#pricing'],
        ['name' => 'Testimonials', 'href' => '/#testimonials'],
    ],
    'support' => [
        ['name' => 'Help Center', 'href' => '/help-center'],
        ['name' => 'Contact Us', 'href' => '/contact-us'],
        ['name' => 'API Documentation', 'href' => '#'],
        ['name' => 'Status', 'href' => '#'],
    ],
    'company' => [
        ['name' => 'About', 'href' => '/about-us'],
        ['name' => 'Blog', 'href' => '/blog'],
        ['name' => 'Careers', 'href' => '/careers'],
        ['name' => 'Press', 'href' => '/press'],
    ],
    'legal' => [
        ['name' => 'Privacy', 'href' => '/privacy-policy'],
        ['name' => 'Terms', 'href' => '/terms-of-service'],
        ['name' => 'Cookie Policy', 'href' => '#'],
    ],
];
@endphp

@section('content')
<div class="font-sans text-gray-800 bg-white">

    {{-- Hero Section --}}
    <section class="bg-green-50 text-center py-20 px-5 relative overflow-hidden">
        <div class="absolute top-1/4 left-[-5%] w-32 h-32 md:w-48 md:h-48 bg-green-200 rounded-full opacity-50"></div>
        <div class="absolute bottom-1/4 right-[-5%] w-24 h-24 md:w-32 md:h-32 bg-green-200 rounded-full opacity-50"></div>
        <div class="max-w-4xl mx-auto relative z-10">
            <h1 class="text-4xl md:text-6xl font-extrabold text-gray-900 leading-tight mb-4">
                Welcome to the Future of Commerce
            </h1>
            <p class="max-w-2xl mx-auto text-lg md:text-xl text-gray-600">
                We're on a mission to make e-commerce accessible to everyone, everywhere, by transforming the world's most popular messaging app into a seamless shopping experience.
            </p>
        </div>
    </section>

    {{-- Main Content --}}
    <div class="bg-white">
        {{-- Our Story Section --}}
        <section class="py-16 md:py-24 px-5">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Story</h2>
                <p class="text-lg text-gray-700 leading-relaxed mb-4">
                    Whamart started with a simple observation: millions of small businesses in emerging markets were already using WhatsApp to connect with customers and sell their products. However, the process was manual, inefficient, and difficult to scale. We saw an opportunity to build a platform that would empower these entrepreneurs.
                </p>
                <p class="text-lg text-gray-700 leading-relaxed">
                    Founded in 2023, our team has been dedicated to creating a tool that is not only powerful but also incredibly easy to use. We believe that technology should be an enabler, not a barrier.
                </p>
            </div>
        </section>

        {{-- Our Mission Section --}}
        <section class="bg-gray-50 py-16 md:py-24 px-5">
            <div class="max-w-6xl mx-auto grid md:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Mission & Values</h2>
                    <p class="text-lg text-gray-700 leading-relaxed mb-6">
                        Our mission is to empower 10 million small businesses to thrive in the digital economy. We are driven by a core set of values that guide everything we do:
                    </p>
                    <ul class="space-y-4">
                        <li class="flex items-start">
                            <span class="text-green-500 font-bold mr-3">✓</span>
                            <span class="text-gray-700"><strong>Customer Obsession:</strong> We succeed when our customers succeed.</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 font-bold mr-3">✓</span>
                            <span class="text-gray-700"><strong>Simplicity:</strong> We build powerful tools that are intuitive and easy to use.</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 font-bold mr-3">✓</span>
                            <span class="text-gray-700"><strong>Innovation:</strong> We are constantly learning and adapting to create the future of social commerce.</span>
                        </li>
                    </ul>
                </div>
                <div>
                    <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?q=80&w=2084&auto=format&fit=crop" alt="Our team collaborating" class="rounded-lg shadow-xl w-full h-auto">
                </div>
            </div>
        </section>

        {{-- Meet the Team Section --}}
        <section class="py-16 md:py-24 px-5">
            <div class="max-w-6xl mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Meet the Team</h2>
                    <p class="text-lg text-gray-700 mt-4">The passionate individuals behind the Whamart revolution.</p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
                    @foreach ($teamMembers as $member)
                        <div class="text-center">
                            <div class="w-52 h-52 rounded-full overflow-hidden mx-auto mb-6 border-4 border-green-100">
                                <img src="{{ $member['image'] }}" alt="{{ $member['name'] }}" class="w-full h-full object-cover">
                            </div>
                            <h3 class="text-2xl font-bold mb-2 text-gray-900">{{ $member['name'] }}</h3>
                            <p class="text-green-500 font-semibold mb-4">{{ $member['title'] }}</p>
                            <p class="text-gray-600">{{ $member['description'] }}</p>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>

        {{-- CTA Section --}}
        <section class="bg-green-50 py-16 md:py-24 px-5 text-center">
            <div class="max-w-2xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Join the Whamart Revolution</h2>
                <p class="text-lg text-gray-700 mb-8">
                    Ready to transform your WhatsApp into a powerful e-commerce platform? Get started today and join thousands of businesses already growing with Whamart.
                </p>
                <a href="{{ url('/register') }}" class="inline-block bg-green-500 text-white font-bold py-4 px-8 rounded-full hover:bg-green-600 transition duration-300 shadow-lg text-lg">
                    Get Started Free
                </a>
            </div>
        </section>
    </div>
</div>
@endsection

@push('footer')
<footer class="bg-gray-900 text-white" aria-labelledby="footer-heading">
    <h2 id="footer-heading" class="sr-only">Footer</h2>
    <div class="mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32">
        <div class="xl:grid xl:grid-cols-3 xl:gap-8">
            <div>
                <a href="{{ url('/') }}" class="text-2xl font-bold text-white">
                    Whamart
                </a>
                <p class="text-sm leading-6 text-gray-300 mt-4">
                    Create your WhatsApp-style store in minutes and connect with customers through automated chat flows.
                </p>
                <div class="flex space-x-6 mt-6">
                    {{-- Social links here --}}
                </div>
            </div>
            <div class="mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0">
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold leading-6 text-white">Product</h3>
                        <ul role="list" class="mt-6 space-y-4">
                            @foreach ($navigation['product'] as $item)
                                <li><a href="{{ url($item['href']) }}" class="text-sm leading-6 text-gray-300 hover:text-white">{{ $item['name'] }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                        <h3 class="text-sm font-semibold leading-6 text-white">Support</h3>
                        <ul role="list" class="mt-6 space-y-4">
                            @foreach ($navigation['support'] as $item)
                                <li><a href="{{ str_starts_with($item['href'], '#') ? $item['href'] : url($item['href']) }}" class="text-sm leading-6 text-gray-300 hover:text-white">{{ $item['name'] }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                <div class="md:grid md:grid-cols-2 md:gap-8">
                    <div>
                        <h3 class="text-sm font-semibold leading-6 text-white">Company</h3>
                        <ul role="list" class="mt-6 space-y-4">
                            @foreach ($navigation['company'] as $item)
                                <li><a href="{{ url($item['href']) }}" class="text-sm leading-6 text-gray-300 hover:text-white">{{ $item['name'] }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                    <div class="mt-10 md:mt-0">
                        <h3 class="text-sm font-semibold leading-6 text-white">Legal</h3>
                        <ul role="list" class="mt-6 space-y-4">
                            @foreach ($navigation['legal'] as $item)
                                <li><a href="{{ str_starts_with($item['href'], '#') ? $item['href'] : url($item['href']) }}" class="text-sm leading-6 text-gray-300 hover:text-white">{{ $item['name'] }}</a></li>
                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-16 border-t border-white/10 pt-8 sm:mt-20 lg:mt-24">
            <p class="text-xs leading-5 text-gray-400">&copy; {{ date('Y') }} Whamart. All rights reserved.</p>
        </div>
    </div>
</footer>
@endpush
