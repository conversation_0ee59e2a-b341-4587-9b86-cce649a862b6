@extends('layouts.app')

@section('title', 'Help Center')

@endphp

@section('content')
<div class="font-sans bg-white text-gray-900">

    {{-- Hero Section --}}
    <section class="bg-green-50 py-16 md:py-20 px-5">
        <div class="max-w-3xl mx-auto text-center">
            <h1 class="text-4xl md:text-5xl font-extrabold text-gray-900">How can we help?</h1>
            <p class="mt-4 text-lg text-gray-600">Find answers to your questions below or contact our support team.</p>
            <div class="mt-8 max-w-lg mx-auto">
                <div class="relative">
                    <input type="search" id="faq-search" placeholder="Search for answers..." class="w-full p-4 pr-12 text-base border-gray-300 rounded-full shadow-sm focus:ring-green-500 focus:border-green-500">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    {{-- Main Content --}}
    <main class="bg-white py-16 md:py-24 px-5">
        <div class="max-w-7xl mx-auto lg:grid lg:grid-cols-12 lg:gap-8">
            
            {{-- Categories Sidebar --}}
            <aside class="lg:col-span-3">
                <h2 class="text-xl font-bold text-gray-900 mb-4">Categories</h2>
                <nav class="space-y-1">
                    @foreach($categories as $category)
                    <a href="#" 
                       class="faq-category-btn @if($loop->first) bg-green-100 text-green-700 @else text-gray-600 hover:bg-gray-100 hover:text-gray-900 @endif group flex items-center px-3 py-2 text-base font-medium rounded-md" 
                       data-category="{{ $category['id'] }}">
                        <span class="mr-3 text-lg">{{ $category['icon'] }}</span>
                        <span>{{ $category['name'] }}</span>
                    </a>
                    @endforeach
                </nav>
            </aside>

            {{-- FAQ Content --}}
            <main class="lg:col-span-9 mt-12 lg:mt-0">
                @foreach($categories as $category)
                <div id="faq-{{ $category['id'] }}" class="faq-content @unless($loop->first) hidden @endunless">
                    <h2 class="text-3xl font-bold text-gray-900 mb-6">{{ $category['name'] }}</h2>
                    <div class="space-y-4">
                        @if(isset($faqs[$category['id']]))
                            @foreach($faqs[$category['id']] as $faq)
                            <details class="p-4 border rounded-lg group faq-item" data-category="{{ $category['id'] }}">
                                <summary class="flex items-center justify-between cursor-pointer list-none">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $faq['question'] }}</h3>
                                    <span class="ml-4 group-open:rotate-180 transition-transform transform-gpu">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                                    </span>
                                </summary>
                                <div class="mt-4 text-gray-600">
                                    <p>{{ $faq['answer'] }}</p>
                                </div>
                            </details>
                            @endforeach
                        @endif
                    </div>
                </div>
                @endforeach
                <div id="no-results" class="hidden text-center py-12">
                    <h3 class="text-xl font-medium text-gray-700">No results found</h3>
                    <p class="text-gray-500 mt-2">Try searching for something else or check your spelling.</p>
                </div>
            </main>

        </div>
    </main>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const categoryButtons = document.querySelectorAll('.faq-category-btn');
        const faqContents = document.querySelectorAll('.faq-content');
        const searchInput = document.getElementById('faq-search');
        const allFaqItems = document.querySelectorAll('.faq-item');
        const noResultsMessage = document.getElementById('no-results');
        let activeCategory = '{{ $categories[0]['id'] ?? 'general' }}';

        function filterFAQs() {
            const query = searchInput.value.toLowerCase();
            let hasResults = false;

            // Hide all content sections first
            faqContents.forEach(content => content.classList.add('hidden'));

            if (query === '') {
                // If search is empty, show active category content
                const activeContent = document.getElementById(`faq-${activeCategory}`);
                if (activeContent) {
                    activeContent.classList.remove('hidden');
                    hasResults = true;
                }
            } else {
                // If searching, show all categories and filter items
                faqContents.forEach(content => content.classList.remove('hidden'));
            }
            
            allFaqItems.forEach(item => {
                const question = item.querySelector('h3').textContent.toLowerCase();
                const answer = item.querySelector('p').textContent.toLowerCase();
                const itemCategory = item.dataset.category;
                const matchesQuery = question.includes(query) || answer.includes(query);
                
                if (matchesQuery && (query !== '' || itemCategory === activeCategory)) {
                    item.classList.remove('hidden');
                    hasResults = true;
                } else {
                    item.classList.add('hidden');
                }
            });

            // Show or hide category titles based on visible items
            faqContents.forEach(content => {
                const visibleItems = content.querySelectorAll('.faq-item:not(.hidden)');
                if (visibleItems.length === 0) {
                    content.classList.add('hidden');
                } else {
                    content.classList.remove('hidden');
                }
            });

            // Show no-results message if applicable
            const anyVisibleContent = document.querySelector('.faq-content:not(.hidden)');
            noResultsMessage.classList.toggle('hidden', !!anyVisibleContent);
        }

        categoryButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                activeCategory = this.dataset.category;

                // Update button styles
                categoryButtons.forEach(btn => {
                    btn.classList.remove('bg-green-100', 'text-green-700');
                    btn.classList.add('text-gray-600', 'hover:bg-gray-100', 'hover:text-gray-900');
                });
                this.classList.add('bg-green-100', 'text-green-700');
                this.classList.remove('text-gray-600', 'hover:bg-gray-100', 'hover:text-gray-900');
                
                searchInput.value = ''; // Clear search on category change
                filterFAQs();
            });
        });

        searchInput.addEventListener('input', filterFAQs);

        // Initial filter call
        filterFAQs();
    });
</script>
@endpush
