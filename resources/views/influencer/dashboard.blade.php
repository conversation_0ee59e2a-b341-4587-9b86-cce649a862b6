@extends('layouts.admin')

@section('content')
<div x-data="influencerDashboard()" x-init="init()" class="p-6">
    <template x-if="loading">
        <div class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
            <div class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500"></div>
        </div>
    </template>

    <div x-show="!loading">
        <!-- Welcome Section -->
        <div class="relative bg-green-600 rounded-lg p-8 mb-8 overflow-hidden">
            <div class="absolute top-0 right-0 -mt-16 -mr-16 w-48 h-48 bg-green-500 rounded-full opacity-50"></div>
            <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-40 h-40 bg-green-500 rounded-full opacity-50"></div>
            <div class="relative z-10">
                <h1 class="text-4xl font-bold text-white">Welcome, <span x-text="currentUser.name"></span>!</h1>
                <p class="text-green-100 mt-2">Here's your performance summary. Keep up the great work!</p>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <template x-for="card in statsCards" :key="card.title">
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <div class="flex justify-between items-start">
                        <p class="text-sm font-medium text-gray-500" x-text="card.title"></p>
                        <div class="w-10 h-10 flex items-center justify-center rounded-full" :class="card.bgColor">
                            <svg class="h-6 w-6" :class="card.iconColor" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-html="card.icon"></svg>
                        </div>
                    </div>
                    <p class="text-3xl font-bold text-gray-900 mt-2" x-text="card.value"></p>
                    <p class="text-sm text-gray-500 mt-1 flex items-center">
                        <span :class="card.trend === 'up' ? 'text-green-500' : 'text-red-500'" class="flex items-center">
                            <svg class="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" :d="card.trend === 'up' ? 'M10 17a.75.75 0 01-.75-.75V5.612L5.03 9.77a.75.75 0 01-1.06-1.06l5.25-5.25a.75.75 0 011.06 0l5.25 5.25a.75.75 0 11-1.06 1.06L10.75 5.612V16.25A.75.75 0 0110 17z' : 'M10 3a.75.75 0 01.75.75v10.638l4.22-4.158a.75.75 0 111.06 1.06l-5.25 5.25a.75.75 0 01-1.06 0l-5.25-5.25a.75.75 0 111.06-1.06L9.25 14.388V3.75A.75.75 0 0110 3z'" clip-rule="evenodd" />
                            </svg>
                            <span x-text="card.change + '%'"></span>
                        </span>
                        <span class="ml-2">vs last month</span>
                    </p>
                </div>
            </template>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column -->
            <div class="lg:col-span-2">
                <!-- Recent Referrals -->
                <div class="bg-white rounded-lg shadow-md">
                    <div class="p-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800">Recent Referrals</h2>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <template x-for="referral in dashboardData.recentReferrals" :key="referral.id">
                                    <tr class="hover:bg-gray-50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="referral.name"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(referral.date)"></td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusBadge(referral.status).color" x-text="getStatusBadge(referral.status).text"></span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatCurrency(referral.commission)"></td>
                                    </tr>
                                </template>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-8">
                <!-- Pending Payouts -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Pending Payouts</h2>
                    <div class="space-y-4">
                        <div>
                            <p class="text-sm text-gray-500">Available for Payout</p>
                            <p class="text-2xl font-bold text-green-600" x-text="formatCurrency(dashboardData.pendingPayouts.available)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Pending Clearance</p>
                            <p class="text-lg font-medium text-gray-700" x-text="formatCurrency(dashboardData.pendingPayouts.pending)"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Next Payout Date</p>
                            <p class="text-lg font-medium text-gray-700" x-text="formatDate(dashboardData.pendingPayouts.nextPayoutDate)"></p>
                        </div>
                    </div>
                    <button class="mt-6 w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-700 transition">Request Payout</button>
                </div>

                <!-- Referrals by Platform -->
                <div class="bg-white rounded-lg shadow-md p-6">
                    <h2 class="text-xl font-semibold text-gray-800 mb-4">Referrals by Platform</h2>
                    <div class="space-y-3">
                        <template x-for="platform in dashboardData.referralsByPlatform" :key="platform.platform">
                            <div>
                                <div class="flex justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700" x-text="platform.platform"></span>
                                    <span class="text-sm font-medium text-gray-700" x-text="platform.count"></span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-green-500 h-2.5 rounded-full" :style="`width: ${getPlatformReferralWidth(platform.count)}%`"></div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Quick Actions</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 mr-4">
                        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Generate Links</h3>
                        <p class="text-sm text-gray-600 mt-1">Create custom referral links</p>
                    </div>
                </a>
                <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-purple-100 mr-4">
                        <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l-1-1m6-5l-1.414-1.414a2 2 0 00-2.828 0L11 11m0 0l-1 1m-6 5h12a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Promo Material</h3>
                        <p class="text-sm text-gray-600 mt-1">Access marketing resources</p>
                    </div>
                </a>
                <a href="#" class="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow flex items-center">
                    <div class="w-12 h-12 flex items-center justify-center rounded-full bg-yellow-100 mr-4">
                        <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">Share Content</h3>
                        <p class="text-sm text-gray-600 mt-1">Upload your promotional content</p>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
function influencerDashboard() {
    return {
        loading: true,
        currentUser: { name: 'Influencer' }, // Placeholder, should be from auth
        dashboardData: null,
        statsCards: [],

        init() {
            // Mock fetching data
            setTimeout(() => {
                this.dashboardData = this.getMockData();
                this.statsCards = this.getStatsCards();
                this.loading = false;
            }, 1000);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
        },

        getStatusBadge(status) {
            switch (status) {
                case 'visited': return { color: 'bg-blue-100 text-blue-800', text: 'Visited' };
                case 'signed_up': return { color: 'bg-yellow-100 text-yellow-800', text: 'Signed Up' };
                case 'purchased': return { color: 'bg-green-100 text-green-800', text: 'Purchased' };
                default: return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
            }
        },

        getStatsCards() {
            const overview = this.dashboardData.overview;
            return [
                { title: 'Total Referrals', value: overview.totalReferrals.value, change: overview.totalReferrals.change, trend: overview.totalReferrals.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />', bgColor: 'bg-blue-100', iconColor: 'text-blue-600' },
                { title: 'Total Signups', value: overview.totalSignups.value, change: overview.totalSignups.change, trend: overview.totalSignups.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />', bgColor: 'bg-yellow-100', iconColor: 'text-yellow-600' },
                { title: 'Total Sales', value: overview.totalSales.value, change: overview.totalSales.change, trend: overview.totalSales.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />', bgColor: 'bg-purple-100', iconColor: 'text-purple-600' },
                { title: 'Total Earnings', value: this.formatCurrency(overview.totalEarnings.value), change: overview.totalEarnings.change, trend: overview.totalEarnings.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />', bgColor: 'bg-green-100', iconColor: 'text-green-600' }
            ];
        },

        getPlatformReferralWidth(count) {
            const maxCount = Math.max(...this.dashboardData.referralsByPlatform.map(p => p.count));
            return (count / maxCount) * 100;
        },

        getMockData() {
            return {
                overview: {
                    totalReferrals: { value: 128, change: 15.2, trend: 'up' },
                    totalSignups: { value: 42, change: 8.3, trend: 'up' },
                    totalSales: { value: 18, change: 12.5, trend: 'up' },
                    totalEarnings: { value: 12450, change: 10.2, trend: 'up' }
                },
                recentReferrals: [
                    { id: 1, name: 'Rahul Sharma', date: '2023-10-25', status: 'signed_up', commission: 0 },
                    { id: 2, name: 'Priya Patel', date: '2023-10-24', status: 'purchased', commission: 1500 },
                    { id: 3, name: 'Amit Kumar', date: '2023-10-23', status: 'visited', commission: 0 },
                    { id: 4, name: 'Neha Singh', date: '2023-10-22', status: 'purchased', commission: 1200 },
                    { id: 5, name: 'Vikram Joshi', date: '2023-10-21', status: 'signed_up', commission: 0 }
                ],
                pendingPayouts: {
                    available: 2700,
                    pending: 1500,
                    nextPayoutDate: '2023-11-07'
                },
                referralsByPlatform: [
                    { platform: 'Instagram', count: 65 },
                    { platform: 'Facebook', count: 32 },
                    { platform: 'WhatsApp', count: 18 },
                    { platform: 'YouTube', count: 8 },
                    { platform: 'Twitter', count: 5 }
                ]
            };
        }
    }
}
</script>
@endsection

