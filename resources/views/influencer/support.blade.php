@extends('layouts.admin')

@section('content')
<div x-data="influencerSupport()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-8">Support Center</h1>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column: FAQ -->
        <div class="lg:col-span-2">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center mb-6">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <h2 class="text-2xl font-bold text-gray-800">Frequently Asked Questions</h2>
                </div>

                <div class="mb-6">
                    <input type="text" x-model="searchTerm" @input.debounce.300ms="filterFaqs()" placeholder="Search for questions..." class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500">
                </div>

                <div class="space-y-4">
                    <template x-for="faq in filteredFaqs" :key="faq.id">
                        <div>
                            <button @click="toggleFaq(faq.id)" class="w-full flex justify-between items-center text-left text-lg font-semibold text-gray-800 p-4 bg-gray-50 hover:bg-gray-100 rounded-lg">
                                <span x-text="faq.question"></span>
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 transition-transform" :class="{ 'rotate-180': expandedFaq === faq.id }" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg>
                            </button>
                            <div x-show="expandedFaq === faq.id" x-collapse class="p-4 bg-white border border-t-0 rounded-b-lg">
                                <p class="text-gray-600" x-text="faq.answer"></p>
                            </div>
                        </div>
                    </template>
                    <template x-if="filteredFaqs.length === 0">
                        <p class="text-center text-gray-500 py-4">No questions found matching your search.</p>
                    </template>
                </div>
            </div>
        </div>

        <!-- Right Column: Contact Form & Info -->
        <div class="space-y-8">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center mb-6">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-3 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>
                    <h2 class="text-2xl font-bold text-gray-800">Contact Support</h2>
                </div>
                <template x-if="formSubmitted">
                    <div class="text-center p-6 bg-green-50 rounded-lg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                        <p class="mt-4 font-semibold text-green-800">Your message has been sent!</p>
                        <p class="text-sm text-green-700">Our support team will get back to you shortly.</p>
                        <button @click="formSubmitted = false" class="mt-4 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">Submit Another Ticket</button>
                    </div>
                </template>
                <form x-show="!formSubmitted" @submit.prevent="handleSubmit" class="space-y-4">
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700">Subject</label>
                        <input type="text" id="subject" x-model="contactForm.subject" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700">Priority</label>
                        <select id="priority" x-model="contactForm.priority" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                            <option value="normal">Normal - General Question</option>
                            <option value="high">High - Urgent Issue</option>
                            <option value="low">Low - Feedback</option>
                        </select>
                    </div>
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700">Message</label>
                        <textarea id="message" x-model="contactForm.message" rows="5" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>
                    <div class="flex justify-end">
                        <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" /></svg>
                            Send Message
                        </button>
                    </div>
                </form>
            </div>

            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h3 class="text-xl font-bold text-gray-800 mb-4">Contact Information</h3>
                <div class="space-y-3 text-sm">
                    <div class="flex items-center"><p class="font-semibold w-24">Mon - Fri:</p><p class="text-gray-600">10:00 AM - 7:00 PM IST</p></div>
                    <div class="flex items-center"><p class="font-semibold w-24">Saturday:</p><p class="text-gray-600">10:00 AM - 5:00 PM IST</p></div>
                    <div class="flex items-center"><p class="font-semibold w-24">Email:</p><a href="mailto:<EMAIL>" class="text-green-600 hover:underline"><EMAIL></a></div>
                    <div class="flex items-center"><p class="font-semibold w-24">Phone:</p><a href="tel:+911234567890" class="text-green-600 hover:underline">+91 12345 67890</a></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function influencerSupport() {
    return {
        loading: false,
        expandedFaq: null,
        searchTerm: '',
        contactForm: { subject: '', message: '', priority: 'normal' },
        formSubmitted: false,
        faqData: [
            { id: 1, question: 'How do I earn commissions as an influencer?', answer: 'You earn a 25% commission on the first-year subscription fee for any user who signs up through your referral link.' },
            { id: 2, question: 'When can I withdraw my earnings?', answer: 'Earnings are available for withdrawal 7 days after a successful subscription purchase. The minimum withdrawal amount is ₹100.' },
            { id: 3, question: 'How do I create and share referral links?', answer: 'Go to the "Referral Links" section to create custom links. You can customize the slug and add UTM parameters for tracking.' },
            { id: 4, question: 'What promotional materials are available?', answer: 'Visit the "Promo Material" section to find banners, social media templates, and video scripts for your campaigns.' },
            { id: 5, question: 'How do I track my referral performance?', answer: 'The "Performance" section provides detailed analytics on clicks, sign-ups, conversions, and earnings for each link.' },
            { id: 6, question: 'What payment methods are available for withdrawals?', answer: 'We support withdrawals via bank transfer (NEFT/IMPS), UPI, and popular digital wallets. Manage your methods in "Settings".' },
        ],
        filteredFaqs: [],

        init() {
            this.filteredFaqs = this.faqData;
        },

        toggleFaq(id) {
            this.expandedFaq = this.expandedFaq === id ? null : id;
        },

        filterFaqs() {
            if (this.searchTerm === '') {
                this.filteredFaqs = this.faqData;
                return;
            }
            this.filteredFaqs = this.faqData.filter(faq =>
                faq.question.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                faq.answer.toLowerCase().includes(this.searchTerm.toLowerCase())
            );
        },

        handleSubmit() {
            if (!this.contactForm.subject || !this.contactForm.message) {
                alert('Please fill out all fields.');
                return;
            }
            console.log('Form submitted:', this.contactForm);
            // In a real app, you would send this to a server.
            this.formSubmitted = true;
            this.contactForm = { subject: '', message: '', priority: 'normal' };
        }
    }
}
</script>
@endsection

