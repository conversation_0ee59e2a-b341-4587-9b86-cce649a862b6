@extends('layouts.admin')

@section('content')
<div x-data="influencerContent()" x-init="fetchContent()" class="p-6">
    <template x-if="loading">
        <div class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
            <div class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500"></div>
        </div>
    </template>

    <div x-show="!loading">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Content Hub</h1>
            <button @click="handleCreateContent()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 flex items-center">
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" /></svg>
                Create Content
            </button>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button @click="activeTab = 'templates'" :class="{'border-green-500 text-green-600': activeTab === 'templates', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'templates'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        Content Templates
                    </button>
                    <button @click="activeTab = 'my-content'" :class="{'border-green-500 text-green-600': activeTab === 'my-content', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'my-content'}" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                        My Content
                    </button>
                </nav>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="mb-6 flex flex-col md:flex-row justify-between items-center gap-4">
            <div class="relative w-full md:w-1/3">
                <input type="text" x-model="searchTerm" placeholder="Search content..." class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                </div>
            </div>
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open" class="flex items-center gap-2 px-4 py-2 border rounded-lg text-gray-600 hover:bg-gray-50">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h18M6 8h12m-9 4h6" /></svg>
                    Filter by Category
                </button>
                <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20">
                    <a @click.prevent="filterCategory = 'all'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All</a>
                    <a @click.prevent="filterCategory = 'announcement'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Announcement</a>
                    <a @click.prevent="filterCategory = 'product'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Product</a>
                    <a @click.prevent="filterCategory = 'promotion'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Promotion</a>
                    <a @click.prevent="filterCategory = 'informational'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Informational</a>
                    <a @click.prevent="filterCategory = 'tutorial'" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Tutorial</a>
                </div>
            </div>
        </div>

        <!-- Content Display -->
        <div class="space-y-6">
            <!-- Templates Tab -->
            <div x-show="activeTab === 'templates'">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <template x-for="template in getFilteredContent(contentTemplates)" :key="template.id">
                        <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800 mb-2" x-text="template.title"></h3>
                                <p class="text-sm text-gray-600 mb-4 h-24 overflow-y-auto" x-text="template.content"></p>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full" x-text="template.category"></span>
                                <button @click="handleUseTemplate(template)" class="text-sm text-green-600 hover:text-green-800 font-semibold">Use Template</button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- My Content Tab -->
            <div x-show="activeTab === 'my-content'">
                <div class="space-y-6">
                    <template x-for="content in getFilteredContent(userContent)" :key="content.id">
                        <div class="bg-white rounded-lg shadow-md p-6">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900" x-text="content.title"></h3>
                                    <div class="flex items-center mt-1">
                                        <span class="text-xs font-medium px-2 py-1 rounded-full mr-2 bg-blue-100 text-blue-800" x-text="content.category"></span>
                                        <span class="text-xs text-gray-500" x-text="'For ' + content.platform"></span>
                                    </div>
                                </div>
                                <div class="flex gap-2">
                                    <button @click="handleEditContent(content)" class="p-2 rounded-md hover:bg-gray-100" title="Edit content">
                                        <svg class="h-4 w-4 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L15.232 5.232z" /></svg>
                                    </button>
                                    <button @click="handleDeleteContent(content.id)" class="p-2 rounded-md hover:bg-gray-100" title="Delete content">
                                        <svg class="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
                                    </button>
                                </div>
                            </div>
                            <div class="bg-gray-50 p-4 rounded-md mb-3">
                                <p class="text-gray-700 whitespace-pre-line" x-text="content.content"></p>
                            </div>
                            <div class="flex items-center justify-end">
                                <button @click="handleCopyContent(content.content)" class="text-gray-500 hover:text-gray-700 p-2 flex items-center text-sm">
                                    <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                                    Copy
                                </button>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <!-- Create/Edit Content Modal -->
    <div x-show="isCreatingContent || isEditingContent" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto p-8 m-4" @click.away="handleCancel()">
            <h2 class="text-2xl font-bold text-gray-800 mb-6" x-text="isEditingContent ? 'Edit Content' : 'Create New Content'"></h2>
            <form @submit.prevent="handleSubmit">
                <div class="space-y-4">
                    <div>
                        <label for="title" class="block text-sm font-medium text-gray-700">Title</label>
                        <input type="text" id="title" x-model="formData.title" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                        <label for="content" class="block text-sm font-medium text-gray-700">Content</label>
                        <textarea id="content" x-model="formData.content" rows="6" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500"></textarea>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                            <select id="category" x-model="formData.category" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                                <option value="">Select a category</option>
                                <option value="announcement">Announcement</option>
                                <option value="product">Product</option>
                                <option value="promotion">Promotion</option>
                                <option value="informational">Informational</option>
                                <option value="tutorial">Tutorial</option>
                            </select>
                        </div>
                        <div>
                            <label for="platform" class="block text-sm font-medium text-gray-700">Platform</label>
                            <select id="platform" x-model="formData.platform" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                                <option value="">Select a platform</option>
                                <option value="WhatsApp">WhatsApp</option>
                                <option value="Instagram">Instagram</option>
                                <option value="Facebook">Facebook</option>
                                <option value="Twitter">Twitter</option>
                                <option value="YouTube">YouTube</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700">Notes (optional)</label>
                        <input type="text" id="notes" x-model="formData.notes" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>
                <template x-if="error">
                    <p class="text-red-500 text-sm mt-4" x-text="error"></p>
                </template>
                <div class="flex justify-end gap-4 mt-6">
                    <button type="button" @click="handleCancel()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600" x-text="isEditingContent ? 'Save Changes' : 'Create Content'"></button>
                </div>
            </form>
        </div>
    </div>

    <!-- Copy Success Toast -->
    <div x-show="copied" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform translate-y-2" x-transition:enter-end="opacity-100 transform translate-y-0" x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 transform translate-y-0" x-transition:leave-end="opacity-0 transform translate-y-2" class="fixed bottom-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-md shadow-lg flex items-center">
        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
        Content copied to clipboard!
    </div>
</div>

<script>
function influencerContent() {
    return {
        loading: true,
        contentTemplates: [],
        userContent: [],
        searchTerm: '',
        filterCategory: 'all',
        activeTab: 'templates',
        isCreatingContent: false,
        isEditingContent: false,
        selectedContent: null,
        formData: { id: null, title: '', content: '', category: '', platform: '', notes: '' },
        error: '',
        copied: false,

        fetchContent() {
            const mockTemplates = [
                { id: 1, title: 'WhatsApp Store Introduction', content: '🛍️ Exciting news! I have partnered with WhaMart to bring you a seamless shopping experience right on WhatsApp! Browse products, place orders, and get support - all in one chat. Check out my store: [Your Referral Link]', category: 'announcement', platform: 'WhatsApp' },
                { id: 2, title: 'Product Showcase', content: '✨ Just discovered these amazing products on WhaMart! Shop directly through WhatsApp with no apps to download. Fast delivery and secure payments. Use my link to get started: [Your Referral Link] #WhaMart #OnlineShopping', category: 'product', platform: 'Instagram' },
                { id: 3, title: 'Special Offer Announcement', content: 'SPECIAL OFFER ALERT! 🔥 My friends at WhaMart are offering exclusive discounts for first-time shoppers! Click my link to browse thousands of products and shop directly through WhatsApp: [Your Referral Link]', category: 'promotion', platform: 'Facebook' },
                { id: 4, title: 'WhaMart Benefits', content: 'Shopping made simple with WhaMart:\n✅ No app downloads needed\n✅ Shop directly in WhatsApp\n✅ Secure payments\n✅ Fast delivery\n✅ 24/7 customer support\n\nTry it now: [Your Referral Link]', category: 'informational', platform: 'Twitter' },
                { id: 5, title: 'Video Script: WhaMart Tutorial', content: 'Hey everyone! Today I am going to show you how to shop on WhatsApp using WhaMart. It is super easy and convenient - no apps to download, just chat and shop! [Show screen recording of browsing a store] Use my link in the description to get started: [Your Referral Link]', category: 'tutorial', platform: 'YouTube' }
            ];
            const mockUserContent = [
                { id: 101, title: 'My WhaMart Announcement', content: 'Excited to share that I have partnered with WhaMart! Now you can shop my recommended products directly through WhatsApp. No more complicated apps or websites - just chat and shop! Check it out here: whamart.com/ref/priya123', category: 'announcement', platform: 'Instagram', notes: '' },
                { id: 102, title: 'My Favorite Healthy Snacks', content: 'You guys know I love healthy snacks! I found the best ones on WhaMart, and you can order them directly from WhatsApp. So convenient! Check them out: whamart.com/ref/priya123/snacks', category: 'product', platform: 'Instagram', notes: 'For IG story' }
            ];
            this.contentTemplates = mockTemplates;
            this.userContent = mockUserContent;
            this.loading = false;
        },

        resetForm() {
            this.formData = { id: null, title: '', content: '', category: '', platform: '', notes: '' };
            this.error = '';
        },

        handleCreateContent() {
            this.isCreatingContent = true;
            this.isEditingContent = false;
            this.resetForm();
        },

        handleEditContent(content) {
            this.isEditingContent = true;
            this.isCreatingContent = false;
            this.selectedContent = content;
            this.formData = { ...content };
        },

        handleCancel() {
            this.isCreatingContent = false;
            this.isEditingContent = false;
            this.selectedContent = null;
            this.resetForm();
        },

        handleSubmit() {
            if (!this.formData.title || !this.formData.content || !this.formData.category || !this.formData.platform) {
                this.error = 'Please fill out all required fields.';
                return;
            }

            if (this.isEditingContent) {
                const index = this.userContent.findIndex(c => c.id === this.selectedContent.id);
                if (index !== -1) {
                    this.userContent[index] = { ...this.formData };
                }
            } else {
                this.userContent.push({ ...this.formData, id: Date.now() });
            }

            this.handleCancel();
        },

        handleDeleteContent(id) {
            if (confirm('Are you sure you want to delete this content?')) {
                this.userContent = this.userContent.filter(c => c.id !== id);
            }
        },

        handleCopyContent(content) {
            navigator.clipboard.writeText(content).then(() => {
                this.copied = true;
                setTimeout(() => { this.copied = false; }, 2000);
            });
        },

        handleUseTemplate(template) {
            this.isCreatingContent = true;
            this.isEditingContent = false;
            this.resetForm();
            this.formData.title = template.title;
            this.formData.content = template.content;
            this.formData.category = template.category;
            this.formData.platform = template.platform;
        },

        getFilteredContent(contentList) {
            return contentList.filter(item => {
                const matchesSearch = item.title.toLowerCase().includes(this.searchTerm.toLowerCase()) || item.content.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesCategory = this.filterCategory === 'all' || item.category === this.filterCategory;
                return matchesSearch && matchesCategory;
            });
        }
    }
}
</script>
@endsection

