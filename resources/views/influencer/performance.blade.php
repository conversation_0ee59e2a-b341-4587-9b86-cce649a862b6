@extends('layouts.admin')

@section('content')
<div x-data="influencerPerformance()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">Performance Dashboard</h1>

        <!-- Performance Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <template x-for="card in statsCards" :key="card.title">
                <div class="bg-white p-6 rounded-lg shadow-md flex items-center justify-between">
                    <div>
                        <p class="text-sm text-gray-500" x-text="card.title"></p>
                        <p class="text-3xl font-bold text-gray-800" x-text="card.value"></p>
                        <div class="flex items-center text-sm mt-1" :class="card.trend === 'up' ? 'text-green-500' : 'text-red-500'">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path x-show="card.trend === 'up'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                                <path x-show="card.trend !== 'up'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                            </svg>
                            <span x-text="card.change + '% vs last month'"></span>
                        </div>
                    </div>
                    <div class="p-3 rounded-full" :class="card.iconBgColor">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" :class="card.iconTextColor" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-html="card.icon"></svg>
                    </div>
                </div>
            </template>
        </div>

        <!-- Referrals List -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-bold text-gray-700 mb-4">Referral Details</h2>
            
            <!-- Filters and Search -->
            <div class="flex flex-col md:flex-row justify-between items-center mb-4 space-y-4 md:space-y-0">
                <div class="relative w-full md:w-1/3">
                    <input type="text" x-model="searchTerm" @input.debounce.300ms="handleSearchChange" placeholder="Search by name or link..." class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                    <div class="absolute top-0 left-0 inline-flex items-center p-2 mt-1 ml-2 text-gray-400">
                         <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                    </div>
                </div>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">Status:</span>
                    <select x-model="filterStatus" @change="handleFilterChange" class="border rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500">
                        <option value="all">All</option>
                        <option value="purchased">Purchased</option>
                        <option value="signed_up">Signed Up</option>
                        <option value="visited">Visited</option>
                    </select>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Referral</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Date</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Platform</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Status</th>
                            <th class="text-right py-3 px-4 uppercase font-semibold text-sm text-gray-600">Commission</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <template x-for="referral in paginatedReferrals" :key="referral.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="py-3 px-4">
                                    <p class="font-medium" x-text="referral.name"></p>
                                    <p class="text-sm text-gray-500" x-text="referral.link"></p>
                                </td>
                                <td class="py-3 px-4" x-text="formatDate(referral.date)"></td>
                                <td class="py-3 px-4" x-text="referral.platform"></td>
                                <td class="py-3 px-4">
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="getStatusBadge(referral.status).color" x-text="getStatusBadge(referral.status).text"></span>
                                </td>
                                <td class="py-3 px-4 text-right font-medium text-green-600" x-text="referral.commission > 0 ? formatCurrency(referral.commission) : '-'"></td>
                            </tr>
                        </template>
                         <template x-if="paginatedReferrals.length === 0">
                            <tr>
                                <td colspan="5" class="text-center py-6 text-gray-500">No referrals found.</td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="flex justify-between items-center mt-4">
                <p class="text-sm text-gray-600">Showing <span x-text="(currentPage - 1) * 10 + 1"></span> to <span x-text="Math.min(currentPage * 10, filteredReferrals.length)"></span> of <span x-text="filteredReferrals.length"></span> referrals</p>
                <div class="flex space-x-2">
                    <button @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 rounded-lg" :class="{'bg-gray-200 text-gray-400 cursor-not-allowed': currentPage === 1, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== 1}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <template x-for="page in Array.from({ length: totalPages }, (_, i) => i + 1)">
                        <button @click="handlePageChange(page)" class="px-3 py-1 rounded-lg" :class="{'bg-green-500 text-white': currentPage === page, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== page}" x-text="page"></button>
                    </template>
                    <button @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 rounded-lg" :class="{'bg-gray-200 text-gray-400 cursor-not-allowed': currentPage === totalPages, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== totalPages}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function influencerPerformance() {
        return {
            loading: false,
            performanceData: null,
            allReferrals: [],
            filteredReferrals: [],
            currentPage: 1,
            totalPages: 1,
            searchTerm: '',
            filterStatus: 'all',

            init() {
                this.fetchPerformanceData();
            },

            fetchPerformanceData() {
                this.loading = true;
                // Mock data
                this.performanceData = {
                    overview: {
                        totalReferrals: { value: 128, change: 15.2, trend: 'up' },
                        totalSignups: { value: 42, change: 8.3, trend: 'up' },
                        totalSales: { value: 18, change: 12.5, trend: 'up' },
                        totalEarnings: { value: 12450, change: 10.2, trend: 'up' }
                    }
                };
                this.allReferrals = [
                    { id: 1, name: 'Rahul Sharma', date: '2023-10-25', status: 'signed_up', commission: 0, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
                    { id: 2, name: 'Priya Patel', date: '2023-10-24', status: 'purchased', commission: 1500, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
                    { id: 3, name: 'Amit Kumar', date: '2023-10-23', status: 'visited', commission: 0, platform: 'WhatsApp', link: 'whamart.com/ref/rahul123' },
                    { id: 4, name: 'Neha Singh', date: '2023-10-22', status: 'purchased', commission: 1200, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
                    { id: 5, name: 'Vikram Joshi', date: '2023-10-21', status: 'signed_up', commission: 0, platform: 'WhatsApp', link: 'whamart.com/ref/rahul123' },
                    { id: 6, name: 'Ananya Desai', date: '2023-10-20', status: 'purchased', commission: 900, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
                    { id: 7, name: 'Rajesh Gupta', date: '2023-10-19', status: 'visited', commission: 0, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
                    { id: 8, name: 'Sonia Mehta', date: '2023-10-18', status: 'signed_up', commission: 0, platform: 'YouTube', link: 'whamart.com/ref/rahul123' },
                    { id: 9, name: 'Kiran Shah', date: '2023-10-17', status: 'purchased', commission: 1800, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
                    { id: 10, name: 'Pooja Mehta', date: '2023-10-15', status: 'purchased', commission: 1350, platform: 'Facebook', link: 'whamart.com/ref/rahul123' },
                    { id: 11, name: 'Ravi Patel', date: '2023-10-12', status: 'purchased', commission: 2100, platform: 'Twitter', link: 'whamart.com/ref/rahul123' },
                    { id: 12, name: 'Anil Kapoor', date: '2023-10-08', status: 'purchased', commission: 1650, platform: 'Instagram', link: 'whamart.com/ref/rahul123' },
                ];
                this.updateFilteredReferrals();
                this.loading = false;
            },

            updateFilteredReferrals() {
                this.filteredReferrals = this.allReferrals.filter(referral => {
                    const searchMatch = this.searchTerm.toLowerCase() === '' || referral.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || referral.link.toLowerCase().includes(this.searchTerm.toLowerCase());
                    const statusMatch = this.filterStatus === 'all' || referral.status === this.filterStatus;
                    return searchMatch && statusMatch;
                });
                this.totalPages = Math.ceil(this.filteredReferrals.length / 10);
                this.currentPage = 1;
            },

            handleSearchChange() {
                this.updateFilteredReferrals();
            },

            handleFilterChange() {
                this.updateFilteredReferrals();
            },

            handlePageChange(page) {
                if (page > 0 && page <= this.totalPages) {
                    this.currentPage = page;
                }
            },

            formatCurrency(amount) {
                return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
            },

            formatDate(dateString) {
                return new Date(dateString).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' });
            },

            getStatusBadge(status) {
                switch (status) {
                    case 'purchased': return { text: 'Purchased', color: 'bg-green-100 text-green-800' };
                    case 'signed_up': return { text: 'Signed Up', color: 'bg-blue-100 text-blue-800' };
                    case 'visited': return { text: 'Visited', color: 'bg-yellow-100 text-yellow-800' };
                    default: return { text: 'Unknown', color: 'bg-gray-100 text-gray-800' };
                }
            },

            get statsCards() {
                if (!this.performanceData) return [];
                const data = this.performanceData.overview;
                return [
                    { title: 'Total Referrals', value: data.totalReferrals.value, change: data.totalReferrals.change, trend: data.totalReferrals.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />', iconBgColor: 'bg-blue-100', iconTextColor: 'text-blue-600' },
                    { title: 'Total Signups', value: data.totalSignups.value, change: data.totalSignups.change, trend: data.totalSignups.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />', iconBgColor: 'bg-indigo-100', iconTextColor: 'text-indigo-600' },
                    { title: 'Total Sales', value: data.totalSales.value, change: data.totalSales.change, trend: data.totalSales.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />', iconBgColor: 'bg-purple-100', iconTextColor: 'text-purple-600' },
                    { title: 'Total Earnings', value: this.formatCurrency(data.totalEarnings.value), change: data.totalEarnings.change, trend: data.totalEarnings.trend, icon: '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v.01" />', iconBgColor: 'bg-green-100', iconTextColor: 'text-green-600' },
                ];
            },

            get paginatedReferrals() {
                const start = (this.currentPage - 1) * 10;
                const end = start + 10;
                return this.filteredReferrals.slice(start, end);
            }
        }
    }
</script>
@endsection

