@extends('layouts.admin')

@section('content')
<div x-data="influencerWithdrawals()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Withdrawals</h1>

    <!-- Balance & Request Section -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <p class="text-sm text-gray-500">Available for Withdrawal</p>
            <p class="text-3xl font-bold text-green-600" x-text="formatCurrency(withdrawalData.availableBalance)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg text-center">
            <p class="text-sm text-gray-500">Pending Balance</p>
            <p class="text-3xl font-bold text-yellow-600" x-text="formatCurrency(withdrawalData.pendingBalance)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg flex items-center justify-center">
            <button @click="showWithdrawalRequest = true" class="w-full bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 font-semibold text-lg flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                Request Withdrawal
            </button>
        </div>
    </div>

    <!-- Withdrawal Request Form -->
    <template x-if="showWithdrawalRequest">
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4">New Withdrawal Request</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Amount (INR)</label>
                    <input type="number" x-model.number="withdrawalAmount" :max="withdrawalData.availableBalance" :min="withdrawalData.minimumWithdrawal" placeholder="Enter amount" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                    <p class="text-xs text-gray-500 mt-1">Minimum: <span x-text="formatCurrency(withdrawalData.minimumWithdrawal)"></span></p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Payment Method</label>
                    <select x-model="withdrawalMethod" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                        <template x-for="method in withdrawalData.paymentMethods" :key="method.id">
                            <option :value="method.id" x-text="method.name"></option>
                        </template>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex justify-end space-x-4">
                <button @click="showWithdrawalRequest = false" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300">Cancel</button>
                <button @click="handleSubmitWithdrawal()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">Submit Request</button>
            </div>
        </div>
    </template>

    <!-- Withdrawal History -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Withdrawal History</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference ID</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="item in paginatedHistory" :key="item.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="formatDate(item.date)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-800" x-text="formatCurrency(item.amount)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="getPaymentMethodName(item.method)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusBadge(item.status).color" x-text="getStatusBadge(item.status).text"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="item.reference"></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        <div class="mt-4 flex justify-between items-center">
            <p class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * itemsPerPage + 1"></span> to <span x-text="Math.min(currentPage * itemsPerPage, withdrawalHistory.length)"></span> of <span x-text="withdrawalHistory.length"></span> results</p>
            <div>
                <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 border rounded-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50">Previous</button>
                <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages" class="ml-2 px-3 py-1 border rounded-md bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50">Next</button>
            </div>
        </div>
    </div>
</div>

<script>
function influencerWithdrawals() {
    return {
        loading: false,
        withdrawalData: { availableBalance: 0, pendingBalance: 0, minimumWithdrawal: 100, paymentMethods: [] },
        withdrawalHistory: [],
        showWithdrawalRequest: false,
        withdrawalAmount: '',
        withdrawalMethod: 'bank_transfer',
        currentPage: 1,
        itemsPerPage: 5,

        init() {
            this.fetchWithdrawalData();
        },

        fetchWithdrawalData() {
            this.loading = true;
            setTimeout(() => {
                this.withdrawalData = {
                    availableBalance: 2700,
                    pendingBalance: 1500,
                    minimumWithdrawal: 100,
                    paymentMethods: [
                        { id: 'bank_transfer', name: 'Bank Transfer' },
                        { id: 'upi', name: 'UPI' },
                        { id: 'paytm', name: 'Paytm' }
                    ]
                };
                this.withdrawalHistory = [
                    { id: 1, amount: 1200, status: 'completed', date: '2023-10-15', method: 'bank_transfer', reference: 'WD123456' },
                    { id: 2, amount: 800, status: 'completed', date: '2023-09-20', method: 'upi', reference: 'WD123455' },
                    { id: 3, amount: 1500, status: 'processing', date: '2023-10-25', method: 'bank_transfer', reference: 'WD123457' },
                    { id: 4, amount: 500, status: 'rejected', date: '2023-08-10', method: 'paytm', reference: 'WD123454' },
                    { id: 5, amount: 1000, status: 'completed', date: '2023-07-25', method: 'bank_transfer', reference: 'WD123453' },
                    { id: 6, amount: 600, status: 'completed', date: '2023-06-15', method: 'upi', reference: 'WD123452' },
                ];
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', minimumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
        },

        getPaymentMethodName(methodId) {
            const method = this.withdrawalData.paymentMethods.find(m => m.id === methodId);
            return method ? method.name : 'Unknown';
        },

        getStatusBadge(status) {
            switch (status) {
                case 'completed': return { color: 'bg-green-100 text-green-800', text: 'Completed' };
                case 'processing': return { color: 'bg-blue-100 text-blue-800', text: 'Processing' };
                case 'rejected': return { color: 'bg-red-100 text-red-800', text: 'Rejected' };
                default: return { color: 'bg-gray-100 text-gray-800', text: 'Pending' };
            }
        },

        handleSubmitWithdrawal() {
            if (this.withdrawalAmount < this.withdrawalData.minimumWithdrawal) {
                alert(`Minimum withdrawal amount is ${this.formatCurrency(this.withdrawalData.minimumWithdrawal)}.`);
                return;
            }
            if (this.withdrawalAmount > this.withdrawalData.availableBalance) {
                alert('Withdrawal amount cannot exceed available balance.');
                return;
            }

            const newWithdrawal = {
                id: Date.now(),
                amount: this.withdrawalAmount,
                status: 'processing',
                date: new Date().toISOString().split('T')[0],
                method: this.withdrawalMethod,
                reference: `WD${Date.now()}`
            };

            this.withdrawalHistory.unshift(newWithdrawal);
            this.withdrawalData.availableBalance -= this.withdrawalAmount;
            this.showWithdrawalRequest = false;
            this.withdrawalAmount = '';
            alert('Withdrawal request submitted successfully!');
        },

        get totalPages() {
            return Math.ceil(this.withdrawalHistory.length / this.itemsPerPage);
        },

        get paginatedHistory() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.withdrawalHistory.slice(start, end);
        },

        changePage(page) {
            if (page > 0 && page <= this.totalPages) {
                this.currentPage = page;
            }
        }
    }
}
</script>
@endsection

