@extends('layouts.admin')

@section('content')
<div x-data="influencerProfile()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">My Profile</h1>
            <div>
                <template x-if="!isEditing">
                    <button @click="toggleEdit()" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L16.732 3.732z" /></svg>
                        Edit Profile
                    </button>
                </template>
                <template x-if="isEditing">
                    <div class="flex space-x-2">
                        <button @click="handleSaveProfile()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                            Save Changes
                        </button>
                        <button @click="handleCancelEdit()" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                            Cancel
                        </button>
                    </div>
                </template>
            </div>
        </div>

        <!-- Profile Details -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Profile Picture -->
            <div class="md:col-span-1 flex flex-col items-center">
                <div class="relative">
                    <img :src="profilePicturePreview || formData.profilePicture || 'https://via.placeholder.com/150'" alt="Profile Picture" class="w-40 h-40 rounded-full object-cover shadow-md">
                    <template x-if="isEditing">
                        <div class="absolute bottom-0 right-0 flex">
                            <label for="profile-picture-upload" class="bg-blue-500 text-white p-2 rounded-full cursor-pointer hover:bg-blue-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                                <input id="profile-picture-upload" type="file" class="hidden" @change="handleProfilePictureUpload">
                            </label>
                            <button @click="handleRemoveProfilePicture()" class="bg-red-500 text-white p-2 rounded-full ml-2 hover:bg-red-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
                            </button>
                        </div>
                    </template>
                </div>
                <h2 class="text-2xl font-bold mt-4" x-text="formData.name"></h2>
                <p class="text-gray-500" x-text="formData.email"></p>
            </div>

            <!-- Profile Form -->
            <div class="md:col-span-2">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <input type="text" x-model="formData.name" :disabled="!isEditing" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email Address</label>
                        <input type="email" x-model="formData.email" disabled class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input type="text" x-model="formData.phone" :disabled="!isEditing" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100">
                    </div>
                    <div class="sm:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Bio</label>
                        <textarea x-model="formData.bio" :disabled="!isEditing" rows="4" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <!-- Social Links -->
        <div class="mt-10">
            <h2 class="text-xl font-bold text-gray-700 mb-4">Social Links</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <template x-for="(link, platform) in formData.socialLinks" :key="platform">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 capitalize" x-text="platform"></label>
                        <div class="mt-1 flex rounded-md shadow-sm">
                            <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                            </span>
                            <input type="text" :value="link" @input="handleSocialLinkChange(platform, $event.target.value)" :disabled="!isEditing" class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-r-md border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 disabled:bg-gray-100">
                        </div>
                    </div>
                </template>
            </div>
        </div>

        <!-- Platform Details -->
        <div class="mt-10">
            <h2 class="text-xl font-bold text-gray-700 mb-4">Platform Details</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Platform</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Followers</th>
                            <th class="text-center py-3 px-4 uppercase font-semibold text-sm text-gray-600">Active</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <template x-for="platform in formData.platforms" :key="platform.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="py-3 px-4 font-medium" x-text="platform.name"></td>
                                <td class="py-3 px-4">
                                    <template x-if="isEditing">
                                        <input type="number" :value="platform.followers" @input="handlePlatformChange(platform.id, 'followers', parseInt($event.target.value) || 0)" class="w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500" min="0">
                                    </template>
                                    <template x-if="!isEditing">
                                        <span x-text="platform.followers.toLocaleString()"></span>
                                    </template>
                                </td>
                                <td class="py-3 px-4 text-center">
                                    <template x-if="isEditing">
                                        <input type="checkbox" :checked="platform.isActive" @change="handlePlatformChange(platform.id, 'isActive', $event.target.checked)" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                                    </template>
                                    <template x-if="!isEditing">
                                        <span class="px-2 py-1 text-xs font-semibold rounded-full" :class="platform.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'" x-text="platform.isActive ? 'Yes' : 'No'"></span>
                                    </template>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    function influencerProfile() {
        return {
            loading: false,
            saving: false,
            isEditing: false,
            profileData: null,
            formData: {},
            profilePicturePreview: null,

            init() {
                this.fetchProfileData();
            },

            fetchProfileData() {
                this.loading = true;
                // Mock data
                const mockData = {
                    id: 1,
                    name: 'Rahul Sharma',
                    email: '<EMAIL>',
                    phone: '+91 9876543210',
                    bio: 'Digital content creator specializing in tech reviews and lifestyle content. I help brands connect with their audience through authentic storytelling.',
                    profilePicture: 'https://randomuser.me/api/portraits/men/32.jpg',
                    socialLinks: {
                        instagram: 'https://instagram.com/rahulsharma',
                        facebook: 'https://facebook.com/rahulsharma',
                        twitter: 'https://twitter.com/rahulsharma',
                        youtube: 'https://youtube.com/rahulsharma',
                        website: 'https://rahulsharma.com'
                    },
                    platforms: [
                        { id: 1, name: 'Instagram', followers: 15000, isActive: true },
                        { id: 2, name: 'Facebook', followers: 8500, isActive: true },
                        { id: 3, name: 'Twitter', followers: 5200, isActive: true },
                        { id: 4, name: 'YouTube', followers: 12000, isActive: true },
                        { id: 5, name: 'WhatsApp', followers: 2000, isActive: false }
                    ]
                };
                this.profileData = JSON.parse(JSON.stringify(mockData));
                this.formData = JSON.parse(JSON.stringify(mockData));
                this.loading = false;
            },

            toggleEdit() {
                this.isEditing = !this.isEditing;
                if (!this.isEditing) {
                    this.handleCancelEdit();
                }
            },

            handleCancelEdit() {
                this.formData = JSON.parse(JSON.stringify(this.profileData));
                this.profilePicturePreview = null;
                this.isEditing = false;
            },

            handleSaveProfile() {
                this.saving = true;
                // Simulate API call
                setTimeout(() => {
                    this.profileData = JSON.parse(JSON.stringify(this.formData));
                    if (this.profilePicturePreview) {
                        this.profileData.profilePicture = this.profilePicturePreview;
                    }
                    this.isEditing = false;
                    this.saving = false;
                    this.profilePicturePreview = null;
                    alert('Profile updated successfully!');
                }, 1000);
            },

            handleSocialLinkChange(platform, value) {
                this.formData.socialLinks[platform] = value;
            },

            handlePlatformChange(platformId, field, value) {
                const platform = this.formData.platforms.find(p => p.id === platformId);
                if (platform) {
                    platform[field] = value;
                }
            },

            handleProfilePictureUpload(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.profilePicturePreview = e.target.result;
                        this.formData.profilePicture = e.target.result; // Or handle file upload separately
                    };
                    reader.readAsDataURL(file);
                }
            },

            handleRemoveProfilePicture() {
                this.formData.profilePicture = null;
                this.profilePicturePreview = null;
            }
        }
    }
</script>
@endsection

