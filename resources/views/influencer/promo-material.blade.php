@extends('layouts.admin')

@section('content')
<div x-data="influencerPromoMaterial()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">Promotional Materials</h1>
        <p class="text-gray-600">Find all the creative assets you need to promote WhaMart effectively.</p>
    </div>

    <!-- Tabs and Filters -->
    <div class="mb-6">
        <div class="sm:flex justify-between items-center">
            <!-- Tabs -->
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <template x-for="tab in tabs" :key="tab.id">
                        <button @click="activeTab = tab.id"
                                :class="activeTab === tab.id ? 'border-green-500 text-green-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none">
                            <span x-text="tab.name"></span>
                        </button>
                    </template>
                </nav>
            </div>

            <!-- Filters -->
            <div class="mt-4 sm:mt-0 sm:flex sm:space-x-4">
                <div class="relative">
                    <input type="text" x-model.debounce.500ms="searchTerm" placeholder="Search materials..." class="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                    </div>
                </div>
                <div class="relative mt-2 sm:mt-0">
                    <select x-model="filterCategory" class="w-full sm:w-48 pl-3 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-green-500 focus:border-green-500">
                        <option value="all">All Categories</option>
                        <option value="subscription">Subscription</option>
                        <option value="features">Features</option>
                        <option value="general">General</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Materials Grid -->
    <div x-show="!loading && filteredMaterials.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <template x-for="material in filteredMaterials" :key="material.id">
            <div class="bg-white rounded-lg shadow-md overflow-hidden transform hover:-translate-y-1 transition-transform duration-300">
                <template x-if="material.type !== 'script'">
                    <img :src="material.imageUrl" alt="material.title" class="w-full h-48 object-cover">
                </template>
                <div class="p-6">
                    <h3 class="text-xl font-bold text-gray-800 mb-2" x-text="material.title"></h3>
                    <p class="text-gray-600 text-sm mb-4" x-text="material.description"></p>
                    
                    <template x-if="material.type === 'script'">
                        <div class="bg-gray-100 p-4 rounded-lg mb-4">
                            <pre class="text-sm text-gray-800 whitespace-pre-wrap font-mono" x-text="material.content"></pre>
                        </div>
                    </template>

                    <div class="flex justify-between items-center text-xs text-gray-500 mb-4">
                        <span x-text="`Added: ${formatDate(material.createdAt)}`"></span>
                        <template x-if="material.dimensions">
                            <span x-text="`Size: ${material.dimensions}`"></span>
                        </template>
                        <template x-if="material.format">
                            <span class="uppercase font-bold" x-text="material.format"></span>
                        </template>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button @click="handleDownload(material)" title="Download" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
                        </button>
                        <button @click="handleCopyText(material.type === 'script' ? material.content : material.downloadUrl)" :title="material.type === 'script' ? 'Copy Script' : 'Copy Link'" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                        </button>
                        <button @click="handleShare(material)" title="Share" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6.002l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg>
                        </button>
                    </div>
                </div>
            </div>
        </template>
    </div>

    <!-- No Results -->
    <div x-show="!loading && filteredMaterials.length === 0" class="text-center py-16">
        <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No materials found</h3>
        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter to find what you're looking for.</p>
    </div>

    <!-- Loading -->
    <div x-show="loading" class="text-center py-16">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto"></div>
        <p class="mt-4 text-gray-600">Loading materials...</p>
    </div>

    <!-- Usage Guidelines -->
    <div class="mt-12 bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Usage Guidelines</h2>
        <div class="grid md:grid-cols-3 gap-8">
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">How to Use</h3>
                <p class="text-sm text-gray-600">These materials are designed to help you promote WhaMart. Use them as is or customize them to match your brand.</p>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Best Practices</h3>
                <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                    <li>Always include your referral link.</li>
                    <li>Personalize captions to be authentic.</li>
                    <li>Share your own experience with WhaMart.</li>
                    <li>Post consistently for maximum reach.</li>
                </ul>
            </div>
            <div>
                <h3 class="text-lg font-semibold text-gray-700 mb-2">Restrictions</h3>
                <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                    <li>Do not modify the WhaMart logo.</li>
                    <li>Do not make false claims about the platform.</li>
                    <li>Do not use for non-promotional purposes.</li>
                    <li>Do not imply you are an official representative.</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
    function influencerPromoMaterial() {
        return {
            loading: true,
            materials: [],
            activeTab: 'banners',
            searchTerm: '',
            filterCategory: 'all',
            tabs: [
                { id: 'banners', name: 'Banners' },
                { id: 'posts', name: 'Post Templates' },
                { id: 'videos', name: 'Videos' },
                { id: 'scripts', name: 'Scripts' },
            ],

            init() {
                this.fetchMaterials();
            },

            fetchMaterials() {
                this.loading = true;
                // Mock data - in a real app, this would be an API call
                setTimeout(() => {
                    this.materials = [
                        // Banners
                        { id: 1, type: 'banners', title: 'WhaMart Standard Plan Promo', description: 'Banner promoting the Standard subscription plan with 25% discount.', imageUrl: 'https://via.placeholder.com/800x400/25D366/FFFFFF?text=WhaMart+Standard+Plan', dimensions: '800x400', format: 'PNG', category: 'subscription', downloadUrl: '#', createdAt: '2023-10-01' },
                        { id: 2, type: 'banners', title: 'WhaMart Gold Plan Promo', description: 'Banner promoting the Gold subscription plan with 25% discount.', imageUrl: 'https://via.placeholder.com/800x400/075E54/FFFFFF?text=WhaMart+Gold+Plan', dimensions: '800x400', format: 'PNG', category: 'subscription', downloadUrl: '#', createdAt: '2023-10-01' },
                        { id: 3, type: 'banners', title: 'WhaMart Launch Banner', description: 'Banner announcing the launch of WhaMart platform.', imageUrl: 'https://via.placeholder.com/1200x600/25D366/FFFFFF?text=WhaMart+Launch', dimensions: '1200x600', format: 'PNG', category: 'general', downloadUrl: '#', createdAt: '2023-09-15' },
                        { id: 4, type: 'banners', title: 'WhatsApp Store Feature Banner', description: 'Banner highlighting the WhatsApp store feature.', imageUrl: 'https://via.placeholder.com/1200x600/075E54/FFFFFF?text=WhatsApp+Store+Feature', dimensions: '1200x600', format: 'PNG', category: 'features', downloadUrl: '#', createdAt: '2023-09-20' },
                        // Posts
                        { id: 5, type: 'posts', title: 'WhaMart Introduction Post', description: 'Post template introducing WhaMart to your audience.', imageUrl: 'https://via.placeholder.com/1080x1080/25D366/FFFFFF?text=WhaMart+Intro', dimensions: '1080x1080', format: 'PNG', category: 'general', downloadUrl: '#', createdAt: '2023-10-05' },
                        { id: 6, type: 'posts', title: 'Feature Highlight: Chat Flow', description: 'A post highlighting the power of the Chat Flow builder.', imageUrl: 'https://via.placeholder.com/1080x1080/128C7E/FFFFFF?text=Chat+Flow+Magic', dimensions: '1080x1080', format: 'PNG', category: 'features', downloadUrl: '#', createdAt: '2023-10-08' },
                        // Videos
                        { id: 7, type: 'videos', title: 'WhaMart Platform Overview', description: 'A 60-second video overview of the WhaMart platform.', imageUrl: 'https://via.placeholder.com/1920x1080/075E54/FFFFFF?text=WhaMart+Overview+Video', dimensions: '1920x1080', format: 'MP4', category: 'general', downloadUrl: '#', createdAt: '2023-10-10' },
                        { id: 8, type: 'videos', title: 'How to Set Up Your Store', description: 'A quick tutorial on setting up a new store on WhaMart.', imageUrl: 'https://via.placeholder.com/1920x1080/128C7E/FFFFFF?text=Store+Setup+Tutorial', dimensions: '1920x1080', format: 'MP4', category: 'features', downloadUrl: '#', createdAt: '2023-10-12' },
                        // Scripts
                        { id: 9, type: 'scripts', title: 'Introductory Tweet', description: 'A short and punchy tweet to introduce WhaMart.', content: 'Just discovered WhaMart and it\'s a game-changer for businesses on WhatsApp! Build your store, automate chats, and more. Check it out! #WhaMart #WhatsAppMarketing', category: 'general', downloadUrl: '#', createdAt: '2023-10-15' },
                        { id: 10, type: 'scripts', title: 'Instagram Story Idea', description: 'A script for an engaging Instagram story.', content: 'Voiceover: \'Tired of manually replying to customer DMs?\' (Show screen recording of being overwhelmed) \'I found a solution! WhaMart automates everything so I can focus on growing my business. Swipe up to learn more!\'', category: 'features', downloadUrl: '#', createdAt: '2023-10-18' },
                    ];
                    this.loading = false;
                }, 500);
            },

            get filteredMaterials() {
                if (this.loading) return [];
                let filtered = this.materials.filter(m => m.type === this.activeTab);

                if (this.filterCategory !== 'all') {
                    filtered = filtered.filter(m => m.category === this.filterCategory);
                }

                if (this.searchTerm.trim() !== '') {
                    const lowerSearchTerm = this.searchTerm.toLowerCase();
                    filtered = filtered.filter(m => 
                        m.title.toLowerCase().includes(lowerSearchTerm) ||
                        m.description.toLowerCase().includes(lowerSearchTerm)
                    );
                }

                return filtered;
            },

            formatDate(dateString) {
                const options = { year: 'numeric', month: 'long', day: 'numeric' };
                return new Date(dateString).toLocaleDateString(undefined, options);
            },

            handleDownload(material) {
                alert(`Downloading: ${material.title}`);
                // In a real app, you would trigger a file download
                // window.location.href = material.downloadUrl;
            },

            handleCopyText(text) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('Copied to clipboard!');
                }, () => {
                    alert('Failed to copy!');
                });
            },

            handleShare(material) {
                if (navigator.share) {
                    navigator.share({
                        title: material.title,
                        text: material.description,
                        url: material.downloadUrl,
                    })
                    .then(() => console.log('Successful share'))
                    .catch((error) => console.log('Error sharing', error));
                } else {
                    alert('Share functionality is not supported in your browser.');
                }
            }
        }
    }
</script>
@endsection

