@extends('layouts.admin')

@section('content')
<div x-data="influencerEarnings()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h1 class="text-3xl font-bold text-gray-800 mb-4">My Earnings</h1>

        <!-- Earnings Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-green-100 p-6 rounded-lg flex items-center">
                <div class="bg-green-500 text-white rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v.01" /></svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-green-700">Total Earnings</p>
                    <p class="text-2xl font-bold text-green-800" x-text="formatCurrency(earningsData.overview.totalEarnings)"></p>
                </div>
            </div>
            <div class="bg-blue-100 p-6 rounded-lg flex items-center">
                <div class="bg-blue-500 text-white rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-blue-700">Available Balance</p>
                    <p class="text-2xl font-bold text-blue-800" x-text="formatCurrency(earningsData.overview.availableBalance)"></p>
                </div>
            </div>
            <div class="bg-yellow-100 p-6 rounded-lg flex items-center">
                <div class="bg-yellow-500 text-white rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-yellow-700">Pending Balance</p>
                    <p class="text-2xl font-bold text-yellow-800" x-text="formatCurrency(earningsData.overview.pendingBalance)"></p>
                </div>
            </div>
            <div class="bg-red-100 p-6 rounded-lg flex items-center">
                <div class="bg-red-500 text-white rounded-full p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" /></svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm text-red-700">Withdrawn Amount</p>
                    <p class="text-2xl font-bold text-red-800" x-text="formatCurrency(earningsData.overview.withdrawnAmount)"></p>
                </div>
            </div>
        </div>

        <!-- Transaction History -->
        <div class="bg-white p-6 rounded-lg shadow-md">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-bold text-gray-700">Transaction History</h2>
                <button @click="handleDownloadReport()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
                    Download Report
                </button>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full bg-white">
                    <thead class="bg-gray-100">
                        <tr>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Date</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Description</th>
                            <th class="text-left py-3 px-4 uppercase font-semibold text-sm text-gray-600">Type</th>
                            <th class="text-right py-3 px-4 uppercase font-semibold text-sm text-gray-600">Amount</th>
                            <th class="text-center py-3 px-4 uppercase font-semibold text-sm text-gray-600">Status</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <template x-for="transaction in paginatedTransactions" :key="transaction.id">
                            <tr class="border-b border-gray-200 hover:bg-gray-50">
                                <td class="py-3 px-4" x-text="formatDate(transaction.date)"></td>
                                <td class="py-3 px-4">
                                    <p class="font-medium" x-text="transaction.description"></p>
                                    <p class="text-sm text-gray-500" x-text="'Ref: ' + transaction.reference"></p>
                                </td>
                                <td class="py-3 px-4">
                                    <span :class="getTypeBadge(transaction.type)" x-text="transaction.type"></span>
                                </td>
                                <td class="py-3 px-4 text-right font-medium" :class="{'text-green-600': transaction.amount > 0, 'text-red-600': transaction.amount < 0}" x-text="formatCurrency(transaction.amount)"></td>
                                <td class="py-3 px-4 text-center">
                                    <span :class="getStatusBadge(transaction.status)" x-text="transaction.status"></span>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="flex justify-between items-center mt-4">
                <p class="text-sm text-gray-600">Showing <span x-text="(currentPage - 1) * 10 + 1"></span> to <span x-text="Math.min(currentPage * 10, transactions.length)"></span> of <span x-text="transactions.length"></span> transactions</p>
                <div class="flex space-x-2">
                    <button @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" class="px-3 py-1 rounded-lg" :class="{'bg-gray-200 text-gray-400 cursor-not-allowed': currentPage === 1, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== 1}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" /></svg>
                    </button>
                    <template x-for="page in Array.from({ length: totalPages }, (_, i) => i + 1)">
                        <button @click="handlePageChange(page)" class="px-3 py-1 rounded-lg" :class="{'bg-green-500 text-white': currentPage === page, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== page}" x-text="page"></button>
                    </template>
                    <button @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" class="px-3 py-1 rounded-lg" :class="{'bg-gray-200 text-gray-400 cursor-not-allowed': currentPage === totalPages, 'bg-white text-gray-700 hover:bg-gray-100 border': currentPage !== totalPages}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Earnings Information -->
        <div class="mt-8 bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-bold text-gray-700 mb-4">Earnings Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Commission Structure</h3>
                    <p class="text-sm text-gray-500">You earn commissions based on the subscription plan purchased by your referrals:</p>
                    <ul class="list-disc list-inside text-sm text-gray-500 mt-2 space-y-1">
                        <li>Standard Plan (₹1,499/year): 15% commission (₹225)</li>
                        <li>Gold Plan (₹3,000/year): 20% commission (₹600)</li>
                        <li>Premium Plan (₹4,999/year): 25% commission (₹1,250)</li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Payment Schedule</h3>
                    <p class="text-sm text-gray-500">Commissions are subject to a 7-day holding period before they become available for withdrawal. This is to allow for potential refunds or disputes.</p>
                </div>
                <div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Withdrawal Options</h3>
                    <p class="text-sm text-gray-500">You can withdraw your available balance through Bank Transfer, UPI, or Paytm. The minimum withdrawal amount is ₹100.</p>
                    <div class="mt-2">
                        <a href="#" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" /></svg>
                            Request Withdrawal
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function influencerEarnings() {
        return {
            loading: false,
            earningsData: {
                overview: {
                    totalEarnings: 12450,
                    availableBalance: 2700,
                    pendingBalance: 1500,
                    withdrawnAmount: 8250
                }
            },
            transactions: [],
            currentPage: 1,
            totalPages: 1,

            init() {
                this.fetchTransactions();
            },

            fetchTransactions() {
                this.loading = true;
                // Mock data, in a real app this would be an API call
                const mockTransactions = [
                    { id: 1, type: 'commission', amount: 1500, date: '2023-10-24', status: 'completed', description: 'Commission from Priya Patel', reference: 'COM123456' },
                    { id: 2, type: 'commission', amount: 1200, date: '2023-10-22', status: 'completed', description: 'Commission from Neha Singh', reference: 'COM123455' },
                    { id: 3, type: 'withdrawal', amount: -2700, date: '2023-10-20', status: 'completed', description: 'Withdrawal to Bank Account', reference: 'WD123456' },
                    { id: 4, type: 'commission', amount: 900, date: '2023-10-20', status: 'completed', description: 'Commission from Ananya Desai', reference: 'COM123454' },
                    { id: 5, type: 'commission', amount: 1800, date: '2023-10-17', status: 'completed', description: 'Commission from Kiran Shah', reference: 'COM123453' },
                    { id: 6, type: 'withdrawal', amount: -1800, date: '2023-10-15', status: 'completed', description: 'Withdrawal to UPI', reference: 'WD123455' },
                    { id: 7, type: 'commission', amount: 1350, date: '2023-10-15', status: 'completed', description: 'Commission from Pooja Mehta', reference: 'COM123452' },
                    { id: 8, type: 'commission', amount: 2100, date: '2023-10-12', status: 'completed', description: 'Commission from Ravi Patel', reference: 'COM123451' },
                    { id: 9, type: 'withdrawal', amount: -3750, date: '2023-10-10', status: 'completed', description: 'Withdrawal to Bank Account', reference: 'WD123454' },
                    { id: 10, type: 'commission', amount: 1650, date: '2023-10-08', status: 'completed', description: 'Commission from Anil Kapoor', reference: 'COM123450' },
                    { id: 11, type: 'commission', amount: 1500, date: '2023-10-05', status: 'pending', description: 'Commission from Suresh Kumar', reference: 'COM123449' },
                    { id: 12, type: 'commission', amount: 1200, date: '2023-10-03', status: 'pending', description: 'Commission from Meena Verma', reference: 'COM123448' },
                ];
                this.transactions = mockTransactions;
                this.totalPages = Math.ceil(mockTransactions.length / 10);
                this.loading = false;
            },

            formatCurrency(amount) {
                return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
            },

            formatDate(dateString) {
                return new Date(dateString).toLocaleDateString('en-GB', { day: 'numeric', month: 'short', year: 'numeric' });
            },

            getStatusBadge(status) {
                switch (status) {
                    case 'completed': return 'bg-green-100 text-green-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                    case 'pending': return 'bg-yellow-100 text-yellow-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                    case 'failed': return 'bg-red-100 text-red-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                    default: return 'bg-gray-100 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                }
            },

            getTypeBadge(type) {
                switch (type) {
                    case 'commission': return 'bg-blue-100 text-blue-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                    case 'withdrawal': return 'bg-purple-100 text-purple-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                    default: return 'bg-gray-100 text-gray-800 text-xs font-medium mr-2 px-2.5 py-0.5 rounded-full';
                }
            },

            handlePageChange(page) {
                if (page > 0 && page <= this.totalPages) {
                    this.currentPage = page;
                }
            },

            handleDownloadReport() {
                alert('Downloading earnings report...');
                // In a real application, you would generate and download a CSV or PDF file.
            },

            get paginatedTransactions() {
                const start = (this.currentPage - 1) * 10;
                const end = start + 10;
                return this.transactions.slice(start, end);
            }
        }
    }
</script>
@endsection

