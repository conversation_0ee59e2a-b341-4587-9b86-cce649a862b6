@extends('layouts.admin')

@section('content')
<div x-data="influencerReferralLinks()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Referral Links</h1>
        <button @click="showCreateForm()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" /></svg>
            Create New Link
        </button>
    </div>

    <!-- Create/Edit Form -->
    <template x-if="isCreatingLink || isEditingLink">
        <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-4" x-text="isEditingLink ? 'Edit Referral Link' : 'Create New Referral Link'"></h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Link Name</label>
                    <input type="text" x-model="formData.name" placeholder="e.g., Instagram Bio Link" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Platform</label>
                    <select x-model="formData.platform" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                        <option value="">Select Platform</option>
                        <option value="Instagram">Instagram</option>
                        <option value="Facebook">Facebook</option>
                        <option value="Twitter">Twitter</option>
                        <option value="YouTube">YouTube</option>
                        <option value="WhatsApp">WhatsApp</option>
                        <option value="Website">Website</option>
                        <option value="Other">Other</option>
                    </select>
                </div>
                <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700">Custom Slug</label>
                    <div class="flex items-center mt-1">
                        <span class="text-gray-500 pr-2">whamart.com/ref/</span>
                        <input type="text" x-model="formData.customSlug" placeholder="your-unique-slug" class="flex-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">UTM Source</label>
                    <input type="text" x-model="formData.utmSource" placeholder="e.g., instagram" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">UTM Medium</label>
                    <input type="text" x-model="formData.utmMedium" placeholder="e.g., social" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">UTM Campaign</label>
                    <input type="text" x-model="formData.utmCampaign" placeholder="e.g., bio_link" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-green-500 focus:border-green-500">
                </div>
            </div>
            <div class="flex justify-end space-x-4 mt-6">
                <button @click="handleCancel()" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300">Cancel</button>
                <button @click="handleSaveLink()" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600" x-text="isEditingLink ? 'Save Changes' : 'Create Link'"></button>
            </div>
        </div>
    </template>

    <!-- Referral Links List -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="space-y-6">
            <template x-for="link in referralLinks" :key="link.id">
                <div class="border border-gray-200 p-4 rounded-lg">
                    <div class="md:flex justify-between items-start">
                        <div class="flex-1">
                            <h3 class="text-xl font-bold text-gray-800" x-text="link.name"></h3>
                            <div class="flex items-center text-green-600 mt-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                                <a :href="'https://' + link.fullUrl" target="_blank" class="hover:underline" x-text="link.fullUrl"></a>
                                <button @click="handleCopyLink(link.fullUrl)" class="ml-2 text-gray-500 hover:text-gray-700">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" /></svg>
                                </button>
                            </div>
                            <p class="text-sm text-gray-500 mt-1" x-text="`Platform: ${link.platform} | Created: ${formatDate(link.createdAt)}`"></p>
                        </div>
                        <div class="flex items-center space-x-2 mt-4 md:mt-0">
                            <button @click="handleShowQRCode(link.fullUrl)" title="QR Code" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6.364 1.636l-.707.707M20 12h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" /></svg></button>
                            <button @click="handleShareLink(link.fullUrl)" title="Share" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12s-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6.002l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.368a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" /></svg></button>
                            <button @click="showEditForm(link)" title="Edit" class="p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.5L16.732 3.732z" /></svg></button>
                            <button @click="handleDeleteLink(link.id)" title="Delete" class="p-2 text-red-500 hover:text-red-700 rounded-full hover:bg-red-100"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg></button>
                        </div>
                    </div>
                    <div class="mt-4 pt-4 border-t border-gray-200 grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-sm text-gray-500">Clicks</p>
                            <p class="text-2xl font-bold text-gray-800" x-text="link.clicks"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Sign-ups</p>
                            <p class="text-2xl font-bold text-gray-800" x-text="link.signups"></p>
                        </div>
                        <div>
                            <p class="text-sm text-gray-500">Sales</p>
                            <p class="text-2xl font-bold text-gray-800" x-text="link.sales"></p>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </div>

    <!-- QR Code Modal -->
    <div x-show="showQRCode" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.self="handleCloseQRCode()">
        <div class="bg-white rounded-lg max-w-md w-full p-6 mx-4">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Scan QR Code</h3>
                <button @click="handleCloseQRCode()" class="text-gray-400 hover:text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg></button>
            </div>
            <div class="flex flex-col items-center">
                <div class="p-4 bg-white rounded-lg shadow-md mb-4">
                    <img :src="`https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${qrCodeLink}`" alt="QR Code">
                </div>
                <p class="text-sm text-gray-600 mb-4 text-center">Scan this code to visit <span class="font-semibold" x-text="qrCodeLink"></span></p>
                <a :href="`https://api.qrserver.com/v1/create-qr-code/?size=512x512&data=${qrCodeLink}`" download="qr-code.png" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">Download QR Code</a>
            </div>
        </div>
    </div>
</div>

<script>
function influencerReferralLinks() {
    return {
        loading: false,
        referralLinks: [],
        isCreatingLink: false,
        isEditingLink: false,
        selectedLink: null,
        formData: {},
        showQRCode: false,
        qrCodeLink: '',

        init() {
            this.resetFormData();
            this.fetchReferralLinks();
        },

        resetFormData() {
            this.formData = {
                id: null,
                name: '',
                platform: '',
                customSlug: '',
                utmSource: '',
                utmMedium: '',
                utmCampaign: ''
            };
        },

        fetchReferralLinks() {
            this.loading = true;
            // Mock data
            setTimeout(() => {
                this.referralLinks = [
                    { id: 1, name: 'Instagram Bio Link', platform: 'Instagram', url: 'whamart.com/ref/rahul123', customSlug: 'rahul123', utmSource: 'instagram', utmMedium: 'social', utmCampaign: 'bio', fullUrl: 'whamart.com/ref/rahul123?utm_source=instagram&utm_medium=social&utm_campaign=bio', clicks: 156, signups: 42, sales: 18, createdAt: '2023-09-15' },
                    { id: 2, name: 'Facebook Post', platform: 'Facebook', url: 'whamart.com/ref/rahul-fb', customSlug: 'rahul-fb', utmSource: 'facebook', utmMedium: 'social', utmCampaign: 'post', fullUrl: 'whamart.com/ref/rahul-fb?utm_source=facebook&utm_medium=social&utm_campaign=post', clicks: 89, signups: 24, sales: 10, createdAt: '2023-09-20' },
                    { id: 3, name: 'WhatsApp Status', platform: 'WhatsApp', url: 'whamart.com/ref/rahul-wa', customSlug: 'rahul-wa', utmSource: 'whatsapp', utmMedium: 'social', utmCampaign: 'status', fullUrl: 'whamart.com/ref/rahul-wa?utm_source=whatsapp&utm_medium=social&utm_campaign=status', clicks: 65, signups: 18, sales: 8, createdAt: '2023-09-25' },
                ];
                this.loading = false;
            }, 500);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });
        },

        showCreateForm() {
            this.isEditingLink = false;
            this.isCreatingLink = true;
            this.resetFormData();
        },

        showEditForm(link) {
            this.isCreatingLink = false;
            this.isEditingLink = true;
            this.selectedLink = link;
            this.formData = { ...link };
        },

        handleCancel() {
            this.isCreatingLink = false;
            this.isEditingLink = false;
            this.resetFormData();
        },

        handleSaveLink() {
            if (!this.formData.name || !this.formData.customSlug) {
                alert('Link Name and Custom Slug are required.');
                return;
            }

            const fullUrl = `whamart.com/ref/${this.formData.customSlug}?utm_source=${this.formData.utmSource}&utm_medium=${this.formData.utmMedium}&utm_campaign=${this.formData.utmCampaign}`;

            if (this.isEditingLink) {
                const index = this.referralLinks.findIndex(l => l.id === this.selectedLink.id);
                this.referralLinks[index] = { ...this.formData, fullUrl };
                alert('Link updated successfully!');
            } else {
                const newLink = {
                    ...this.formData,
                    id: Date.now(),
                    fullUrl,
                    clicks: 0,
                    signups: 0,
                    sales: 0,
                    createdAt: new Date().toISOString().split('T')[0]
                };
                this.referralLinks.unshift(newLink);
                alert('Link created successfully!');
            }
            this.handleCancel();
        },

        handleDeleteLink(linkId) {
            if (confirm('Are you sure you want to delete this link?')) {
                this.referralLinks = this.referralLinks.filter(l => l.id !== linkId);
                alert('Link deleted.');
            }
        },

        handleCopyLink(linkUrl) {
            navigator.clipboard.writeText(linkUrl).then(() => {
                alert('Link copied to clipboard!');
            });
        },

        handleShowQRCode(linkUrl) {
            this.qrCodeLink = encodeURIComponent(linkUrl);
            this.showQRCode = true;
        },

        handleCloseQRCode() {
            this.showQRCode = false;
            this.qrCodeLink = '';
        },

        handleShareLink(linkUrl) {
            if (navigator.share) {
                navigator.share({ title: 'My Referral Link', url: `https://${linkUrl}` });
            } else {
                alert('Web Share API not supported in your browser.');
            }
        }
    }
}
</script>
@endsection

