@extends('layouts.admin')

@section('content')
<div x-data="influencerCampaigns()" x-init="fetchCampaigns()" class="p-6">
    <template x-if="loading">
        <div class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50">
            <div class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-green-500"></div>
        </div>
    </template>

    <div x-show="!loading">
        <!-- Header -->
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-gray-800">Campaigns</h1>
        </div>

        <!-- Filters and Search -->
        <div class="mb-6 flex flex-col md:flex-row justify-between items-center gap-4">
            <div class="relative w-full md:w-1/3">
                <input type="text" x-model="searchTerm" @input.debounce.300ms="handleSearchChange" placeholder="Search campaigns..." class="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" /></svg>
                </div>
            </div>
            <div class="flex items-center gap-4">
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center gap-2 px-4 py-2 border rounded-lg text-gray-600 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h18M6 8h12m-9 4h6" /></svg>
                        Filter
                    </button>
                    <div x-show="open" @click.away="open = false" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl z-20">
                        <a @click.prevent="handleFilterChange('all')" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">All</a>
                        <a @click.prevent="handleFilterChange('active')" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Active</a>
                        <a @click.prevent="handleFilterChange('pending')" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Pending</a>
                        <a @click.prevent="handleFilterChange('completed')" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Completed</a>
                        <a @click.prevent="handleFilterChange('rejected')" href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Rejected</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Campaigns List -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <template x-for="campaign in getFilteredCampaigns()" :key="campaign.id">
                <div @click="handleSelectCampaign(campaign)" class="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow cursor-pointer overflow-hidden">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <img :src="campaign.vendorLogo" alt="Vendor Logo" class="w-12 h-12 rounded-full mr-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800" x-text="campaign.product"></h3>
                                <p class="text-sm text-gray-600" x-text="campaign.vendor"></p>
                            </div>
                        </div>
                        <p class="text-sm text-gray-700 mb-4 h-20 overflow-hidden" x-text="campaign.description"></p>
                        <div class="flex justify-between items-center text-sm mb-4">
                            <span class="font-medium">Commission</span>
                            <span class="font-bold text-green-600" x-text="campaign.commission + '%'"></span>
                        </div>
                        <div class="flex justify-between items-center text-sm">
                            <span class="font-medium">Status</span>
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="getStatusBadge(campaign.status).color" x-text="getStatusBadge(campaign.status).text"></span>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-6 py-3">
                        <p class="text-xs text-gray-500">Ends on: <span x-text="formatDate(campaign.endDate)"></span></p>
                    </div>
                </div>
            </template>
        </div>

        <!-- Pagination -->
        <div class="mt-8 flex justify-center">
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <a @click.prevent="handlePageChange(currentPage - 1)" href="#" :class="{'cursor-not-allowed': currentPage === 1}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Previous</span>
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
                </a>
                <template x-for="page in Array.from({ length: totalPages }, (_, i) => i + 1)" :key="page">
                    <a @click.prevent="handlePageChange(page)" href="#" :class="{'z-10 bg-green-50 border-green-500 text-green-600': currentPage === page, 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': currentPage !== page}" class="relative inline-flex items-center px-4 py-2 border text-sm font-medium" x-text="page"></a>
                </template>
                <a @click.prevent="handlePageChange(currentPage + 1)" href="#" :class="{'cursor-not-allowed': currentPage === totalPages}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="sr-only">Next</span>
                    <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" /></svg>
                </a>
            </nav>
        </div>
    </div>

    <!-- Campaign Details Modal -->
    <div x-show="selectedCampaign" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-30" x-cloak>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-screen overflow-y-auto p-8 m-4" @click.away="handleCloseCampaignDetails()">
            <template x-if="selectedCampaign">
                <div>
                    <div class="flex justify-between items-start mb-6">
                        <div class="flex items-center">
                            <img :src="selectedCampaign.vendorLogo" alt="Vendor Logo" class="w-16 h-16 rounded-full mr-4">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-800" x-text="selectedCampaign.product"></h2>
                                <p class="text-md text-gray-600" x-text="selectedCampaign.vendor"></p>
                            </div>
                        </div>
                        <button @click="handleCloseCampaignDetails()" class="text-gray-400 hover:text-gray-600">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                        </button>
                    </div>

                    <p class="text-gray-700 mb-6" x-text="selectedCampaign.description"></p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Campaign Details</h4>
                            <div class="bg-gray-50 p-4 rounded-lg space-y-2 text-sm">
                                <p><span class="font-medium">Duration:</span> <span x-text="formatDate(selectedCampaign.startDate) + ' to ' + formatDate(selectedCampaign.endDate)"></span></p>
                                <p><span class="font-medium">Commission:</span> <span x-text="selectedCampaign.commission + '%'"></span></p>
                                <p><span class="font-medium">Plan Type:</span> <span x-text="selectedCampaign.planType + ' (' + formatCurrency(selectedCampaign.planPrice) + '/year)'"></span></p>
                                <template x-if="selectedCampaign.rejectionReason">
                                    <p class="text-red-500"><span class="font-medium">Rejection Reason:</span> <span x-text="selectedCampaign.rejectionReason"></span></p>
                                </template>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Performance Metrics</h4>
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <div class="grid grid-cols-2 gap-4">
                                    <div><p class="text-xs text-gray-500">Referrals</p><p class="text-lg font-bold" x-text="selectedCampaign.referrals"></p></div>
                                    <div><p class="text-xs text-gray-500">Signups</p><p class="text-lg font-bold" x-text="selectedCampaign.signups"></p></div>
                                    <div><p class="text-xs text-gray-500">Sales</p><p class="text-lg font-bold" x-text="selectedCampaign.sales"></p></div>
                                    <div><p class="text-xs text-gray-500">Earnings</p><p class="text-lg font-bold text-green-600" x-text="formatCurrency(selectedCampaign.earnings)"></p></div>
                                </div>
                                <template x-if="selectedCampaign.referrals > 0">
                                    <div class="mt-4">
                                        <p class="text-xs text-gray-500 mb-1">Conversion Rate</p>
                                        <div class="w-full bg-gray-200 rounded-full h-2.5 mb-1">
                                            <div class="bg-green-600 h-2.5 rounded-full" :style="`width: ${(selectedCampaign.sales / selectedCampaign.referrals) * 100}%`"></div>
                                        </div>
                                        <p class="text-xs text-gray-500 text-right" x-text="`${Math.round((selectedCampaign.sales / selectedCampaign.referrals) * 100)}% (${selectedCampaign.sales} of ${selectedCampaign.referrals})`"></p>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <template x-if="selectedCampaign.status === 'active'">
                        <div class="mb-6">
                            <h4 class="text-sm font-medium text-gray-500 mb-2">Your Referral Link</h4>
                            <div class="flex items-center bg-gray-50 p-3 rounded-lg">
                                <svg class="h-5 w-5 text-gray-400 mr-2 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" /></svg>
                                <span class="text-sm text-gray-600 mr-2 truncate" x-text="selectedCampaign.referralLink"></span>
                                <button @click="handleCopyReferralLink(selectedCampaign.referralLink)" class="ml-auto bg-green-500 text-white px-3 py-1 rounded-md text-sm hover:bg-green-600">Copy</button>
                            </div>
                        </div>
                    </template>

                    <div class="flex justify-end">
                        <button @click="handleCloseCampaignDetails()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">Close</button>
                    </div>
                </div>
            </template>
        </div>
    </div>
</div>

<script>
function influencerCampaigns() {
    return {
        loading: true,
        campaigns: [],
        currentPage: 1,
        itemsPerPage: 6,
        totalPages: 1,
        searchTerm: '',
        filterStatus: 'all',
        selectedCampaign: null,

        fetchCampaigns() {
            // Mock data, in reality this would be an API call
            const mockCampaigns = [
                { id: 1, vendor: 'Grocery Store A', vendorLogo: 'https://randomuser.me/api/portraits/men/32.jpg', product: 'Organic Vegetables', description: 'Promote our fresh organic vegetables to your audience. We offer a wide range of seasonal produce directly from farms.', commission: 15, planType: 'Standard', planPrice: 1499, status: 'active', startDate: '2023-10-01', endDate: '2023-12-31', referralLink: 'whamart.com/ref/rahul123/grocery-a', referrals: 32, signups: 12, sales: 5, earnings: 1125 },
                { id: 2, vendor: 'Snack Center B', vendorLogo: 'https://randomuser.me/api/portraits/women/44.jpg', product: 'Healthy Snacks', description: 'Promote our range of healthy snacks made with natural ingredients. Perfect for health-conscious consumers.', commission: 20, planType: 'Gold', planPrice: 3000, status: 'active', startDate: '2023-09-15', endDate: '2023-12-15', referralLink: 'whamart.com/ref/rahul123/snack-b', referrals: 45, signups: 18, sales: 8, earnings: 4800 },
                { id: 3, vendor: 'Home Business C', vendorLogo: 'https://randomuser.me/api/portraits/women/68.jpg', product: 'Handmade Crafts', description: 'Help promote our handmade crafts and home decor items. Each piece is unique and made with love.', commission: 25, planType: 'Premium', planPrice: 4999, status: 'pending', startDate: '2023-11-01', endDate: '2024-01-31', referralLink: 'whamart.com/ref/rahul123/home-c', referrals: 0, signups: 0, sales: 0, earnings: 0 },
                { id: 4, vendor: 'Local Bakery D', vendorLogo: 'https://randomuser.me/api/portraits/men/75.jpg', product: 'Fresh Bread', description: 'Promote our freshly baked bread and pastries. We use traditional recipes and high-quality ingredients.', commission: 15, planType: 'Standard', planPrice: 1499, status: 'active', startDate: '2023-10-10', endDate: '2023-12-10', referralLink: 'whamart.com/ref/rahul123/bakery-d', referrals: 28, signups: 10, sales: 4, earnings: 900 },
                { id: 5, vendor: 'Organic Farm E', vendorLogo: 'https://randomuser.me/api/portraits/women/90.jpg', product: 'Farm Products', description: 'Promote our organic farm products, including fresh milk, cheese, and eggs. We are committed to sustainable farming.', commission: 18, planType: 'Gold', planPrice: 3000, status: 'completed', startDate: '2023-08-01', endDate: '2023-10-31', referralLink: 'whamart.com/ref/rahul123/farm-e', referrals: 62, signups: 25, sales: 15, earnings: 8100 },
                { id: 6, vendor: 'Fashion Boutique F', vendorLogo: 'https://randomuser.me/api/portraits/women/52.jpg', product: 'Designer Wear', description: 'Promote our exclusive collection of designer wear for women. We offer the latest trends and styles.', commission: 30, planType: 'Premium', planPrice: 4999, status: 'rejected', rejectionReason: 'Campaign does not align with our brand values.', startDate: '2023-12-01', endDate: '2024-02-28', referralLink: 'whamart.com/ref/rahul123/fashion-f', referrals: 0, signups: 0, sales: 0, earnings: 0 },
                { id: 7, vendor: 'Tech Gadgets G', vendorLogo: 'https://randomuser.me/api/portraits/men/81.jpg', product: 'Smart Devices', description: 'Promote our latest smart devices and tech gadgets. We offer competitive prices and excellent customer service.', commission: 12, planType: 'Standard', planPrice: 1499, status: 'active', startDate: '2023-11-15', endDate: '2024-01-15', referralLink: 'whamart.com/ref/rahul123/tech-g', referrals: 15, signups: 5, sales: 2, earnings: 360 },
            ];
            this.campaigns = mockCampaigns;
            this.totalPages = Math.ceil(this.campaigns.length / this.itemsPerPage);
            this.loading = false;
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
        },

        getStatusBadge(status) {
            switch (status) {
                case 'active': return { color: 'bg-green-100 text-green-800', text: 'Active' };
                case 'pending': return { color: 'bg-yellow-100 text-yellow-800', text: 'Pending' };
                case 'completed': return { color: 'bg-blue-100 text-blue-800', text: 'Completed' };
                case 'rejected': return { color: 'bg-red-100 text-red-800', text: 'Rejected' };
                default: return { color: 'bg-gray-100 text-gray-800', text: 'Unknown' };
            }
        },

        handleSearchChange() {
            this.currentPage = 1;
        },

        handleFilterChange(status) {
            this.filterStatus = status;
            this.currentPage = 1;
        },

        handlePageChange(page) {
            if (page > 0 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },

        handleSelectCampaign(campaign) {
            this.selectedCampaign = campaign;
        },

        handleCloseCampaignDetails() {
            this.selectedCampaign = null;
        },

        handleCopyReferralLink(link) {
            navigator.clipboard.writeText(link).then(() => {
                alert('Referral link copied to clipboard!');
            });
        },

        getFilteredCampaigns() {
            const filtered = this.campaigns.filter(campaign => {
                const matchesSearch = campaign.product.toLowerCase().includes(this.searchTerm.toLowerCase()) || campaign.vendor.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesStatus = this.filterStatus === 'all' || campaign.status === this.filterStatus;
                return matchesSearch && matchesStatus;
            });
            this.totalPages = Math.ceil(filtered.length / this.itemsPerPage);
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return filtered.slice(start, end);
        }
    }
}
</script>
@endsection

