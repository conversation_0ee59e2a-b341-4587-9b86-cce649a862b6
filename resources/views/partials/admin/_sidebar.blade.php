@php
$navigation = [
    ['name' => 'Dashboard', 'icon' => 'fas fa-home', 'route' => 'vendor.dashboard', 'color' => '#7ED957'],
    ['name' => 'Orders', 'icon' => 'fas fa-shopping-bag', 'route' => 'vendor.orders', 'color' => '#4CAF50'],
    ['name' => 'Products', 'icon' => 'fas fa-box', 'route' => 'vendor.products.index', 'color' => '#7ED957'],
    ['name' => 'Customers', 'icon' => 'fas fa-users', 'route' => 'vendor.customers', 'color' => '#4CAF50'],
    ['name' => 'Analytics', 'icon' => 'fas fa-chart-line', 'route' => 'vendor.analytics', 'color' => '#7ED957'],
    ['name' => 'Marketing', 'icon' => 'fas fa-bullhorn', 'route' => 'vendor.marketing', 'color' => '#4CAF50'],
    ['name' => 'Chat Flows', 'icon' => 'fas fa-comments', 'route' => 'vendor.chat-flow.list', 'color' => '#7ED957'],
    ['name' => 'Store Settings', 'icon' => 'fas fa-store', 'route' => 'vendor.store-settings', 'color' => '#4CAF50'],
];

$bottomNavigation = [
    ['name' => 'Settings', 'icon' => 'fas fa-cog', 'route' => 'vendor.settings', 'color' => '#666'],
    ['name' => 'Support', 'icon' => 'fas fa-life-ring', 'route' => 'vendor.support', 'color' => '#666'],
];
@endphp

<div x-show="sidebarVisible" 
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="-translate-x-full"
     x-transition:enter-end="translate-x-0"
     x-transition:leave="transition ease-in duration-300"
     x-transition:leave-start="translate-x-0"
     x-transition:leave-end="-translate-x-full"
     class="vendor-sidebar">
    
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="/WhaMart_Logo.png" alt="Whamart Logo" class="sidebar-logo-img" 
                 onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'sidebar-logo-fallback\'>W</div>');">
        </div>
        <div class="sidebar-brand">
            <h3 class="sidebar-brand-title">Whamart</h3>
            <p class="sidebar-brand-subtitle">Vendor Dashboard</p>
        </div>
    </div>

    <!-- Main Navigation -->
    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4 class="nav-section-title">Main Menu</h4>
            <ul class="nav-list">
                @foreach ($navigation as $item)
                    <li class="nav-item">
                        <a href="{{ route($item['route']) }}" 
                           class="nav-link {{ request()->routeIs($item['route']) ? 'active' : '' }}"
                           style="--nav-color: {{ $item['color'] }}">
                            <div class="nav-icon">
                                <i class="{{ $item['icon'] }}"></i>
                            </div>
                            <span class="nav-text">{{ $item['name'] }}</span>
                            @if(request()->routeIs($item['route']))
                                <div class="nav-indicator"></div>
                            @endif
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>

        <!-- Bottom Navigation -->
        <div class="nav-section nav-section-bottom">
            <ul class="nav-list">
                @foreach ($bottomNavigation as $item)
                    <li class="nav-item">
                        <a href="{{ route($item['route']) }}" 
                           class="nav-link {{ request()->routeIs($item['route']) ? 'active' : '' }}"
                           style="--nav-color: {{ $item['color'] }}">
                            <div class="nav-icon">
                                <i class="{{ $item['icon'] }}"></i>
                            </div>
                            <span class="nav-text">{{ $item['name'] }}</span>
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <!-- User Profile Section -->
    <div class="sidebar-user">
        <div class="user-profile">
            <div class="user-avatar">
                @if(Auth::user()->profilePicture)
                    <img src="{{ Auth::user()->profilePicture }}" alt="Profile" class="user-avatar-img">
                @else
                    <div class="user-avatar-fallback">
                        {{ substr(Auth::user()->name, 0, 1) }}
                    </div>
                @endif
            </div>
            <div class="user-info">
                <h4 class="user-name">{{ Auth::user()->name }}</h4>
                <p class="user-role">Vendor</p>
            </div>
            <div class="user-actions">
                <form method="POST" action="{{ route('logout') }}" class="logout-form">
                    @csrf
                    <button type="submit" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div x-show="sidebarOpen" 
     x-transition:enter="transition-opacity ease-linear duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="transition-opacity ease-linear duration-300"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0"
     @click="sidebarOpen = false"
     class="sidebar-overlay md:hidden"></div>

<!-- Mobile Sidebar -->
<div x-show="sidebarOpen"
     x-transition:enter="transition ease-in-out duration-300 transform"
     x-transition:enter-start="-translate-x-full"
     x-transition:enter-end="translate-x-0"
     x-transition:leave="transition ease-in-out duration-300 transform"
     x-transition:leave-start="translate-x-0"
     x-transition:leave-end="-translate-x-full"
     class="mobile-sidebar md:hidden">
    
    <!-- Mobile Sidebar Content (same as desktop) -->
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <img src="/WhaMart_Logo.png" alt="Whamart Logo" class="sidebar-logo-img" 
                 onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'sidebar-logo-fallback\'>W</div>');">
        </div>
        <div class="sidebar-brand">
            <h3 class="sidebar-brand-title">Whamart</h3>
            <p class="sidebar-brand-subtitle">Vendor Dashboard</p>
        </div>
        <button @click="sidebarOpen = false" class="mobile-close-btn">
            <i class="fas fa-times"></i>
        </button>
    </div>

    <nav class="sidebar-nav">
        <div class="nav-section">
            <h4 class="nav-section-title">Main Menu</h4>
            <ul class="nav-list">
                @foreach ($navigation as $item)
                    <li class="nav-item">
                        <a href="{{ route($item['route']) }}" 
                           class="nav-link {{ request()->routeIs($item['route']) ? 'active' : '' }}"
                           style="--nav-color: {{ $item['color'] }}"
                           @click="sidebarOpen = false">
                            <div class="nav-icon">
                                <i class="{{ $item['icon'] }}"></i>
                            </div>
                            <span class="nav-text">{{ $item['name'] }}</span>
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>

        <div class="nav-section nav-section-bottom">
            <ul class="nav-list">
                @foreach ($bottomNavigation as $item)
                    <li class="nav-item">
                        <a href="{{ route($item['route']) }}" 
                           class="nav-link {{ request()->routeIs($item['route']) ? 'active' : '' }}"
                           style="--nav-color: {{ $item['color'] }}"
                           @click="sidebarOpen = false">
                            <div class="nav-icon">
                                <i class="{{ $item['icon'] }}"></i>
                            </div>
                            <span class="nav-text">{{ $item['name'] }}</span>
                        </a>
                    </li>
                @endforeach
            </ul>
        </div>
    </nav>

    <div class="sidebar-user">
        <div class="user-profile">
            <div class="user-avatar">
                @if(Auth::user()->profilePicture)
                    <img src="{{ Auth::user()->profilePicture }}" alt="Profile" class="user-avatar-img">
                @else
                    <div class="user-avatar-fallback">
                        {{ substr(Auth::user()->name, 0, 1) }}
                    </div>
                @endif
            </div>
            <div class="user-info">
                <h4 class="user-name">{{ Auth::user()->name }}</h4>
                <p class="user-role">Vendor</p>
            </div>
            <div class="user-actions">
                <form method="POST" action="{{ route('logout') }}" class="logout-form">
                    @csrf
                    <button type="submit" class="logout-btn" title="Logout">
                        <i class="fas fa-sign-out-alt"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
