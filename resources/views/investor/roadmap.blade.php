@extends('layouts.admin')

@section('content')
<div x-data="investorRoadmap()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Product Roadmap</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && roadmapData">
        <div>
            <!-- Quarter Selector -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <template x-for="quarter in roadmapData.quarters" :key="quarter">
                            <button @click="activeQuarter = quarter"
                                :class="{'border-blue-500 text-blue-600': activeQuarter === quarter, 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeQuarter !== quarter}"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                                <span x-text="quarter"></span>
                            </button>
                        </template>
                    </nav>
                </div>
            </div>

            <!-- Features Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <template x-for="feature in getFeaturesByQuarter(activeQuarter)" :key="feature.id">
                    <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <span class="px-3 py-1 text-xs font-semibold rounded-full" :class="getStatusColor(feature.status)" x-text="feature.status.replace('-', ' ')"></span>
                                <span class="px-3 py-1 text-xs font-semibold rounded-full" :class="getPriorityColor(feature.priority)" x-text="feature.priority"></span>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-2" x-text="feature.name"></h3>
                            <p class="text-sm text-gray-600 mb-4" x-text="feature.description"></p>
                            <div class="mb-4">
                                <div class="flex justify-between text-xs text-gray-600 mb-1"><span>Progress</span><span x-text="`${feature.progress}%`"></span></div>
                                <div class="w-full bg-gray-200 rounded-full h-2"><div class="bg-blue-600 h-2 rounded-full" :style="`width: ${feature.progress}%`"></div></div>
                            </div>
                            <button @click="handleFeedbackClick(feature)" class="w-full text-sm text-blue-600 font-semibold hover:text-blue-800">Provide Feedback</button>
                        </div>
                    </div>
                </template>
            </div>

            <!-- Milestones -->
            <div class="mt-12">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Major Milestones</h2>
                <div class="relative">
                    <div class="border-l-2 border-gray-200 absolute h-full top-0 left-4"></div>
                    <template x-for="(milestone, index) in roadmapData.milestones" :key="index">
                        <div class="flex items-center w-full mb-8">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white z-10">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>
                            </div>
                            <div class="ml-6">
                                <p class="font-bold text-gray-900" x-text="milestone.title"></p>
                                <p class="text-sm text-gray-600" x-text="milestone.date"></p>
                            </div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </template>

    <!-- Feedback Modal -->
    <div x-show="showFeedbackModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" x-cloak>
        <div class="relative top-20 mx-auto p-5 border w-full max-w-lg shadow-lg rounded-md bg-white">
            <div class="flex justify-between items-center pb-3 border-b">
                <p class="text-2xl font-bold">Feature Feedback</p>
                <button @click="showFeedbackModal = false" class="cursor-pointer z-50">
                    <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                </button>
            </div>
            <div class="mt-5">
                <template x-if="selectedFeature">
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2" x-text="selectedFeature.name"></h4>
                        <textarea x-model="feedbackText" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="Share your thoughts..."></textarea>
                        <div class="mt-4 flex justify-end">
                            <button @click="handleFeedbackSubmit()" class="px-4 py-2 bg-blue-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700">Submit</button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

<script>
function investorRoadmap() {
    return {
        loading: true,
        roadmapData: null,
        activeQuarter: 'Q3 2023',
        showFeedbackModal: false,
        selectedFeature: null,
        feedbackText: '',

        init() {
            this.fetchRoadmapData();
        },

        fetchRoadmapData() {
            this.loading = true;
            setTimeout(() => {
                this.roadmapData = {
                    quarters: ['Q3 2023', 'Q4 2023', 'Q1 2024', 'Q2 2024'],
                    features: [
                        { id: 1, name: 'AI Chat Recommendations', description: 'Intelligent product recommendations in chat.', status: 'in-development', eta: 'Q3 2023', priority: 'high', progress: 75 },
                        { id: 2, name: 'Multi-language Support', description: 'Support for 10 major Indian languages.', status: 'planning', eta: 'Q4 2023', priority: 'medium', progress: 30 },
                        { id: 3, name: 'Advanced Analytics', description: 'Enhanced analytics with customer behavior insights.', status: 'in-development', eta: 'Q3 2023', priority: 'high', progress: 60 },
                        { id: 4, name: 'Integrated Payments', description: 'Direct payment processing within the platform.', status: 'research', eta: 'Q1 2024', priority: 'medium', progress: 15 },
                        { id: 5, name: 'Vendor Mobile App', description: 'Native mobile app for vendors.', status: 'planning', eta: 'Q2 2024', priority: 'high', progress: 10 },
                        { id: 6, name: 'Automated Marketing', description: 'Tools for automated marketing campaigns.', status: 'research', eta: 'Q2 2024', priority: 'low', progress: 5 }
                    ],
                    milestones: [
                        { title: '10,000 Vendors Onboarded', date: 'August 2023' },
                        { title: 'Launch in Tier 2 Cities', date: 'November 2023' },
                        { title: 'Secure Series B Funding', date: 'January 2024' },
                        { title: 'Achieve 1M Active Users', date: 'June 2024' }
                    ]
                };
                this.loading = false;
            }, 500);
        },

        getStatusColor(status) {
            const colors = { 'in-development': 'bg-blue-100 text-blue-800', 'planning': 'bg-yellow-100 text-yellow-800', 'research': 'bg-purple-100 text-purple-800', 'completed': 'bg-green-100 text-green-800' };
            return colors[status] || 'bg-gray-100 text-gray-800';
        },

        getPriorityColor(priority) {
            const colors = { 'high': 'bg-red-100 text-red-800', 'medium': 'bg-yellow-100 text-yellow-800', 'low': 'bg-green-100 text-green-800' };
            return colors[priority] || 'bg-gray-100 text-gray-800';
        },

        getFeaturesByQuarter(quarter) {
            if (!this.roadmapData) return [];
            return this.roadmapData.features.filter(f => f.eta === quarter);
        },

        handleFeedbackClick(feature) {
            this.selectedFeature = feature;
            this.feedbackText = '';
            this.showFeedbackModal = true;
        },

        handleFeedbackSubmit() {
            console.log(`Feedback for '${this.selectedFeature.name}': ${this.feedbackText}`);
            this.showFeedbackModal = false;
            this.selectedFeature = null;
            this.feedbackText = '';
        }
    }
}
</script>
@endsection

