@extends('layouts.admin')

@section('content')
<div x-data="investorInvestments()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">My Investments</h1>
        <button @click="showAddInvestmentModal = true" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 font-semibold flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
            Add Investment
        </button>
    </div>

    <!-- Investment Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Total Invested</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(investmentsData.totalInvested)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Current Portfolio Value</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(investmentsData.currentValue)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Total Return</p>
            <p class="text-3xl font-bold mt-2" :class="investmentsData.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'" x-text="`${investmentsData.totalReturn.toFixed(2)}%`"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Total Equity Owned</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="`${investmentsData.equityOwned}%`"></p>
        </div>
    </div>

    <!-- Investments Table -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Investment History</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equity</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Documents</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="investment in investmentsData.investments" :key="investment.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="formatDate(investment.date)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-800" x-text="formatCurrency(investment.amount)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="`${investment.equityPercentage}%`"></td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 capitalize" x-text="investment.status"></span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <template x-for="doc in investment.documents" :key="doc.id">
                                    <a :href="doc.url" class="text-green-600 hover:text-green-800 mr-2" download x-text="doc.name"></a>
                                </template>
                            </td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add Investment Modal -->
    <div x-show="showAddInvestmentModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.away="showAddInvestmentModal = false">
        <div class="bg-white p-8 rounded-lg shadow-2xl w-full max-w-lg" @click.stop>
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Add New Investment</h2>
            <form @submit.prevent="handleSubmit">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Amount (INR)</label>
                        <input type="number" x-model="formData.amount" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="date" x-model="formData.date" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea x-model="formData.notes" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" @click="showAddInvestmentModal = false" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">Cancel</button>
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg">Submit Investment</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function investorInvestments() {
    return {
        loading: true,
        investmentsData: { investments: [], nextMilestones: [], investmentOpportunities: [] },
        showAddInvestmentModal: false,
        formData: { amount: '', date: '', notes: '' },

        init() {
            this.fetchInvestmentsData();
        },

        fetchInvestmentsData() {
            this.loading = true;
            setTimeout(() => {
                this.investmentsData = {
                    investments: [
                        { id: 1, amount: 300000, date: '2023-01-01', equityPercentage: 5, status: 'completed', documents: [{ id: 1, name: 'Agreement.pdf', url: '#' }] }
                    ],
                    totalInvested: 300000,
                    currentValue: 345000,
                    totalReturn: 15,
                    equityOwned: 5,
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
        },

        handleSubmit() {
            const newInvestment = {
                id: Date.now(),
                amount: parseFloat(this.formData.amount),
                date: this.formData.date,
                equityPercentage: 0, // Placeholder
                status: 'pending',
                documents: []
            };

            this.investmentsData.investments.push(newInvestment);
            this.investmentsData.totalInvested += newInvestment.amount;

            this.showAddInvestmentModal = false;
            this.formData = { amount: '', date: '', notes: '' };
        }
    }
}
</script>
@endsection

