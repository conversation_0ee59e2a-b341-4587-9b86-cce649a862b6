@extends('layouts.admin')

@section('content')
<div x-data="investorVendorAnalytics()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Vendor Analytics</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && vendorData">
        <div>
            <!-- Vendor Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Total Vendors</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="vendorData.vendorMetrics.totalVendors"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Active Vendors</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="vendorData.vendorMetrics.activeVendors"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Vendor Growth</p><p class="text-3xl font-bold mt-2 text-green-600" x-text="`${formatPercentage(vendorData.vendorMetrics.vendorGrowthRate)}`"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Retention Rate</p><p class="text-3xl font-bold mt-2 text-blue-600" x-text="`${formatPercentage(vendorData.vendorMetrics.vendorRetentionRate)}`"></p></div>
            </div>

            <!-- Vendor List -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">All Vendors</h2>
                <div class="flex items-center justify-between mb-4">
                    <input type="text" x-model="searchTerm" placeholder="Search vendors..." class="w-full md:w-1/3 px-4 py-2 border border-gray-300 rounded-lg">
                    <select x-model="filterStatus" class="ml-4 px-4 py-2 border border-gray-300 rounded-lg">
                        <option value="all">All Statuses</option><option value="active">Active</option><option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Join Date</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Revenue</th></tr></thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="vendor in paginatedVendors" :key="vendor.id">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="vendor.name"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="vendor.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" x-text="vendor.status"></span></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(vendor.joinDate)"></td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900" x-text="formatCurrency(vendor.revenue)"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
                <!-- Pagination -->
                <div class="mt-4 flex justify-between items-center">
                    <p class="text-sm text-gray-700">Showing <span x-text="(currentPage - 1) * itemsPerPage + 1"></span> to <span x-text="Math.min(currentPage * itemsPerPage, filteredVendors.length)"></span> of <span x-text="filteredVendors.length"></span> results</p>
                    <div>
                        <button @click="currentPage = Math.max(1, currentPage - 1)" :disabled="currentPage === 1" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Prev</button>
                        <button @click="currentPage = Math.min(totalPages, currentPage + 1)" :disabled="currentPage === totalPages" class="ml-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</button>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function investorVendorAnalytics() {
    return {
        loading: true,
        vendorData: null,
        searchTerm: '',
        filterStatus: 'all',
        currentPage: 1,
        itemsPerPage: 10,

        init() {
            this.fetchVendorData();
        },

        fetchVendorData() {
            this.loading = true;
            setTimeout(() => {
                this.vendorData = {
                    vendorMetrics: { totalVendors: 128, activeVendors: 115, vendorGrowthRate: 13.2, vendorRetentionRate: 92 },
                    vendorList: Array.from({ length: 128 }, (_, i) => ({
                        id: i + 1, name: `Vendor ${i + 1}`,
                        status: i % 10 === 0 ? 'inactive' : 'active',
                        joinDate: new Date(2023, 0, i + 1).toISOString().split('T')[0],
                        revenue: Math.floor(500 + Math.random() * 2000)
                    }))
                };
                this.loading = false;
            }, 500);
        },

        get filteredVendors() {
            if (!this.vendorData) return [];
            return this.vendorData.vendorList.filter(vendor => {
                const matchesSearch = vendor.name.toLowerCase().includes(this.searchTerm.toLowerCase());
                const matchesStatus = this.filterStatus === 'all' || vendor.status === this.filterStatus;
                return matchesSearch && matchesStatus;
            });
        },

        get totalPages() {
            return Math.ceil(this.filteredVendors.length / this.itemsPerPage);
        },

        get paginatedVendors() {
            const start = (this.currentPage - 1) * this.itemsPerPage;
            const end = start + this.itemsPerPage;
            return this.filteredVendors.slice(start, end);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatPercentage(value) {
            return `${value.toFixed(1)}%`;
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-IN', { year: 'numeric', month: 'short', day: 'numeric' });
        }
    }
}
</script>
@endsection

