@extends('layouts.admin')

@section('content')
<div x-data="investorDashboard()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Investor Dashboard</h1>
        <a href="#" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 font-semibold flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
            Add Capital
        </a>
    </div>

    <!-- Overview Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <template x-for="card in statsCards" :key="card.title">
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="flex justify-between items-start">
                    <p class="text-gray-600 text-sm" x-text="card.title"></p>
                    <div class="p-2 rounded-full" :class="card.iconBg">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" :class="card.iconColor" fill="none" viewBox="0 0 24 24" stroke="currentColor" x-html="card.icon"></svg>
                    </div>
                </div>
                <p class="text-3xl font-bold text-gray-900 mt-2" x-text="card.value"></p>
                <div class="flex items-center text-sm mt-1" :class="card.change > 0 ? 'text-green-600' : 'text-red-600'">
                     <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="card.change > 0 ? 'M12 19V5m-7 7l7-7 7 7' : 'M12 5v14m-7-7l7 7 7-7'" />
                    </svg>
                    <span x-text="`${Math.abs(card.change)}% vs last month`"></span>
                </div>
            </div>
        </template>
    </div>

    <!-- Vendor & Revenue Metrics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Vendor Metrics -->
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Vendor Metrics</h2>
            <div class="space-y-4">
                <div class="flex justify-between items-center">
                    <p class="text-gray-600">Total Vendors</p>
                    <p class="font-bold text-gray-900" x-text="dashboardData.vendorMetrics.totalVendors"></p>
                </div>
                <div class="flex justify-between items-center">
                    <p class="text-gray-600">New This Month</p>
                    <p class="font-bold text-green-600" x-text="`+${dashboardData.vendorMetrics.newVendorsThisMonth}`"></p>
                </div>
                <div>
                    <p class="text-gray-600 mb-1">Vendors by Plan</p>
                    <div class="space-y-2">
                        <template x-for="plan in dashboardData.vendorMetrics.vendorsByPlan" :key="plan.plan">
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div class="h-2.5 rounded-full" :class="plan.color" :style="`width: ${plan.count / dashboardData.vendorMetrics.totalVendors * 100}%`"></div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Metrics -->
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Revenue Metrics</h2>
            <p class="text-gray-600">Projected Annual Revenue</p>
            <p class="text-2xl font-bold text-gray-900 mb-4" x-text="formatCurrency(dashboardData.revenueMetrics.projectedAnnualRevenue)"></p>
            <p class="text-gray-600 mb-2">Monthly Revenue (Last 5 Months)</p>
            <div class="flex items-end h-24 space-x-2">
                <template x-for="month in dashboardData.revenueMetrics.monthlyRevenue" :key="month.month">
                    <div class="flex-1 bg-green-400 rounded-t-md" :style="`height: ${(month.value / 120000) * 100}%`" :title="`${month.month}: ${formatCurrency(month.value)}`"></div>
                </template>
            </div>
        </div>
    </div>

     <!-- Market Metrics -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Market & Growth</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div>
                <p class="text-sm text-gray-500">Total Market Size</p>
                <p class="text-2xl font-bold" x-text="`${(dashboardData.marketMetrics.totalMarketSize / 100000).toFixed(1)} Lakh`"></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Current Market Share</p>
                <p class="text-2xl font-bold" x-text="`${dashboardData.marketMetrics.currentMarketShare}%`"></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">WhaMart Growth (YoY)</p>
                <p class="text-2xl font-bold text-green-600" x-text="`${dashboardData.marketMetrics.whamartGrowth}%`"></p>
            </div>
            <div>
                <p class="text-sm text-gray-500">Competitor Growth (YoY)</p>
                <p class="text-2xl font-bold text-red-500" x-text="`${dashboardData.marketMetrics.competitorGrowth}%`"></p>
            </div>
        </div>
    </div>

</div>

<script>
function investorDashboard() {
    return {
        loading: true,
        dashboardData: {
            overview: { totalInvestment: {}, currentValue: {}, equityPercentage: {}, monthlyRevenue: {} },
            vendorMetrics: { vendorsByPlan: [] },
            revenueMetrics: { monthlyRevenue: [], projectedAnnualRevenue: 0 },
            marketMetrics: { totalMarketSize: 0 }
        },

        init() {
            this.fetchDashboardData();
        },

        fetchDashboardData() {
            this.loading = true;
            setTimeout(() => {
                this.dashboardData = {
                    overview: {
                        totalInvestment: { value: 300000, change: 0, trend: 'neutral' },
                        currentValue: { value: 345000, change: 15, trend: 'up' },
                        equityPercentage: { value: 5, change: 0, trend: 'neutral' },
                        monthlyRevenue: { value: 117000, change: 12.5, trend: 'up' }
                    },
                    vendorMetrics: {
                        totalVendors: 780,
                        newVendorsThisMonth: 120,
                        vendorsByPlan: [
                            { plan: 'Standard', count: 468, color: 'bg-blue-500' },
                            { plan: 'Gold', count: 234, color: 'bg-yellow-500' },
                            { plan: 'Premium', count: 78, color: 'bg-purple-500' }
                        ],
                    },
                    revenueMetrics: {
                        monthlyRevenue: [
                            { month: 'May', value: 78000 },
                            { month: 'Jun', value: 89000 },
                            { month: 'Jul', value: 98000 },
                            { month: 'Aug', value: 104000 },
                            { month: 'Sep', value: 117000 }
                        ],
                        projectedAnnualRevenue: 1800000,
                    },
                    marketMetrics: {
                        totalMarketSize: 500000,
                        currentMarketShare: 0.156,
                        whamartGrowth: 85,
                        competitorGrowth: 25
                    }
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatPercentage(value) {
            return `${value.toFixed(2)}%`;
        },

        get statsCards() {
            const overview = this.dashboardData.overview;
            return [
                {
                    title: 'Total Investment',
                    value: this.formatCurrency(overview.totalInvestment.value),
                    change: overview.totalInvestment.change,
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414-.336.75-.75.75h-.75m0-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-1.5a1.125 1.125 0 0 1-1.125-1.125v-9.75c0-.621.504-1.125 1.125-1.125h1.5Z" />',
                    iconBg: 'bg-blue-100',
                    iconColor: 'text-blue-600'
                },
                {
                    title: 'Current Value',
                    value: this.formatCurrency(overview.currentValue.value),
                    change: overview.currentValue.change,
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941" />',
                    iconBg: 'bg-green-100',
                    iconColor: 'text-green-600'
                },
                {
                    title: 'Equity Percentage',
                    value: this.formatPercentage(overview.equityPercentage.value),
                    change: overview.equityPercentage.change,
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" d="M10.125 2.25h-4.5c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125v-9M10.125 2.25h.375a9 9 0 0 1 9 9v.375M10.125 2.25A3.375 3.375 0 0 1 13.5 5.625v1.5c0 .621.504 1.125 1.125 1.125h1.5a3.375 3.375 0 0 1 3.375 3.375M9 15l2.25 2.25L15 12" />',
                    iconBg: 'bg-indigo-100',
                    iconColor: 'text-indigo-600'
                },
                {
                    title: 'Monthly Revenue',
                    value: this.formatCurrency(overview.monthlyRevenue.value),
                    change: overview.monthlyRevenue.change,
                    icon: '<path stroke-linecap="round" stroke-linejoin="round" d="M2.25 8.25h19.5M2.25 9h19.5m-16.5 5.25h6m-6 2.25h3m-3.75 3h15a2.25 2.25 0 0 0 2.25-2.25V6.75A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25v10.5A2.25 2.25 0 0 0 4.5 21Z" />',
                    iconBg: 'bg-yellow-100',
                    iconColor: 'text-yellow-600'
                }
            ];
        }
    }
}
</script>
@endsection

