@extends('layouts.admin')

@section('content')
<div x-data="investorCapital()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-800">Capital Management</h1>
        <button @click="showAddCapitalModal = true" class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 font-semibold flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" /></svg>
            Add Capital
        </button>
    </div>

    <!-- Capital Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Total Invested</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(capitalData.capitalSummary.totalInvested)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Current Value</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(capitalData.capitalSummary.currentValue)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Return on Investment</p>
            <p class="text-3xl font-bold mt-2" :class="capitalData.capitalSummary.returnOnInvestment >= 0 ? 'text-green-600' : 'text-red-600'" x-text="`${capitalData.capitalSummary.returnOnInvestment.toFixed(2)}%`"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Equity Percentage</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="`${capitalData.capitalSummary.equityPercentage}%`"></p>
        </div>
    </div>

    <!-- Capital Transactions -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Capital Transactions</h2>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equity Received</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <template x-for="transaction in capitalData.capitalTransactions" :key="transaction.id">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="formatDate(transaction.date)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-800" x-text="formatCurrency(transaction.amount)"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600 capitalize" x-text="transaction.type"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="`${transaction.equityReceived}%`"></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600" x-text="transaction.notes"></td>
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Add Capital Modal -->
    <div x-show="showAddCapitalModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.away="showAddCapitalModal = false">
        <div class="bg-white p-8 rounded-lg shadow-2xl w-full max-w-lg" @click.stop>
            <h2 class="text-2xl font-bold text-gray-800 mb-6">Add New Capital</h2>
            <form @submit.prevent="handleSubmit">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Amount (INR)</label>
                        <input type="number" x-model="formData.amount" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date</label>
                        <input type="date" x-model="formData.date" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Type</label>
                        <select x-model="formData.type" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm">
                            <option value="investment">Investment</option>
                            <option value="loan">Loan</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Notes</label>
                        <textarea x-model="formData.notes" rows="3" class="mt-1 block w-full border-gray-300 rounded-md shadow-sm"></textarea>
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-4">
                    <button type="button" @click="showAddCapitalModal = false" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">Cancel</button>
                    <button type="submit" class="bg-green-500 text-white px-4 py-2 rounded-lg">Add Capital</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function investorCapital() {
    return {
        loading: true,
        capitalData: { capitalSummary: {}, capitalTransactions: [] },
        showAddCapitalModal: false,
        formData: { amount: '', date: '', type: 'investment', notes: '' },

        init() {
            this.fetchCapitalData();
        },

        fetchCapitalData() {
            this.loading = true;
            setTimeout(() => {
                this.capitalData = {
                    capitalSummary: {
                        totalInvested: 300000,
                        currentValue: 345000,
                        returnOnInvestment: 15,
                        equityPercentage: 5
                    },
                    capitalTransactions: [
                        { id: 1, date: '2025-05-15', amount: 300000, type: 'investment', equityReceived: 5, notes: 'Initial investment' }
                    ]
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
        },

        handleSubmit() {
            const newTransaction = {
                id: Date.now(),
                date: this.formData.date,
                amount: parseFloat(this.formData.amount),
                type: this.formData.type,
                equityReceived: this.formData.type === 'investment' ? (parseFloat(this.formData.amount) / 6000000 * 100) : 0, // Example equity calculation
                notes: this.formData.notes
            };

            this.capitalData.capitalTransactions.push(newTransaction);
            this.capitalData.capitalSummary.totalInvested += newTransaction.amount;
            // Recalculate other summary data as needed

            this.showAddCapitalModal = false;
            this.formData = { amount: '', date: '', type: 'investment', notes: '' };
        }
    }
}
</script>
@endsection

