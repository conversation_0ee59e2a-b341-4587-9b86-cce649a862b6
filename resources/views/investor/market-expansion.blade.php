@extends('layouts.admin')

@section('content')
<div x-data="investorMarketExpansion()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Market Expansion</h1>

    <!-- Expansion Summary -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Current Cities</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="expansionData.currentMarkets.totalCities"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Planned Expansions</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="expansionData.expansionPlans.plannedCities"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Overall Market Penetration</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="`${expansionData.marketPenetration.overall}%`"></p>
        </div>
    </div>

    <!-- Current & Upcoming Markets -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Current Markets</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">City</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">Revenue</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">Growth</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="city in expansionData.currentMarkets.activeCities" :key="city.name">
                            <tr>
                                <td class="px-4 py-2" x-text="city.name"></td>
                                <td class="px-4 py-2" x-text="formatCurrency(city.revenue)"></td>
                                <td class="px-4 py-2 text-green-600" x-text="`+${city.growth}%`"></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Upcoming Expansions</h2>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">City</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">Target Date</th>
                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500">Est. Revenue</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="city in expansionData.expansionPlans.upcomingExpansions" :key="city.name">
                            <tr>
                                <td class="px-4 py-2" x-text="city.name"></td>
                                <td class="px-4 py-2" x-text="formatDate(city.targetDate)"></td>
                                <td class="px-4 py-2" x-text="formatCurrency(city.estimatedRevenue)"></td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Expansion Strategy & Market Sizing -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Expansion Strategy & Market Sizing</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
                <h3 class="font-semibold text-gray-700 mb-2">Key Success Factors</h3>
                <ul class="list-disc list-inside space-y-1 text-sm text-gray-600">
                    <template x-for="factor in expansionData.expansionStrategy.keyFactors" :key="factor">
                        <li x-text="factor"></li>
                    </template>
                </ul>
            </div>
            <div>
                <h3 class="font-semibold text-gray-700 mb-2">Target Market Share</h3>
                <div class="space-y-3">
                    <div>
                        <div class="flex justify-between text-sm"><span class="text-gray-600">1-Year Target</span><span x-text="`${expansionData.marketSizing.targetMarketShare.oneYear}%`"></span></div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="bg-green-500 h-2.5 rounded-full" :style="`width: ${expansionData.marketSizing.targetMarketShare.oneYear}%`"></div></div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm"><span class="text-gray-600">3-Year Target</span><span x-text="`${expansionData.marketSizing.targetMarketShare.threeYear}%`"></span></div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="bg-blue-500 h-2.5 rounded-full" :style="`width: ${expansionData.marketSizing.targetMarketShare.threeYear}%`"></div></div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm"><span class="text-gray-600">5-Year Target</span><span x-text="`${expansionData.marketSizing.targetMarketShare.fiveYear}%`"></span></div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5"><div class="bg-purple-500 h-2.5 rounded-full" :style="`width: ${expansionData.marketSizing.targetMarketShare.fiveYear}%`"></div></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function investorMarketExpansion() {
    return {
        loading: true,
        expansionData: { currentMarkets: { activeCities: [] }, expansionPlans: { upcomingExpansions: [] }, marketPenetration: {}, expansionStrategy: { keyFactors: [] }, marketSizing: { targetMarketShare: {} } },

        init() {
            this.fetchExpansionData();
        },

        fetchExpansionData() {
            this.loading = true;
            setTimeout(() => {
                this.expansionData = {
                    currentMarkets: {
                        totalCities: 12,
                        activeCities: [
                            { name: 'Mumbai', revenue: 31250, growth: 15 },
                            { name: 'Delhi', revenue: 27500, growth: 12 },
                            { name: 'Bangalore', revenue: 21875, growth: 18 },
                        ]
                    },
                    expansionPlans: {
                        plannedCities: 8,
                        upcomingExpansions: [
                            { name: 'Surat', targetDate: '2024-09-30', estimatedRevenue: 12000 },
                            { name: 'Bhopal', targetDate: '2024-10-31', estimatedRevenue: 9500 },
                            { name: 'Nagpur', targetDate: '2024-11-30', estimatedRevenue: 8000 },
                        ]
                    },
                    marketPenetration: { overall: 1.38 },
                    expansionStrategy: {
                        keyFactors: ['Hyperlocal marketing', 'Vendor onboarding incentives', 'Logistics partnerships']
                    },
                    marketSizing: {
                        targetMarketShare: { oneYear: 5, threeYear: 15, fiveYear: 30 }
                    }
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { month: 'short', year: 'numeric' });
        }
    }
}
</script>
@endsection

