@extends('layouts.admin')

@section('content')
<div x-data="investorRevenueAnalytics()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Revenue Analytics</h1>

    <template x-if="loading">
        <div class="flex justify-center items-center h-64"><div class="loader"></div></div>
    </template>

    <template x-if="!loading && revenueData">
        <div>
            <!-- Revenue Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Total Revenue</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(revenueData.revenueMetrics.totalRevenue)"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Monthly Revenue</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(revenueData.revenueMetrics.monthlyRevenue)"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Revenue Growth</p><p class="text-3xl font-bold mt-2 text-green-600" x-text="`${formatPercentage(revenueData.revenueMetrics.revenueGrowthRate)}`"></p></div>
                <div class="bg-white p-6 rounded-lg shadow-lg"><p class="text-gray-600 text-sm">Avg. Revenue/Vendor</p><p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(revenueData.revenueMetrics.averageRevenuePerVendor)"></p></div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Left Column -->
                <div class="lg:col-span-2">
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Expense Breakdown</h2>
                        <div class="space-y-4">
                            <template x-for="expense in revenueData.expenseBreakdown" :key="expense.category">
                                <div class="flex items-center">
                                    <span class="w-48 text-sm font-medium text-gray-900" x-text="expense.category"></span>
                                    <div class="flex-1"><div class="h-4 bg-gray-200 rounded-full"><div class="h-full bg-blue-500 rounded-full" :style="`width: ${expense.percentage}%`"></div></div></div>
                                    <span class="w-24 text-right text-sm font-medium text-gray-900" x-text="formatCurrency(expense.value)"></span>
                                    <span class="w-16 text-right text-xs text-gray-500" x-text="formatPercentage(expense.percentage)"></span>
                                </div>
                            </template>
                        </div>
                        <div class="mt-6 pt-6 border-t border-gray-200 flex justify-between items-center">
                            <div><p class="text-sm text-gray-500">Total Monthly Expenses</p><p class="text-xl font-bold text-gray-900" x-text="formatCurrency(revenueData.expenseBreakdown.reduce((acc, curr) => acc + curr.value, 0))"></p></div>
                            <div><p class="text-sm text-gray-500">Monthly Profit</p><p class="text-xl font-bold text-green-600" x-text="formatCurrency(revenueData.revenueMetrics.monthlyRevenue - revenueData.expenseBreakdown.reduce((acc, curr) => acc + curr.value, 0))"></p></div>
                        </div>
                    </div>
                </div>

                <!-- Right Column -->
                <div>
                    <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Revenue by Plan</h2>
                        <div class="space-y-3">
                            <template x-for="plan in revenueData.revenueByPlan" :key="plan.plan">
                                <div class="flex items-center">
                                    <span class="w-24 text-sm font-medium text-gray-900" x-text="plan.plan"></span>
                                    <div class="flex-1"><div class="h-4 bg-gray-200 rounded-full"><div class="h-full bg-green-500 rounded-full" :style="`width: ${plan.percentage}%`"></div></div></div>
                                    <span class="w-16 text-right text-xs text-gray-500" x-text="formatPercentage(plan.percentage)"></span>
                                </div>
                            </template>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Profit Margin</h2>
                        <div class="space-y-4">
                            <div><div class="flex justify-between items-center mb-1"><p class="text-sm text-gray-500">Current</p><p class="text-lg font-bold text-gray-900" x-text="formatPercentage(revenueData.profitMargin.current)"></p></div><div class="h-2 bg-gray-200 rounded-full"><div class="h-full bg-green-500 rounded-full" :style="`width: ${revenueData.profitMargin.current}%`"></div></div></div>
                            <div><div class="flex justify-between items-center mb-1"><p class="text-sm text-gray-500">5-Year Projection</p><p class="text-lg font-bold text-gray-900" x-text="formatPercentage(revenueData.profitMargin.projected.fiveYear)"></p></div><div class="h-2 bg-gray-200 rounded-full"><div class="h-full bg-green-500 rounded-full" :style="`width: ${revenueData.profitMargin.projected.fiveYear}%`"></div></div></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Revenue by Location -->
            <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Revenue by Location</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50"><tr><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">City</th><th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Revenue</th></tr></thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="location in revenueData.revenueByLocation" :key="location.city">
                                <tr><td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="location.city"></td><td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatCurrency(location.value)"></td></tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function investorRevenueAnalytics() {
    return {
        loading: true,
        revenueData: null,

        init() {
            this.fetchRevenueData();
        },

        fetchRevenueData() {
            this.loading = true;
            setTimeout(() => {
                this.revenueData = {
                    revenueMetrics: { totalRevenue: 1450000, monthlyRevenue: 125000, revenueGrowthRate: 8.5, averageRevenuePerVendor: 976 },
                    revenueByPlan: [
                        { plan: 'Standard', value: 58500, percentage: 46.8 },
                        { plan: 'Gold', value: 42000, percentage: 33.6 },
                        { plan: 'Premium', value: 24500, percentage: 19.6 }
                    ],
                    revenueByLocation: [
                        { city: 'Mumbai', value: 31250 }, { city: 'Delhi', value: 27500 }, { city: 'Bangalore', value: 21875 },
                        { city: 'Hyderabad', value: 15000 }, { city: 'Chennai', value: 12500 }, { city: 'Kolkata', value: 10000 },
                        { city: 'Pune', value: 6875 }
                    ],
                    expenseBreakdown: [
                        { category: 'Server & Infrastructure', value: 25000, percentage: 20 },
                        { category: 'Marketing & Acquisition', value: 37500, percentage: 30 },
                        { category: 'Development', value: 31250, percentage: 25 },
                        { category: 'Operations & Support', value: 18750, percentage: 15 },
                        { category: 'Administrative', value: 12500, percentage: 10 }
                    ],
                    profitMargin: { current: 20.4, projected: { fiveYear: 35.2 } }
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatPercentage(value) {
            return `${value.toFixed(1)}%`;
        }
    }
}
</script>
@endsection

