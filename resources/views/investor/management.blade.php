@extends('layouts.admin')

@section('content')
<div x-data="investorManagement()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Governance & Management</h1>

    <!-- Tabs -->
    <div class="mb-6 border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <a href="#" @click.prevent="activeTab = 'board'" :class="{ 'border-green-500 text-green-600': activeTab === 'board', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'board' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Board & Management</a>
            <a href="#" @click.prevent="activeTab = 'meetings'" :class="{ 'border-green-500 text-green-600': activeTab === 'meetings', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'meetings' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Meetings</a>
            <a href="#" @click.prevent="activeTab = 'proposals'" :class="{ 'border-green-500 text-green-600': activeTab === 'proposals', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'proposals' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Proposals</a>
            <a href="#" @click.prevent="activeTab = 'documents'" :class="{ 'border-green-500 text-green-600': activeTab === 'documents', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'documents' }" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">Documents</a>
        </nav>
    </div>

    <!-- Board & Management Tab -->
    <div x-show="activeTab === 'board'">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Board Members</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <template x-for="member in managementData.boardMembers" :key="member.id">
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <div class="flex items-center mb-4">
                        <img :src="member.image" class="h-16 w-16 rounded-full mr-4">
                        <div>
                            <p class="font-bold text-lg text-gray-900" x-text="member.name"></p>
                            <p class="text-sm text-gray-600" x-text="member.role"></p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-700" x-text="member.bio"></p>
                </div>
            </template>
        </div>
    </div>

    <!-- Meetings Tab -->
    <div x-show="activeTab === 'meetings'">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Board Meetings</h2>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <ul class="divide-y divide-gray-200">
                <template x-for="meeting in managementData.boardMeetings" :key="meeting.id">
                    <li class="py-4">
                        <p class="font-semibold text-gray-800" x-text="meeting.title"></p>
                        <p class="text-sm text-gray-600" x-text="formatDate(meeting.date)"></p>
                        <a :href="meeting.agendaUrl" class="text-sm text-green-600 hover:text-green-800">View Agenda</a>
                    </li>
                </template>
            </ul>
        </div>
    </div>

    <!-- Proposals Tab -->
    <div x-show="activeTab === 'proposals'">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Board Proposals</h2>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Proposal</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Action</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <template x-for="proposal in managementData.boardProposals" :key="proposal.id">
                            <tr>
                                <td class="px-6 py-4" x-text="proposal.title"></td>
                                <td class="px-6 py-4"><span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" :class="proposal.status === 'Passed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'" x-text="proposal.status"></span></td>
                                <td class="px-6 py-4">
                                    <button @click="handleVoteClick(proposal)" class="text-green-600 hover:text-green-800 font-semibold" :disabled="proposal.voted">Vote</button>
                                </td>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Documents Tab -->
    <div x-show="activeTab === 'documents'">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Governance Documents</h2>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <ul class="divide-y divide-gray-200">
                <template x-for="doc in managementData.governanceDocuments" :key="doc.id">
                    <li class="py-3 flex justify-between items-center">
                        <span x-text="doc.name"></span>
                        <a :href="doc.url" class="text-green-600 hover:text-green-800 font-semibold" download>Download</a>
                    </li>
                </template>
            </ul>
        </div>
    </div>

    <!-- Vote Modal -->
    <div x-show="showVoteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click.away="showVoteModal = false">
        <div class="bg-white p-8 rounded-lg shadow-2xl w-full max-w-lg" @click.stop x-show="selectedProposal">
            <h3 class="text-xl font-bold text-gray-800 mb-4" x-text="selectedProposal.title"></h3>
            <p class="text-sm text-gray-600 mb-6" x-text="selectedProposal.description"></p>
            <div class="space-y-4">
                <label class="flex items-center"><input type="radio" x-model="voteValue" value="for" class="mr-2"> For</label>
                <label class="flex items-center"><input type="radio" x-model="voteValue" value="against" class="mr-2"> Against</label>
                <label class="flex items-center"><input type="radio" x-model="voteValue" value="abstain" class="mr-2"> Abstain</label>
            </div>
            <div class="mt-6 flex justify-end space-x-4">
                <button @click="showVoteModal = false" class="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">Cancel</button>
                <button @click="handleVoteSubmit()" class="bg-green-500 text-white px-4 py-2 rounded-lg" :disabled="!voteValue">Submit Vote</button>
            </div>
        </div>
    </div>
</div>

<script>
function investorManagement() {
    return {
        loading: true,
        managementData: { boardMembers: [], boardMeetings: [], boardProposals: [], governanceDocuments: [] },
        activeTab: 'board',
        showVoteModal: false,
        selectedProposal: null,
        voteValue: null,

        init() {
            this.fetchManagementData();
        },

        fetchManagementData() {
            this.loading = true;
            setTimeout(() => {
                this.managementData = {
                    boardMembers: [
                        { id: 1, name: 'Karan Solanki', role: 'Founder & CEO', image: 'https://randomuser.me/api/portraits/men/1.jpg', bio: 'Founder and CEO of WhaMart.' },
                        { id: 2, name: 'Rajiv Mehta', role: 'Strategic Investor', image: 'https://randomuser.me/api/portraits/men/3.jpg', bio: 'Angel investor with a portfolio of successful startups.' },
                        { id: 3, name: 'Current Investor', role: 'Investor', image: 'https://randomuser.me/api/portraits/men/5.jpg', bio: 'Your position on the board.' }
                    ],
                    boardMeetings: [
                        { id: 1, title: 'Q2 2024 Board Meeting', date: '2024-06-30', agendaUrl: '#' },
                        { id: 2, title: 'Q1 2024 Board Meeting', date: '2024-03-31', agendaUrl: '#' }
                    ],
                    boardProposals: [
                        { id: 1, title: 'Approve Q2 Budget', description: 'Proposal to approve the operating budget for the second quarter.', status: 'Voting Open', voted: false },
                        { id: 2, title: 'New Stock Option Plan', description: 'Proposal for a new employee stock option plan.', status: 'Passed', voted: true }
                    ],
                    governanceDocuments: [
                        { id: 1, name: 'Articles of Incorporation', url: '#' },
                        { id: 2, name: 'Shareholder Agreement', url: '#' }
                    ]
                };
                this.loading = false;
            }, 500);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'long', year: 'numeric' });
        },

        handleVoteClick(proposal) {
            if (proposal.voted) return;
            this.selectedProposal = proposal;
            this.voteValue = null;
            this.showVoteModal = true;
        },

        handleVoteSubmit() {
            if (!this.selectedProposal || !this.voteValue) return;
            // In a real app, this would send the vote to the server
            this.selectedProposal.voted = true;
            this.selectedProposal.status = `Voted: ${this.voteValue}`;
            this.showVoteModal = false;
        }
    }
}
</script>
@endsection

