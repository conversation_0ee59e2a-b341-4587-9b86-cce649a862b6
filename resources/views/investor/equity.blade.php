@extends('layouts.admin')

@section('content')
<div x-data="investorEquity()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Equity Overview</h1>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Left Column -->
        <div class="lg:col-span-2 space-y-8">
            <!-- Equity Distribution -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Equity Distribution</h2>
                <div class="space-y-4">
                    <template x-for="holder in equityData.equityDistribution" :key="holder.holder">
                        <div class="flex items-center">
                            <div class="w-2/5 text-sm font-medium text-gray-700" x-text="holder.holder"></div>
                            <div class="w-3/5 bg-gray-200 rounded-full h-6">
                                <div :style="`width: ${holder.percentage}%; background-color: ${holder.color};`" class="h-6 rounded-full flex items-center justify-center text-white text-xs font-bold">
                                    <span x-text="`${holder.percentage}%`"></span>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Company Valuation -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Company Valuation</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div>
                        <p class="text-gray-600 text-sm">Current Valuation</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1" x-text="formatCurrency(equityData.companyValuation.current)"></p>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">Previous Round</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1" x-text="formatCurrency(equityData.companyValuation.previousRound)"></p>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">Growth</p>
                        <p class="text-2xl font-bold text-green-600 mt-1" x-text="`${equityData.companyValuation.growth}%`"></p>
                    </div>
                    <div>
                        <p class="text-gray-600 text-sm">Your Equity Value</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1" x-text="formatCurrency(equityData.investmentDetails.currentValue)"></p>
                    </div>
                </div>
            </div>

            <!-- Future Funding Rounds -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Future Funding Rounds</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Next Round Details</h3>
                        <p class="text-sm"><span class="font-medium">Target Date:</span> <span x-text="formatDate(equityData.futureRounds.nextRound.targetDate)"></span></p>
                        <p class="text-sm"><span class="font-medium">Target Valuation:</span> <span x-text="formatCurrency(equityData.futureRounds.nextRound.targetValuation)"></span></p>
                        <p class="text-sm"><span class="font-medium">Funding Target:</span> <span x-text="formatCurrency(equityData.futureRounds.nextRound.fundingTarget)"></span></p>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-700 mb-2">Potential Dilution Impact</h3>
                        <p class="text-sm"><span class="font-medium">Current Equity:</span> <span x-text="`${equityData.futureRounds.dilutionImpact.currentEquity}%`"></span></p>
                        <p class="text-sm"><span class="font-medium">Post-Dilution Equity:</span> <span x-text="`${equityData.futureRounds.dilutionImpact.postDilution}%`"></span></p>
                        <p class="text-sm"><span class="font-medium">Projected Value:</span> <span class="font-bold text-green-600" x-text="formatCurrency(equityData.futureRounds.dilutionImpact.value.postDilution)"></span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-8">
            <!-- Investment Details -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Your Investment</h2>
                <div class="space-y-2">
                    <p class="text-sm flex justify-between"><span>Initial Investment:</span> <span class="font-semibold" x-text="formatCurrency(equityData.investmentDetails.initialInvestment)"></span></p>
                    <p class="text-sm flex justify-between"><span>Investment Date:</span> <span class="font-semibold" x-text="formatDate(equityData.investmentDetails.investmentDate)"></span></p>
                    <p class="text-sm flex justify-between"><span>Equity Stake:</span> <span class="font-semibold" x-text="`${equityData.investmentDetails.equityPercentage}%`"></span></p>
                    <p class="text-sm flex justify-between"><span>Current Value:</span> <span class="font-semibold" x-text="formatCurrency(equityData.investmentDetails.currentValue)"></span></p>
                    <p class="text-sm flex justify-between"><span>ROI:</span> <span class="font-semibold text-green-600" x-text="`${equityData.investmentDetails.roi}%`"></span></p>
                </div>
            </div>

            <!-- Equity Documents -->
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Equity Documents</h2>
                <ul class="space-y-3">
                    <template x-for="doc in equityData.equityDocuments" :key="doc.id">
                        <li class="flex items-center justify-between">
                            <div class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>
                                <div>
                                    <p class="text-sm font-medium text-gray-800" x-text="doc.name"></p>
                                    <p class="text-xs text-gray-500" x-text="`Dated: ${formatDate(doc.date)}`"></p>
                                </div>
                            </div>
                            <a :href="doc.url" download class="text-green-600 hover:text-green-800 text-sm font-semibold">Download</a>
                        </li>
                    </template>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
function investorEquity() {
    return {
        loading: true,
        equityData: { equityDistribution: [], companyValuation: {}, investmentDetails: {}, futureRounds: { nextRound: {}, dilutionImpact: { value: {} } }, equityDocuments: [] },

        init() {
            this.fetchEquityData();
        },

        fetchEquityData() {
            this.loading = true;
            setTimeout(() => {
                this.equityData = {
                    equityDistribution: [
                        { holder: 'Founder (Karan Solanki)', percentage: 60, value: 4140000, color: '#25D366' },
                        { holder: 'Current Investor', percentage: 5, value: 345000, color: '#075E54' },
                        { holder: 'Future Investors', percentage: 20, value: 1380000, color: '#128C7E' },
                        { holder: 'ESOP', percentage: 10, value: 690000, color: '#34B7F1' },
                        { holder: 'Advisors', percentage: 5, value: 345000, color: '#9CA3AF' }
                    ],
                    companyValuation: {
                        current: 6900000,
                        previousRound: 6000000,
                        growth: 15,
                    },
                    investmentDetails: {
                        initialInvestment: 300000,
                        investmentDate: '2023-01-01',
                        equityPercentage: 5,
                        currentValue: 345000,
                        roi: 15
                    },
                    equityDocuments: [
                        { id: 1, name: 'Term Sheet.pdf', date: '2023-01-01', url: '#' },
                        { id: 2, name: 'Shareholders Agreement.pdf', date: '2023-01-01', url: '#' },
                        { id: 4, name: 'Cap Table.xlsx', date: '2023-01-15', url: '#' }
                    ],
                    futureRounds: {
                        nextRound: {
                            targetDate: '2024-06-30',
                            targetValuation: 15000000,
                            fundingTarget: 2250000
                        },
                        dilutionImpact: {
                            currentEquity: 5,
                            postDilution: 4.25,
                            value: {
                                postDilution: 637500
                            }
                        }
                    }
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' });
        }
    }
}
</script>
@endsection

