@extends('layouts.admin')

@section('content')
<div x-data="investorProjections()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Financial Projections</h1>

    <!-- Scenario Selector -->
    <div class="mb-8">
        <div class="flex justify-center bg-gray-200 rounded-lg p-1">
            <button @click="projectionScenario = 'conservative'" :class="{'bg-white text-blue-600 shadow': projectionScenario === 'conservative'}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-lg">Conservative</button>
            <button @click="projectionScenario = 'moderate'" :class="{'bg-white text-blue-600 shadow': projectionScenario === 'moderate'}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-lg mx-2">Moderate</button>
            <button @click="projectionScenario = 'aggressive'" :class="{'bg-white text-blue-600 shadow': projectionScenario === 'aggressive'}" class="px-4 py-2 text-sm font-medium text-gray-700 rounded-lg">Aggressive</button>
        </div>
    </div>

    <template x-if="!loading">
        <div>
            <!-- Projections Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <!-- Valuation -->
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Valuation Projection</h2>
                    <template x-for="(item, index) in projectionData.projections.valuation[projectionScenario]" :key="index">
                        <div class="flex items-center mb-2">
                            <span class="w-16" x-text="item.year"></span>
                            <div class="w-full bg-gray-200 rounded-full h-4 mr-2"><div class="bg-blue-500 h-4 rounded-full" :style="`width: ${(item.value / 1500000000) * 100}%`"></div></div>
                            <span class="w-32 text-right font-semibold" x-text="formatCurrency(item.value)"></span>
                        </div>
                    </template>
                </div>

                <!-- Revenue -->
                <div class="bg-white p-6 rounded-lg shadow-lg">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Revenue Projection</h2>
                    <template x-for="(item, index) in projectionData.projections.revenue[projectionScenario]" :key="index">
                        <div class="flex items-center mb-2">
                            <span class="w-16" x-text="item.year"></span>
                            <div class="w-full bg-gray-200 rounded-full h-4 mr-2"><div class="bg-green-500 h-4 rounded-full" :style="`width: ${(item.value / 300000000) * 100}%`"></div></div>
                            <span class="w-32 text-right font-semibold" x-text="formatCurrency(item.value)"></span>
                        </div>
                    </template>
                </div>
            </div>

            <!-- Key Assumptions -->
            <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Key Assumptions (<span x-text="projectionScenario.charAt(0).toUpperCase() + projectionScenario.slice(1)"></span>)</h2>
                <ul class="list-disc list-inside space-y-2 text-gray-700">
                    <template x-for="(assumption, index) in projectionData.keyAssumptions[projectionScenario]" :key="index">
                        <li x-text="assumption"></li>
                    </template>
                </ul>
            </div>

            <!-- Risk Factors -->
            <div class="mt-8 bg-white p-6 rounded-lg shadow-lg">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Risk Factors</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk Factor</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mitigation Strategy</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="(risk, index) in projectionData.riskFactors" :key="index">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="risk.factor"></td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="{
                                            'bg-red-100 text-red-800': risk.impact === 'High',
                                            'bg-yellow-100 text-yellow-800': risk.impact === 'Medium',
                                            'bg-green-100 text-green-800': risk.impact === 'Low'
                                        }" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full" x-text="risk.impact"></span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500" x-text="risk.mitigation"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function investorProjections() {
    return {
        loading: true,
        projectionScenario: 'moderate',
        projectionData: {},

        init() {
            this.fetchProjectionData();
        },

        fetchProjectionData() {
            this.loading = true;
            setTimeout(() => {
                this.projectionData = {
                    projections: {
                        valuation: {
                            conservative: [{ year: '2025', value: 30000000 }, { year: '2026', value: 60000000 }, { year: '2027', value: 120000000 }, { year: '2028', value: 240000000 }, { year: '2029', value: 480000000 }],
                            moderate: [{ year: '2025', value: 30000000 }, { year: '2026', value: 100000000 }, { year: '2027', value: 300000000 }, { year: '2028', value: 750000000 }, { year: '2029', value: 1500000000 }],
                            aggressive: [{ year: '2025', value: 30000000 }, { year: '2026', value: 150000000 }, { year: '2027', value: 600000000 }, { year: '2028', value: 1800000000 }, { year: '2029', value: 3000000000 }]
                        },
                        revenue: {
                            conservative: [{ year: '2025', value: 1404000 }, { year: '2026', value: 2808000 }, { year: '2027', value: 5616000 }, { year: '2028', value: 11232000 }, { year: '2029', value: 22464000 }],
                            moderate: [{ year: '2025', value: 1404000 }, { year: '2026', value: 4212000 }, { year: '2027', value: 12636000 }, { year: '2028', value: 37908000 }, { year: '2029', value: 113724000 }],
                            aggressive: [{ year: '2025', value: 1404000 }, { year: '2026', value: 7020000 }, { year: '2027', value: 35100000 }, { year: '2028', value: 175500000 }, { year: '2029', value: 877500000 }]
                        }
                    },
                    keyAssumptions: {
                        conservative: ['Market growth at 15% annually', 'Customer acquisition cost increases by 10% YoY', 'Vendor churn rate at 8%'],
                        moderate: ['Market growth at 25% annually', 'Stable customer acquisition cost', 'Vendor churn rate at 5%'],
                        aggressive: ['Market growth at 40% annually', 'Decreasing customer acquisition cost due to brand recognition', 'Vendor churn rate at 3%']
                    },
                    riskFactors: [
                        { factor: 'Increased Competition', impact: 'High', mitigation: 'Focus on product differentiation and superior customer service.' },
                        { factor: 'Regulatory Changes', impact: 'Medium', mitigation: 'Maintain a flexible business model and stay informed on policy changes.' },
                        { factor: 'Economic Downturn', impact: 'Low', mitigation: 'Diversify revenue streams and maintain a lean operational structure.' }
                    ]
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        }
    }
}
</script>
@endsection

