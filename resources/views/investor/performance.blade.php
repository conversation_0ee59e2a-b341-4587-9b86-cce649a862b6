@extends('layouts.admin')

@section('content')
<div x-data="investorPerformance()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Performance Dashboard</h1>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Investment Value</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(performanceData.investmentPerformance.currentValue)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Total Return</p>
            <p class="text-3xl font-bold mt-2 text-green-600" x-text="`${formatPercentage(performanceData.investmentPerformance.totalReturn)}`"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Company Valuation</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(performanceData.companyValuation.currentValuation)"></p>
        </div>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-gray-600 text-sm">Projected Annual Revenue</p>
            <p class="text-3xl font-bold text-gray-900 mt-2" x-text="formatCurrency(performanceData.revenuePerformance.projectedAnnualRevenue)"></p>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Key Performance Indicators</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Customer Metrics -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-700 mb-2">Customer Metrics</h3>
                <div class="space-y-2 text-sm">
                    <p>CAC: <span class="font-bold" x-text="formatCurrency(performanceData.keyPerformanceIndicators.customerAcquisitionCost)"></span></p>
                    <p>LTV: <span class="font-bold" x-text="formatCurrency(performanceData.keyPerformanceIndicators.lifetimeValue)"></span></p>
                    <p>LTV to CAC Ratio: <span class="font-bold" x-text="performanceData.keyPerformanceIndicators.ltvToCAC"></span></p>
                </div>
            </div>
            <!-- Vendor Metrics -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-700 mb-2">Vendor Metrics</h3>
                <div class="space-y-2 text-sm">
                    <p>Active Vendors: <span class="font-bold" x-text="performanceData.keyPerformanceIndicators.monthlyActiveVendors"></span></p>
                    <p>Retention Rate: <span class="font-bold text-green-600" x-text="formatPercentage(performanceData.keyPerformanceIndicators.vendorRetentionRate)"></span></p>
                    <p>Avg. Revenue/Vendor: <span class="font-bold" x-text="formatCurrency(performanceData.keyPerformanceIndicators.averageRevenuePerVendor)"></span></p>
                </div>
            </div>
            <!-- Market Position -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-700 mb-2">Market Position</h3>
                <div class="space-y-2 text-sm">
                    <p>TAM: <span class="font-bold" x-text="formatCurrency(performanceData.marketMetrics.totalAddressableMarket)"></span></p>
                    <p>Market Penetration: <span class="font-bold" x-text="formatPercentage(performanceData.marketMetrics.marketPenetration)"></span></p>
                    <p>Cities Covered: <span class="font-bold" x-text="performanceData.marketMetrics.marketExpansion.currentCities"></span></p>
                </div>
            </div>
            <!-- Revenue Metrics -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-semibold text-gray-700 mb-2">Revenue Metrics</h3>
                <div class="space-y-2 text-sm">
                    <p>Growth Rate: <span class="font-bold text-green-600" x-text="formatPercentage(performanceData.revenuePerformance.revenueGrowthRate)"></span></p>
                    <p>Projected Revenue: <span class="font-bold" x-text="formatCurrency(performanceData.revenuePerformance.projectedAnnualRevenue)"></span></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function investorPerformance() {
    return {
        loading: true,
        performanceData: {
            investmentPerformance: {},
            companyValuation: {},
            revenuePerformance: {},
            marketMetrics: { marketExpansion: {} },
            keyPerformanceIndicators: {}
        },

        init() {
            this.fetchPerformanceData();
        },

        fetchPerformanceData() {
            this.loading = true;
            setTimeout(() => {
                this.performanceData = {
                    investmentPerformance: {
                        currentValue: 345000,
                        totalReturn: 15,
                    },
                    companyValuation: {
                        currentValuation: 6900000,
                    },
                    revenuePerformance: {
                        projectedAnnualRevenue: 1450000,
                        revenueGrowthRate: 18.5,
                    },
                    marketMetrics: {
                        totalAddressableMarket: *********,
                        marketPenetration: 1.38,
                        marketExpansion: {
                            currentCities: 12,
                        },
                    },
                    keyPerformanceIndicators: {
                        customerAcquisitionCost: 1200,
                        lifetimeValue: 4500,
                        ltvToCAC: '3.75x',
                        monthlyActiveVendors: 125,
                        vendorRetentionRate: 92,
                        averageRevenuePerVendor: 11600,
                    }
                };
                this.loading = false;
            }, 500);
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR', maximumFractionDigits: 0 }).format(amount);
        },

        formatPercentage(value) {
            return `${value.toFixed(2)}%`;
        }
    }
}
</script>
@endsection

