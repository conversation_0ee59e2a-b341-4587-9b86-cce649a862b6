@extends('layouts.admin')

@section('content')
<div x-data="investorProfile()" class="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <template x-if="loading">
        <div class="flex justify-center items-center h-64">
            <div class="loader"></div>
        </div>
    </template>

    <template x-if="!loading && profileData">
        <div>
            <!-- Header -->
            <div class="bg-white p-6 rounded-lg shadow-lg mb-8 flex items-center">
                <div class="relative mr-6">
                    <img :src="profileImagePreview" alt="Profile" class="w-24 h-24 rounded-full object-cover">
                    <label x-show="isEditing" for="profileImage" class="absolute bottom-0 right-0 bg-blue-600 p-1 rounded-full cursor-pointer hover:bg-blue-700">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" /></svg>
                        <input type="file" id="profileImage" class="hidden" @change="handleImageChange">
                    </label>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-gray-800" x-text="profileData.name"></h1>
                    <p class="text-gray-600" x-text="profileData.designation + ' at ' + profileData.company"></p>
                    <p class="text-sm text-gray-500" x-text="profileData.email"></p>
                </div>
                <div class="ml-auto">
                    <button @click="isEditing = !isEditing" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z" /><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd" /></svg>
                        <span x-text="isEditing ? 'Cancel' : 'Edit Profile'"></span>
                    </button>
                </div>
            </div>

            <!-- View/Edit Content -->
            <div x-show="isEditing" x-cloak>
                <form @submit.prevent="handleSubmit" class="bg-white p-6 rounded-lg shadow-lg">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div><label class="block text-sm font-medium text-gray-700">Name</label><input type="text" x-model="formData.name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Email</label><input type="email" x-model="formData.email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Phone</label><input type="text" x-model="formData.phone" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Company</label><input type="text" x-model="formData.company" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Designation</label><input type="text" x-model="formData.designation" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Address</label><input type="text" x-model="formData.address" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div class="md:col-span-2"><label class="block text-sm font-medium text-gray-700">Bio</label><textarea x-model="formData.bio" rows="3" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></textarea></div>
                        <div><label class="block text-sm font-medium text-gray-700">PAN Number</label><input type="text" x-model="formData.panNumber" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Bank Name</label><input type="text" x-model="formData.bankName" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">Account Number</label><input type="text" x-model="formData.accountNumber" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                        <div><label class="block text-sm font-medium text-gray-700">IFSC Code</label><input type="text" x-model="formData.ifscCode" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"></div>
                    </div>
                    <div class="mt-6 flex justify-end">
                        <button type="submit" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>

            <div x-show="!isEditing">
                <div class="bg-white p-6 rounded-lg shadow-lg mb-8">
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Profile Details</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div><p class="text-sm text-gray-500">Investment Focus</p><p x-text="profileData.investmentFocus"></p></div>
                        <div><p class="text-sm text-gray-500">Address</p><p x-text="profileData.address"></p></div>
                        <div class="md:col-span-2"><p class="text-sm text-gray-500">Bio</p><p x-text="profileData.bio"></p></div>
                        <div><p class="text-sm text-gray-500">PAN Number</p><p x-text="profileData.panNumber"></p></div>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Bank Details</h2>
                        <div class="space-y-4">
                            <div><p class="text-sm text-gray-500">Bank Name</p><p x-text="profileData.bankDetails.bankName"></p></div>
                            <div><p class="text-sm text-gray-500">Account Number</p><p x-text="profileData.bankDetails.accountNumber"></p></div>
                            <div><p class="text-sm text-gray-500">IFSC Code</p><p x-text="profileData.bankDetails.ifscCode"></p></div>
                        </div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-lg">
                        <h2 class="text-xl font-bold text-gray-800 mb-4">Investment Details</h2>
                        <div class="space-y-4">
                            <div><p class="text-sm text-gray-500">Initial Investment</p><p x-text="formatCurrency(profileData.investmentDetails.initialInvestment)"></p></div>
                            <div><p class="text-sm text-gray-500">Investment Date</p><p x-text="formatDate(profileData.investmentDetails.investmentDate)"></p></div>
                            <div><p class="text-sm text-gray-500">Equity Percentage</p><p x-text="profileData.investmentDetails.equityPercentage + '%'"></p></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>
</div>

<script>
function investorProfile() {
    return {
        loading: true,
        isEditing: false,
        profileData: null,
        formData: {},
        profileImagePreview: null,

        init() {
            this.fetchProfileData();
        },

        fetchProfileData() {
            this.loading = true;
            setTimeout(() => {
                const mockData = {
                    name: 'Investor Name',
                    email: '<EMAIL>',
                    phone: '+91 **********',
                    company: 'Investor Ventures',
                    designation: 'Managing Partner',
                    investmentFocus: 'Early Stage Tech Startups',
                    bio: 'Experienced angel investor with a portfolio of 15+ successful startups.',
                    profileImage: 'https://randomuser.me/api/portraits/men/5.jpg',
                    address: '123 Investor Street, Mumbai, Maharashtra 400001',
                    panNumber: '**********',
                    bankDetails: {
                        bankName: 'HDFC Bank',
                        accountNumber: 'XXXX XXXX XXXX 1234',
                        ifscCode: 'HDFC0001234'
                    },
                    investmentDetails: {
                        initialInvestment: 300000,
                        investmentDate: '2023-01-01',
                        equityPercentage: 5
                    }
                };
                this.profileData = mockData;
                this.formData = {
                    name: mockData.name,
                    email: mockData.email,
                    phone: mockData.phone,
                    company: mockData.company,
                    designation: mockData.designation,
                    address: mockData.address,
                    bio: mockData.bio,
                    panNumber: mockData.panNumber,
                    bankName: mockData.bankDetails.bankName,
                    accountNumber: mockData.bankDetails.accountNumber,
                    ifscCode: mockData.bankDetails.ifscCode
                };
                this.profileImagePreview = mockData.profileImage;
                this.loading = false;
            }, 500);
        },

        handleImageChange(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onloadend = () => {
                    this.profileImagePreview = reader.result;
                };
                reader.readAsDataURL(file);
            }
        },

        handleSubmit() {
            console.log('Form submitted', this.formData);
            // Mock saving data
            this.profileData.name = this.formData.name;
            this.profileData.email = this.formData.email;
            this.profileData.phone = this.formData.phone;
            this.profileData.company = this.formData.company;
            this.profileData.designation = this.formData.designation;
            this.profileData.address = this.formData.address;
            this.profileData.bio = this.formData.bio;
            this.profileData.panNumber = this.formData.panNumber;
            this.profileData.bankDetails.bankName = this.formData.bankName;
            this.profileData.bankDetails.accountNumber = this.formData.accountNumber;
            this.profileData.bankDetails.ifscCode = this.formData.ifscCode;
            this.isEditing = false;
        },

        formatCurrency(amount) {
            return new Intl.NumberFormat('en-IN', { style: 'currency', currency: 'INR' }).format(amount);
        },

        formatDate(dateString) {
            const options = { year: 'numeric', month: 'long', day: 'numeric' };
            return new Date(dateString).toLocaleDateString('en-US', options);
        }
    }
}
</script>
@endsection

