@props([
    'isOpen' => false,
    'message' => null, // Pass initial message data
])

@php
// Mock data for product categories, to be replaced by dynamic data
$categories = [
    'Electronics',
    'Books',
    'Clothing',
    'Home & Kitchen',
    'Sports',
];
@endphp

<div
    x-data="{
        show: @js($isOpen),
        categoryFilter: 'all',
        categories: @js($categories),
        init() {
            if ({{ $message ? 'true' : 'false' }}) {
                this.categoryFilter = {{ $message }}?.categoryFilter || 'all';
            }
            this.$watch('show', value => {
                if (value) {
                    document.body.classList.add('overflow-hidden');
                } else {
                    document.body.classList.remove('overflow-hidden');
                }
            });
        },
        openEditor(msg) {
            this.categoryFilter = msg?.categoryFilter || 'all';
            this.show = true;
        },
        closeEditor() {
            this.show = false;
        },
        saveChanges() {
            this.$dispatch('save-product-catalog', { categoryFilter: this.categoryFilter });
            this.closeEditor();
        }
    }"
    x-on:open-product-catalog-editor.window="openEditor($event.detail)"
    @keydown.escape.window="closeEditor()"
    x-show="show"
    class="fixed inset-0 z-50 overflow-hidden"
    style="display: none;"
    x-cloak
>
    <!-- Overlay -->
    <div x-show="show" x-transition.opacity class="absolute inset-0 bg-gray-500 bg-opacity-75"></div>

    <!-- Side panel -->
    <div class="fixed inset-y-0 right-0 flex max-w-full pl-10">
        <div 
            x-show="show"
            x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:enter-start="translate-x-full"
            x-transition:enter-end="translate-x-0"
            x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:leave-start="translate-x-0"
            x-transition:leave-end="translate-x-full"
            class="w-screen max-w-md"
        >
            <div class="flex flex-col h-full bg-white shadow-xl">
                <!-- Header -->
                <div class="px-6 py-4 bg-gray-50 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900 flex items-center">
                            <i class="fas fa-shopping-cart h-5 w-5 mr-2 text-green-600"></i>
                            Edit Product Catalog
                        </h2>
                        <button @click="closeEditor()" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-times h-6 w-6"></i>
                        </button>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="saveChanges()" class="flex flex-col flex-1 overflow-y-auto">
                    <div class="flex-1 px-6 py-6">
                        <div class="mb-6">
                            <label for="category-filter" class="block text-sm font-medium text-gray-700 mb-2">
                                Product Category
                            </label>
                            <select id="category-filter" x-model="categoryFilter" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                                <option value="all">All Products</option>
                                <template x-for="category in categories" :key="category">
                                    <option :value="category" x-text="category"></option>
                                </template>
                            </select>
                            <p class="mt-2 text-sm text-gray-500">
                                Select which product category to display in this catalog message.
                            </p>

                            <!-- Product preview message -->
                            <div class="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100 text-sm text-blue-700">
                                <p class="font-medium mb-1">Preview:</p>
                                <p>
                                    <span x-show="categoryFilter === 'all'">All products will be displayed in this catalog.</span>
                                    <span x-show="categoryFilter !== 'all'">Only products from the "<strong x-text="categoryFilter"></strong>" category will be displayed.</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end px-6 py-4 space-x-3 bg-gray-50 border-t border-gray-200">
                        <button type="button" @click="closeEditor()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md shadow-sm hover:bg-green-700">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
