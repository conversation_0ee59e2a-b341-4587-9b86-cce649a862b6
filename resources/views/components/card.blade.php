@props(['hover' => false])

@php
    $baseClasses = 'bg-white rounded-lg shadow-sm overflow-hidden';
    $hoverClasses = $hover ? 'transition-transform duration-300 hover:shadow-md hover:-translate-y-1' : '';
@endphp

<div {{ $attributes->merge(['class' => "$baseClasses $hoverClasses"]) }}>
    @if (isset($image))
        <div class="w-full">
            {{ $image }}
        </div>
    @endif

    <div class="p-6">
        @if (isset($title))
            <h3 class="text-lg font-medium text-gray-900">
                {{ $title }}
            </h3>
        @endif

        <div class="mt-2 text-base text-gray-500">
            {{ $slot }}
        </div>
    </div>

    @if (isset($footer))
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-100">
            {{ $footer }}
        </div>
    @endif
</div>
