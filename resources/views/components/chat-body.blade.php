@props(['messages' => []])

<div
    x-data
    x-init="$nextTick(() => $refs.messagesEnd.scrollIntoView())"
    class="relative flex-1 overflow-y-auto p-4"
    style="background-color: #E5DDD5; background-image: url('{{ asset('images/chat-background.jpg') }}'); background-repeat: repeat; background-size: 300px auto;"
>
    <!-- Overlay -->
    <div class="absolute inset-0" style="background-color: rgba(230, 222, 178, 0.15); pointer-events: none; z-index: 1;"></div>

    <!-- Content -->
    <div class="relative z-10">
        <!-- Official Service Banner -->
        <div class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4 rounded-md shadow-sm" role="alert">
            <p class="text-sm">This is an official business account. All messages are end-to-end encrypted.</p>
        </div>

        @php $lastDate = null; @endphp
        @foreach ($messages as $message)
            @php
                $currentMessageDate = \Carbon\Carbon::parse($message['timestamp'])->startOfDay();
                $showDateSeparator = false;
                if ($lastDate === null || !$currentMessageDate->isSameDay($lastDate)) {
                    $showDateSeparator = true;
                    $lastDate = $currentMessageDate;
                }
            @endphp

            @if ($showDateSeparator)
                <x-date-separator :date="$message['timestamp']" />
            @endif

            <x-chat-message :message="$message" />
        @endforeach

        <!-- Reference for auto-scrolling -->
        <div x-ref="messagesEnd"></div>
    </div>
</div>
