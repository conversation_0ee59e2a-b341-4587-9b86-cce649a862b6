@props(['isOpen' => false, 'storeName' => 'Your Store'])

<div x-data="chatPreviewModal()" x-show="isOpen" @keydown.escape.window="isOpen = false" @quick-reply.window="handleQuickReply($event.detail)" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div x-show="isOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="isOpen = false" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div x-show="isOpen" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-transparent rounded-lg text-left overflow-hidden transform transition-all sm:my-8 sm:align-middle sm:max-w-sm sm:w-full">
            <!-- Phone Mockup -->
            <div class="relative mx-auto border-gray-800 dark:border-gray-800 bg-gray-800 border-[14px] rounded-[2.5rem] h-[600px] w-[300px] shadow-xl">
                <div class="w-[148px] h-[18px] bg-gray-800 top-0 rounded-b-[1rem] left-1/2 -translate-x-1/2 absolute"></div>
                <div class="h-[46px] w-[3px] bg-gray-800 absolute -left-[17px] top-[124px] rounded-l-lg"></div>
                <div class="h-[46px] w-[3px] bg-gray-800 absolute -left-[17px] top-[178px] rounded-l-lg"></div>
                <div class="h-[64px] w-[3px] bg-gray-800 absolute -right-[17px] top-[142px] rounded-r-lg"></div>
                <div class="rounded-[2rem] overflow-hidden w-full h-full bg-white dark:bg-gray-800">
                    <!-- Chat Header -->
                    <div class="bg-whatsapp-teal text-white p-3 flex items-center">
                        <img src="https://via.placeholder.com/40" class="w-8 h-8 rounded-full mr-2">
                        <div>
                            <h2 class="font-medium text-sm" x-text="storeName"></h2>
                            <p class="text-xs opacity-80">online</p>
                        </div>
                        <button @click="isOpen = false" class="absolute top-2 right-2 text-white">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Chat Body -->
                    <div class="h-[calc(100%-100px)] overflow-y-auto p-3" style="background-color: #E5DDD5; background-image: url('{{ asset('images/chat-background.jpg') }}');">
                        <template x-for="message in messages" :key="message.id">
                            <x-chat-message :message="message" :interactive="true" />
                        </template>
                        <div x-ref="messagesEnd"></div>
                    </div>

                    <!-- Chat Input -->
                    <div class="bg-gray-200 p-2 absolute bottom-0 w-full">
                        <div class="flex items-center bg-white rounded-full px-3 py-1">
                            <input type="text" placeholder="Type a message" class="flex-1 border-none outline-none bg-transparent text-sm" x-model="inputMessage" @keydown.enter.prevent="handleSendMessage()">
                            <button class="text-gray-500 ml-2">
                                <i class="fas fa-paper-plane" x-show="inputMessage.trim() !== ''" @click="handleSendMessage()"></i>
                                <i class="fas fa-microphone" x-show="inputMessage.trim() === ''"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function chatPreviewModal() {
        return {
            isOpen: @json($isOpen),
            storeName: @json($storeName),
            messages: [],
            inputMessage: '',
            currentNodeId: null,
            flow: {
                nodes: [
                    { id: '1', data: { content: 'Welcome! How can I help?', buttons: [{text: 'Order Status'}, {text: 'Support'}] } },
                    { id: '2', data: { content: 'Please provide your order number.' } },
                    { id: '3', data: { content: 'Our support team is here to help. What is your question?' } }
                ],
                edges: [
                    { source: '1', target: '2', buttonText: 'Order Status' },
                    { source: '1', target: '3', buttonText: 'Support' }
                ]
            },
            init() {
                this.$watch('isOpen', (value) => {
                    if (value) this.startFlow();
                });
            },
            startFlow() {
                this.messages = [];
                const firstNode = this.flow.nodes[0];
                this.currentNodeId = firstNode.id;
                this.addBotMessage(firstNode.data);
            },
            addBotMessage(data) {
                const botMessage = {
                    id: this.messages.length + 1,
                    content: data.content,
                    sender: 'store',
                    timestamp: new Date(),
                    status: 'read',
                    buttons: data.buttons ? data.buttons.map(btn => ({ type: 'quick_reply', text: btn.text })) : []
                };
                this.messages.push(botMessage);
                this.scrollToBottom();
            },
            handleQuickReply(buttonText) {
                const userMessage = {
                    id: this.messages.length + 1,
                    content: buttonText,
                    sender: 'user',
                    timestamp: new Date(),
                    status: 'sent'
                };
                this.messages.push(userMessage);
                this.scrollToBottom();

                const edge = this.flow.edges.find(e => e.source === this.currentNodeId && e.buttonText === buttonText);
                if (edge) {
                    const targetNode = this.flow.nodes.find(n => n.id === edge.target);
                    if (targetNode) {
                        this.currentNodeId = targetNode.id;
                        setTimeout(() => this.addBotMessage(targetNode.data), 500);
                    }
                }
            },
            handleSendMessage() {
                if (this.inputMessage.trim() === '') return;
                const userMessage = {
                    id: this.messages.length + 1,
                    content: this.inputMessage,
                    sender: 'user',
                    timestamp: new Date(),
                    status: 'sent'
                };
                this.messages.push(userMessage);
                this.inputMessage = '';
                this.scrollToBottom();
                
                setTimeout(() => {
                    const defaultResponse = { content: 'Sorry, I can only respond to buttons in this preview.' };
                    this.addBotMessage(defaultResponse);
                }, 500);
            },
            scrollToBottom() {
                this.$nextTick(() => {
                    this.$refs.messagesEnd.scrollIntoView({ behavior: 'smooth' });
                });
            }
        }
    }
</script>
@endpush
