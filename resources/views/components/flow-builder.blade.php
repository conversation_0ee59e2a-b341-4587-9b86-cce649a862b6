@props([
    'initialFlowName' => 'New Chat Flow',
    'initialFlowDescription' => '',
])

<div
    x-data="{
        isSidebarOpen: true,
        isEditorOpen: false,
        isDeleteModalOpen: false,
        isErrorModalOpen: false,
        flowName: '{{ $initialFlowName }}',
        flowDescription: '{{ $initialFlowDescription }}'
    }"
    class="relative flex h-[calc(100vh-theme(height.16))] w-full bg-gray-100"
>
    <!-- Flow Sidebar -->
    <div x-show="isSidebarOpen" x-transition>
        <x-flow-sidebar
            :flow-name="$flowName"
            :flow-description="$flowDescription"
            @save-flow="console.log('Flow saved:', $event.detail)"
        />
    </div>

    <!-- Main Flow Canvas -->
    <div class="relative flex-1 h-full">
        <!-- Canvas with a static SVG background representing a flow -->
        <div class="absolute inset-0 w-full h-full bg-gray-200 bg-repeat"
             style="background-image: url('data:image/svg+xml,%3Csvg width=\'40\' height=\'40\' viewBox=\'0 0 40 40\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'%23d1d5db\' fill-opacity=\'0.4\' fill-rule=\'evenodd\'%3E%3Cpath d=\'M0 38.59l2.83-2.83 1.41 1.41L1.41 40H0v-1.41zM0 1.4l2.83 2.83 1.41-1.41L1.41 0H0v1.41zM38.59 40l-2.83-2.83 1.41-1.41L40 38.59V40h-1.41zM40 1.41l-2.83 2.83-1.41-1.41L38.59 0H40v1.41zM20 18.59l2.83-2.83 1.41 1.41L21.41 20l2.83 2.83-1.41 1.41L20 21.41l-2.83 2.83-1.41-1.41L18.59 20l-2.83-2.83 1.41-1.41L20 18.59z\'/\'%3E%3C/g\'%3E%3C/svg%3E');">
        </div>

        <!-- Static representation of a flow -->
        <div class="absolute inset-0 flex items-center justify-center p-8">
            <img src="{{ asset('images/sample-flow.svg') }}" alt="Sample Chat Flow" class="max-w-full max-h-full object-contain"/>
        </div>

        <!-- UI Controls -->
        <div class="absolute top-4 left-4">
            <button @click="isSidebarOpen = !isSidebarOpen" class="bg-white p-2 rounded-md shadow-md hover:bg-gray-50">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <div class="absolute top-4 right-4 flex space-x-2">
            <x-button variant="primary" @click="$dispatch('open-chat-preview')">
                <i class="fas fa-eye mr-2"></i>
                Preview
            </x-button>
        </div>
    </div>

    <!-- Modals -->
    <x-chat-preview-modal />

    <x-delete-confirmation-modal modal-id="flow-delete-confirmation" />
    <x-error-modal modal-id="flow-error" />

</div>
