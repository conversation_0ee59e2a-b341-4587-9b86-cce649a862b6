@props([
    'categories' => [],
    'storeId' => null,
])

<div 
    x-data="productCatalog({ categories: {{ json_encode($categories) }} })"
    class="w-full max-w-lg mx-auto bg-white rounded-lg shadow-lg p-4 font-sans"
>
    <!-- Category Tabs -->
    <div class="mb-4 border-b border-gray-200">
        <nav class="-mb-px flex space-x-4 overflow-x-auto" aria-label="Tabs">
            <template x-for="category in allCategories" :key="category.id">
                <button 
                    @click="handleCategoryChange(category.id)"
                    :class="{
                        'border-whatsapp-default text-whatsapp-default': activeCategory === category.id,
                        'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeCategory !== category.id
                    }"
                    class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ease-in-out focus:outline-none"
                    x-text="category.name"
                ></button>
            </template>
        </nav>
    </div>

    <!-- Product Grid -->
    <div class="grid grid-cols-2 gap-4 min-h-[200px]">
        <template x-if="isLoading">
            <div class="col-span-2 text-center py-4 text-gray-500">Loading...</div>
        </template>

        <template x-if="!isLoading && currentProducts().length > 0">
            <template x-for="product in currentProducts()" :key="product.id">
                <div @click="selectProduct(product)" class="cursor-pointer bg-gray-50 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="w-full h-24 bg-gray-200 flex items-center justify-center">
                        <img :src="product.image" @error="handleImageError($event)" class="w-full h-full object-cover" :alt="product.name">
                    </div>
                    <div class="p-2 text-center">
                        <h3 class="text-xs font-bold uppercase text-gray-800 truncate" x-text="product.name"></h3>
                        <p class="text-xs font-bold text-whatsapp-default mt-1" x-text="`₹${parseFloat(product.price).toFixed(2)}`"></p>
                    </div>
                </div>
            </template>
        </template>

        <template x-if="!isLoading && currentProducts().length === 0">
            <div class="col-span-2 text-center py-4 text-gray-500">
                <div class="font-bold">No products available.</div>
                <div class="text-xs mt-1">Check back later or select another category.</div>
            </div>
        </template>
    </div>

    <!-- Pagination -->
    <template x-if="totalPages() > 1">
        <div class="flex justify-center items-center gap-4 mt-4">
            <button @click="goToPrevPage()" :disabled="currentPage === 1" class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-200" :class="currentPage === 1 ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-whatsapp-default text-white hover:bg-whatsapp-dark'">
                <i class="fas fa-chevron-left w-4 h-4"></i>
            </button>
            <button @click="goToNextPage()" :disabled="currentPage === totalPages()" class="flex items-center justify-center w-8 h-8 rounded-full transition-colors duration-200" :class="currentPage === totalPages() ? 'bg-gray-200 text-gray-400 cursor-not-allowed' : 'bg-whatsapp-default text-white hover:bg-whatsapp-dark'">
                <i class="fas fa-chevron-right w-4 h-4"></i>
            </button>
        </div>
    </template>
</div>

<script>
document.addEventListener('alpine:init', () => {
    Alpine.data('productCatalog', ({ categories }) => ({
        allCategories: [],
        activeCategory: null,
        currentPage: 1,
        productsPerPage: 4,
        isLoading: true,

        init() {
            this.allCategories = categories || [];
            this.isLoading = false;
            if (this.allCategories.length > 0) {
                const allCat = this.allCategories.find(c => c.id === 'all' && c.products?.length > 0);
                if (allCat) {
                    this.activeCategory = allCat.id;
                } else {
                    const firstCatWithProducts = this.allCategories.find(c => c.products?.length > 0);
                    this.activeCategory = firstCatWithProducts ? firstCatWithProducts.id : this.allCategories[0].id;
                }
            }
        },

        activeCategoryData() {
            return this.allCategories.find(c => c.id === this.activeCategory) || { products: [] };
        },

        totalPages() {
            const products = this.activeCategoryData().products || [];
            return Math.ceil(products.length / this.productsPerPage);
        },

        currentProducts() {
            const products = this.activeCategoryData().products || [];
            const start = (this.currentPage - 1) * this.productsPerPage;
            const end = start + this.productsPerPage;
            return products.slice(start, end);
        },

        handleCategoryChange(categoryId) {
            this.activeCategory = categoryId;
            this.currentPage = 1;
        },

        goToNextPage() {
            if (this.currentPage < this.totalPages()) {
                this.currentPage++;
            }
        },

        goToPrevPage() {
            if (this.currentPage > 1) {
                this.currentPage--;
            }
        },

        selectProduct(product) {
            this.$dispatch('select-product', { product });
        },

        handleImageError(event) {
            event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMzAwIiBoZWlnaHQ9IjMwMCIgZmlsbD0iI2YwZjBmMCIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGRvbWluYW50LWJhc2VsaW5lPSJtaWRkbGUiIGZpbGw9IiM5OTk5OTkiPk5vIEltYWdlPC90ZXh0Pjwvc3ZnPg==';
        }
    }));
});
</script>
