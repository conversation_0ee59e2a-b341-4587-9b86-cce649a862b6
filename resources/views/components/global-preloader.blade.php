{{--
    Global Preloader Component
    Shows a loading screen across the entire application.

    This component listens for global window events to control its visibility.
    - To show: `window.dispatchEvent(new CustomEvent('show-preloader'))`
    - To hide: `window.dispatchEvent(new CustomEvent('hide-preloader'))`
--}}
<div
    x-data="{ loading: true }"
    x-init="document.addEventListener('DOMContentLoaded', () => { loading = false })"
    x-show="loading"
    x-transition:leave="transition ease-in duration-300"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="fixed inset-0 z-[9999] flex items-center justify-center bg-white"
>
    <div class="text-center">
        <x-application-logo class="inline-block h-24 w-auto animate-pulse" />
        
        <div class="mt-4">
            <div class="w-10 h-10 border-4 border-whatsapp-default border-t-transparent rounded-full animate-spin mx-auto"></div>
        </div>

        <div class="mt-4 text-lg font-medium text-gray-700">
            दुकान Online है ❤️
        </div>
    </div>
</div>
