<div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-8">
        <div>
            <span class="inline-block px-3 py-1 text-xs font-semibold text-green-800 bg-green-100 rounded-full">Store Status: Active</span>
            <h2 class="mt-2 text-2xl font-bold text-gray-800">Your WhatsApp Store</h2>
            <p class="mt-2 text-gray-600 max-w-xl">
                Your WhatsApp-style store is active and ready to receive customers. Share your store link with customers to start selling.
            </p>

            <div class="flex flex-wrap gap-4 mt-6">
                <a href="{{ url('/vendor/store') }}" class="inline-block px-6 py-3 text-sm font-medium text-white bg-whatsapp-default rounded-lg shadow-sm hover:bg-whatsapp-dark transition-colors">
                    View Store
                </a>

                <button class="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition-colors">
                    <i class="fas fa-share-alt h-5 w-5"></i>
                    Share Store
                </button>
            </div>
        </div>

        <div class="bg-gray-50 p-6 rounded-lg lg:min-w-[280px] w-full lg:w-auto">
            <h4 class="font-semibold text-lg text-gray-800 mb-4">Quick Access</h4>
            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 rounded-full bg-whatsapp-default flex items-center justify-center">
                        <i class="fab fa-whatsapp h-5 w-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">WhatsApp Number</p>
                        <p class="text-sm text-gray-500">+1 (555) 123-4567</p>
                    </div>
                </div>

                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center">
                        <i class="fas fa-qrcode h-5 w-5 text-white"></i>
                    </div>
                    <div>
                        <p class="font-medium text-gray-800">Store QR Code</p>
                        <button class="text-sm text-whatsapp-default hover:underline">Generate QR</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
