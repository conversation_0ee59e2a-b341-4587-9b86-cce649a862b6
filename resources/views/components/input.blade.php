@props([
    'disabled' => false,
    'name' => '',
    'label' => '',
    'type' => 'text',
    'error' => null,
])

@php
$id = $attributes->get('id', 'input_'.Str::random(8));
$baseClasses = 'block w-full border-gray-300 rounded-md shadow-sm focus:ring-whatsapp-default focus:border-whatsapp-default sm:text-sm';
$errorClasses = $error ? 'border-red-500 text-red-900 placeholder-red-300 focus:ring-red-500 focus:border-red-500' : '';
@endphp

<div>
    @if ($label)
        <label for="{{ $id }}" class="block text-sm font-medium text-gray-700">{{ $label }}</label>
    @endif

    <div class="mt-1">
        <input 
            type="{{ $type }}" 
            name="{{ $name }}" 
            id="{{ $id }}" 
            {{ $disabled ? 'disabled' : '' }} 
            {{ $attributes->merge(['class' => "$baseClasses $errorClasses"]) }}
        >
    </div>

    @if ($error)
        <p class="mt-2 text-sm text-red-600">{{ $error }}</p>
    @endif
</div>
