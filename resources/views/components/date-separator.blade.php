@props(['date'])

@php
    $carbonDate = \Carbon\Carbon::parse($date);
    $formattedDate = '';

    if ($carbonDate->isToday()) {
        $formattedDate = 'Today';
    } elseif ($carbonDate->isYesterday()) {
        $formattedDate = 'Yesterday';
    } elseif ($carbonDate->diffInDays(now()) < 7) {
        $formattedDate = $carbonDate->format('l'); // e.g., Monday
    } else {
        $formattedDate = $carbonDate->format('M j, Y'); // e.g., Jun 24, 2025
    }
@endphp

<div class="flex justify-center my-2">
    <div class="bg-whatsapp-light text-gray-600 text-xs font-semibold px-2.5 py-1 rounded-full shadow-sm">
        {{ $formattedDate }}
    </div>
</div>
