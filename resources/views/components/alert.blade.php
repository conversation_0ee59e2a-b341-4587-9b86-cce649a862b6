@props(['type' => 'info', 'message'])

@php
$wrapperClasses = [
    'info' => 'bg-blue-100 border-blue-500 text-blue-700',
    'success' => 'bg-green-100 border-green-500 text-green-700',
    'warning' => 'bg-yellow-100 border-yellow-500 text-yellow-700',
    'error' => 'bg-red-100 border-red-500 text-red-700',
];

$iconClasses = [
    'info' => 'fas fa-info-circle',
    'success' => 'fas fa-check-circle',
    'warning' => 'fas fa-exclamation-triangle',
    'error' => 'fas fa-times-circle',
];

$wrapperClass = $wrapperClasses[$type] ?? $wrapperClasses['info'];
$iconClass = $iconClasses[$type] ?? $iconClasses['info'];
@endphp

<div x-data="{ show: true }" x-show="show" class="border-l-4 p-4 {{ $wrapperClass }}" role="alert">
    <div class="flex">
        <div class="py-1">
            <i class="{{ $iconClass }} mr-3"></i>
        </div>
        <div>
            <p class="font-bold">{{ ucfirst($type) }}</p>
            <p class="text-sm">{{ $message }}</p>
        </div>
        <div class="ml-auto pl-3">
            <button @click="show = false" class="-mx-1.5 -my-1.5 bg-transparent rounded-lg focus:ring-2 focus:ring-offset-2 p-1.5 inline-flex h-8 w-8 {{ $wrapperClass }}" aria-label="Dismiss">
                <span class="sr-only">Dismiss</span>
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>
