@php
// Mock data for recent orders. In a real application, this would be passed as a prop from the controller.
$recentOrders = [
    [
        'id' => 1, 'orderNumber' => 'WH-1005', 'customerName' => '<PERSON><PERSON><PERSON>', 'totalAmount' => 2500.00,
        'items' => [['id' => 1], ['id' => 2]], 'status' => 'completed', 'createdAt' => now()->subMinutes(30)->toIso8601String(),
    ],
    [
        'id' => 2, 'orderNumber' => 'WH-1004', 'customerName' => '<PERSON><PERSON> Patel', 'totalAmount' => 1250.50,
        'items' => [['id' => 1]], 'status' => 'processing', 'createdAt' => now()->subHours(2)->toIso8601String(),
    ],
    [
        'id' => 3, 'orderNumber' => 'WH-1003', 'customerName' => '<PERSON><PERSON><PERSON>', 'totalAmount' => 780.00,
        'items' => [['id' => 1], ['id' => 2], ['id' => 3]], 'status' => 'shipped', 'createdAt' => now()->subDays(1)->toIso8601String(),
    ],
    [
        'id' => 4, 'orderNumber' => 'WH-1002', 'customerName' => 'Sanya Singh', 'totalAmount' => 3200.75,
        'items' => [['id' => 1], ['id' => 2]], 'status' => 'pending', 'createdAt' => now()->subDays(3)->toIso8601String(),
    ],
    [
        'id' => 5, 'orderNumber' => 'WH-1001', 'customerName' => 'Vikram Kumar', 'totalAmount' => 500.00,
        'items' => [['id' => 1]], 'status' => 'completed', 'createdAt' => now()->subDays(5)->toIso8601String(),
    ],
];

// Helper function to format currency
if (!function_exists('formatCurrency')) {
    function formatCurrency($amount) {
        return '₹' . number_format(floatval($amount), 2);
    }
}

// Helper function to format relative time using Carbon
if (!function_exists('formatRelativeTime')) {
    function formatRelativeTime($dateString) {
        try {
            return \Carbon\Carbon::parse($dateString)->diffForHumans();
        } catch (\Exception $e) {
            return $dateString;
        }
    }
}

// Helper function to get status badge class
if (!function_exists('getStatusClass')) {
    function getStatusClass($status) {
        return [
            'completed' => 'bg-green-100 text-green-800',
            'shipped' => 'bg-blue-100 text-blue-800',
            'processing' => 'bg-yellow-100 text-yellow-800',
            'pending' => 'bg-gray-100 text-gray-800',
            'cancelled' => 'bg-red-100 text-red-800',
        ][$status] ?? 'bg-gray-100 text-gray-800';
    }
}
@endphp

<div class="bg-white p-6 rounded-lg shadow-sm">
    <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-gray-800">Recent Orders</h2>
        <a href="{{ url('/vendor/orders') }}" class="flex items-center gap-2 text-sm font-medium text-whatsapp-default hover:text-whatsapp-dark transition-colors">
            View all orders
            <i class="fas fa-arrow-right h-4 w-4"></i>
        </a>
    </div>
    <div class="space-y-4">
        @forelse ($recentOrders as $order)
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors">
                <div class="flex items-center gap-4">
                    <div class="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-500 font-bold">
                        {{ strtoupper(substr($order['customerName'], 0, 1)) }}
                    </div>
                    <div>
                        <p class="text-sm font-bold text-gray-900">
                            Order #{{ $order['orderNumber'] }}
                        </p>
                        <p class="text-xs text-gray-500">
                            by {{ $order['customerName'] }}
                        </p>
                    </div>
                </div>
                <div class="flex items-center gap-6">
                    <div class="text-right">
                        <p class="text-sm font-semibold text-gray-900">
                            {{ formatCurrency($order['totalAmount']) }}
                        </p>
                        <p class="text-xs text-gray-500">
                            {{ count($order['items']) }} {{ count($order['items']) === 1 ? 'item' : 'items' }}
                        </p>
                    </div>
                    <div class="text-right">
                        <span class="px-2 py-1 text-xs font-medium rounded-full {{ getStatusClass($order['status']) }}">
                            {{ ucfirst($order['status']) }}
                        </span>
                        <p class="text-xs text-gray-500 mt-1">
                            {{ formatRelativeTime($order['createdAt']) }}
                        </p>
                    </div>
                </div>
            </div>
        @empty
            <div class="text-center py-8">
                <p class="text-gray-500">No recent orders found.</p>
            </div>
        @endforelse
    </div>
</div>
