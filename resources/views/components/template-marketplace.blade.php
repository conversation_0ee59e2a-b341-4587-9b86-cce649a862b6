@props(['isOpen' => false, 'templateType' => 'message'])

@php
$categories = [
    ['id' => 'all', 'name' => 'All Templates'],
    ['id' => 'retail', 'name' => 'Retail & E-commerce'],
    ['id' => 'food', 'name' => 'Food & Restaurants'],
    ['id' => 'services', 'name' => 'Services'],
    ['id' => 'education', 'name' => 'Education'],
];

$templates = [
    [
        'id' => 1,
        'category' => 'retail',
        'name' => 'Welcome Message',
        'description' => 'Initial greeting for new customers',
        'content' => 'Welcome to our store! How can I help you today?',
        'buttons' => [
            ['type' => 'quick_reply', 'text' => 'View Products'],
            ['type' => 'quick_reply', 'text' => 'Check Order'],
            ['type' => 'call', 'text' => 'Call Support', 'phoneNumber' => '+91123456789'],
        ],
        'header' => 'Welcome',
        'footer' => 'Reply to continue',
    ],
    [
        'id' => 9,
        'category' => 'retail',
        'name' => 'Item Details Handler',
        'description' => 'REQUIRED for catalog: This node handles item selection and displays details.',
        'content' => 'This item is currently in stock and ready to ship. Would you like to place an order?',
        'buttons' => [
            ['type' => 'quick_reply', 'text' => 'Buy Now'],
            ['type' => 'quick_reply', 'text' => 'Back to Catalog'],
        ],
        'header' => 'ITEM DETAILS',
        'image' => 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?auto=format&fit=crop&w=600',
        'handleProductSelection' => true,
    ],
    // Add other templates here...
];
@endphp

<div x-data="templateMarketplace({ templates: {{ json_encode($templates) }} })" x-show="{{ $isOpen ? 'true' : 'false' }}" x-cloak
    class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div x-show="{{ $isOpen ? 'true' : 'false' }}" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
            class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$dispatch('close')"></div>

        <!-- Modal panel -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div x-show="{{ $isOpen ? 'true' : 'false' }}" x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
            x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Choose a Template
                            </h3>
                            <button @click="$dispatch('close')" class="text-gray-400 hover:text-gray-500">
                                <i class="fas fa-times h-6 w-6"></i>
                            </button>
                        </div>

                        <!-- Category Tabs -->
                        <div class="border-b border-gray-200 mb-6">
                            <nav class="-mb-px flex space-x-8 overflow-x-auto" aria-label="Tabs">
                                @foreach ($categories as $category)
                                    <button @click="activeCategory = '{{ $category['id'] }}'"
                                        :class="{'border-green-500 text-green-600': activeCategory === '{{ $category['id'] }}', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeCategory !== '{{ $category['id'] }}'}"
                                        class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm">
                                        {{ $category['name'] }}
                                    </button>
                                @endforeach
                            </nav>
                        </div>

                        <!-- Templates Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 h-96 overflow-y-auto pr-2">
                            <template x-for="template in filteredTemplates" :key="template.id">
                                <div @click="$dispatch('select-template', template)"
                                    class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer">
                                    <div class="p-4">
                                        <h3 class="text-md font-medium text-gray-900 mb-1" x-text="template.name"></h3>
                                        <p class="text-xs text-gray-500 mb-3" x-text="template.description"></p>
                                        <div class="bg-gray-50 p-3 rounded-md text-xs border border-gray-200">
                                            <template x-if="template.header">
                                                <div class="text-xs font-semibold text-green-600 mb-1 p-1 bg-green-50 rounded" x-text="template.header"></div>
                                            </template>
                                            <template x-if="template.image">
                                                <img :src="template.image" alt="Template preview" class="w-full h-auto max-h-20 object-cover rounded-md mb-2">
                                            </template>
                                            <div class="text-gray-700 mb-1" x-text="template.content.substring(0, 80) + (template.content.length > 80 ? '...' : '')"></div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:initializing', () => {
    Alpine.data('templateMarketplace', (data) => ({
        activeCategory: 'all',
        templates: data.templates,
        get filteredTemplates() {
            if (this.activeCategory === 'all') {
                return this.templates;
            }
            return this.templates.filter(t => t.category === this.activeCategory);
        }
    }));
});
</script>
