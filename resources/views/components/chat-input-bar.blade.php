<div class="bg-gray-200 p-3">
    <div class="flex items-center bg-white rounded-full px-4 py-2">
        <button class="text-gray-500 mr-2">
            <i class="fas fa-paperclip"></i>
        </button>

        <input 
            type="text" 
            placeholder="Type a message"
            class="flex-1 border-none outline-none bg-transparent"
            x-model="inputMessage"
            @keydown.enter.prevent="handleSendMessage()"
        />

        <template x-if="inputMessage.trim() !== ''">
            <button class="text-whatsapp-default" @click="handleSendMessage()">
                <i class="fas fa-paper-plane"></i>
            </button>
        </template>

        <template x-if="inputMessage.trim() === ''">
            <button class="text-gray-500">
                <i class="fas fa-microphone"></i>
            </button>
        </template>
    </div>
</div>
