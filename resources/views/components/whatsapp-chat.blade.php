@props(['storeName' => 'Vendor Store'])

<div x-data="whatsappChat({ storeName: '{{ $storeName }}' })" class="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg flex flex-col h-[70vh]">
    <!-- Chat Header -->
    <x-chat-header :store-name="$storeName" :is-verified="true" />

    <!-- Messages -->
    <div class="flex-1 p-4 overflow-y-auto bg-whatsapp-light-gray" x-ref="messagesBody">
        <template x-for="message in messages" :key="message.id">
            <div class="flex mb-2" :class="message.sender === 'bot' ? 'justify-start' : 'justify-end'">
                <div class="rounded-lg px-4 py-2 max-w-xs md:max-w-md shadow"
                     :class="message.sender === 'bot' ? 'bg-white text-gray-800' : 'bg-whatsapp-message-out text-gray-800'">
                    <p class="text-sm" style="white-space: pre-wrap;" x-text="message.text"></p>
                    <p class="text-right text-xs text-gray-400 mt-1" x-text="message.timestamp"></p>
                </div>
            </div>
        </template>
    </div>

    <!-- Chat Input -->
    <div class="bg-gray-200 p-3">
        <div class="flex items-center bg-white rounded-full px-4 py-2">
            <button @click="showOptions = !showOptions" class="text-gray-500">
                <i class="fas fa-paperclip h-5 w-5"></i>
            </button>

            <input type="text" placeholder="Type a message" x-model="inputMessage" @keydown.enter="handleSendMessage"
                   class="flex-1 border-none outline-none bg-transparent px-3">

            <button @click="handleSendMessage" class="text-whatsapp-teal">
                <i class="fas fa-paper-plane h-5 w-5 transform rotate-90"></i>
            </button>
        </div>

        <!-- Quick Options -->
        <div x-show="showOptions" x-cloak class="mt-2 grid grid-cols-2 gap-2">
            <button @click="sendQuickReply('1')" class="bg-white p-2 rounded-lg text-sm text-gray-700">View Products</button>
            <button @click="sendQuickReply('2')" class="bg-white p-2 rounded-lg text-sm text-gray-700">Check Order Status</button>
            <button @click="sendQuickReply('3')" class="bg-white p-2 rounded-lg text-sm text-gray-700">Contact Support</button>
            <button @click="sendQuickReply('4')" class="bg-white p-2 rounded-lg text-sm text-gray-700">About Us</button>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:initializing', () => {
    Alpine.data('whatsappChat', (data) => ({
        storeName: data.storeName,
        messages: [],
        inputMessage: '',
        showOptions: false,
        init() {
            this.messages = [
                {
                    id: 1,
                    text: `Welcome to ${this.storeName}! 👋`,
                    sender: 'bot',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                },
                {
                    id: 2,
                    text: 'How can I help you today?',
                    sender: 'bot',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                },
                {
                    id: 3,
                    text: '1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us',
                    sender: 'bot',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                }
            ];
            this.$watch('messages', () => {
                this.$nextTick(() => { this.$refs.messagesBody.scrollTop = this.$refs.messagesBody.scrollHeight });
            });
        },
        handleSendMessage() {
            if (this.inputMessage.trim() === '') return;

            this.messages.push({
                id: this.messages.length + 1,
                text: this.inputMessage,
                sender: 'user',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            });

            const userMessage = this.inputMessage;
            this.inputMessage = '';

            setTimeout(() => {
                let botResponseText;
                if (userMessage === '1' || userMessage.toLowerCase().includes('product')) {
                    botResponseText = 'Here are our popular products:\n\n🥗 Fresh Vegetables - $5.99\n🍞 Whole Grain Bread - $3.49\n🥛 Organic Milk - $4.29\n\nReply with the product name to learn more.';
                } else if (userMessage === '2' || userMessage.toLowerCase().includes('order')) {
                    botResponseText = 'To check your order status, please provide your order number.';
                } else if (userMessage === '3' || userMessage.toLowerCase().includes('support')) {
                    botResponseText = 'Our support team is available from 9 AM to 6 PM. You can also email <NAME_EMAIL>.';
                } else if (userMessage === '4' || userMessage.toLowerCase().includes('about')) {
                    botResponseText = `${this.storeName} is a local business committed to providing high-quality products.`;
                } else {
                    botResponseText = 'I\'m not sure I understand. Please select one of the options:\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us';
                }

                this.messages.push({
                    id: this.messages.length + 1,
                    text: botResponseText,
                    sender: 'bot',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                });
            }, 1000);
        },
        sendQuickReply(message) {
            this.inputMessage = message;
            this.showOptions = false;
            this.handleSendMessage();
        }
    }));
});
</script>
