@php
$features = [
    [
        'name' => 'WhatsApp-Style Chat Interface',
        'description' => 'Create automated chat flows that guide customers through your products and services with a familiar WhatsApp-like experience.',
        'icon' => 'fas fa-comments',
    ],
    [
        'name' => 'Easy Store Management',
        'description' => 'Manage your products, orders, and customers all in one place with our intuitive dashboard designed for small businesses.',
        'icon' => 'fas fa-shopping-bag',
    ],
    [
        'name' => 'Influencer Collaboration',
        'description' => 'Connect with influencers to promote your products and reach a wider audience through our built-in influencer marketplace.',
        'icon' => 'fas fa-users',
    ],
    [
        'name' => 'Secure Payment Processing',
        'description' => 'Accept payments directly through WhatsApp with our secure payment gateway integration, supporting multiple payment methods.',
        'icon' => 'fas fa-dollar-sign',
    ],
    [
        'name' => 'Mobile-First Design',
        'description' => 'Optimized for mobile devices to provide the best shopping experience for your customers wherever they are.',
        'icon' => 'fas fa-mobile-alt',
    ],
    [
        'name' => 'Analytics & Insights',
        'description' => 'Track your store performance with detailed analytics and get insights to optimize your sales and marketing strategies.',
        'icon' => 'fas fa-chart-bar',
    ],
];
@endphp

<div id="features" class="py-24 bg-gray-50">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-base font-semibold leading-7 text-whatsapp-default">Features</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Everything you need to succeed
            </p>
            <p class="mt-6 text-lg leading-8 text-gray-600">
                Our platform provides all the tools you need to create a successful WhatsApp-style store.
            </p>
        </div>

        <div class="mt-16 grid grid-cols-1 gap-x-8 gap-y-10 sm:grid-cols-2 lg:grid-cols-3">
            @foreach ($features as $feature)
                <x-card class="h-full">
                    <div class="mb-5 flex h-12 w-12 items-center justify-center rounded-lg bg-whatsapp-default bg-opacity-10">
                        <i class="{{ $feature['icon'] }} text-2xl text-whatsapp-default"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">{{ $feature['name'] }}</h3>
                    <p class="mt-2 text-base text-gray-600">{{ $feature['description'] }}</p>
                </x-card>
            @endforeach
        </div>
    </div>
</div>
