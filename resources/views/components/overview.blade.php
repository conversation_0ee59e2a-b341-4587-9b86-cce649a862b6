@php
$stats = [
    [
        'name' => 'Total Products',
        'stat' => '48',
        'icon' => 'fas fa-shopping-bag',
        'color' => 'rgba(37, 211, 102, 0.1)',
        'iconColor' => '#25D366',
        'description' => 'Products in your catalog'
    ],
    [
        'name' => 'Active Chats',
        'stat' => '24',
        'icon' => 'fas fa-comments',
        'color' => 'rgba(7, 94, 84, 0.1)',
        'iconColor' => '#075E54',
        'description' => 'Ongoing customer conversations'
    ],
    [
        'name' => 'Total Revenue',
        'stat' => '$2,430',
        'icon' => 'fas fa-dollar-sign',
        'color' => 'rgba(37, 211, 102, 0.1)',
        'iconColor' => '#25D366',
        'description' => 'Monthly earnings'
    ],
    [
        'name' => 'Total Customers',
        'stat' => '156',
        'icon' => 'fas fa-users',
        'color' => 'rgba(7, 94, 84, 0.1)',
        'iconColor' => '#075E54',
        'description' => 'Unique customers'
    ],
];
@endphp

<div class="p-6 bg-white shadow-md rounded-lg">
    <h2 class="text-xl font-semibold text-gray-800 mb-4">Store Performance</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        @foreach ($stats as $item)
            <div class="bg-white p-5 rounded-lg border border-gray-200 flex flex-col">
                <div class="flex items-center justify-center w-12 h-12 rounded-full mb-4" style="background-color: {{ $item['color'] }};">
                    <i class="{{ $item['icon'] }} text-2xl" style="color: {{ $item['iconColor'] }};"></i>
                </div>
                <h4 class="text-gray-500 text-sm font-medium">{{ $item['name'] }}</h4>
                <p class="text-2xl font-bold text-gray-800 mt-1">{{ $item['stat'] }}</p>
                <p class="text-sm text-gray-500 mt-2">{{ $item['description'] }}</p>
            </div>
        @endforeach
    </div>
</div>

