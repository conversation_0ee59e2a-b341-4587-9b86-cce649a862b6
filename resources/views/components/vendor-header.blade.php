<header class="vendor-header">
    <div class="header-content">
        <!-- Mobile Menu Toggle -->
        <button @click="sidebarOpen = !sidebarOpen" class="mobile-menu-toggle md:hidden">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Desktop Sidebar Toggle -->
        <button @click="sidebarVisible = !sidebarVisible" class="sidebar-toggle hidden md:block">
            <i class="fas fa-bars"></i>
        </button>

        <!-- Page Title -->
        <div class="header-title">
            <h1 class="page-title">@yield('page-title', 'Dashboard')</h1>
            <p class="page-subtitle">@yield('page-subtitle', 'Manage your store and track performance')</p>
        </div>

        <!-- Header Actions -->
        <div class="header-actions">
            <!-- Notifications -->
            <div class="header-action-item" x-data="{ open: false }">
                <button @click="open = !open" class="action-btn notification-btn">
                    <i class="fas fa-bell"></i>
                    <span class="notification-badge">3</span>
                </button>
                
                <!-- Notifications Dropdown -->
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="notifications-dropdown">
                    <div class="dropdown-header">
                        <h3>Notifications</h3>
                        <span class="notification-count">3 new</span>
                    </div>
                    <div class="notifications-list">
                        <div class="notification-item unread">
                            <div class="notification-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="notification-content">
                                <p class="notification-title">New Order Received</p>
                                <p class="notification-text">Order #1234 from John Doe</p>
                                <span class="notification-time">2 minutes ago</span>
                            </div>
                        </div>
                        <div class="notification-item unread">
                            <div class="notification-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="notification-content">
                                <p class="notification-title">New Review</p>
                                <p class="notification-text">5-star review on Product ABC</p>
                                <span class="notification-time">1 hour ago</span>
                            </div>
                        </div>
                        <div class="notification-item">
                            <div class="notification-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="notification-content">
                                <p class="notification-title">Weekly Report</p>
                                <p class="notification-text">Your weekly sales report is ready</p>
                                <span class="notification-time">1 day ago</span>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-footer">
                        <a href="#" class="view-all-btn">View All Notifications</a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="header-action-item" x-data="{ open: false }">
                <button @click="open = !open" class="action-btn quick-action-btn">
                    <i class="fas fa-plus"></i>
                </button>
                
                <!-- Quick Actions Dropdown -->
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="quick-actions-dropdown">
                    <div class="dropdown-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="quick-actions-list">
                        <a href="{{ route('vendor.products.create') }}" class="quick-action-item">
                            <div class="action-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="action-content">
                                <p class="action-title">Add Product</p>
                                <p class="action-description">Create a new product</p>
                            </div>
                        </a>
                        <a href="{{ route('vendor.services.create') }}" class="quick-action-item">
                            <div class="action-icon">
                                <i class="fas fa-concierge-bell"></i>
                            </div>
                            <div class="action-content">
                                <p class="action-title">Add Service</p>
                                <p class="action-description">Create a new service</p>
                            </div>
                        </a>
                        <a href="{{ route('vendor.chat-flow.builder') }}" class="quick-action-item">
                            <div class="action-icon">
                                <i class="fas fa-comments"></i>
                            </div>
                            <div class="action-content">
                                <p class="action-title">Create Chat Flow</p>
                                <p class="action-description">Build customer chat flow</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Profile -->
            <div class="header-action-item" x-data="{ open: false }">
                <button @click="open = !open" class="user-profile-btn">
                    <div class="user-avatar">
                        @if(Auth::user()->profilePicture)
                            <img src="{{ Auth::user()->profilePicture }}" alt="Profile" class="user-avatar-img">
                        @else
                            <div class="user-avatar-fallback">
                                {{ substr(Auth::user()->name, 0, 1) }}
                            </div>
                        @endif
                    </div>
                    <div class="user-info hidden md:block">
                        <p class="user-name">{{ Auth::user()->name }}</p>
                        <p class="user-role">Vendor</p>
                    </div>
                    <i class="fas fa-chevron-down user-dropdown-icon"></i>
                </button>
                
                <!-- User Dropdown -->
                <div x-show="open" 
                     @click.away="open = false"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     class="user-dropdown">
                    <div class="dropdown-header">
                        <div class="user-avatar">
                            @if(Auth::user()->profilePicture)
                                <img src="{{ Auth::user()->profilePicture }}" alt="Profile" class="user-avatar-img">
                            @else
                                <div class="user-avatar-fallback">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                            @endif
                        </div>
                        <div class="user-info">
                            <h4 class="user-name">{{ Auth::user()->name }}</h4>
                            <p class="user-email">{{ Auth::user()->email }}</p>
                        </div>
                    </div>
                    <div class="dropdown-menu">
                        <a href="{{ route('vendor.profile') }}" class="dropdown-item">
                            <i class="fas fa-user"></i>
                            <span>Profile Settings</span>
                        </a>
                        <a href="{{ route('vendor.store-settings') }}" class="dropdown-item">
                            <i class="fas fa-store"></i>
                            <span>Store Settings</span>
                        </a>
                        <a href="{{ route('vendor.settings') }}" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            <span>Account Settings</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <form method="POST" action="{{ route('logout') }}" class="logout-form">
                            @csrf
                            <button type="submit" class="dropdown-item logout-item">
                                <i class="fas fa-sign-out-alt"></i>
                                <span>Logout</span>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</header>
