@php
$navigation = [
    ['name' => 'Dashboard', 'icon' => 'fa-home', 'route' => 'vendor.dashboard'],
    ['name' => 'Orders', 'icon' => 'fa-box', 'route' => 'vendor.orders'],
    ['name' => 'Products', 'icon' => 'fa-tag', 'route' => 'vendor.products'],
    ['name' => 'Customers', 'icon' => 'fa-users', 'route' => 'vendor.customers'],
    ['name' => 'Analytics', 'icon' => 'fa-chart-bar', 'route' => 'vendor.analytics'],
    ['name' => 'Marketing', 'icon' => 'fa-bullhorn', 'route' => 'vendor.marketing'],
];

$secondaryNavigation = [
    ['name' => 'Settings', 'icon' => 'fa-cog', 'route' => 'vendor.settings'],
    ['name' => 'Support', 'icon' => 'fa-life-ring', 'route' => 'vendor.support'],
];
@endphp

<div x-data="{ open: false }" class="flex h-screen bg-gray-100">
    <!-- Static sidebar for desktop -->
    <aside class="hidden md:flex md:flex-shrink-0">
        <div class="flex flex-col w-64">
            <div class="flex flex-col h-0 flex-1 bg-white border-r border-gray-200">
                <div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
                    <div class="flex items-center flex-shrink-0 px-4">
                        <i class="fas fa-store text-whatsapp-default text-2xl"></i>
                        <span class="ml-3 text-xl font-bold text-gray-800">WhaMart Vendor</span>
                    </div>
                    <nav class="mt-5 flex-1 px-2 space-y-1">
                        @foreach ($navigation as $item)
                            <a href="{{ route($item['route']) }}" 
                               class="{{ request()->routeIs($item['route']) ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }} group flex items-center px-2 py-2 text-sm font-medium rounded-md">
                                <i class="fas {{ $item['icon'] }} mr-3 flex-shrink-0 h-6 w-6 text-gray-400 group-hover:text-gray-500"></i>
                                {{ $item['name'] }}
                            </a>
                        @endforeach
                    </nav>
                </div>
                <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                    <a href="#" class="flex-shrink-0 w-full group block">
                        <div class="flex items-center">
                            <div>
                                <img class="inline-block h-9 w-9 rounded-full" src="https://i.pravatar.cc/150?u=a042581f4e29026704d" alt="">
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900">Vendor Name</p>
                                <p class="text-xs font-medium text-gray-500 group-hover:text-gray-700">View profile</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </aside>

    <!-- Mobile menu button -->
    <div class="md:hidden">
        <button @click="open = true" class="p-4 text-gray-500 hover:text-gray-600">
            <i class="fas fa-bars h-6 w-6"></i>
        </button>
    </div>

    <!-- Mobile menu -->
    <div x-show="open" class="fixed inset-0 flex z-40 md:hidden" x-cloak>
        <!-- Off-canvas menu overlay, show/hide based on off-canvas menu state. -->
        <div x-show="open" 
             x-transition:enter="transition-opacity ease-linear duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition-opacity ease-linear duration-300"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="open = false"></div>

        <!-- Off-canvas menu, show/hide based on off-canvas menu state. -->
        <div x-show="open"
             x-transition:enter="transition ease-in-out duration-300 transform"
             x-transition:enter-start="-translate-x-full"
             x-transition:enter-end="translate-x-0"
             x-transition:leave="transition ease-in-out duration-300 transform"
             x-transition:leave-start="translate-x-0"
             x-transition:leave-end="-translate-x-full"
             class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div class="absolute top-0 right-0 -mr-12 pt-2">
                <button @click="open = false" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                    <span class="sr-only">Close sidebar</span>
                    <i class="fas fa-times text-white h-6 w-6"></i>
                </button>
            </div>
            <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                <div class="flex-shrink-0 flex items-center px-4">
                    <i class="fas fa-store text-whatsapp-default text-2xl"></i>
                    <span class="ml-3 text-xl font-bold text-gray-800">WhaMart Vendor</span>
                </div>
                <nav class="mt-5 px-2 space-y-1">
                    @foreach ($navigation as $item)
                        <a href="{{ route($item['route']) }}" class="{{ request()->routeIs($item['route']) ? 'bg-gray-100 text-gray-900' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900' }} group flex items-center px-2 py-2 text-base font-medium rounded-md">
                            <i class="fas {{ $item['icon'] }} mr-4 flex-shrink-0 h-6 w-6 text-gray-400 group-hover:text-gray-500"></i>
                            {{ $item['name'] }}
                        </a>
                    @endforeach
                </nav>
            </div>
            <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                <a href="#" class="flex-shrink-0 group block">
                    <div class="flex items-center">
                        <div>
                            <img class="inline-block h-10 w-10 rounded-full" src="https://i.pravatar.cc/150?u=a042581f4e29026704d" alt="">
                        </div>
                        <div class="ml-3">
                            <p class="text-base font-medium text-gray-700 group-hover:text-gray-900">Vendor Name</p>
                            <p class="text-sm font-medium text-gray-500 group-hover:text-gray-700">View profile</p>
                        </div>
                    </div>
                </a>
            </div>
        </div>
        <div class="flex-shrink-0 w-14"></div><!-- Force sidebar to shrink to fit close icon -->
    </div>
</div>
