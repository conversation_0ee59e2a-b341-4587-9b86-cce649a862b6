@props(['message', 'interactive' => false])

@php
    $isIncoming = $message['sender'] === 'store';
    $bubbleClasses = $isIncoming
        ? 'bg-white text-gray-800 rounded-tr-lg rounded-bl-lg rounded-br-lg'
        : 'bg-whatsapp-message-out text-gray-800 rounded-tl-lg rounded-bl-lg rounded-br-lg';

    if (!function_exists('getStatusIcon')) {
        function getStatusIcon($status) {
            switch ($status) {
                case 'sent':
                    return '<i class="fas fa-check text-gray-400"></i>';
                case 'delivered':
                    return '<i class="fas fa-check-double text-gray-400"></i>';
                case 'read':
                    return '<i class="fas fa-check-double text-blue-500"></i>';
                default:
                    return '';
            }
        }
    }

    if (!function_exists('getButtonIcon')) {
        function getButtonIcon($type) {
            switch ($type) {
                case 'quick_reply':
                    return '<i class="fas fa-reply mr-2"></i>';
                case 'url':
                    return '<i class="fas fa-external-link-alt mr-2"></i>';
                case 'call':
                    return '<i class="fas fa-phone mr-2"></i>';
                default:
                    return '';
            }
        }
    }
@endphp

<div class="flex {{ $isIncoming ? 'justify-start' : 'justify-end' }} mb-2">
    <div class="max-w-xs md:max-w-md lg:max-w-lg">
        <div class="relative px-4 py-2 shadow-md {{ $bubbleClasses }}">
            @if(isset($message['image']))
                <div class="mb-2">
                    <img src="{{ $message['image'] }}" alt="Message attachment" class="rounded-lg w-full object-cover">
                </div>
            @endif

            @if(isset($message['productCatalog']))
                <div class="p-2 border-t border-gray-200">
                    <p class="text-sm font-semibold">Product Catalog</p>
                    <div class="grid grid-cols-1 gap-2 mt-2">
                        @foreach($message['productCatalog'] as $product)
                            <div class="bg-white rounded-lg shadow-md p-2">
                                <img src="{{ $product['image'] }}" alt="Product image" class="w-full object-cover h-40">
                                <p class="text-sm font-semibold mt-2">{{ $product['title'] }}</p>
                                <p class="text-xs text-gray-500">{{ $product['description'] }}</p>
                                <p class="text-sm font-semibold mt-2">{{ $product['price'] }}</p>
                            </div>
                        @endforeach
                    </div>
                </div>
            @else
                <div class="text-sm">
                    {!! $message['content'] !!}
                </div>
            @endif

            @if(isset($message['footer']))
                <div class="text-xs text-gray-500 mt-1 pt-1 border-t border-gray-200/50">
                    {{ $message['footer'] }}
                </div>
            @endif

            @if(isset($message['buttons']) && count($message['buttons']) > 0)
                <div class="mt-2 border-t border-gray-200/50">
                    @foreach($message['buttons'] as $button)
                        @if($interactive && $button['type'] === 'quick_reply')
                            <button @click.prevent="$dispatch('quick-reply', '{{ $button['text'] }}')"
                               class="block text-center text-blue-600 hover:bg-gray-100 py-2 text-sm font-medium w-full">
                                {!! getButtonIcon($button['type']) !!}
                                <span>{{ $button['text'] }}</span>
                            </button>
                        @else
                            <a href="{{ $button['url'] ?? '#' }}" 
                               target="{{ isset($button['url']) ? '_blank' : '_self' }}"
                               class="block text-center text-blue-600 hover:bg-gray-100 py-2 text-sm font-medium w-full">
                                {!! getButtonIcon($button['type']) !!}
                                <span>{{ $button['text'] }}</span>
                            </a>
                        @endif
                    @endforeach
                </div>
            @endif

            <div class="text-right text-xs text-gray-400 mt-1">
                <span>{{ \Carbon\Carbon::parse($message['timestamp'])->format('h:i A') }}</span>
                @if(!$isIncoming)
                    <span class="ml-1">
                        {!! getStatusIcon($message['status']) !!}
                    </span>
                @endif
            </div>
        </div>
    </div>
</div>
