<div class="relative isolate overflow-hidden pt-14">
    <!-- Background gradient -->
    <div class="absolute inset-x-0 top-0 -z-10 transform-gpu overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-b from-whatsapp-teal to-whatsapp-dark opacity-80"></div>
    </div>

    <!-- Decorative elements -->
    <div class="absolute inset-0 -z-10 opacity-30">
        <svg class="absolute left-[calc(50%-30rem)] top-0 h-[42rem] max-w-none -translate-x-1/2 stroke-gray-200 [mask-image:radial-gradient(32rem_32rem_at_center,white,transparent)]" aria-hidden="true">
            <defs>
                <pattern id="chat-pattern" width="200" height="200" x="50%" y="-1" patternUnits="userSpaceOnUse">
                    <path d="M.5 200V.5H200" fill="none" />
                </pattern>
            </defs>
            <svg x="50%" y="-1" class="overflow-visible fill-whatsapp-light/20">
                <path d="M-200 0h201v201h-201Z M600 0h201v201h-201Z M-400 600h201v201h-201Z M200 800h201v201h-201Z" strokeWidth={0} />
            </svg>
            <rect width="100%" height="100%" strokeWidth={0} fill="url(#chat-pattern)" />
        </svg>
    </div>

    <div class="mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:flex lg:items-center lg:gap-x-10 lg:px-8 lg:py-40">
        <div class="mx-auto max-w-2xl lg:mx-0 lg:flex-auto">
            <h1 class="max-w-lg text-4xl font-bold tracking-tight text-white sm:text-6xl">
                Create Your WhatsApp Store in Minutes
            </h1>
            <p class="mt-6 text-lg leading-8 text-whatsapp-light">
                Connect with customers through automated chat flows and boost your sales with our WhatsApp-style e-commerce platform. Perfect for small shop owners looking to expand their online presence.
            </p>
            <div class="mt-10 flex items-center gap-x-6">
                <x-button href="/register" variant="white" size="lg">
                    Get Started
                    <i class="fas fa-arrow-right ml-2"></i>
                </x-button>
                <a href="#how-it-works" class="text-sm font-semibold leading-6 text-white">
                    Learn more <span aria-hidden="true">→</span>
                </a>
            </div>
        </div>

        <!-- Chat demo mockup -->
        <div class="mt-16 sm:mt-24 lg:mt-0 lg:flex-shrink-0 lg:flex-grow">
            <div class="relative mx-auto w-[300px] h-[600px] border-8 border-gray-800 rounded-[40px] shadow-xl overflow-hidden bg-white">
                <!-- Phone notch -->
                <div class="absolute top-0 inset-x-0 h-6 bg-gray-800 rounded-b-lg"></div>

                <!-- WhatsApp-style header -->
                <div class="bg-whatsapp-teal text-white p-3">
                    <div class="flex items-center">
                        <div class="w-10 h-10 rounded-full bg-gray-300 flex-shrink-0"></div>
                        <div class="ml-3">
                            <div class="font-medium">Whamart Store</div>
                            <div class="text-xs opacity-80">Online</div>
                        </div>
                    </div>
                </div>

                <!-- Chat area -->
                <div class="h-[calc(100%-120px)] bg-[#e5ddd5] p-3 overflow-y-auto bg-opacity-70" style="background-image: url('https://web.whatsapp.com/img/bg-chat-tile-light_04fcacde539c58cca6745483d4858c52.png')">
                    <!-- Received message -->
                    <div class="mb-4 max-w-[80%]">
                        <div class="bg-white rounded-lg p-2 shadow-sm">
                            <p class="text-sm">👋 Welcome to Whamart Store! How can I help you today?</p>
                            <p class="text-right text-xs text-gray-500 mt-1">10:30 AM</p>
                        </div>
                    </div>

                    <!-- Sent message -->
                    <div class="mb-4 ml-auto max-w-[80%]">
                        <div class="bg-[#dcf8c6] rounded-lg p-2 shadow-sm">
                            <p class="text-sm">I'd like to see your latest products</p>
                            <p class="text-right text-xs text-gray-500 mt-1">10:31 AM</p>
                        </div>
                    </div>

                    <!-- Received message with products -->
                    <div class="mb-4 max-w-[80%]">
                        <div class="bg-white rounded-lg p-2 shadow-sm">
                            <p class="text-sm">Here are our latest products:</p>
                            <div class="mt-2 grid grid-cols-2 gap-2">
                                <div class="bg-gray-100 rounded p-1 text-center text-xs">Product 1</div>
                                <div class="bg-gray-100 rounded p-1 text-center text-xs">Product 2</div>
                                <div class="bg-gray-100 rounded p-1 text-center text-xs">Product 3</div>
                                <div class="bg-gray-100 rounded p-1 text-center text-xs">Product 4</div>
                            </div>
                            <p class="text-right text-xs text-gray-500 mt-1">10:32 AM</p>
                        </div>
                    </div>
                </div>

                <!-- Input area -->
                <div class="absolute bottom-0 inset-x-0 bg-[#f0f0f0] p-2">
                    <div class="flex items-center bg-white rounded-full px-3 py-1">
                        <input type="text" placeholder="Type a message" class="flex-1 border-0 bg-transparent text-sm focus:outline-none" />
                        <button class="w-8 h-8 rounded-full bg-whatsapp-default text-white flex items-center justify-center">
                            <i class="fas fa-paper-plane transform rotate-90"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
