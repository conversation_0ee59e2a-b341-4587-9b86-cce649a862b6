<section class="hero-section">
    <div class="decorative-circle-1"></div>
    <div class="decorative-circle-2"></div>

    <div class="hero-content">
        <div class="hero-badge">WhatsApp-Style E-commerce Platform</div>
        <h2 class="hero-title">Create Your WhatsApp Store in <span class="text-primary">Minutes</span></h2>
        <p class="hero-subtitle">
            Connect with customers through automated chat flows and boost your sales with our WhatsApp-style e-commerce platform. Perfect for small shop owners looking to expand their online presence.
        </p>
        <div class="hero-actions">
            <a href="/register" class="btn btn-primary btn-lg">Get Started Free</a>
            <a href="#how-it-works" class="btn-watch-video">
                <div class="play-icon-wrapper">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M6.271 5.055a.5.5 0 0 1 .52.038l3.5 2.5a.5.5 0 0 1 0 .814l-3.5 2.5A.5.5 0 0 1 6 10.5v-5a.5.5 0 0 1 .271-.445z"/>
                    </svg>
                </div>
                See how it works
            </a>
        </div>
        <div class="social-proof">
            <div class="user-avatars">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="User 1" class="avatar" />
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User 2" class="avatar" />
                <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="User 3" class="avatar" />
                <div class="avatar-count">+2k</div>
            </div>
            <div class="reviews">
                <div class="stars">
                    @for ($i = 0; $i < 5; $i++)
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.282.95l-3.522 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>
                        </svg>
                    @endfor
                </div>
                <div class="review-text"><strong>4.9/5</strong> from over 2,000 reviews</div>
            </div>
        </div>
    </div>

    <div class="hero-visual">
        <div class="phone-mockup">
            <div class="phone-notch"></div>
            <div class="phone-header">
                <div class="phone-header-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 16a2 2 0 0 0 2-2H6a2 2 0 0 0 2 2zM8 1.918l-.797.161A4.002 4.002 0 0 0 4 6c0 .628-.134 2.197-.459 3.742-.16.767-.376 1.566-.663 2.258h10.244c-.287-.692-.502-1.49-.663-2.258C12.134 8.197 12 6.628 12 6a4.002 4.002 0 0 0-3.203-3.92L8 1.917zM14.22 12c.223.447.481.801.78 1H1c.299-.199.557-.553.78-1C2.68 10.2 3 6.88 3 6c0-2.42 1.72-4.44 4.005-4.901a1 1 0 1 1 1.99 0A5.002 5.002 0 0 1 13 6c0 .88.32 4.2 1.22 6z"/>
                    </svg>
                </div>
                <div>
                    <div class="phone-header-title">Whamart Store</div>
                    <div class="phone-header-status">Online</div>
                </div>
            </div>
            <div class="phone-chat-area">
                <div class="chat-message bot">
                    <div class="message-bubble">
                        <p>👋 Welcome to Whamart Store! How can I help you today?</p>
                        <div class="message-time">10:30 AM</div>
                    </div>
                </div>
                <div class="chat-message user">
                    <div class="message-bubble">
                        <p>I'd like to see your latest products</p>
                        <div class="message-time">10:31 AM</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
