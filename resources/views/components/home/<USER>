<section id="comparison" class="comparison-section">
    <div class="decorative-circle-5"></div>

    <div class="section-header">
        <div class="section-badge">Detailed Comparison</div>
        <h2 class="section-title">
            WhatsApp Business API vs <span class="text-primary">WhaMart</span>
        </h2>
        <p class="section-subtitle">
            See why WhaMart is the better choice for small businesses looking to create a WhatsApp-style store.
        </p>
    </div>

    <div class="comparison-table-wrapper">
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>Feature</th>
                    <th>WhatsApp Business API</th>
                    <th class="whamart-col">WhaMart</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="feature-name">Messaging Limits</td>
                    <td>Limited to 1,000 messages per day initially, with tiered increases based on usage and quality rating</td>
                    <td class="tick-cell">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                        </svg>
                        <span>Unlimited automated messages without extra costs</span>
                    </td>
                </tr>
                <tr>
                    <td class="feature-name">Automated Chat Flows</td>
                    <td>Limited to pre-approved message templates with minimal customization</td>
                    <td class="tick-cell">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                        </svg>
                        <span>Fully customizable drag-and-drop chat flow builder</span>
                    </td>
                </tr>
                <tr>
                    <td class="feature-name">Group Chat</td>
                    <td>Limited functionality, businesses can only respond to messages in groups</td>
                    <td class="tick-cell">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                        </svg>
                        <span>Customer segmentation with broadcast capabilities</span>
                    </td>
                </tr>
                <tr>
                    <td class="feature-name">Product Catalog</td>
                    <td>Basic catalog with limited customization options</td>
                    <td class="tick-cell">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                            <path d="M10.97 4.97a.235.235 0 0 0-.02.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-1.071-1.05z"/>
                        </svg>
                        <span>Rich product catalogs with categories, variants, and more</span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</section>
