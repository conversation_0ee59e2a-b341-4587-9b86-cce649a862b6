@php
$testStoreNames = [
    "Anita Kirana Store",
    "Anita <PERSON> Store & General Merchandise",
    "Short Name"
];
@endphp

<div class="w-full max-w-md mx-auto my-5 border border-gray-300 rounded-lg overflow-hidden shadow-lg">
    <h2 class="p-3 text-center font-bold text-lg bg-gray-50">Header Test</h2>
    
    <div class="p-4 space-y-6">
        @foreach ($testStoreNames as $name)
            <div class="border border-gray-200 rounded-md overflow-hidden">
                <p class="px-3 py-2 bg-gray-100 text-sm text-gray-700">
                    Testing with name: <span class="font-semibold">"{{ $name }}"</span>
                </p>
                <x-chat-header 
                    :storeName="$name" 
                    :storeLogoUrl="null" 
                    :isVerified="true" 
                />
            </div>
        @endforeach
    </div>
</div>
