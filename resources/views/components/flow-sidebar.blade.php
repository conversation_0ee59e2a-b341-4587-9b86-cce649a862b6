@props(['flowName', 'flowDescription'])

<div
    x-data="{
        name: '{{ $flowName }}',
        description: '{{ $flowDescription }}'
    }"
    class="w-80 bg-white border-r border-gray-200 h-full overflow-y-auto p-4 flex flex-col"
>
    <!-- Flow Name & Description -->
    <div class="mb-4">
        <label for="flowName" class="block text-sm font-medium text-gray-700 mb-1">Flow Name *</label>
        <x-input id="flowName" type="text" x-model="name" placeholder="Enter flow name" required />
    </div>
    <div class="mb-6">
        <label for="flowDescription" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
        <textarea
            id="flowDescription"
            x-model="description"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
            placeholder="Enter flow description (optional)"
            rows="3"
        ></textarea>
    </div>

    <!-- Action Buttons -->
    <div class="space-y-3 mb-6">
        <x-button class="w-full justify-center" disabled>
            <i class="fas fa-plus mr-2"></i> Add Message
        </x-button>
        <x-button variant="info" class="w-full justify-center" disabled>
            <i class="fas fa-shopping-cart mr-2"></i> Add Product Catalog
        </x-button>
        <x-button variant="secondary" class="w-full justify-center" disabled>
            <i class="fas fa-briefcase mr-2"></i> Add Service Catalog
        </x-button>
    </div>

    <!-- Save Button -->
    <div class="mb-6">
        <x-button
            variant="primary"
            class="w-full justify-center"
            @click="$dispatch('save-flow', { name, description })"
        >
            Save Flow
        </x-button>
    </div>

    <!-- Instructions -->
    <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-4">
        <div class="flex items-center mb-2">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            <h3 class="text-sm font-medium text-gray-700">How to Build a Flow</h3>
        </div>
        <ol class="text-xs text-gray-600 space-y-2 ml-4 list-decimal">
            <li>Click "Add Message" to create a new message node.</li>
            <li>Drag nodes to position them on the canvas.</li>
            <li>Click on a node to edit its content and add buttons.</li>
            <li>Connect nodes by dragging from the green socket on the right side to another node.</li>
        </ol>
    </div>

    <!-- Tips -->
    <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
        <div class="flex items-center mb-2">
            <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
            <h3 class="text-sm font-medium text-gray-700">Tips</h3>
        </div>
        <ul class="text-xs text-gray-600 space-y-2 ml-4 list-disc">
            <li>Use the mouse wheel to zoom in and out.</li>
            <li>Hold the mouse button and drag to pan the canvas.</li>
            <li>Each Quick Reply button can connect to only one message.</li>
        </ul>
    </div>

    <div class="mt-auto pt-4 text-xs text-gray-500 text-center">
        WhaMart Chat Flow Builder
    </div>
</div>
