@props([
    'storeData' => [],
    'chatFlow' => null
])

<div x-data="storeTheme({
    storeData: {{ json_encode($storeData) }},
    chatFlow: {{ json_encode($chatFlow) }}
})" x-init="init()" class="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg font-sans">

    <!-- Loading Screen -->
    <div x-show="loading" x-cloak class="flex flex-col items-center justify-center p-8 h-96">
        <img src="/WhaMart_Logo.png" alt="WhaMart Logo" class="w-32 mb-4">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        <p class="mt-4 text-lg font-semibold">Loading Store...</p>
        <p class="text-sm text-gray-500">दुकान Online है ❤️</p>
    </div>

    <!-- Main Chat Interface -->
    <div x-show="!loading" x-cloak class="flex flex-col h-[80vh] bg-whatsapp-background bg-repeat">
        <!-- Chat Header -->
        <header class="flex items-center p-3 border-b border-gray-200 bg-gray-100 flex-shrink-0">
            <img :src="store.logoUrl || 'https://i.pravatar.cc/40'" alt="Store Logo" class="w-10 h-10 rounded-full">
            <div class="ml-3">
                <h2 class="text-md font-semibold text-gray-800 flex items-center">
                    <span x-text="store.name"></span>
                    <i x-show="store.isVerified" class="fas fa-check-circle text-green-500 ml-2 text-sm"></i>
                </h2>
                <p class="text-xs text-gray-500">Typically replies instantly</p>
            </div>
        </header>

        <!-- Chat Body -->
        <main class="flex-1 p-4 overflow-y-auto" x-ref="chatBody">
            <template x-for="message in messages" :key="message.id">
                <div :class="{'justify-end': message.sender === 'visitor', 'justify-start': message.sender !== 'visitor'}" class="flex mb-4">
                    <div :class="{'bg-whatsapp-user-message': message.sender === 'visitor', 'bg-white': message.sender !== 'visitor'}" class="rounded-lg px-4 py-2 max-w-xs shadow-sm">
                        <!-- Message Header (e.g., for product catalogs) -->
                        <template x-if="message.header">
                            <div class="font-bold text-sm mb-1" x-text="message.header"></div>
                        </template>

                        <!-- Message Image -->
                        <template x-if="message.image">
                            <img :src="message.image" alt="Message attachment" class="rounded-md mb-2 w-full">
                        </template>

                        <!-- Message Content -->
                        <p class="text-sm text-gray-800" x-html="message.content"></p>

                        <!-- Message Buttons (Quick Replies) -->
                        <template x-if="message.buttons && message.buttons.length">
                            <div class="mt-2 border-t border-gray-200 pt-2">
                                <template x-for="(button, index) in message.buttons" :key="index">
                                    <button @click="handleQuickReply(button)" class="w-full text-center text-blue-600 font-semibold py-1 hover:bg-gray-100 rounded-md">
                                        <span x-text="button.text"></span>
                                    </button>
                                </template>
                            </div>
                        </template>

                        <!-- Timestamp -->
                        <div class="text-right text-xs text-gray-400 mt-1">
                            <span x-text="new Date(message.timestamp).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})"></span>
                            <i x-show="message.sender === 'visitor'" class="fas fa-check-double ml-1" :class="{'text-blue-500': message.status === 'read', 'text-gray-400': message.status !== 'read'}"></i>
                        </div>
                    </div>
                </div>
            </template>
        </main>

        <!-- Chat Input -->
        <footer class="p-3 bg-gray-100 flex items-center flex-shrink-0">
            <input type="text" x-model="userInput" @keydown.enter="handleSendMessage" placeholder="Type a message..." class="w-full px-4 py-2 border rounded-full focus:outline-none focus:ring-2 focus:ring-whatsapp-default">
            <button @click="handleSendMessage" class="ml-3 text-whatsapp-default p-2 rounded-full hover:bg-gray-200">
                <i class="fas fa-paper-plane text-xl"></i>
            </button>
        </footer>
    </div>
</div>

<script>
document.addEventListener('alpine:initializing', () => {
    Alpine.data('storeTheme', (initialData) => ({
        store: { name: 'Store Name', isVerified: true, logoUrl: 'https://i.pravatar.cc/40' },
        chatFlow: null,
        messages: [],
        loading: true,
        userInput: '',

        init() {
            this.store = { ...this.store, ...initialData.storeData };
            this.chatFlow = initialData.chatFlow;

            setTimeout(() => {
                this.loading = false;
                this.startChatFlow();
            }, 1500);
        },

        startChatFlow() {
            if (!this.chatFlow || !this.chatFlow.flowData || !this.chatFlow.flowData.nodes) {
                this.addMessage({ content: 'Chat flow not configured.', sender: 'store' });
                return;
            }
            const startNode = this.chatFlow.flowData.nodes.find(node => node.type === 'startNode') || this.chatFlow.flowData.nodes[0];
            if (startNode) {
                const message = this.createMessageFromNode(startNode);
                this.addMessage(message);
            }
        },

        createMessageFromNode(node) {
            const message = {
                id: node.id || Date.now(),
                content: node.data.content || '',
                sender: 'store',
                timestamp: new Date(),
                header: node.data.header,
                image: node.data.image ? (typeof node.data.image === 'string' ? node.data.image : (node.data.image.url || null)) : null,
                buttons: []
            };

            if (node.data.buttons && node.data.buttons.length > 0) {
                message.buttons = node.data.buttons.map(button => ({ ...button }));
            } else {
                const outgoingEdges = this.chatFlow.flowData.edges.filter(edge => edge.source === node.id);
                if (outgoingEdges.length > 0) {
                    message.buttons = outgoingEdges.map((edge, index) => {
                        const targetNode = this.chatFlow.flowData.nodes.find(n => n.id === edge.target);
                        return {
                            text: edge.label || targetNode?.data?.buttonLabel || `Option ${index + 1}`,
                            nextNodeId: edge.target
                        };
                    });
                }
            }
            return message;
        },

        handleQuickReply(button) {
            const userMessage = {
                id: Date.now(),
                content: button.text,
                sender: 'visitor',
                timestamp: new Date(),
                status: 'sent'
            };
            this.addMessage(userMessage);

            setTimeout(() => this.updateMessageStatus(userMessage.id, 'read'), 500);

            if (button.nextNodeId) {
                const nextNode = this.chatFlow.flowData.nodes.find(node => node.id === button.nextNodeId);
                if (nextNode) {
                    setTimeout(() => {
                        const nextMessage = this.createMessageFromNode(nextNode);
                        this.addMessage(nextMessage);
                    }, 1000);
                } else {
                    this.addSystemMessage("This part of the chat hasn't been configured yet.");
                }
            }
        },

        handleSendMessage() {
            if (!this.userInput.trim()) return;

            const userMessage = {
                id: Date.now(),
                content: this.userInput.trim(),
                sender: 'visitor',
                timestamp: new Date(),
                status: 'sent'
            };
            this.addMessage(userMessage);
            this.userInput = '';

            setTimeout(() => this.updateMessageStatus(userMessage.id, 'read'), 500);

            setTimeout(() => {
                this.addSystemMessage("I can only respond to button clicks for now.");
            }, 1500);
        },

        addMessage(message) {
            this.messages.push(message);
            this.scrollToBottom();
        },

        addSystemMessage(content) {
            this.addMessage({ id: Date.now(), content, sender: 'store', timestamp: new Date() });
        },

        updateMessageStatus(messageId, status) {
            const index = this.messages.findIndex(m => m.id === messageId);
            if (index !== -1) {
                this.messages[index].status = status;
            }
        },

        scrollToBottom() {
            this.$nextTick(() => {
                this.$refs.chatBody.scrollTop = this.$refs.chatBody.scrollHeight;
            });
        }
    }));
});
</script>
