{{-- Reusable Button Component --}}
@props([
    'variant' => 'primary',
    'size' => 'md',
    'to' => null,
    'fullWidth' => false,
])

@php
// Determine the tag based on the 'to' prop
$tag = $to ? 'a' : 'button';

// Base classes for all button variants
$baseClasses = "inline-flex items-center justify-center font-medium rounded-md focus:outline-none transition-colors";

// Size classes
$sizeClasses = [
    'sm' => 'px-3 py-2 text-sm',
    'md' => 'px-4 py-2.5 text-base',
    'lg' => 'px-6 py-3 text-lg',
];

// Variant classes
$variantClasses = [
    'primary' => 'bg-whatsapp-default text-white hover:bg-whatsapp-dark shadow-sm',
    'secondary' => 'bg-whatsapp-teal text-white hover:bg-opacity-90 shadow-sm',
    'outline' => 'border border-whatsapp-default text-whatsapp-default hover:bg-whatsapp-light hover:bg-opacity-20',
    'text' => 'text-whatsapp-default hover:bg-whatsapp-light hover:bg-opacity-20',
    'white' => 'bg-white text-whatsapp-teal hover:bg-gray-50 shadow-sm',
];

// Width classes
$widthClasses = $fullWidth ? 'w-full' : '';

// Combine all classes
$classes = collect([
    $baseClasses,
    $sizeClasses[$size] ?? $sizeClasses['md'],
    $variantClasses[$variant] ?? $variantClasses['primary'],
    $widthClasses,
])->filter()->implode(' ');

// For <a> tags, set the href attribute
$linkAttributes = $to ? ['href' => $to] : [];

// For <button> tags, set the type attribute if not already set
if ($tag === 'button' && !$attributes->has('type')) {
    $attributes = $attributes->merge(['type' => 'button']);
}

@endphp

<{{ $tag }} {{ $attributes->merge($linkAttributes)->merge(['class' => $classes]) }}>
    {{ $slot }}
</{{ $tag }}>
