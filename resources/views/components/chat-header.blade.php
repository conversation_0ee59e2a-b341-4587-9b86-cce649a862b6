@props([
    'storeName' => 'Default Store',
    'storeLogoUrl' => null,
    'isVerified' => false
])

<div x-data="{ showMenu: false }" @click.away="showMenu = false" class="flex items-center p-3 bg-white text-black h-16 relative border-b border-gray-200 shadow-sm">
    <!-- Back button -->
    <button class="text-black p-1 mr-1">
        <i class="fas fa-arrow-left w-5 h-5"></i>
    </button>

    <!-- Store info -->
    <div class="flex items-center flex-1 ml-1 h-full">
        <div class="w-10 h-10 rounded-full overflow-hidden bg-gray-200 flex-shrink-0 mr-2 flex items-center justify-center">
            @if($storeLogoUrl)
                <img src="{{ $storeLogoUrl }}" alt="{{ $storeName }} Logo" class="w-full h-full object-cover">
            @else
                <i class="fas fa-store text-gray-500"></i>
            @endif
        </div>
        <div class="flex items-center">
            <span class="font-semibold text-base whitespace-nowrap overflow-hidden text-ellipsis max-w-[150px]">{{ $storeName }}</span>
            @if($isVerified)
                <x-verification-badge class="ml-2" />
            @endif
        </div>
    </div>

    <!-- Call button -->
    <button class="text-black p-1 ml-1">
        <i class="fas fa-phone-alt w-5 h-5"></i>
    </button>

    <!-- Menu button with dropdown -->
    <div class="relative">
        <button @click="showMenu = !showMenu" class="text-black p-1 ml-1">
            <i class="fas fa-ellipsis-v w-5 h-5"></i>
        </button>

        <!-- Dropdown menu -->
        <div x-show="showMenu" x-cloak
             x-transition:enter="transition ease-out duration-100"
             x-transition:enter-start="transform opacity-0 scale-95"
             x-transition:enter-end="transform opacity-100 scale-100"
             x-transition:leave="transition ease-in duration-75"
             x-transition:leave-start="transform opacity-100 scale-100"
             x-transition:leave-end="transform opacity-0 scale-95"
             class="absolute top-full right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-20 ring-1 ring-black ring-opacity-5">
            <ul class="py-1">
                <li>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Store Info</a>
                </li>
                <li>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Visit Store</a>
                </li>
                <li>
                    <a href="#" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Create Your Store</a>
                </li>
            </ul>
        </div>
    </div>
</div>
