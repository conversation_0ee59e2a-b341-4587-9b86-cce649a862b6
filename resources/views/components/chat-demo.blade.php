<div x-data="chatDemo()" class="w-full max-w-md mx-auto bg-gray-100 rounded-xl shadow-lg overflow-hidden">
    <x-chat-header />

    <div class="h-96 overflow-y-auto p-4" 
         style="background-color: #E5DDD5; background-image: url('{{ asset('images/chat-background.jpg') }}'); background-repeat: repeat; background-size: 300px auto;"
         x-ref="chatBody">
        <template x-for="message in messages" :key="message.id">
            <div :class="message.sender === 'user' ? 'flex justify-end mb-4' : 'flex justify-start mb-4'">
                <div :class="message.sender === 'user' ? 'bg-whatsapp-message-out' : 'bg-white'" class="rounded-lg px-4 py-2 max-w-[80%] shadow-sm">
                    <p class="text-sm whitespace-pre-line" x-text="message.text"></p>
                    <p class="text-right text-xs text-gray-500 mt-1" x-text="message.timestamp"></p>
                </div>
            </div>
        </template>
        <div x-ref="messagesEnd"></div>
    </div>

    <x-chat-input-bar />
</div>

@push('scripts')
<script>
    function chatDemo() {
        return {
            messages: [
                {
                    id: 1,
                    text: "👋 Welcome to Whamart Store! How can I help you today?\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us",
                    sender: 'bot',
                    timestamp: '10:30 AM'
                }
            ],
            inputMessage: '',
            init() {
                this.$nextTick(() => this.scrollToBottom());
            },
            scrollToBottom() {
                this.$refs.messagesEnd.scrollIntoView({ behavior: 'smooth' });
            },
            handleSendMessage() {
                if (this.inputMessage.trim() === '') return;

                const userMessage = {
                    id: this.messages.length + 1,
                    text: this.inputMessage,
                    sender: 'user',
                    timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                };

                this.messages.push(userMessage);
                const currentInput = this.inputMessage;
                this.inputMessage = '';
                this.$nextTick(() => this.scrollToBottom());

                setTimeout(() => {
                    let botResponseText;
                    if (currentInput === '1' || currentInput.toLowerCase().includes('product')) {
                        botResponseText = "Here are our latest products:\n\n🔹 Premium WhatsApp Theme - $29\n🔹 Basic Store Setup - $49\n🔹 Pro Store Package - $99\n\nReply with the product name to learn more.";
                    } else if (currentInput === '2' || currentInput.toLowerCase().includes('order')) {
                        botResponseText = "Please provide your order number to check the status.";
                    } else if (currentInput === '3' || currentInput.toLowerCase().includes('support')) {
                        botResponseText = "Our support team is available Monday-Friday, 9am-5pm. How can we help you today?";
                    } else if (currentInput === '4' || currentInput.toLowerCase().includes('about')) {
                        botResponseText = "Whamart is a WhatsApp-themed e-commerce platform that helps small shop owners create WhatsApp-style stores with chat interface functionality.";
                    } else {
                        botResponseText = "I'm not sure I understand. Please select one of the options:\n\n1️⃣ View Products\n2️⃣ Check Order Status\n3️⃣ Contact Support\n4️⃣ About Us";
                    }

                    const botResponse = {
                        id: this.messages.length + 1,
                        text: botResponseText,
                        sender: 'bot',
                        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
                    };

                    this.messages.push(botResponse);
                    this.$nextTick(() => this.scrollToBottom());
                }, 1000);
            }
        }
    }
</script>
@endpush
