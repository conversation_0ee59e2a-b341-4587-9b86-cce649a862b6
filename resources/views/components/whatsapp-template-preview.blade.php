@props(['message', 'isOpen' => false])

<div x-data="{ isOpen: @json($isOpen), message: @json($message) }" x-show="isOpen" @keydown.escape.window="isOpen = false" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50" x-cloak>
    <div class="relative bg-white rounded-lg shadow-xl w-full max-w-sm mx-auto">
        <!-- Modal Close Button -->
        <button @click="isOpen = false" class="absolute top-2 right-2 text-gray-500 hover:text-gray-800">
            <i class="fas fa-times h-6 w-6"></i>
        </button>

        <!-- Mobile Frame -->
        <div class="mobile-frame">
            <div class="phone-content">
                <!-- Header -->
                <div class="flex items-center p-3 border-b bg-white">
                    <img src="{{ asset('logo192.png') }}" alt="Store Logo" class="w-8 h-8 rounded-full mr-3">
                    <div class="font-semibold">
                        <span x-text="message.storeName || 'Store Name'"></span>
                        <img src="{{ asset('verified-badge.webp') }}" alt="Verified" class="w-4 h-4 ml-1 inline-block">
                    </div>
                </div>

                <!-- Chat Body -->
                <div class="p-4 bg-whatsapp-light-gray overflow-y-auto">
                    <div class="flex justify-end mb-2">
                        <div class="message-template">
                            <!-- Image -->
                            <template x-if="message.image">
                                <div class="template-image">
                                    <img :src="message.image" alt="Template Image" class="template-img">
                                </div>
                            </template>

                            <!-- Header -->
                            <template x-if="message.header">
                                <div class="template-header" x-text="message.header"></div>
                            </template>

                            <!-- Content -->
                            <div class="template-content" x-html="message.content || 'Your message content will appear here.'"></div>

                            <!-- Footer -->
                            <template x-if="message.footer">
                                <div class="template-footer" x-text="message.footer"></div>
                            </template>

                            <!-- Buttons -->
                            <template x-for="(button, index) in message.buttons" :key="index">
                                <div>
                                    <div class="template-divider"></div>
                                    <div class="template-button">
                                        <div class="button-content" :class="'button-content-' + button.type">
                                            <i class="mr-2" :class="getButtonIcon(button.type)"></i>
                                            <span x-text="button.text"></span>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('alpine:initializing', () => {
    window.getButtonIcon = function(type) {
        switch (type) {
            case 'quick_reply': return 'fas fa-reply';
            case 'url': return 'fas fa-external-link-alt';
            case 'call': return 'fas fa-phone';
            default: return '';
        }
    }
});
</script>

<style>
.mobile-frame {
    background-color: #f8f9fa;
    border: 8px solid #333;
    border-radius: 36px;
    padding: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}
.phone-content {
    background-color: #e5ddd5; /* WhatsApp background color */
    border-radius: 20px;
    overflow: hidden;
    height: 600px;
    display: flex;
    flex-direction: column;
}
.bg-whatsapp-light-gray {
    background-color: #e5ddd5;
}
.message-template {
    background-color: #dcf8c6; /* WhatsApp outgoing message color */
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    width: 100%;
    max-width: 320px;
}
.template-header {
    padding: 8px 12px 4px;
    font-weight: 600;
    font-size: 14px;
    color: #333;
}
.template-image {
    width: 100%;
    overflow: hidden;
}
.template-img {
    width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}
.template-content {
    padding: 8px 12px;
    font-size: 14px;
    line-height: 1.4;
    color: #111;
    word-break: break-word;
}
.template-footer {
    padding: 4px 12px 8px;
    font-size: 12px;
    color: #8c8c8c;
}
.template-divider {
    height: 0.5px;
    background-color: rgba(0,0,0,0.08);
}
.template-button {
    padding: 12px;
    cursor: pointer;
    text-align: center;
}
.button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}
.button-content-quick_reply { color: #128C7E; }
.button-content-url { color: #0078FF; }
.button-content-call { color: #9C27B0; }
</style>
