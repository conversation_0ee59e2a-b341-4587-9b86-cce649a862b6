@props([
    'isOpen' => false,
    'message' => null, // Pass initial message data as a JSON string or array
])

<div
    x-data="messageEditor({ isOpen: {{ $isOpen ? 'true' : 'false' }}, message: {{ $message ? Illuminate\Support\Js::from($message) : 'null' }} })"
    @keydown.escape.window="closeEditor()"
    x-on:open-editor.window="openEditor($event.detail)"
    x-show="show"
    class="fixed inset-0 z-50 overflow-hidden"
    style="display: none;"
>
    <!-- Overlay -->
    <div x-show="show" x-transition:enter="ease-in-out duration-500" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in-out duration-500" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="absolute inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

    <div class="fixed inset-y-0 right-0 flex max-w-full pl-10">
        <div 
            x-show="show"
            x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:enter-start="translate-x-full"
            x-transition:enter-end="translate-x-0"
            x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
            x-transition:leave-start="translate-x-0"
            x-transition:leave-end="translate-x-full"
            class="w-screen max-w-md"
        >
            <div class="flex flex-col h-full bg-white shadow-xl">
                <!-- Header -->
                <div class="px-4 py-6 bg-gray-50 sm:px-6">
                    <div class="flex items-center justify-between">
                        <h2 class="text-lg font-medium text-gray-900">Message Editor</h2>
                        <button @click="closeEditor()" class="text-gray-400 hover:text-gray-500">
                            <x-heroicon-o-x-mark class="w-6 h-6" />
                        </button>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="saveChanges()" class="flex flex-col flex-1 overflow-y-auto">
                    <div class="flex-1 px-4 py-6 sm:px-6">
                        <!-- Header Text -->
                        <div class="mb-4">
                            <label for="msg-header" class="block text-sm font-medium text-gray-700">Header</label>
                            <input type="text" x-model="formData.header" id="msg-header" class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-whatsapp-default focus:border-whatsapp-default sm:text-sm" placeholder="Optional header text">
                        </div>

                        <!-- Content -->
                        <div class="mb-4">
                            <label for="msg-content" class="block text-sm font-medium text-gray-700">Content *</label>
                            <textarea x-model="formData.content" id="msg-content" rows="5" class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-whatsapp-default focus:border-whatsapp-default sm:text-sm" placeholder="Enter your message content..." required></textarea>
                        </div>

                        <!-- Footer -->
                        <div class="mb-4">
                            <label for="msg-footer" class="block text-sm font-medium text-gray-700">Footer</label>
                            <input type="text" x-model="formData.footer" id="msg-footer" class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-whatsapp-default focus:border-whatsapp-default sm:text-sm" placeholder="Optional footer text">
                        </div>
                        
                        <!-- Buttons -->
                        <div class="p-3 my-4 border border-gray-200 rounded-md">
                            <h3 class="mb-2 text-sm font-medium text-gray-800">Buttons</h3>
                            <div class="space-y-3">
                                <template x-for="(button, index) in formData.buttons" :key="index">
                                    <div class="flex items-center space-x-2">
                                        <input type="text" x-model="button.text" class="flex-1 block w-full border-gray-300 rounded-md shadow-sm sm:text-sm" placeholder="Button text">
                                        <button @click="removeButton(index)" type="button" class="text-red-500 hover:text-red-700">
                                            <x-heroicon-o-x-mark class="w-5 h-5" />
                                        </button>
                                    </div>
                                </template>
                            </div>
                            <template x-if="formData.buttons.length < 3">
                                <button @click="addButton()" type="button" class="inline-flex items-center px-2 py-1 mt-3 text-sm font-medium text-white rounded-md bg-whatsapp-default hover:bg-whatsapp-dark">
                                    <x-heroicon-o-plus class="w-4 h-4 mr-1"/> Add Button
                                </button>
                            </template>
                        </div>

                    </div>

                    <!-- Actions -->
                    <div class="flex justify-end px-4 py-4 space-x-3 bg-gray-50 border-t border-gray-200 sm:px-6">
                        <button type="button" @click="closeEditor()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50">Cancel</button>
                        <button type="submit" class="px-4 py-2 text-sm font-medium text-white rounded-md shadow-sm bg-whatsapp-default hover:bg-whatsapp-dark">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('messageEditor', ({ isOpen, message }) => ({
            show: isOpen,
            formData: {},
            init() {
                this.resetForm(message);
                this.$watch('show', value => {
                    if (value) {
                        document.body.classList.add('overflow-hidden');
                    } else {
                        document.body.classList.remove('overflow-hidden');
                    }
                });
            },
            resetForm(msg) {
                const defaultMessage = {
                    id: null,
                    header: '',
                    content: '',
                    footer: '',
                    buttons: [],
                };
                this.formData = { ...defaultMessage, ...(msg || {}) };
                // Ensure buttons is always an array
                if (!Array.isArray(this.formData.buttons)) {
                    this.formData.buttons = [];
                }
            },
            openEditor(msg) {
                this.resetForm(msg);
                this.show = true;
            },
            closeEditor() {
                this.show = false;
            },
            saveChanges() {
                if (!this.formData.content.trim()) {
                    alert('Message content is required.');
                    return;
                }
                this.$dispatch('save-message', JSON.parse(JSON.stringify(this.formData)));
                this.closeEditor();
            },
            addButton() {
                if (this.formData.buttons.length < 3) {
                    this.formData.buttons.push({ text: '' });
                }
            },
            removeButton(index) {
                this.formData.buttons.splice(index, 1);
            },
        }));
    });
</script>
