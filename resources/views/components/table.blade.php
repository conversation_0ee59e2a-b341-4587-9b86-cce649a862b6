@props([
    'headers' => [],
    'rows' => []
])

<div class="bg-white shadow-sm rounded-lg overflow-hidden">
    <div class="flex flex-col">
        <div class="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
                <div class="overflow-hidden border-b border-gray-200">
                    <div class="min-w-full divide-y divide-gray-200">
                        <div class="bg-gray-50">
                            <div class="flex px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                @foreach($headers as $header)
                                    <div class="flex-1">{{ $header }}</div>
                                @endforeach
                            </div>
                        </div>
                        <div class="bg-white divide-y divide-gray-200">
                            @if(count($rows) > 0)
                                @foreach($rows as $row)
                                    <div class="flex px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        @foreach($row as $cell)
                                            <div class="flex-1">{!! $cell !!}</div>
                                        @endforeach
                                    </div>
                                @endforeach
                            @else
                                <div class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    No data available.
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
