@php
$steps = [
    [
      'id' => '01',
      'name' => 'Sign Up',
      'description' => 'Create your account and choose your subscription plan.',
    ],
    [
      'id' => '02',
      'name' => 'Set Up Your Store',
      'description' => 'Add your products, customize your chat flows, and configure your payment methods.',
    ],
    [
      'id' => '03',
      'name' => 'Share Your Store Link',
      'description' => 'Share your unique WhatsApp store link with your customers through social media or direct messages.',
    ],
    [
      'id' => '04',
      'name' => 'Start Selling',
      'description' => 'Customers can browse and purchase products through a familiar WhatsApp-like chat interface.',
    ],
];
@endphp

<div id="how-it-works" class="py-24 bg-white">
    <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <div class="mx-auto max-w-2xl text-center">
            <h2 class="text-base font-semibold leading-7 text-whatsapp-default">How It Works</h2>
            <p class="mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                Start selling in minutes
            </p>
            <p class="mt-6 text-lg leading-8 text-gray-600">
                Our simple 4-step process gets you up and running quickly with your WhatsApp store.
            </p>
        </div>

        <div class="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl class="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-4">
                @foreach ($steps as $step)
                    <div class="flex flex-col">
                        <dt class="flex items-center gap-x-3 text-base font-semibold leading-7 text-gray-900">
                            <div class="flex h-12 w-12 items-center justify-center rounded-full bg-whatsapp-default text-white text-lg font-bold">
                                {{ $step['id'] }}
                            </div>
                            {{ $step['name'] }}
                        </dt>
                        <dd class="mt-4 flex flex-auto flex-col text-base leading-7 text-gray-600">
                            <p class="flex-auto">{{ $step['description'] }}</p>
                            @if ($step['id'] === '04')
                                <p class="mt-6 flex items-center text-sm font-medium text-whatsapp-default">
                                    <i class="fas fa-check-circle h-5 w-5 mr-2"></i>
                                    Start your 14-day free trial
                                </p>
                            @endif
                        </dd>
                    </div>
                @endforeach
            </dl>
        </div>
    </div>
</div>
