<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Create Account - Whamart</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('css/auth.css') }}">
    <link rel="stylesheet" href="{{ asset('css/auth-mobile.css') }}">
    <meta name="theme-color" content="#25D366">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
</head>
<body>
    <div class="auth-container">
        <!-- Mobile Header -->
        <div class="mobile-auth-header">
            <div class="mobile-logo">
                <div class="mobile-logo-text">W</div>
            </div>
            <h1 class="mobile-brand-name">Whamart</h1>
            <p class="mobile-brand-tagline">Start your journey! Create your account</p>
        </div>

        <!-- Mobile Form Container -->
        <div class="mobile-form-container">
            <div class="mobile-form-header">
                <h2 class="mobile-form-title">Create Account</h2>
                <p class="mobile-form-subtitle">Fill in your details to get started with your store</p>
            </div>

            @if ($errors->any())
                <div class="mobile-alert mobile-alert-error">
                    @foreach ($errors->all() as $error)
                        <div>{{ $error }}</div>
                    @endforeach
                </div>
            @endif

            @if (session('success'))
                <div class="mobile-alert mobile-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            <form method="POST" action="{{ route('register') }}" class="mobile-auth-form">
                @csrf

                <div class="mobile-form-group">
                    <label for="name" class="mobile-form-label">Full Name</label>
                    <input
                        type="text"
                        id="name"
                        name="name"
                        class="mobile-form-input"
                        placeholder="Enter your full name"
                        value="{{ old('name') }}"
                        required
                        autofocus
                    >
                </div>

                <div class="mobile-form-group">
                    <label for="email" class="mobile-form-label">Email Address</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="mobile-form-input"
                        placeholder="Enter your email"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                    >
                </div>

                <div class="mobile-form-group">
                    <label for="password" class="mobile-form-label">Password</label>
                    <div class="mobile-password-field">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="mobile-form-input"
                            placeholder="Create a strong password"
                            required
                            autocomplete="new-password"
                        >
                        <button type="button" class="mobile-password-toggle" onclick="toggleMobilePassword('password')">
                            <i class="fas fa-eye" id="password-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mobile-form-group">
                    <label for="password_confirmation" class="mobile-form-label">Confirm Password</label>
                    <div class="mobile-password-field">
                        <input
                            type="password"
                            id="password_confirmation"
                            name="password_confirmation"
                            class="mobile-form-input"
                            placeholder="Confirm your password"
                            required
                            autocomplete="new-password"
                        >
                        <button type="button" class="mobile-password-toggle" onclick="toggleMobilePassword('password_confirmation')">
                            <i class="fas fa-eye" id="password_confirmation-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Hidden field for default userType -->
                <input type="hidden" name="userType" value="vendor">

                <div class="mobile-checkbox-group">
                    <input type="checkbox" id="terms" name="terms" class="mobile-checkbox" required>
                    <label for="terms" class="mobile-checkbox-label">
                        I agree to the <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" class="mobile-primary-button">
                    Create My Account
                </button>
            </form>

            <div class="mobile-auth-footer">
                <p>
                    Already have an account?
                    <a href="{{ route('login') }}">Sign In</a>
                </p>
            </div>
        </div>

        <!-- Desktop Layout - Hidden on Mobile -->
        <div class="auth-branding">
            <div class="brand-logo">
                <div class="logo-fallback">W</div>
                <div class="brand-name">Whamart</div>
            </div>
            <div class="brand-tagline">Start Your Journey!</div>
            <div class="brand-description">
                Create your account and launch your online store in minutes.
            </div>
            <div class="brand-features">
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>10-minute setup</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>0% commission</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>24/7 support</span>
                </div>
            </div>
        </div>

        <!-- Desktop Form Container - Hidden on Mobile -->
        <div class="auth-form-container">
            <div class="auth-header">
                <h1 class="auth-title">Create Account</h1>
                <p class="auth-subtitle">Fill in your details to get started with your online store</p>
            </div>

            @if ($errors->any())
                <div class="error-message">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="success-message">
                    {{ session('success') }}
                </div>
            @endif

            <form method="POST" action="{{ route('register') }}" class="auth-form">
                @csrf

                <div class="form-group">
                    <label for="desktop-name" class="form-label">Full Name</label>
                    <input
                        type="text"
                        id="desktop-name"
                        name="name"
                        class="form-input"
                        placeholder="Enter your full name"
                        value="{{ old('name') }}"
                        required
                    >
                </div>

                <div class="form-group">
                    <label for="desktop-email" class="form-label">Email Address</label>
                    <input
                        type="email"
                        id="desktop-email"
                        name="email"
                        class="form-input"
                        placeholder="Enter your email address"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                    >
                </div>

                <div class="form-group">
                    <label for="desktop-password" class="form-label">Password</label>
                    <div class="password-field">
                        <input
                            type="password"
                            id="desktop-password"
                            name="password"
                            class="form-input"
                            placeholder="Create a strong password"
                            required
                            autocomplete="new-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('desktop-password')">
                            <i class="fas fa-eye" id="desktop-password-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="desktop-password-confirmation" class="form-label">Confirm Password</label>
                    <div class="password-field">
                        <input
                            type="password"
                            id="desktop-password-confirmation"
                            name="password_confirmation"
                            class="form-input"
                            placeholder="Confirm your password"
                            required
                            autocomplete="new-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('desktop-password-confirmation')">
                            <i class="fas fa-eye" id="desktop-password-confirmation-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Hidden field for default userType -->
                <input type="hidden" name="userType" value="vendor">

                <div class="form-checkbox">
                    <input type="checkbox" id="desktop-terms" name="terms" class="checkbox-input" required>
                    <label for="desktop-terms" class="checkbox-label">
                        I agree to the <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" class="auth-button">
                    Create My Account
                </button>
            </form>

            <div class="auth-footer">
                <p>
                    Already have an account?
                    <a href="{{ route('login') }}">Sign In</a>
                </p>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/mobile-auth.js') }}"></script>
    <script>
        // Mobile password toggle function
        function toggleMobilePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(fieldId + '-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Desktop password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(fieldId + '-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Enhanced mobile experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch feedback to buttons
            const buttons = document.querySelectorAll('.mobile-primary-button, .mobile-social-button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });
                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Auto-focus on mobile
            if (window.innerWidth <= 768) {
                const nameField = document.getElementById('name');
                if (nameField) {
                    setTimeout(() => nameField.focus(), 500);
                }
            }

            // Prevent zoom on input focus (iOS)
            const inputs = document.querySelectorAll('.mobile-form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });
                input.addEventListener('blur', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
                    }
                });
            });

            // Password strength indicator
            const passwordField = document.getElementById('password');
            if (passwordField) {
                passwordField.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    
                    if (password.length >= 8) strength++;
                    if (password.match(/[a-z]/)) strength++;
                    if (password.match(/[A-Z]/)) strength++;
                    if (password.match(/[0-9]/)) strength++;
                    if (password.match(/[^a-zA-Z0-9]/)) strength++;
                    
                    // Visual feedback can be added here
                });
            }
        });

        // Auto-hide messages after 5 seconds
        setTimeout(() => {
            const messages = document.querySelectorAll('.error-message, .success-message, .mobile-alert');
            messages.forEach(message => {
                message.style.opacity = '0';
                message.style.transform = 'translateY(-10px)';
                setTimeout(() => message.remove(), 300);
            });
        }, 5000);

        // Handle form submission with loading state
        document.querySelector('.mobile-auth-form')?.addEventListener('submit', function() {
            const button = this.querySelector('.mobile-primary-button');
            button.textContent = 'Creating Account...';
            button.style.opacity = '0.7';
            button.disabled = true;
        });
    </script>
</body>
</html>
