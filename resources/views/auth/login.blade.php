<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Sign In - Whamart</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="{{ asset('css/auth.css') }}">
    <link rel="stylesheet" href="{{ asset('css/auth-mobile.css') }}">
    <meta name="theme-color" content="#25D366">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
</head>
<body>
    <div class="auth-container">
        <!-- Mobile Header -->
        <div class="mobile-auth-header">
            <div class="mobile-logo">
                <div class="mobile-logo-text">W</div>
            </div>
            <h1 class="mobile-brand-name">Whamart</h1>
            <p class="mobile-brand-tagline">Welcome back! Sign in to continue</p>
        </div>

        <!-- Mobile Form Container -->
        <div class="mobile-form-container">
            <div class="mobile-form-header">
                <h2 class="mobile-form-title">Sign In</h2>
                <p class="mobile-form-subtitle">Enter your credentials to access your dashboard</p>
            </div>

            @if ($errors->any())
                <div class="mobile-alert mobile-alert-error">
                    @foreach ($errors->all() as $error)
                        <div>{{ $error }}</div>
                    @endforeach
                </div>
            @endif

            @if (session('success'))
                <div class="mobile-alert mobile-alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('status'))
                <div class="mobile-alert mobile-alert-success">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}" class="mobile-auth-form">
                @csrf

                <div class="mobile-form-group">
                    <label for="email" class="mobile-form-label">Email Address</label>
                    <input
                        type="email"
                        id="email"
                        name="email"
                        class="mobile-form-input"
                        placeholder="Enter your email"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                        autofocus
                    >
                </div>

                <div class="mobile-form-group">
                    <label for="password" class="mobile-form-label">Password</label>
                    <div class="mobile-password-field">
                        <input
                            type="password"
                            id="password"
                            name="password"
                            class="mobile-form-input"
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="mobile-password-toggle" onclick="toggleMobilePassword('password')">
                            <i class="fas fa-eye" id="password-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="mobile-checkbox-group">
                    <input type="checkbox" id="remember" name="remember" class="mobile-checkbox">
                    <label for="remember" class="mobile-checkbox-label">
                        Keep me signed in for 30 days
                    </label>
                </div>

                <button type="submit" class="mobile-primary-button">
                    Sign In to Dashboard
                </button>
            </form>

            <div class="mobile-auth-footer">
                <p>
                    Don't have an account?
                    <a href="{{ route('register') }}">Create Account</a>
                </p>
                @if (Route::has('password.request'))
                    <p style="margin-top: 12px;">
                        <a href="{{ route('password.request') }}">Forgot your password?</a>
                    </p>
                @endif
            </div>
        </div>

        <!-- Desktop Layout - Hidden on Mobile -->
        <div class="auth-branding">
            <div class="brand-logo">
                <div class="logo-fallback">W</div>
                <div class="brand-name">Whamart</div>
            </div>
            <div class="brand-tagline">Welcome Back!</div>
            <div class="brand-description">
                Sign in to your account and continue building your online store success story.
            </div>
            <div class="brand-features">
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Manage your online store</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Track orders and analytics</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Access customer support</span>
                </div>
                <div class="feature-item">
                    <i class="fas fa-check"></i>
                    <span>Update store settings</span>
                </div>
            </div>
        </div>

        <!-- Desktop Form Container - Hidden on Mobile -->
        <div class="auth-form-container">
            <div class="auth-header">
                <h1 class="auth-title">Sign In</h1>
                <p class="auth-subtitle">Enter your credentials to access your Whamart dashboard</p>
            </div>

            @if ($errors->any())
                <div class="error-message">
                    <ul style="margin: 0; padding-left: 20px;">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @if (session('success'))
                <div class="success-message">
                    {{ session('success') }}
                </div>
            @endif

            @if (session('status'))
                <div class="success-message">
                    {{ session('status') }}
                </div>
            @endif

            <form method="POST" action="{{ route('login') }}" class="auth-form">
                @csrf

                <div class="form-group">
                    <label for="desktop-email" class="form-label">Email Address</label>
                    <input
                        type="email"
                        id="desktop-email"
                        name="email"
                        class="form-input"
                        placeholder="Enter your email address"
                        value="{{ old('email') }}"
                        required
                        autocomplete="email"
                    >
                </div>

                <div class="form-group">
                    <label for="desktop-password" class="form-label">Password</label>
                    <div class="password-field">
                        <input
                            type="password"
                            id="desktop-password"
                            name="password"
                            class="form-input"
                            placeholder="Enter your password"
                            required
                            autocomplete="current-password"
                        >
                        <button type="button" class="password-toggle" onclick="togglePassword('desktop-password')">
                            <i class="fas fa-eye" id="desktop-password-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-checkbox">
                    <input type="checkbox" id="desktop-remember" name="remember" class="checkbox-input">
                    <label for="desktop-remember" class="checkbox-label">
                        Remember me for 30 days
                    </label>
                </div>

                <button type="submit" class="auth-button">
                    Sign In to Dashboard
                </button>
            </form>

            <div class="auth-footer">
                <p>
                    Don't have an account?
                    <a href="{{ route('register') }}">Create Account</a>
                </p>
                <p style="margin-top: 10px;">
                    @if (Route::has('password.request'))
                        <a href="{{ route('password.request') }}">Forgot your password?</a>
                    @endif
                </p>
            </div>
        </div>
    </div>

    <script src="{{ asset('js/mobile-auth.js') }}"></script>
    <script>
        // Mobile password toggle function
        function toggleMobilePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(fieldId + '-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Desktop password toggle function
        function togglePassword(fieldId) {
            const passwordField = document.getElementById(fieldId);
            const eyeIcon = document.getElementById(fieldId + '-eye');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                eyeIcon.classList.remove('fa-eye');
                eyeIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                eyeIcon.classList.remove('fa-eye-slash');
                eyeIcon.classList.add('fa-eye');
            }
        }

        // Enhanced mobile experience
        document.addEventListener('DOMContentLoaded', function() {
            // Add touch feedback to buttons
            const buttons = document.querySelectorAll('.mobile-primary-button, .mobile-social-button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.98)';
                });
                button.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Auto-focus on mobile
            if (window.innerWidth <= 768) {
                const emailField = document.getElementById('email');
                if (emailField) {
                    setTimeout(() => emailField.focus(), 500);
                }
            }

            // Prevent zoom on input focus (iOS)
            const inputs = document.querySelectorAll('.mobile-form-input');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                    }
                });
                input.addEventListener('blur', function() {
                    if (window.innerWidth <= 768) {
                        document.querySelector('meta[name=viewport]').setAttribute('content', 'width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover');
                    }
                });
            });
        });

        // Auto-hide messages after 5 seconds
        setTimeout(() => {
            const messages = document.querySelectorAll('.error-message, .success-message, .mobile-alert');
            messages.forEach(message => {
                message.style.opacity = '0';
                message.style.transform = 'translateY(-10px)';
                setTimeout(() => message.remove(), 300);
            });
        }, 5000);

        // Handle form submission with loading state
        document.querySelector('.mobile-auth-form')?.addEventListener('submit', function() {
            const button = this.querySelector('.mobile-primary-button');
            button.textContent = 'Signing In...';
            button.style.opacity = '0.7';
            button.disabled = true;
        });
    </script>
</body>
</html>

